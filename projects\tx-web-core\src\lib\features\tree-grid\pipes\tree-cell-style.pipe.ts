import { Pipe, PipeTransform } from '@angular/core';
@Pipe({
  standalone: true,
  name: 'treeCellStyle',
})
export class TreeCellStylePipe implements PipeTransform {
  transform(level: number, enableNativeTreeLook?: boolean): { [key: string]: string } {
    const baseWidth = level ? 30 : 36;
    const finalWidth = enableNativeTreeLook ? 36 : baseWidth;
    const flexValue = '0 0 ' + ((level ?? 0) + 1) * finalWidth + 'px';
    const justifyContentValue = enableNativeTreeLook ? 'none' : 'flex-end';

    return {
      flex: flexValue,
      'justify-content': justifyContentValue,
    };
  }
}
