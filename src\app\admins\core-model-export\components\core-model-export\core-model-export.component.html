<app-breadcrumd class="breadcrumd"></app-breadcrumd>
<div class="spinner-container" *ngIf="isLoading$ | async">
  <mat-spinner [diameter]="50" color="accent"></mat-spinner>
</div>
<div class="content">
  <header>
    <div class="title-button-actions">
      <h1 class="h1-title">
        {{ 'admins.wording.coreModelsExport' | translate }}
      </h1>
      <mat-divider> </mat-divider>
      <div class="actions-container">
        <button
          mat-flat-button
          (click)="exportCoreModel()"
          [disabled]="(canExport$ | async) === false"
          color="accent">
          {{ 'button.export' | translate }}
        </button>
        <button mat-stroked-button (click)="refreshCoreModelConcepts()">
          {{ 'button.refresh' | translate }}
        </button>
      </div>
    </div>
    <mat-divider></mat-divider>
  </header>
  <mat-tab-group [selectedIndex]="selectedIndex" mat-stretch-tabs="false" color="accent">
    <mat-tab>
      <ng-template mat-tab-label>
        {{ 'admins.coreModels.toExport' | translate }}
      </ng-template>
      <ng-template matTabContent>
        <div class="tab-container">
          <app-core-models-concepts
            (filterOnConceptsInError)="filterOnConceptsInError($event)"
            [flatConcepts]="flatConcepts$ | async"
            [nbErrors]="conceptsNbErrors$ | async"
            [showOnlyErrors]="showOnlyErrors$ | async"
            [isLoading]="isLoading$ | async"></app-core-models-concepts>
        </div>
      </ng-template>
    </mat-tab>
    <ng-container *ngIf="history$ | async as history">
      <mat-tab [disabled]="history.length === 0">
        <ng-template mat-tab-label>
          {{ 'generic.history' | translate }}
        </ng-template>
        <ng-template matTabContent>
          <div class="tab-container">
            <app-core-model-export-history
              [history]="history"
              [isLoading]="isLoading$ | async"></app-core-model-export-history>
          </div>
        </ng-template>
      </mat-tab>
    </ng-container>
  </mat-tab-group>
</div>
