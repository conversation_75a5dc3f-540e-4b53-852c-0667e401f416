import { LegacyTxTableTypeService } from './table-type.service';
import { TxFileTypeService } from './file-type.service';
import { LegacyTxDataType } from './../models/data';
import { TxApiService } from './api.service';
import { Observable, throwError } from 'rxjs';
import { Injectable } from '@angular/core';
import {
  LegacyTxAttribute,
  TxAttributeLink,
  TxAttributeMail,
  LegacyTxAttributeFile,
  TxAttributePoint,
  LegacyTxAttributeTable,
  TxAttributeUrl,
} from './../models/attribute';
import { TxLinkTypeService } from './link-type.service';
import { TxStructureService } from './structure.service';

@Injectable({
  providedIn: 'root',
})
export class LegacyTxAttributesService {
  private attributes: LegacyTxAttribute[] = [];

  private createAttribute(attribute: any): LegacyTxAttribute {
    let newAttribute: any;
    switch (attribute.dataType) {
      case LegacyTxDataType.LnkDirect:
      case LegacyTxDataType.Enum:
      case LegacyTxDataType.LnkInv:
      case LegacyTxDataType.LnkBi:
        newAttribute = new TxAttributeLink(attribute);
        newAttribute.linkType = this.linkTypeService.get(attribute.idLinkType);

        if (newAttribute.linkType.isAssociative) {
          // load asso attributes
          this.listObjectTypeAttributes(newAttribute.linkType.idSourceObjectType);
        }
        break;
      case LegacyTxDataType.Email:
        newAttribute = new TxAttributeMail(attribute);
        break;
      case LegacyTxDataType.File:
        newAttribute = new LegacyTxAttributeFile(attribute);
        newAttribute.fileType = this.fileTypeService.get(attribute.idFileType);
        break;
      case LegacyTxDataType.DecRangeMean:
      case LegacyTxDataType.DecRange:
      case LegacyTxDataType.DecUnique:
        newAttribute = new TxAttributePoint(attribute);
        break;
      case LegacyTxDataType.Table:
        newAttribute = new LegacyTxAttributeTable(attribute);
        newAttribute.tableType = this.tableTypeService.get(attribute.idTableType);
        break;
      case LegacyTxDataType.URL:
        newAttribute = new TxAttributeUrl(attribute);
        break;
      default:
        newAttribute = new LegacyTxAttribute(attribute);
        break;
    }

    if (!this.attributes.some((a) => a.id === newAttribute.id)) {
      this.attributes.push(newAttribute);
    }

    return newAttribute;
  }

  private createAttributes(attributes: any[]): LegacyTxAttribute[] {
    return attributes.map((a) => this.createAttribute(a));
  }

  private getAttribute(param: string | number) {
    return this.attributes.find((att) => {
      if (typeof param === 'number') {
        return att.id === param;
      } else if (typeof param === 'string') {
        return att.tags.indexOf(param.toString()) > -1;
      }
    });
  }

  constructor(
    private apiService: TxApiService,
    public fileTypeService: TxFileTypeService,
    public tableTypeService: LegacyTxTableTypeService,
    public linkTypeService: TxLinkTypeService,
    public structureService: TxStructureService
  ) {
    (window as any)['attributeService'] = this;
  }

  tagToId(tag: string): number;
  tagToId(tags: string[]): number[];
  tagToId(param: string | string[]): number | number[] {
    const isArray = Array.isArray(param);
    const tags: string[] = isArray ? (param as string[]) : [param as string];
    const ids: number[] = [];

    tags.forEach((t) => {
      const attribute = this.getAttribute(t);
      if (attribute) {
        ids.push(attribute.id);
      }
    });

    return isArray ? ids : ids[0];
  }

  getHint(id: number): string {
    let hint = '';
    const attribute = this.getAttribute(id);

    if (attribute) {
      hint = attribute.name;

      if (!this.apiService.productionMode) {
        hint += `\nid: ${attribute.id}`;

        if (attribute.tags?.length) {
          hint += `
            Tags: ${attribute.tags.join(', ')}`;
        }
      }
    }

    return hint;
  }

  isLink(dataType: LegacyTxDataType): boolean {
    return [
      LegacyTxDataType.Link,
      LegacyTxDataType.LinkAss,
      LegacyTxDataType.LnkInv,
      LegacyTxDataType.LnkBi,
      LegacyTxDataType.LnkDirect,
    ].includes(dataType);
  }

  findAttribute(idAttribute: number): LegacyTxAttribute;
  findAttribute(tag: string): LegacyTxAttribute;
  findAttribute(param: any) {
    const attribute = this.getAttribute(param);

    if (!attribute) {
      throwError(`The attribute with this param ${param} not exists or not loaded yet...`);
      return attribute;
    }

    return attribute;
  }

  listObjectTypeAttributes(idObjectType: number): Observable<LegacyTxAttribute[]> {
    return new Observable((observer) => {
      this.apiService.listAttributesFromObjectType(idObjectType).subscribe((result) => {
        if (this.structureService.structureLoaded.isStopped) {
          const attributes = this.createAttributes(result);
          observer.next(attributes);
          observer.complete();
        } else {
          this.structureService.structureLoaded.subscribe(() => {
            const attributes = this.createAttributes(result);
            observer.next(attributes);
            observer.complete();
          });
        }
      });
    });
  }

  findAttributeFromAssociativeAttribute(idAttAssociative: number): LegacyTxAttribute[] {
    const assoAtt = this.findAttribute(idAttAssociative) as TxAttributeLink;
    let assoAtts: any[] = [];

    if (assoAtt) {
      assoAtts = this.attributes.filter(
        (a) => a.idObjectType === assoAtt.linkType?.idSourceObjectType
      );
    }
    return assoAtts;
  }

  listAttributes(param: number[] | number | string[] | string): Observable<LegacyTxAttribute[]> {
    return new Observable((observer) => {
      const _updateAttributes = (result: any) => {
        const newAttributes = this.createAttributes(result);

        elementsLoaded.forEach((element, index) => {
          if (element.toReplace) {
            const key = element.id ? 'id' : 'tags';
            const attribute = newAttributes.find((attribute) => attribute[key] == element[key]);
            elementsLoaded[index] = attribute;
          }
        });

        if (!elementsLoaded.length) {
          elementsLoaded = newAttributes;
        }

        observer.next(elementsLoaded);
        observer.complete();
      };

      const isAttributeArray = Array.isArray(param);
      const byId = isAttributeArray
        ? param[0]
          ? typeof param[0] === 'number'
          : null
        : typeof param === 'number';
      const attributes: LegacyTxAttribute[] = [];
      const attributesIdsToLoad: number[] = [];
      const attributesTagsToLoad: string[] = [];
      let elementsLoaded: any[] = [];
      const elements = isAttributeArray ? param : [param];

      elements.forEach((id: any, index: number) => {
        const attribute = this.getAttribute(id);
        if (attribute) {
          attributes.push(attribute);
          elementsLoaded.push(attribute);
        } else {
          if (byId) {
            elementsLoaded.push({ id: id, toReplace: true });
            attributesIdsToLoad.push(id as number);
          } else {
            attributesTagsToLoad.push(id as string);
            elementsLoaded.push({ tag: id as string, toReplace: true });
          }
        }
      });

      if (attributesIdsToLoad.length) {
        this.apiService.listAttributesFromIds(attributesIdsToLoad).subscribe((result) => {
          if (this.structureService.structureLoaded.isStopped) {
            _updateAttributes(result);
          } else {
            this.structureService.structureLoaded.subscribe(() => {
              _updateAttributes(result);
            });
          }
        });
      } else if (attributesTagsToLoad.length) {
        this.apiService.listAttributesFromTags(attributesTagsToLoad).subscribe((result) => {
          if (this.structureService.structureLoaded.isStopped) {
            _updateAttributes(result);
          } else {
            this.structureService.structureLoaded.subscribe(() => {
              _updateAttributes(result);
            });
          }
        });
      } else {
        observer.next(attributes);
        observer.complete();
      }
    });
  }
}
