import {
  TxGridFilterConditions,
  TxGridFilterOperators,
  TxGridFilterOption,
  ValueLabel,
} from './grid.interface';

export const CONDITIONS_LIST: ValueLabel<TxGridFilterOperators>[] = [
  { value: 'is-empty', label: 'txWebCore.tx-grid.is-empty' }, //Is empty
  { value: 'is-not-empty', label: 'txWebCore.tx-grid.is-not-empty' }, //Is not empty
  { value: 'equal', label: 'txWebCore.tx-grid.is-equal' }, //Is equal
  { value: 'notEqual', label: 'txWebCore.tx-grid.is-not-equal' }, //Is not equal
  { value: 'contains', label: 'txWebCore.tx-grid.contains' }, //Contains
];

export type TxGridFilterValueType = string | number | Date;

export const CONDITIONS_FUNCTIONS: TxGridFilterConditions = {
  'is-empty': function (value: TxGridFilterValueType, filteredValue: TxGridFilterValueType) {
    return value === '';
  },
  'is-not-empty': function (value: TxGridFilterValueType, filteredValue: TxGridFilterValueType) {
    return value !== '';
  },
  equal: function (value: TxGridFilterValueType, filteredValue: TxGridFilterValueType) {
    if (value instanceof Date && filteredValue instanceof Date) {
      return new Date(value).getTime() === new Date(filteredValue).getTime();
    }
    return value == filteredValue;
  },
  notEqual: function (value: TxGridFilterValueType, filteredValue: TxGridFilterValueType) {
    return value != filteredValue;
  },
  contains: function (value: TxGridFilterValueType, filteredValue: string) {
    return value?.toString().toLowerCase().includes(filteredValue.toLowerCase());
  },
  startsWith: function (value: TxGridFilterValueType, filteredValue: string) {
    return value?.toString().toLowerCase().startsWith(filteredValue.toLowerCase());
  },
  endsWith: function (value: TxGridFilterValueType, filteredValue: string) {
    return value?.toString().toLowerCase().endsWith(filteredValue.toLowerCase());
  },
  'is-greater': function (value: TxGridFilterValueType, filteredValue: TxGridFilterValueType) {
    return new Date(value).getTime() >= new Date(filteredValue).getTime();
  },
  'is-lesser': function (value: TxGridFilterValueType, filteredValue: TxGridFilterValueType) {
    return new Date(value).getTime() < new Date(filteredValue).getTime();
  },
  greaterThan: function (value: TxGridFilterValueType, filteredValue: TxGridFilterValueType) {
    return value > filteredValue;
  },
  greaterThanOrEqual: function (
    value: TxGridFilterValueType,
    filteredValue: TxGridFilterValueType
  ) {
    return value >= filteredValue;
  },
  lessThan: function (value: TxGridFilterValueType, filteredValue: TxGridFilterValueType) {
    return value < filteredValue;
  },
  lessThanOrEqual: function (value: TxGridFilterValueType, filteredValue: TxGridFilterValueType) {
    return value <= filteredValue;
  },
};

export const CONDITIONS_LIST_DATE: TxGridFilterOption[] = [
  { value: 'is-greater', label: 'txWebCore.tx-grid.today-or-after' }, //Today or after
  { value: 'is-lesser', label: 'txWebCore.tx-grid.before' }, //Before
];

export enum TxGridDataType {
  TEXT = 'string',
  NUMBER = 'number',
  DATE = 'date',
  BOOLEAN = 'boolean',
}

export enum TxGridEditType {
  DropDown = 'dropdown', //dropdownedit
  Boolean = 'boolean', //booleanedit
  Text = 'text',
  AutoComplete = 'autoComplete'
}
export type TxGroupByValue = 'txGroupByValue';
export const TxGroupByValue: TxGroupByValue = 'txGroupByValue';

export { TxGridFilterOperators };
