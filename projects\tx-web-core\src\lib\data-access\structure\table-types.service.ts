import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { SeriesType, TableType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { ArrayUtils } from '@bassetti-group/tx-web-core/src/lib/utilities';
import { TxConfigService } from '@bassetti-group/tx-web-core/src/lib/data-access/config';
import { TxAbstractConceptService } from './abstract-concept.service';
import { TxObjectTypeIconService } from '@bassetti-group/tx-web-core/src/lib/data-access/session';

@Injectable({
  providedIn: 'root',
})
export class TxTableTypesService extends TxAbstractConceptService<TableType> {
  public reloadAll = true;

  protected urlListAllConcepts = 'api/Structure/tabletype';
  private seriesTypeUrl = 'api/Structure/seriesType';

  constructor(
    http: HttpClient,
    configService: TxConfigService,
    objectTypeIcon: TxObjectTypeIconService
  ) {
    super(configService, http, objectTypeIcon);
  }

  isReady(): Observable<boolean> {
    return new Observable((observer) => {
      if (this.reloadAll) {
        this.getTableTypes().subscribe(() => {
          if (!this.reloadAll) {
            observer.next(true);
            observer.complete();
          }
        });
      } else {
        observer.next(true);
        observer.complete();
      }
    });
  }

  public getTableTypes(): Observable<TableType[]> {
    this.concepts = [];
    this.loaderSub.next(true);
    this.http
      .get<TableType[]>(this.apiUrl + this.urlListAllConcepts)
      .pipe(
        tap((tables) => {
          this.concepts = ArrayUtils.sortByName(tables);
          this.reloadAll = false;
          this.concepts.forEach((table) => {
            table.series?.sort((a, b) => a.order - b.order);
          });
          this.updateTables();
          this.loaderSub.next(false);
        })
      )
      .subscribe();
    return this.conceptsSub.asObservable();
  }

  public getSeriesType(id: number): Observable<SeriesType> {
    return this.http.get<SeriesType>(this.apiUrl + this.seriesTypeUrl + '/' + id);
  }

  public addTableType(tableType: TableType): Observable<TableType> {
    return this.http.post<TableType>(this.apiUrl + this.urlListAllConcepts, tableType).pipe(
      tap((newTable) => {
        this.concepts.push(newTable);
        ArrayUtils.sortByName(this.concepts);
        this.updateTables();
      })
    );
  }

  public editTableType(tableType: TableType): Observable<TableType> {
    const copyTable = { ...tableType }; // create a copy to be sure to not modify any reference object
    delete copyTable.series;
    delete copyTable.isUsed;
    return this.http
      .put<TableType>(this.apiUrl + this.urlListAllConcepts + '/' + tableType.id, copyTable)
      .pipe(
        tap(() => {
          const existingTable = this.concepts.find((tt) => tt.id === tableType.id);
          if (existingTable) {
            Object.assign(existingTable, tableType);
            this.updateTables();
          }
        })
      );
  }

  public deleteTableType(tableType: TableType): Observable<any> {
    return this.http.delete(this.apiUrl + this.urlListAllConcepts + '/' + tableType.id).pipe(
      tap(() => {
        // remove table type
        this.concepts = this.concepts.filter((tt) => tt.id !== tableType.id);
        this.updateTables();
      })
    );
  }

  public addSeriesType(seriesType: SeriesType): Observable<SeriesType> {
    return this.http.post<SeriesType>(this.apiUrl + 'api/Structure/seriestype', seriesType).pipe(
      tap((newSeries) => {
        const tableType = this.concepts.find((tt) => tt.id === seriesType.idTableType);
        if (tableType?.series) {
          tableType.series = [...tableType.series, newSeries];
        }
        this.updateTables();
      })
    );
  }

  public editSeriesType(seriesType: SeriesType): Observable<SeriesType> {
    return this.http
      .put<SeriesType>(this.apiUrl + this.seriesTypeUrl + '/' + seriesType.id, seriesType)
      .pipe(
        tap(() => {
          const tableType = this.concepts.find((tt) => tt.id === seriesType.idTableType);
          const existingSeries = tableType?.series?.find((s) => s.id === seriesType.id);
          if (existingSeries) {
            existingSeries.idUnit = undefined; // prevent idUnit deletion
            Object.assign(existingSeries, seriesType);
            this.updateTables();
          }
        })
      );
  }

  public updateSeriesTypeOrder(
    seriesType: SeriesType
  ): Observable<[{ id: number; order: number }]> {
    return this.http
      .put<[{ id: number; order: number }]>(this.apiUrl + this.urlListAllConcepts + '/order', {
        id: seriesType.id,
        order: seriesType.order,
      })
      .pipe(
        tap((results) => {
          const tableType = this.concepts.find((tt) => tt.id === seriesType.idTableType);
          let existingSeries: SeriesType | undefined;
          results.forEach((r) => {
            existingSeries = tableType?.series?.find((s) => s.id === r.id);
            if (existingSeries) {
              existingSeries.order = r.order;
            }
          });
          this.updateTables();
        })
      );
  }

  public deleteSeriesType(seriesType: SeriesType): Observable<any> {
    return this.http.delete(this.apiUrl + 'api/Structure/seriestype/' + seriesType.id).pipe(
      tap(() => {
        // remove series type
        const tableType = this.concepts.find((tt) => tt.id === seriesType.idTableType);
        if (tableType) {
          tableType.series = tableType.series?.filter((s) => s.id !== seriesType.id);
        }
        this.updateTables();
      })
    );
  }

  /* Private Methods */
  private updateTables(): void {
    this.conceptsSub.next(this.concepts);
  }
}
