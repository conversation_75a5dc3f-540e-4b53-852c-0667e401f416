import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { DataModelExport, Worksheets, RawCell } from 'src/app/shared/models/data-model-export';
import { ExportDataMapSettings } from '../models/export-data-map-settings';
import { FilesUtils } from 'src/app/core/utils/files';
import { LinkTypeFilteringType } from 'src/app/shared/models/link-type';
import { UnitsService } from 'src/app/core/services/structure/units.service';
import { Unit } from 'src/app/shared/models/units';
import { TableType } from 'src/app/shared/models/table-types';
import { CTxFileType } from 'src/app/shared/models/file-type';
import {
  TxAttribute,
  TxConfigService,
  TxDataType,
  TxFileTypesService,
  TxLockingType,
  TxObjectType,
  TxObjectTypeDetailed,
  TxObjectTypeType,
  TxObjectsTypeService,
  TxTableTypesService,
} from '@bassetti-group/tx-web-core';

@Injectable({
  providedIn: 'root',
})
export class DataModelService {
  private apiUrl?: string;
  private listUnits: Unit[] = [];
  private listTableType: TableType[] = [];
  private listFileType: CTxFileType[] = [];

  constructor(
    private http: HttpClient,
    private configService: TxConfigService,
    private otService: TxObjectsTypeService,
    private unitService: UnitsService,
    private tableTypeService: TxTableTypesService,
    private filTypeService: TxFileTypesService
  ) {
    this.apiUrl = this.configService.getOfficeRestUrl();
    this.unitService.getUnits().subscribe((units) => {
      this.listUnits = units ?? [];
    });
    this.tableTypeService.getTableTypes().subscribe((tables) => {
      this.listTableType = tables ?? [];
    });
    this.filTypeService.listAll().subscribe((fileType) => {
      this.listFileType = fileType ?? [];
    });
  }

  public mapToJson(map: Map<number, ExportDataMapSettings>) {
    const worksheetsArr: Worksheets[] = [];

    // Generate Attribute Worksheet
    const objectTypeList: TxObjectType[] = [];
    let attributeList: TxAttribute[] = [];
    const oTWithNoAttributes: number[] = [];
    const listOTnotExtract: number[] = [];
    map.forEach((elt, key) => {
      if (elt.allAttributesChecked) {
        // tout les attributes sont check donc on ajoute tous les TxAttribute
        if (
          elt.attributeSetLevels.length === elt.attributes.length &&
          elt.attributes.length !== 0
        ) {
          // les attributes ont ete chargé donc on peut direct les ajouter
          attributeList = attributeList.concat(elt.attributes);
          listOTnotExtract.push(key);
        } else {
          // tous les Attributes sont checked mais on les a pas chargé donc faut les chercher dans le service
          oTWithNoAttributes.push(key);
        }
      } else if (elt.attributeSetLevels.length > 0) {
        // tous les attributes ne sont pas coché donc a chargé les TxAttribute et on ajoute seulement ceux selectionné
        const currentAttChecked: TxAttribute[] = [];
        elt.attributeSetLevels.forEach((attSetLevel) => {
          elt.attributes.forEach((att) => {
            if (attSetLevel.idAttribute === att.id) {
              currentAttChecked.push(att);
            }
          });
        });
        attributeList = attributeList.concat(currentAttChecked);
        listOTnotExtract.push(key);
      }
    });
    // add ObjectType and Attribute that have no Attributes preloaded
    this.otService
      .getObjectTypeWithAttributes(oTWithNoAttributes)
      .subscribe((objectDetailled: TxObjectTypeDetailed[]) => {
        objectDetailled.forEach((objectType) => {
          if (objectType.attributes) {
            objectTypeList.push(Object.assign(objectType));
            attributeList = attributeList.concat(
              objectType.attributes.filter(
                (att) => att.dataType !== TxDataType.Tab && att.dataType !== TxDataType.Group
              )
            );
          }
        });
        // add remaining ObjectType
        this.otService
          .getObjectTypeByIds(listOTnotExtract)
          .subscribe((objectypes: TxObjectType[]) => {
            objectypes.forEach((objectType) => {
              objectTypeList.push(Object.assign(objectType));
            });
            // Generate Attribute Worksheet
            const attributeWorksheet: Worksheets = this.createAttributeWorksheet(
              objectTypeList,
              attributeList
            );
            worksheetsArr.push(attributeWorksheet);

            // Generate ObjectType Worksheet
            const objectTypeWorksheet: Worksheets = this.createObjectWorksheet(objectTypeList);
            worksheetsArr.push(objectTypeWorksheet);

            // Generate DataType Worksheet
            const dataTypeWorksheet: Worksheets = this.createDataTypeWorksheet();
            worksheetsArr.push(dataTypeWorksheet);

            const rawData: DataModelExport = {
              name: 'ExportDataModel.xlsm',
              worksheets: worksheetsArr,
            };

            this.exportDataModel(rawData).subscribe(
              (blob) => {
                FilesUtils.downloadBlobFile(
                  blob,
                  FilesUtils.generateFileName('ExportDataModel', 'xlsm')
                );
              },
              (error) => {}
            );
          });
      });
  }

  public exportDataModel(jsonTemplate: any) {
    return this.http.post(`${this.apiUrl}api/ExcelConverter/JsonToExcel`, jsonTemplate, {
      responseType: 'blob',
    });
  }

  private createAttributeWorksheet(
    objectTypes: TxObjectType[],
    attributeSetLevels: TxAttribute[]
  ): Worksheets {
    let cellsArr: RawCell[] = [];

    type dataType = keyof typeof TxDataType;
    type linkType = keyof typeof LinkTypeFilteringType;
    //TxLinkType
    attributeSetLevels.forEach((elt, index) => {
      const parentOT = objectTypes.filter((ot) => ot.id === elt.idObjectType)[0];
      const otName = parentOT.name; // Object.keys(TxObjectTypeType).find(key => TxObjectTypeType[key as ott] === parentOT.type) ?? '';
      const dataType =
        Object.keys(TxDataType).find((key) => TxDataType[key as dataType] === elt.dataType) ?? '';
      let decimalName = '';
      let tableName = '';
      let fileName = '';
      let listorMultiple = '';
      if (elt.idUnit) {
        decimalName = this.listUnits?.filter((unit) => unit.id === elt.idUnit)[0].name ?? '';
      }
      if (elt.idTableType) {
        tableName =
          this.listTableType?.filter((table) => table.id === elt.idTableType)[0].name ?? '';
      }
      if (elt.idFileType) {
        fileName = this.listFileType?.filter((file) => file.id === elt.idFileType)[0].name ?? '';
      }
      if (['Link', 'File', 'Url', 'Email'].includes(dataType)) {
        listorMultiple = (elt.isList ? 'true' : 'false') ?? '';
      }
      const cells: RawCell[] = [
        { cellValue: otName, row: index + 3, col: 1, fontColor: 'black' },
        { cellValue: elt.name, row: index + 3, col: 2, fontColor: 'black' },
        {
          cellValue: elt.tags.toString(),
          row: index + 3,
          col: 3,
          cellType: '',
          fontColor: 'black',
        },
        {
          cellValue: (elt.explanation ?? '').toString(),
          row: index + 3,
          col: 4,
          cellType: '',
          fontColor: 'black',
        },
        {
          cellValue: (elt.description ?? '').toString(),
          row: index + 3,
          col: 5,
          cellType: '',
          fontColor: 'black',
        },
        {
          cellValue: dataType,
          row: index + 3,
          col: 6,
          cellType: 'ctList:Data Type',
          fontColor: 'black',
        },
        {
          cellValue:
            Object.keys(LinkTypeFilteringType).find(
              (key) => LinkTypeFilteringType[key as linkType] === elt.linkType?.filteringType
            ) ?? '',
          row: index + 3,
          col: 9,
          cellType: '',
          fontColor: 'black',
        },
        { cellValue: decimalName, row: index + 3, col: 10, cellType: '', fontColor: 'black' },
        { cellValue: tableName, row: index + 3, col: 11, cellType: '', fontColor: 'black' },
        { cellValue: fileName, row: index + 3, col: 12, cellType: '', fontColor: 'black' },
        {
          cellValue: listorMultiple,
          row: index + 3,
          col: 13,
          cellType: 'ctList:Boolean',
          fontColor: 'black',
        },
        {
          cellValue: (elt.isTrackable ? 'true' : 'false') ?? '',
          row: index + 3,
          col: 14,
          cellType: 'ctList:Boolean',
          fontColor: 'black',
        },
      ];
      cellsArr = cellsArr.concat(cells);
    });

    const worksheet: Worksheets = {
      name: 'Attributes',
      isVisible: true,
      cells: cellsArr,
    };

    return worksheet;
  }

  private createObjectWorksheet(objectTypes: TxObjectType[]): Worksheets {
    let cellsArr: RawCell[] = [];
    type ott = keyof typeof TxObjectTypeType;
    type lock = keyof typeof TxLockingType;
    objectTypes.forEach((elt, index) => {
      const cells: RawCell[] = [
        { cellValue: elt.name, row: index + 3, col: 1, fontColor: 'black' },
        {
          cellValue: elt.tags.toString(),
          row: index + 3,
          col: 2,
          cellType: '',
          fontColor: 'black',
        },
        {
          cellValue: (elt.explanation ?? '').toString(),
          row: index + 3,
          col: 3,
          cellType: '',
          fontColor: 'black',
        },
        {
          cellValue: (elt.description ?? '').toString(),
          row: index + 3,
          col: 4,
          cellType: '',
          fontColor: 'black',
        },
        {
          cellValue:
            Object.keys(TxObjectTypeType).find(
              (key) => TxObjectTypeType[key as ott] === elt.type
            ) ?? '',
          row: index + 3,
          col: 5,
          cellType: 'ctList:Object Type Type',
          fontColor: 'black',
        },
        {
          cellValue: elt.isVisible ? 'true' : 'false',
          row: index + 3,
          col: 9,
          cellType: 'ctList:Boolean',
          fontColor: 'black',
        },
        {
          cellValue: elt.displayResultInTextSearch ? 'true' : 'false',
          row: index + 3,
          col: 10,
          cellType: 'ctList:Boolean',
          fontColor: 'black',
        },
        {
          cellValue:
            Object.keys(TxLockingType).find(
              (key) => TxLockingType[key as lock] === elt.lockingType
            ) ?? '',
          row: index + 3,
          col: 11,
          cellType: 'ctList:Locking Type',
          fontColor: 'black',
        },
        {
          cellValue: elt.lockingDuration.toString(),
          row: index + 3,
          col: 12,
          cellType: '',
          fontColor: 'black',
        },
      ];
      cellsArr = cellsArr.concat(cells);
    });

    const worksheet: Worksheets = {
      name: 'Object Types',
      isVisible: true,
      cells: cellsArr,
    };

    return worksheet;
  }

  private createDataTypeWorksheet() {
    type dataType = keyof typeof TxDataType;
    let cell: RawCell[] = [];

    Object.keys(TxDataType).forEach((key) => {
      const name = TxDataType[key as dataType].toString();
      if (Number(name).toString() !== name) {
        cell.push({
          cellValue: TxDataType[key as dataType].toString(),
          cellType: 'ctEnumeration:Data Type',
          row: -1,
          col: -1,
          fontColor: 'black',
        });
      }
    });

    const boolean = [
      {
        cellValue: 'True',
        cellType: 'ctEnumeration:Boolean',
        row: -1,
        col: -1,
        fontColor: 'black',
      },
      {
        cellValue: 'False',
        cellType: 'ctEnumeration:Boolean',
        row: -1,
        col: -1,
        fontColor: 'black',
      },
    ];
    cell = cell.concat(boolean);

    Object.keys(TxLockingType).forEach((key) => {
      cell.push({
        cellValue: key,
        cellType: 'ctEnumeration:Locking Type',
        row: -1,
        col: -1,
        fontColor: 'black',
      });
    });

    Object.keys(TxObjectTypeType).forEach((key) => {
      cell.push({
        cellValue: key,
        cellType: 'ctEnumeration:Object Type Type',
        row: -1,
        col: -1,
        fontColor: 'black',
      });
    });

    const worksheet: Worksheets = {
      name: 'Enumerations',
      isVisible: false,
      cells: cell,
    };

    return worksheet;
  }
}
