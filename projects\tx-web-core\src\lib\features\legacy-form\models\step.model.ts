import { TxFormConfiguration } from './formConfiguration/businessClass/form-configuration';
import { TxTableConfiguration } from './table-configuration';
import { TxVisualDesignConfiguration } from './visual-design-configuration';

export class TxStep {
  type!: TxStepType;
  formConfig!: TxFormConfiguration;
  tableConfig!: TxTableConfiguration;
  visualDesginConfig!: TxVisualDesignConfiguration;

  constructor() {}

  assign(step?: Partial<TxStep>) {
    this.type = step?.type as TxStepType;
    this.formConfig = step?.formConfig as TxFormConfiguration;
    this.tableConfig = step?.tableConfig as TxTableConfiguration;
    this.visualDesginConfig = step?.visualDesginConfig as TxVisualDesignConfiguration;
  }
}

enum TxStepType {
  writeForm,
  readForm,
  tableView,
  blackBox,
  LIMSSamples,
  visualDesign,
}
