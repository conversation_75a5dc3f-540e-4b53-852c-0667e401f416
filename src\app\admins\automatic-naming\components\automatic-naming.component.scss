.admin-content {
  display: flex;
  flex-direction: column;

  .automatic-naming-container {
    flex: 1;
    overflow: hidden;
  }
}

.form-field {
  width: 300px;
  padding-bottom: 8px;
}

.form-container {
  padding: 16px 0px;
}

.form-field-type {
  width: 300px;
  padding-bottom: 0px;
  margin-bottom: -16px;
}

.form-container-config {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 64px;

  .h2-section-subtitle {
    grid-row: 1;
    grid-column: 1 / 3;
    padding-top: 16px;
    padding-bottom: 8px;
  }

  .form-field-config {
    padding-bottom: 8px;
    grid-row: 2;
  }

  tx-objects-type-dropdown {
    grid-row: 3;
  }
}

.selection-field-container {
  display: flex;
  column-gap: 4rem;

  .form-field {
    flex: 1;
    width: auto;
  }
}

.no-settings {
  position: absolute;
  top: calc(50% - 87px);
  left: calc(50% - 50px);
}

.data-type-settings-form {
  position: relative;
  height: 184px;

  .form-button-group-field {
    margin: 16px;
  }

  .data-type-field {
    margin: 16px 0px 0px 16px;
  }
}

.data-general-settings {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 4rem;
  padding-bottom: 0.5rem;

  .form-field-down {
    display: grid;
    column-gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
    align-items: start;
  }
}

.hint {
  display: flex;
  font-size: 12px;
  padding-bottom: 16px;

  .hint-icon {
    font-size: 14px;
    margin-right: 4px;
  }
}

.form-button-group {
  height: 24px;
  border-radius: 8px;
  margin-top: 8px;
  box-shadow: 0px 2px 7px -4px rgba(0, 0, 0, 0.6);
}

.required-label::after {
  content: ' *';
}

.tree-grid-container {
  max-height: 328px;
  display: flex;
  tx-attributes-tree-grid {
    width: 100%;
  }
}

.preview-name-field {
  text-align: end;
}

.column-type,
.column-value,
.column-format {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-height: 1.25rem;
  flex: 1;
}

.column-value {
  fa-icon {
    margin-right: 0;
  }
}

.highlight-item {
  font-weight: bold;
  background-color: mark;
  color: marktext;
}

.item-arrow-size {
  font-size: 13px !important; 
}