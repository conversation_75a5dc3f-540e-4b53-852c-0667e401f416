import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TestedCoreModelConceptsComponent } from './tested-core-model-concepts.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { TRANSLATIONS_MOCK } from 'src/app/core/translation/translations.mock';
import { TxGridService } from '@bassetti-group/tx-web-core';

describe('TestedCoreModelConceptsComponent', () => {
  let component: TestedCoreModelConceptsComponent;
  let fixture: ComponentFixture<TestedCoreModelConceptsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TestedCoreModelConceptsComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [TranslateTestingModule.withTranslations(TRANSLATIONS_MOCK)],
      providers: [TxGridService],
    }).compileComponents();

    fixture = TestBed.createComponent(TestedCoreModelConceptsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
