import { TestBed, waitForAsync } from '@angular/core/testing';
import { TxComboboxObjectComponent } from './combobox-object.component';
import {
  ComboBoxModule,
  DropDownListModule,
  MultiSelectAllModule,
  MultiSelectComponent,
  MultiSelectModule,
} from '@syncfusion/ej2-angular-dropdowns';
import { TxObject } from '../../../../business-models';
import { LegacyTxObjectsService } from '../../services/structure/services/objects.service';
import { MockService } from 'ng-mocks';
import { LegacyTxObjectTypeService } from '../../services/structure/services/object-type.service';
import { of } from 'rxjs';

describe('TxComboboxObjectComponent', () => {
  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxComboboxObjectComponent, MultiSelectComponent],
      imports: [DropDownListModule, MultiSelectModule, ComboBoxModule, MultiSelectAllModule],
      providers: [
        {
          provide: LegacyTxObjectsService,
          useValue: MockService(LegacyTxObjectsService, {
            listObjects: () => of(),
          }),
        },
        {
          provide: LegacyTxObjectTypeService,
          useValue: MockService(LegacyTxObjectTypeService, {
            listFromIds: () => of(),
          }),
        },
      ],
    }).compileComponents();
  }));

  it('should create', () => {
    const fixture = TestBed.createComponent(TxComboboxObjectComponent);
    const app = fixture.debugElement.componentInstance;
    expect(app).toBeTruthy();
  });

  it('should display objects of the parametered object type', () => {
    const fixture = TestBed.createComponent(TxComboboxObjectComponent);
    const app = fixture.debugElement.componentInstance;

    app.idObjectType = 1;
    fixture.detectChanges();
    const element = app.txObjects.find(
      (txObject: TxObject) => txObject.idObjectType !== app.idObjectType
    );
    expect(element).toBeUndefined();
  });
});
