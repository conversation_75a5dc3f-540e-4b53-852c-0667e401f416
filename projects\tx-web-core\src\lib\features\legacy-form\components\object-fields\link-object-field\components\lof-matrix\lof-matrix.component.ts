import { TxAttributeLink } from '../../../../../services/structure/models/attribute';
import { TxVirtualAttributeField } from './../../../../../models/formConfiguration/businessClass/virtual-attribute-field';
import { Component, Input, OnInit } from '@angular/core';
import { LegacyTxData } from '../../../../../services/structure/models/data';

@Component({
  selector: 'tx-lof-matrix',
  templateUrl: './lof-matrix.component.html',
  styleUrls: ['./lof-matrix.component.scss'],
})
export class TxLofMatrixComponent implements OnInit {
  @Input() idObject = 0;
  @Input() attribute = TxAttributeLink;
  @Input() linkedFields: TxVirtualAttributeField[] = [];
  @Input() data: LegacyTxData[] = [];
  @Input() readMode = true;
  @Input() transposed = false;

  constructor() {}

  ngOnInit() {
    //console.log('TxLofMatrixComponent', this)
  }

  updateData(data: LegacyTxData) {
    console.log('updateData matrix', data);
  }
}
