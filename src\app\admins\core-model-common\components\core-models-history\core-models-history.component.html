<tx-grid
  #coreModelGrid
  primaryKey="id"
  class="core-models-history__table"
  [columns]="columns"
  [filterColumns]="filterColumns"
  [data]="history"
  [enableSearching]="true"
  inputPlaceholder="generic.search"
  [isRowSelectable]="true"
  [enableSearchHighlight]="true"
  [enableRowTooltip]="true"
  [enableFiltering]="true"
  (searchInputChange)="searchItem($event)">
  <tx-grid-toolbar *ngIf="optionalTableBarInputRef">
    <ng-template #optionalTableBarInput>
      <ng-container
        [ngTemplateOutlet]="optionalTableBarInputRef"
        [ngTemplateOutletContext]="{ $implicit: history }"></ng-container>
    </ng-template>
  </tx-grid-toolbar>
  <tx-grid-column
    *ngFor="let overrideColumnDir of overrideColumnsDirs"
    [fieldName]="overrideColumnDir.appOverrideColumn">
    <ng-template let-data let-searchValue="searchValue">
      <ng-container
        [ngTemplateOutlet]="overrideColumnDir.templateRef"
        [ngTemplateOutletContext]="{
          $implicit: data,
          searchValue: searchValue
        }"></ng-container>
    </ng-template>
  </tx-grid-column>
  <tx-grid-column [fieldName]="CORE_MODELS_HISTORY_DATE_FIELD">
    <ng-template let-data let-searchValue="searchValue">
      <span
        [matTooltip]="data[CORE_MODELS_HISTORY_DATE_FIELD] | localizedDate : 'short' : true"
        [innerHTML]="
          data[CORE_MODELS_HISTORY_DATE_FIELD]
            | localizedDate : 'short' : true
            | escapeHtml
            | highlightSearch : (searchValue | escapeHtml)
        ">
      </span>
    </ng-template>
  </tx-grid-column>
</tx-grid>
<div class="core-models-history__table-info border-grey">
  {{ history.length }} {{ 'generic.item-s' | translate }}
</div>
