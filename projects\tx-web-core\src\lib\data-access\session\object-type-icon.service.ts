import { DestroyRef, Injectable } from '@angular/core';
import { EMPTY, catchError, switchMap, filter, from, map, take, tap, of } from 'rxjs';
import { IconsSetType } from './icon-set-type';
import { AbstractSessionService } from './session.abstract.service';
import { PreferencesSettings } from './configuration-settings.model';
import { IconUrl, SpecificIconsUrls } from './object-type-icon.model';
import { toSpecificIconsUrls } from './object-type-icon-utils';

@Injectable({
  providedIn: 'root',
})
export class TxObjectTypeIconService {
  private readonly NB_OT_ICONS_BASE = 300;
  private iconSetType = IconsSetType.IconShock;
  private specificIcons: SpecificIconsUrls = {
    iconShocks: new Map<number, IconUrl>(),
    txIcons: new Map<number, IconUrl>(),
  };
  constructor(
    private readonly sessionService: AbstractSessionService,
    private readonly destroyRef: DestroyRef
  ) {
    this.destroyRef.onDestroy(() => {
      [...this.specificIcons.iconShocks, ...this.specificIcons.txIcons].forEach(([, iconUrl]) => {
        URL.revokeObjectURL(iconUrl);
      });
    });

    const preferences$ = this.sessionService.preferences$ ?? of(undefined);

    from(preferences$)
      .pipe(
        take(1),
        filter(
          (preferences: PreferencesSettings | undefined): preferences is PreferencesSettings =>
            preferences !== undefined
        ),
        map((preferences) => this.mapToIconsSetType(preferences?.bUseNewIconSet ?? '')),
        tap((iconSetType) => {
          this.iconSetType = iconSetType;
        }),
        switchMap(() => this.sessionService.getSpecificIconsZip()),
        tap(async (specificIconsZip) => {
          const specificIconsFiles = await toSpecificIconsUrls(specificIconsZip);
          this.specificIcons = specificIconsFiles;
        }),
        catchError((error) => {
          console.error(error);
          return EMPTY;
        })
      )
      .subscribe();
  }
  getIconPath(icon: number | string): string {
    const iconNumber = Number(icon);
    if (this.isSpecificIcon(iconNumber)) {
      return this.getSpecificIconPath(iconNumber);
    }
    return this.getStandardIconPath(icon);
  }

  private mapToIconsSetType(useNewIconSet: string): IconsSetType {
    return useNewIconSet.toLocaleLowerCase() !== 'false'
      ? IconsSetType.IconShock
      : IconsSetType.TxIcons;
  }

  private isSpecificIcon(icon: number): boolean {
    return !isNaN(icon) && icon > this.NB_OT_ICONS_BASE;
  }

  /**
   * Throw error
   * @param icon
   * @returns
   */
  private getSpecificIconPath(icon: number): string {
    try {
      const path =
        (this.iconSetType === IconsSetType.TxIcons
          ? this.specificIcons.txIcons.get(icon)
          : this.specificIcons.iconShocks.get(icon)) ?? this.specificIcons.txIcons.get(icon);
      if (path === undefined) {
        throw new Error(icon + ' was not found in the specific icons list');
      } else {
        return path;
      }
    } catch (e: any) {
      console.error(e.message);
      return '';
    }
  }
  private getStandardIconPath(icon: number | string): string {
    return this.iconSetType === IconsSetType.IconShock
      ? `./assets/tx-web-core/img/icons/svg/${icon}.svg`
      : `./assets/tx-web-core/img/icons/png/${icon}.png`;
  }
}
