import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import {
  Audit,
  Cell,
  DatabaseInformation,
  InformationAudit,
  Section,
  Worksheet,
  Worksheets,
} from '../models/audit';
import { Observable, BehaviorSubject, of, merge, Subject } from 'rxjs';
import { catchError, map, takeUntil, tap } from 'rxjs/operators';
import { TxConfigService, ToastComponent } from '@bassetti-group/tx-web-core';

@Injectable()
export class AuditsService {
  public informationAudit: InformationAudit = {
    isFileUploaded: false,
    numberAuditPoints: 0,
  };

  public sections: Section[] = [];
  public idAuditClient = 0;
  public idAudit = 0;
  public fileName?: string;

  public toastProgress: ToastComponent | null = null;

  protected ngUnsubscribe: Subject<void> = new Subject<void>();

  private apiUrl?: string;

  private sectionsSub = new BehaviorSubject<Section[]>([]);
  private loadingSub = new BehaviorSubject<boolean>(true);

  private informationAuditSub: BehaviorSubject<InformationAudit> = new BehaviorSubject(
    this.informationAudit
  );

  constructor(private http: HttpClient, private configService: TxConfigService) {
    this.apiUrl = this.configService.getApiUrl();
    this.callTxAudit();
  }

  callTxAudit(): void {
    this.http
      .get(this.apiUrl + 'api/TxAudit/' + this.idAudit)
      .pipe(
        tap((response: any) => {
          this.sections = response.sections as Section[];
          this.informationAudit.isFileUploaded = false;
          this.informationAudit.fileName = undefined;
          this.idAudit = response.idAudit;
          this.updateSections();
          this.updateInformationAuditPoint();
          this.updateLoadingBar();
        })
      )
      .subscribe();
  }

  updateSections(): void {
    this.sectionsSub.next(this.sections);
  }

  updateInformationAuditPoint(): void {
    let num = 0;
    this.sections.forEach((section) => {
      num += section.auditPoints.length;
    });
    this.informationAudit.numberAuditPoints = num;
    this.informationAuditSub.next(this.informationAudit);
  }

  updateLoadingBar(): void {
    this.loadingSub.next(false);
  }
  getSections(): Observable<Section[]> {
    return this.sectionsSub.asObservable();
  }

  getLoadingBar(): Observable<boolean> {
    return this.loadingSub.asObservable();
  }

  getInfomation(): Observable<DatabaseInformation> {
    return this.http.get<DatabaseInformation>(this.apiUrl + 'api/TxAudit/informations');
  }

  getInformationAuditPoint(): Observable<InformationAudit> {
    return this.informationAuditSub.asObservable();
  }

  getAuditPoint(idAuditPoint: any) {
    let auditPoint: Audit | undefined;
    this.sections.forEach((section) => {
      if (auditPoint === undefined) {
        auditPoint = section.auditPoints.find((point) => point.id === idAuditPoint);
      }
    });
    return auditPoint;
  }

  isAlreadyExecuted(): boolean {
    if (this.toastProgress !== null) {
      return true;
    }
    return this.sections.some((section) =>
      section.auditPoints ? section.auditPoints.some((ap) => ap.status !== undefined) : false
    );
  }

  postFile(fileToUpload: File): Observable<any> {
    const endpoint = this.apiUrl + 'api/TxAudit/auditfile';
    const formData: FormData = new FormData();
    formData.append('file', fileToUpload, fileToUpload.name);
    return this.http.post(endpoint, formData).pipe(
      tap((response: any) => {
        this.sections = response.sections as Section[];
        this.idAuditClient = response.idAudit;
        this.informationAudit.isFileUploaded = true;
        this.informationAudit.fileName = fileToUpload.name;
        this.updateSections();
        this.updateInformationAuditPoint();
      })
    );
  }

  updateAuditPoint(audit: Audit) {
    for (const section of this.sections) {
      const apfound = section.auditPoints.find((ap) => ap.id === audit.id);
      if (apfound) {
        Object.assign(apfound, audit);
        break;
      }
    }
    this.updateSections();
  }

  updateAuditPoints() {
    this.ngUnsubscribe.next();
    this.sections.forEach((section) => {
      section.auditPoints.forEach((point) => {
        point.status = undefined;
        point.result = '';
        point.isMultiLine = false;
        point.allResult = '';
        point.isShowAllLine = false;
      });
    });
    this.updateSections();
  }

  exportResults() {
    const worksheets: Worksheets = {
      worksheets: [],
    };
    this.sections.forEach((section) => {
      let index = 1;
      const worksheet: Worksheet = {
        name: section.name,
        cells: [],
      };
      const cellTitleName: Cell = {
        row: index,
        col: 1,
        data: 'Name',
      };
      const cellTitleStatus: Cell = {
        row: index,
        col: 2,
        data: 'Status',
      };
      const cellTitleMessage: Cell = {
        row: index,
        col: 3,
        data: 'Message',
      };
      worksheets.worksheets.push(worksheet);
      worksheet.cells.push(cellTitleName, cellTitleStatus, cellTitleMessage);
      index++;
      section.auditPoints.forEach((ap) => {
        if (ap.result.includes('\n')) {
          const splitValue = ap.result.split('\n');
          splitValue.forEach((splvalue) => {
            const cellName: Cell = {
              row: index,
              col: 1,
              data: ap.name,
            };
            const cellStatus: Cell = {
              row: index,
              col: 2,
              data: ap.status,
            };
            const cellMessage: Cell = {
              row: index,
              col: 3,
              data: splvalue,
            };
            worksheet.cells.push(cellName, cellStatus, cellMessage);
            index++;
          });
        } else {
          const cellName: Cell = {
            row: index,
            col: 1,
            data: ap.name,
          };
          const cellStatus: Cell = {
            row: index,
            col: 2,
            data: ap.status,
          };
          const cellMessage: Cell = {
            row: index,
            col: 3,
            data: ap.result,
          };
          worksheet.cells.push(cellName, cellStatus, cellMessage);
          index++;
        }
      });
    });
    return this.http.post(this.apiUrl + 'api/Export/spreadsheet/xlsx', worksheets, {
      responseType: 'blob',
    });
  }

  deleteAuditFiles() {
    return this.http.delete(
      this.apiUrl +
        'api/TxAudit/files/' +
        (this.informationAudit.isFileUploaded ? this.idAuditClient : this.idAudit)
    );
  }

  executeQueries(): Observable<Audit> {
    let idAp = this.idAudit;
    if (this.informationAudit.isFileUploaded) {
      idAp = this.idAuditClient;
    }
    const requests: Observable<any>[] = [];

    this.sections.forEach((section) => {
      section.auditPoints.forEach((point) => {
        requests.push(
          this.handleQueryError(
            this.http.get(`${this.apiUrl}api/TxAudit/queryresult/${idAp}/${point.id}`)
          )
        );
      });
    });

    return merge(...requests).pipe(
      takeUntil(this.ngUnsubscribe),
      map((audit: Audit) => {
        if (audit.status === 'error') {
          const a = this.getAuditPoint(audit.id);
          audit.name = a ? a.name : '';
        }
        audit.isShowAllLine = false;
        audit.result = this.transform(audit);
        return audit;
      }),
      tap((audit: Audit) => {
        this.updateAuditPoint(audit);
      })
    );
  }

  handleQueryError(httpResponse: Observable<any>): Observable<any> {
    return httpResponse.pipe(
      catchError((error: HttpErrorResponse) => {
        // eslint-disable-next-line radix
        let idAudit = -1;
        if (error.url) {
          idAudit = parseInt(
            error.url.slice(error.url.indexOf('queryresult')).substr(12).split('/')[1]
          );
        }
        let errorReason = 'Unknown reason';
        if (typeof error.error === 'string') {
          errorReason = error.error;
        } else if (error.error.reason) {
          errorReason = `${error.error.reason}.`;
        } else if (error.error.message) {
          errorReason = error.error.message;
        }
        return of({
          id: idAudit,
          status: 'error',
          result: `${errorReason} Please check the audit file for this point`,
        });
      })
    );
  }

  /* Private Methods */
  private transform(audit: Audit): string {
    const value = audit.result;
    if (value.includes('\n')) {
      const splitValue = value.split('\n', 3);
      const splitAll = value.split('\n');
      audit.isMultiLine = splitAll.length > 3;
      audit.allResult = splitAll.slice(3).join('\n');
      return splitValue.join('\n');
    } else {
      audit.isMultiLine = false;
      return value;
    }
  }
}
