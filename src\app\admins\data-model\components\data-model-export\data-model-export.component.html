<div class="panel-header">
  <fa-icon [icon]="['fas', 'file-export']" class="panel-icon-title"></fa-icon>
  <div class="panel-title h2-section-title">
    {{ 'admins.dataModelExport.exportDataModel' | translate }}
  </div>
</div>
<mat-divider></mat-divider>
<div class="admin-content">
  <mat-tab-group
    mat-stretch-tabs="false"
    id="main-tab-export"
    color="accent"
    [selectedIndex]="0"
    (selectedIndexChange)="onChangeTab($event)">
    <mat-tab>
      <ng-template mat-tab-label>
        {{ 'generic.settings' | translate }}
      </ng-template>
      <div class="dm-container-body">
        <div class="dm-container-left">
          <!-- TODO: replace by tx-object-type-tree-grid -->
          <!-- <txold-object-types-tree-grid
            [showTagsColumn]="false"
            [showCheckbox]="true"
            [folderCheckable]="false"
            [checkedIds]="oTsChecked"
            (changeSelection)="changeObjectType($event)"
            (checkChange)="onOTChecked($event)"
            (checkMultiple)="onOTSelectOrUnselectAll($event)">
          </txold-object-types-tree-grid> -->
        </div>
        <div class="dm-container-right">
          <!-- TODO : replace by app-attribute-tree-grid when data structure admin will be refound 
            <txold-attributes-tree-grid
            [showCheckbox]="true"
            [showIdColumn]="true"
            [multipleSelection]="true"
            [displayFilteredSelectionButton]="true"
            [allowLoadingLinkedAttributes]="true"
            [objectType]="objectType"
            [rootAttributeTypesDisplayed]="filtredDataType"
            [attributeSetLevels]="attributeSetLevels"
            (changecheckAllAttribute)="changecheckAllAttribute($event)"
            (checkChangeAttribute)="onAttributeChecked($event)"
            (attributesLoaded)="onAttributeLoaded($event)">
          </txold-attributes-tree-grid> -->
        </div>
      </div>
    </mat-tab>
    <mat-tab>
      <ng-template mat-tab-label>
        {{ 'admins.wording.logs' | translate }}
      </ng-template>
    </mat-tab>
  </mat-tab-group>
  <div class="footer-button">
    <div class="button-export">
      <button mat-flat-button color="accent" (click)="onExport()">
        {{ 'button.export' | translate }}
      </button>
    </div>
    <div class="button-cancel">
      <button (click)="onCancel()" mat-stroked-button>{{ 'button.cancel' | translate }}</button>
    </div>
  </div>
</div>
