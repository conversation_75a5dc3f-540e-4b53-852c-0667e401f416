import { Injectable } from '@angular/core';
import { TxGridService } from '@bassetti-group/tx-web-core/src/lib/features/grid';
import { RowDataBoundEventArgs, RowInfo } from '@syncfusion/ej2-angular-grids';
import {
  RowCollapsedEventArgs,
  RowExpandedEventArgs,
  TreeGridComponent,
} from '@syncfusion/ej2-angular-treegrid';
import { TxObjectType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TxTreeGrid } from './tree-grid.models';

@Injectable()
export class TxTreeGridService extends TxGridService {
  private increment = 0;

  public getIdComponent() {
    return this.increment++;
  }

  /**
   * Handles the event when a node is expanded in a tree grid component.
   * It updates the 'expanded' property of the corresponding data object to 'true' .
   * @param event The event object for the expanded row.
   * @param data An array of TreeGridObject data used to find and update the expanded state.
   */
  public onNodeExpanded<T>(event: RowExpandedEventArgs, data: TxTreeGrid<T>[]) {
    const obj = data.find((ot) => ot.id === (event.data as TxTreeGrid<T>).id);
    if (obj !== undefined) {
      obj.expanded = true;
    }
  }

  /**
   * Handles the event when a node is collapsed in a tree grid component.
   * It updates the 'expanded' property of the corresponding data object to 'false'.
   * @param event The event object for the collapsed row.
   * @param data An array of TreeGridObject data used to find and update the expanded state.
   */
  public onNodeCollapsed<T>(event: RowCollapsedEventArgs, data: TxTreeGrid<T>[]) {
    const obj = data.find((ot) => ot.id === (event.data as TxTreeGrid<T>).id);
    if (obj) {
      obj.expanded = false;
    }
  }

  /**
   * Retrieves the expanded state of an object within a tree grid.
   * @param obj The object for which the expanded state is being retrieved.
   * @param objGrid An array of TreeGridObject data used to find the corresponding object and its expanded state.
   * @returns True if the object is found in the objGrid array and it is expanded; otherwise, it returns true by default.
   */
  public getExpandedState<T>(obj: TxObjectType, objGrid: TxTreeGrid<T>[]): boolean {
    if (objGrid) {
      const o = objGrid.find((og) => og.id === obj.id);
      return o?.expanded ?? true;
    }
    return true;
  }

  /**
   * Handles the event when a row is bound in the grid, and it adds a 'row-opacity' class to non-integer 'id' rows (explanations).
   * @param args The event object for the bound row.
   */
  public onRowBound(args: RowDataBoundEventArgs) {
    if (!Number.isInteger((args.data as any).id)) {
      args.row?.classList.add('row-opacity');
    }
  }

  /**
   * Expands parent rows recursively to reveal child rows within a tree grid.
   * @param rowInfo The row information for the current row.
   * @param grid The tree grid component where the rows are being expanded.
   */
  public expandParentRows(rowInfo: RowInfo, grid: TreeGridComponent) {
    if ((rowInfo.rowData as any).parentItem) {
      const parentRow = grid.getRowByIndex((rowInfo.rowData as any).parentItem.index);
      grid.expandRow(parentRow as HTMLTableRowElement);
      this.expandParentRows(grid.getRowInfo(parentRow), grid);
    }
  }

  /**
   * Recursively retrieve a tree grid structure rooted at the specified parent ID.
   * This function fetches all child elements, including nested descendants, from the tree grid data.
   * @param parentId The id of the parent that is to be retrieved.
   * @param data - The data source representing the tree grid
   * @returns - An array containing all the child elements, including descendants, under the specified parent.
   */
  public getTreeGridFromParent<T>(parentId: number, data: TxTreeGrid<T>[]): TxTreeGrid<T>[] {
    const children: TxTreeGrid<T>[] = [];
    for (const element of data) {
      if (element.idParent === parentId) {
        children.push(element);
        const grandchildren = this.getTreeGridFromParent(element.id, data);
        children.push(...grandchildren);
      }
    }

    return children;
  }
}
