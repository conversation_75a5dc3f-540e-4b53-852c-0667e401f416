import type { Meta, StoryObj } from '@storybook/angular';
import { TxObjectTypeTreeViewComponent } from './object-type-tree-view.component';
import { TreeData } from './object-type-tree-view.model';
import { MOCK_TREE_DATA } from './specs/mock-tree-data';

const meta: Meta<TxObjectTypeTreeViewComponent<{ name: string }>> = {
  component: TxObjectTypeTreeViewComponent,
  title: 'TxObjectTypeTreeViewComponent',
  tags: ['autodocs'],
  render: (args) => ({
    props: args,
    template: `
      <tx-object-type-tree-view
        [treeData]="treeData"
        [filteredTreeData]="filteredTreeData"
        [treeDataOptions]="treeDataOptions"
        [nodeTemplate]="nodeTemplate"
        [checkedIds]="checkedIds"
        [disabledIds]="disabledIds"
        [showCheckBox]="showCheckBox"
        [multipleSelection]="multipleSelection"
        [hierarchicalSelection]="hierarchicalSelection">
      </tx-object-type-tree-view>
      <ng-template #nodeTemplate let-node="node">
        <div class="node-row">
          <img
          [alt]="'concept-icon-' + node.objectData.icon"
          [src]="node.objectData.iconPath" />
          <span> {{ node.name }}</span>
        </div>
      </ng-template>
    `,
  }),
};
export default meta;
type Story = StoryObj<TxObjectTypeTreeViewComponent<TreeData>>;

export const Primary: Story = {
  args: {
    treeData: MOCK_TREE_DATA(),
    treeDataOptions: { idProperty: 'id', idParentProperty: 'idObjectTypeParent' },
    filteredTreeData: [],
    checkedIds: [],
    showCheckBox: false,
    multipleSelection: false,
    hierarchicalSelection: false,
  },
};
