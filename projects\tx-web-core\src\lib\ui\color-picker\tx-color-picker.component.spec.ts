import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TxColorPickerComponent } from './tx-color-picker.component';
import { NgxColorsModule } from 'ngx-colors';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('TxColorPickerComponent', () => {
  let component: TxColorPickerComponent;
  let fixture: ComponentFixture<TxColorPickerComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        TxColorPickerComponent,
        NgxColorsModule,
        TranslateTestingModule.withTranslations({
          en: {},
        }),
      ],
    });
    fixture = TestBed.createComponent(TxColorPickerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
