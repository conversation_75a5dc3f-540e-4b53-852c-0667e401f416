<!-- TODO: replace by tx-object-type-tree-grid -->
<!-- <txold-object-types-tree-grid
  [objectTypesFilteredIds]="objectTypesFilteredIds"
  (changeSelection)="changeObjectType($event)">
  <div toolbar-buttons class="dm-buttons">
    <span [matTooltip]="getAddOTTooltip() | translate"
      ><button mat-button [disabled]="!canAddOT()" (click)="addObjectType()">
        <fa-icon [icon]="['fal', 'plus-circle']" size="lg"></fa-icon>{{ 'button.add' | translate }}
      </button></span
    >
    <span [matTooltip]="getEditOTTooltip() | translate"
      ><button mat-button [disabled]="!canEditOT()" (click)="editObjectType()">
        <fa-icon [icon]="['fal', 'pen']" size="lg"></fa-icon>{{ 'button.edit' | translate }}
      </button></span
    >
  </div>
</txold-object-types-tree-grid> -->
<ng-template #templateAddObjectType>
  <app-object-type-form
    [rightPaneRef]="rightPaneRef"
    [settings]="settingFormObjectType"></app-object-type-form>
</ng-template>
