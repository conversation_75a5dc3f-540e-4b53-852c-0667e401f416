<mat-form-field [style]="{ width: width }" color="accent">
  <mat-label>{{ label | translate }}</mat-label>
  <input matInput [formControl]="inputControl" />
  <tx-color-picker
    matSuffix
    [colorFormControl]="colorControl"
    [colorPalette]="colorPalette"></tx-color-picker>
  <mat-error *ngIf="inputControl.hasError('invalidColor')">{{
    'txWebCore.components.colorPicker.invalidColor' | translate
  }}</mat-error>
</mat-form-field>
