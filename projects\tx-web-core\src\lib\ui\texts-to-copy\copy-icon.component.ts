import { ClipboardModule } from '@angular/cdk/clipboard';
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ToastType, ToastService } from '@bassetti-group/tx-web-core/src/lib/ui/toast';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { SizeProp } from '@fortawesome/fontawesome-svg-core';
import { TranslateModule} from '@ngx-translate/core';
@Component({
  standalone: true,
  imports: [FontAwesomeModule, CommonModule, MatTooltipModule, ClipboardModule, TranslateModule],
  selector: 'tx-copy-icon',
  templateUrl: './copy-icon.component.html',
  styleUrls: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TxCopyIconComponent {
  @Input() text = '';
  @Input() size: SizeProp = 'lg';
  constructor(
    private readonly toastService: ToastService,
  ) {}

  notify(
    type: ToastType = 'information',
    message = 'txWebCore.generic.tagCopy',
    duration = 4000,
    isPersistent = false
  ): void {
    this.toastService.show({
      templateContext: { test: { state: type, message, progress: 0 } },
      type,
      date: new Date(),
      title: '',
      description: message,
      isPersistent,
      interval: duration,
    });
  }
}
