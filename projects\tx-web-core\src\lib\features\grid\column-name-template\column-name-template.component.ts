import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import {
  TxEscapeHtmlPipe,
  TxHighlightSearchPipe,
} from '@bassetti-group/tx-web-core/src/lib/utilities';
import { FormatConceptNameTooltipPipe } from '../pipes/format-concept-name-tooltip.pipe';

interface ColumnDataTemplate {
  id: number;
  name: string;
  idParent?: number;
  icon?: string;
  explanation?: string;
  isDisabled?: boolean;
}

@Component({
  standalone: true,
  imports: [
    TranslateModule,
    FontAwesomeModule,
    MatTooltipModule,
    CommonModule,
    TxEscapeHtmlPipe,
    TxHighlightSearchPipe,
    FormatConceptNameTooltipPipe,
  ],
  selector: 'tx-column-name-template',
  templateUrl: './column-name-template.component.html',
  styleUrls: ['./column-name-template.component.scss'],
})
export class TxColumnNameTemplateComponent {
  @Input() data?: ColumnDataTemplate;
  @Input() hasNoIcon = false;
  @Input() searchId?: number;
  @Input() dataLength?: number;
  @Input() hasMainParentObject = false;
  @Input() inputSearchValue = '';
  @Input() showMoreTooltips = false;
  @Input() hideTooltipId = false;
}
