import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxComboboxObjectTypeComponent } from './combobox-object-type.component';
import {
  ComboBoxModule,
  DropDownListModule,
  MultiSelectAllModule,
  MultiSelectModule,
} from '@syncfusion/ej2-angular-dropdowns';

describe('TxComboObjectTypeComponent', () => {
  let component: TxComboboxObjectTypeComponent;
  let fixture: ComponentFixture<TxComboboxObjectTypeComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxComboboxObjectTypeComponent],
      imports: [DropDownListModule, MultiSelectModule, ComboBoxModule, MultiSelectAllModule],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxComboboxObjectTypeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
