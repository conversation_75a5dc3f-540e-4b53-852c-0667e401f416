import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AccessRightsGuard } from 'src/app/core/guards/access-rights.guard';
import { AuthenticationGuard } from 'src/app/core/guards/authentication.guard';
import { DataModelComponent } from './components/data-model.component';
import { AdminRights } from '@bassetti-group/tx-web-core';

const routes: Routes = [
  {
    path: '',
    component: DataModelComponent,
    data: {
      breadcrumb: 'admins.wording.dataModel',
      adminRights: AdminRights.CanAdministrateStructure,
    },
    canActivate: [AuthenticationGuard, AccessRightsGuard],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DataModelRoutingModule {}
