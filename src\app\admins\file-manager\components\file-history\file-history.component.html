<div class="panel-header">
  <fa-icon [icon]="['fas', 'history']"></fa-icon>
  <span>
    <div class="panel-title h2-section-title">{{ 'admins.resources.history' | translate }}</div>
  </span>
</div>
<mat-divider></mat-divider>

<mat-progress-bar
  *ngIf="isLoaderActive"
  mode="indeterminate"
  color="accent"
  style="position: absolute; top: 0; left: 0"></mat-progress-bar>
<div class="panel-content">
  <div
    *ngIf="isFolderHistory(currentFileHistory?.type); else titleFile"
    class="panel-section-subtitle h2-section-subtitle">
    {{ 'admins.resources.folderTwoPoints' | translate }}{{ currentFileHistory?.name }}
  </div>
  <ng-template #titleFile>
    <div class="panel-section-subtitle h2-section-subtitle">
      {{ 'admins.resources.fileTwoPoints' | translate }}{{ currentFileHistory?.name }}
    </div>
  </ng-template>

  <div *ngFor="let history of histories">
    <div class="fm-panel-content">
      <div class="fm-panel-title">
        <fa-icon [icon]="['fas', 'caret-right']" size="lg"></fa-icon>
        <span class="fm-panel-date form-label">{{
          history.dateOfAction | localizedDate : 'medium' : true
        }}</span>
      </div>

      <div class="fm-panel-section-body">
        <div class="fm-panel-body-content">
          <ng-container [ngSwitch]="history.actionType">
            <ng-container
              *ngSwitchCase="
                [actionType.atModifyFile, actionType.atEditFile]
                  | switchMultiCase : history.actionType
              ">
              <span>
                <fa-icon [icon]="['fal', 'pen']" size="lg"></fa-icon>
              </span>
              <span
                class="fm-panel-body-action"
                [innerHTML]="
                  (history.itemType === fileItemType.directory
                    ? 'admins.resources.authorModifiedFolder'
                    : 'admins.resources.authorModifiedFile'
                  )
                    | translate
                      : {
                          authorName: history.authorContactName,
                          nameFile: history.itemName,
                          folder: history.path ? history.path : '',
                          alias: history.alias
                        }
                "></span>
            </ng-container>
            <ng-container *ngSwitchCase="actionType.atAddFile">
              <span>
                <fa-icon
                  [icon]="[
                    'fal',
                    history.itemType === fileItemType.directory ? 'folder-plus' : 'file-plus'
                  ]"
                  size="lg"></fa-icon>
              </span>
              <span
                class="fm-panel-body-action"
                [innerHTML]="
                  (history.itemType === fileItemType.directory
                    ? 'admins.resources.authorAddedFolder'
                    : 'admins.resources.authorAddedFile'
                  )
                    | translate
                      : {
                          authorName: history.authorContactName,
                          nameFile: history.itemName,
                          folder: history.path ? history.path : '',
                          alias: history.alias
                        }
                "></span>
            </ng-container>
            <ng-container *ngSwitchCase="actionType.atDeleteFile">
              <span>
                <fa-icon [icon]="['fal', 'trash-alt']" size="lg"></fa-icon>
              </span>
              <span
                class="fm-panel-body-action"
                [innerHTML]="
                  (history.itemType === fileItemType.directory
                    ? 'admins.resources.authorDeletedFolder'
                    : 'admins.resources.authorDeletedFile'
                  )
                    | translate
                      : {
                          authorName: history.authorContactName,
                          nameFile: history.itemName,
                          folder: history.path,
                          alias: history.alias
                        }
                "></span>
            </ng-container>
          </ng-container>
        </div>

        <div class="fm-panel-body-buttons">
          <fa-icon
            [matTooltip]="getDownloadVersionTooltip(history) | translate"
            size="lg"
            [icon]="['fal', 'file-download']"
            id="fm-download-version-button"
            class="fm-button-field"
            [ngClass]="{ 'icon-disabled': !canDownloadVersionFile(history) }"
            (click)="downloadVersionFile(history)"></fa-icon>
          <fa-icon
            [matTooltip]="getRestoreVersionTooltip(history) | translate"
            size="lg"
            [icon]="['fal', 'trash-undo']"
            id="fm-restore-version-button"
            class="fm-button-field"
            [ngClass]="{ 'icon-disabled': !canRestoreVersion(history) }"
            (click)="restoreVersionFile(history)"></fa-icon>
        </div>
      </div>
    </div>

    <mat-divider></mat-divider>
  </div>
</div>
