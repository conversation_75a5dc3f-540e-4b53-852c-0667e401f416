import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxInputNumbersFieldComponent } from './input-numbers-field.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TxInputNumbersControlComponent } from './input-numbers-control/input-numbers-control.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ReactiveFormsModule } from '@angular/forms';

describe('InputNumbersFieldComponent', () => {
  let component: TxInputNumbersFieldComponent;
  let fixture: ComponentFixture<TxInputNumbersFieldComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TxInputNumbersFieldComponent, TxInputNumbersControlComponent],
      imports: [MatFormFieldModule, MatTooltipModule, BrowserAnimationsModule, ReactiveFormsModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxInputNumbersFieldComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
