import { TxIsNodeDisabledPipe } from '../is-node-disabled.pipe';
import { FlatTreeNode, TreeData, TreeDataOptions } from '../object-type-tree-view.model';

describe('IsNodeDisabledPipe', () => {
  let pipe: TxIsNodeDisabledPipe;

  beforeEach(() => {
    pipe = new TxIsNodeDisabledPipe();
  });

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return true if node is disabled', () => {
    const treeDataOptions: TreeDataOptions = {
      idProperty: 'id',
      idParentProperty: '',
    };
    const node: FlatTreeNode<TreeData> = {
      name: 'Node 1',
      level: 0,
      expandable: true,
      objectData: { id: '1', name: 'Node 1' },
    };
    const checkList: (string | number)[] = ['1'];
    const result = pipe.transform(node, checkList, treeDataOptions);
    expect(result).toBe(true);
  });

  it('should return false if node is not disabled', () => {
    const node: FlatTreeNode<TreeData> = {
      name: 'Node 2',
      level: 0,
      expandable: true,
      objectData: { name: 'Node 2' },
    };
    const checkList: (string | number)[] = [];
    const treeDataOptions: TreeDataOptions = {
      idProperty: 'id',
      idParentProperty: '',
    };
    const result = pipe.transform(node, checkList, treeDataOptions);
    expect(result).toBe(false);
  });
});
