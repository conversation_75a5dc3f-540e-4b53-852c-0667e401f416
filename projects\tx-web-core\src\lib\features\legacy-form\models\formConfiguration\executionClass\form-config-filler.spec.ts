import { TestBed } from '@angular/core/testing';
import {
  LegacyTxAttribute,
  LegacyTxAttributeRight,
} from '../../../services/structure/models/attribute';
import { LegacyTxDataType } from '../../../services/structure/models/data';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { AttributesMockService } from '../../../testing.mock';
import { TxAttributeField } from '../businessClass/attribute-field';
import { TxGroupAttributeField } from '../businessClass/group-attribute-field';
import { TxTabAttributeField } from '../businessClass/tab-attribute-field';
import { TxFormConfigFiller } from './form-config-filler';
import { TxLinkTypeService } from '../../../services/structure/services/link-type.service';
import { MockService } from 'ng-mocks';
import { TxEditionMode } from '../businessClass/form-enum';

describe('formConfigFiller', () => {
  let attributesService: LegacyTxAttributesService;
  let linkTypeService: TxLinkTypeService;
  let formConfigFiller: TxFormConfigFiller;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        { provide: LegacyTxAttributesService, useClass: AttributesMockService },
        {
          provide: TxLinkTypeService,
          useValue: MockService(TxLinkTypeService),
        },
      ],
    });

    attributesService = TestBed.inject(LegacyTxAttributesService);
    linkTypeService = TestBed.inject(TxLinkTypeService);
    formConfigFiller = new TxFormConfigFiller(attributesService, linkTypeService);
  });

  it('should be created', () => {
    expect(formConfigFiller).toBeTruthy();
  });

  it('should return list of children', () => {
    const attrList = [
      new LegacyTxAttribute({
        id: 1,
        idAttributeParent: 10,
        name: '',
        dataType: LegacyTxDataType.Boolean,
        idObjectType: 0,
        tags: [],
        right: LegacyTxAttributeRight.None,
        isInherited: false,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      }),
      new LegacyTxAttribute({
        id: 2,
        idAttributeParent: 4,
        name: '',
        dataType: LegacyTxDataType.Boolean,
        idObjectType: 0,
        tags: [],
        right: LegacyTxAttributeRight.None,
        isInherited: false,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      }),
      new LegacyTxAttribute({
        id: 3,
        idAttributeParent: 10,
        name: '',
        dataType: LegacyTxDataType.Boolean,
        idObjectType: 0,
        tags: [],
        right: LegacyTxAttributeRight.None,
        isInherited: false,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      }),
      new LegacyTxAttribute({
        id: 4,
        idAttributeParent: 1,
        name: '',
        dataType: LegacyTxDataType.Boolean,
        idObjectType: 0,
        tags: [],
        right: LegacyTxAttributeRight.None,
        isInherited: false,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      }),
      new LegacyTxAttribute({
        id: 5,
        idAttributeParent: 10,
        name: '',
        dataType: LegacyTxDataType.Boolean,
        idObjectType: 0,
        tags: [],
        right: LegacyTxAttributeRight.None,
        isInherited: false,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      }),
    ];
    const childList = formConfigFiller.getChildrenList(attrList, 10);
    const emptyChildList = formConfigFiller.getChildrenList(attrList, 0);
    expect(childList.length).toBe(3);
    expect(childList).toContain(attrList[0]);
    expect(childList).toContain(attrList[2]);
    expect(childList).toContain(attrList[4]);
    expect(emptyChildList.length).toBe(0);
  });

  it('should create and return root children attribute field', () => {
    const attrObjectList = [
      { id: 1, idAttributeParent: 0, dataType: 13 },
      { id: 2, idAttributeParent: 1, dataType: 10 },
      { id: 3, idAttributeParent: 0, dataType: 13 },
      { id: 4, idAttributeParent: 3, dataType: 13 },
      { id: 5, idAttributeParent: 4, dataType: 9 },
    ];
    const rootAttrList = formConfigFiller.filterChildren(0, false, attrObjectList, true);
    const idsRootAttrList = [];
    for (const rootAttr of rootAttrList) {
      idsRootAttrList.push(rootAttr.id);
    }
    expect(rootAttrList.length).toEqual(2);
    expect(idsRootAttrList).toContain(1);
    expect(idsRootAttrList).toContain(3);
  });

  it('should create and return recursive children attribute field', () => {
    const attrObjectList = [
      { id: 1, idAttributeParent: 0, dataType: 13 },
      { id: 2, idAttributeParent: 1, dataType: 10 },
      { id: 3, idAttributeParent: 0, dataType: 13 },
      { id: 4, idAttributeParent: 3, dataType: 13 },
      { id: 5, idAttributeParent: 4, dataType: 9 },
      { id: 6, idAttributeParent: 7, dataType: 2 },
    ];
    const rootAttrList = formConfigFiller.filterChildren(0, true, attrObjectList, true);
    const idsRootAttrList = [];
    for (const rootAttr of rootAttrList) {
      idsRootAttrList.push(rootAttr.id);
    }
    expect(rootAttrList.length).toEqual(5);
    expect(idsRootAttrList).toContain(1);
    expect(idsRootAttrList).toContain(2);
    expect(idsRootAttrList).toContain(3);
    expect(idsRootAttrList).toContain(4);
    expect(idsRootAttrList).toContain(5);
    expect(idsRootAttrList).not.toContain(6);
  });

  it('should get object type attributes and fill grpField of his children', () => {
    const spyGetAttributes = jest.spyOn(attributesService, 'listObjectTypeAttributes');
    const spyFilterChildren = jest.spyOn(formConfigFiller, 'filterChildren');
    const spyAddField = jest.spyOn(formConfigFiller, 'addFields');
    const tabField = new TxTabAttributeField(TxEditionMode.read);
    tabField.id = 2;
    attributesService.listObjectTypeAttributes(0).subscribe((listAttr) => {
      // formConfigFiller.addChildrenAttributes(tabField, listAttr, true);
      const idsChild1List = [];
      for (const child of tabField.children) {
        idsChild1List.push(child.id);
      }
      expect(idsChild1List.length).toEqual(2);
      expect(idsChild1List).toContain(1);
      expect(idsChild1List).toContain(329);
      expect(idsChild1List).not.toContain(330);
      const idsChild2List = [];
      for (const child of (tabField.children[0] as TxGroupAttributeField).children) {
        idsChild2List.push(child.id);
      }
      expect(idsChild2List.length).toEqual(1);
      expect(idsChild1List).toContain(89);
      expect(idsChild1List).not.toContain(330);
      expect(spyGetAttributes).toHaveBeenCalled();
      expect(spyFilterChildren).toHaveBeenCalledTimes(2);
      expect(spyAddField).toHaveBeenCalled();
    });
  });

  it('should set mandatories', () => {
    const attrList = [
      new LegacyTxAttribute({
        id: 1,
        idAttributeParent: 10,
        dataType: LegacyTxDataType.Group,
        name: '',
        idObjectType: 0,
        tags: [],
        right: LegacyTxAttributeRight.None,
        isInherited: false,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      }),
      new LegacyTxAttribute({
        id: 2,
        idAttributeParent: 4,
        dataType: LegacyTxDataType.String,
        name: '',
        idObjectType: 0,
        tags: [],
        right: LegacyTxAttributeRight.None,
        isInherited: false,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      }),
      new LegacyTxAttribute({
        id: 3,
        idAttributeParent: 10,
        dataType: LegacyTxDataType.File,
        name: '',
        idObjectType: 0,
        tags: [],
        right: LegacyTxAttributeRight.None,
        isInherited: false,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      }),
      new LegacyTxAttribute({
        id: 4,
        idAttributeParent: 1,
        dataType: LegacyTxDataType.Group,
        name: '',
        idObjectType: 0,
        tags: [],
        right: LegacyTxAttributeRight.None,
        isInherited: false,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      }),
      new LegacyTxAttribute({
        id: 5,
        idAttributeParent: 10,
        dataType: LegacyTxDataType.Text,
        name: '',
        idObjectType: 0,
        tags: [],
        right: LegacyTxAttributeRight.None,
        isInherited: false,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      }),
      new LegacyTxAttribute({
        id: 10,
        idAttributeParent: 0,
        dataType: LegacyTxDataType.Tab,
        name: '',
        idObjectType: 0,
        tags: [],
        right: LegacyTxAttributeRight.None,
        isInherited: false,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      }),
    ];
    const tabField = new TxTabAttributeField(TxEditionMode.read);
    const groupField1 = new TxGroupAttributeField(TxEditionMode.read);
    const groupField2 = new TxGroupAttributeField(TxEditionMode.read);
    const attrField1 = new TxAttributeField();
    const attrField2 = new TxAttributeField();
    const attrField3 = new TxAttributeField();
    tabField.attribute = attrList[5];
    groupField1.attribute = attrList[0];
    groupField2.attribute = attrList[3];
    attrField1.attribute = attrList[2];
    attrField2.attribute = attrList[4];
    attrField3.attribute = attrList[1];
    tabField.children.push(groupField1);
    tabField.children.push(attrField1);
    tabField.children.push(attrField2);
    groupField1.children.push(groupField2);
    groupField2.children.push(attrField3);
    formConfigFiller.doSetMandatories(tabField, [2, 5], []);
    const mandatoryProperty = { mandatory: true };
    // expect(tabField.properties).not.toContainEqual(mandatoryProperty);
    // expect(attrField1.properties).not.toContainEqual(mandatoryProperty);
    // expect(attrField2.properties).toContainEqual(mandatoryProperty);
    // expect(attrField3.properties).toContainEqual(mandatoryProperty);
    // expect(groupField1.properties).not.toContainEqual(mandatoryProperty);
    // expect(groupField2.properties).not.toContainEqual(mandatoryProperty);
  });
});
