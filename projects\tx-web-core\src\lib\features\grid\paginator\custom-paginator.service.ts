import { Injectable } from '@angular/core';
import { MatPaginatorIntl } from '@angular/material/paginator';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';

@Injectable()
export class CustomPaginatorService implements MatPaginatorIntl {
  changes = new Subject<void>();

  firstPageLabel = '';
  itemsPerPageLabel = '';
  lastPageLabel = '';
  nextPageLabel = '';
  previousPageLabel = '';

  constructor(private translate: TranslateService) {}

  getRangeLabel(page: number, pageSize: number, length: number): string {
    this.firstPageLabel = this.translate.instant(_('txWebCore.components.paginator.firstPage'));
    this.lastPageLabel = this.translate.instant(_('txWebCore.components.paginator.lastPage'));
    this.itemsPerPageLabel = this.translate.instant(
      _('txWebCore.components.paginator.itemsPerPage')
    );
    this.nextPageLabel = this.translate.instant(_('txWebCore.components.paginator.nextPage'));
    this.previousPageLabel = this.translate.instant(
      _('txWebCore.components.paginator.previousPage')
    );

    if (length === 0) {
      return this.translate.instant(_('txWebCore.components.paginator.pageOf'), {
        page: 1,
        amountPages: 1,
        itemsCount: length,
      });
    }
    const amountPages = Math.ceil(length / pageSize);
    return this.translate.instant(_('txWebCore.components.paginator.pageOf'), {
      page: page + 1,
      amountPages,
      itemsCount: length,
    });
  }
}
