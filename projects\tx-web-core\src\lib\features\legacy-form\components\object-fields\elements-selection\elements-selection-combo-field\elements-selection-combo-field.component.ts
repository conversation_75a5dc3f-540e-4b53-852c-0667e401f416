import { TxEscfPopupComponent } from './sub-components/escf-popup/escf-popup.component';
import { TxElementSelectionBaseFieldComponent } from './../_element-selection-base-field/element-selection-base-field.component';
import {
  Component,
  Input,
  OnInit,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  AfterViewInit,
} from '@angular/core';
import { LegacyTxAttributesService } from '../../../../services/structure/services/attributes.service';
import { TxObjectCheckEventArgs } from '../../../../trees/tree-objects/tree-objects.component';

@Component({
  selector: 'tx-elements-selection-combo-field',
  templateUrl: './elements-selection-combo-field.component.html',
  styleUrls: ['./elements-selection-combo-field.component.scss'],
})
export class TxElementsSelectionComboFieldComponent
  extends TxElementSelectionBaseFieldComponent
  implements OnInit, AfterViewInit
{
  @Input() allowSearch = true;
  @Input() allowFavorite = true;
  @Input() displayAdvancedOptions = false;
  @Input() displayAdvancedMode = false;
  @Input() favoriteElements: any[] = [];
  @Input() width = 300;
  @Input() height = 300;
  @Input() idObjectType!: number;

  @Output() checkElement = new EventEmitter<any>();

  @ViewChild('popup') popup!: TxEscfPopupComponent;
  @ViewChild('spanSpin') spanSpin!: ElementRef;
  @ViewChild('input') input!: ElementRef;
  @ViewChild('mainDiv') mainDiv!: ElementRef;

  isRemoveSpanVisible = false;

  constructor(public attributesService: LegacyTxAttributesService) {
    super(attributesService);
  }

  private isPopupOpen(): boolean {
    return this.popup.isOpen;
  }

  private updateInputValue() {
    let inputValue = '';

    if (this.selectedElements && this.selectedElements.length) {
      inputValue = this.selectedElements.map((e) => e[this.textKey]).join(', ');
    }
    // add 'x more...' when the list of selected elements is too large.

    const inputValueDisplayed = inputValue;

    this.input.nativeElement.value = inputValueDisplayed;
    this.input.nativeElement.setAttribute('title', inputValue);
    this.input.nativeElement.setAttribute('aria-label', inputValue);

    if (inputValue === '') {
      this.mainDiv.nativeElement.classList.remove('input-with-value');
      this.isRemoveSpanVisible = false;
    } else {
      this.mainDiv.nativeElement.classList.add('input-with-value');
      this.isRemoveSpanVisible = true;
    }
  }

  ngOnInit() {
    super.ngOnInit();
  }

  ngAfterViewInit() {
    super.ngAfterViewInit();
    this.updateInputValue();
  }

  togglePopup() {
    this.popup.toggle();
  }

  onRemovingSelection() {
    this.selectedElements = [];
    this.updateInputValue();
    this.popup.removeSelection();
  }

  onFocusInput(target: HTMLElement) {
    const openned = this.isPopupOpen();

    if (openned) {
      this.popup.inputSearch.nativeElement.focus();
      return;
    }

    this.spanSpin.nativeElement.click();
    this.focusIn(target);
  }

  // Focus Event function for input component
  focusIn(target: HTMLElement): void {
    target.parentElement?.classList.add('e-input-focus');
  }

  // FocusOut Event function for input component
  focusOut(target: HTMLElement): void {
    target.parentElement?.classList.remove('e-input-focus');
  }

  // MouseDown Event function for input component
  onMouseDown(target: HTMLElement): void {
    this.focusIn(target.parentElement?.getElementsByClassName('tx-input-esc')[0] as HTMLElement);
    target.classList.add('e-input-btn-ripple');
  }

  // MouseUp Event function for input component
  onMouseUp(target: HTMLElement): void {
    const ele: HTMLElement = target;
    setTimeout(() => {
      ele.classList.remove('e-input-btn-ripple');
    }, 500);
  }

  onSpinClicked() {
    const openned = this.isPopupOpen();

    if (openned) {
      // hide the popup
      this.spanSpin.nativeElement.classList.remove('e-spin-up');
      this.spanSpin.nativeElement.classList.add('e-spin-down');
    } else {
      // open the popup
      this.spanSpin.nativeElement.classList.remove('e-spin-down');
      this.spanSpin.nativeElement.classList.add('e-spin-up');
    }
    this.popup.toggle();
  }

  onElementChecked(args: TxObjectCheckEventArgs) {
    if (this.selection === 'Single') {
      this.selectedElements = [];
    }

    if (args.checked) {
      this.addElementSelected(args.txObject);
    } else {
      this.removeElementSelected(args.txObject);
    }

    this.updateInputValue();

    this.popup.updateSelectedElements(this.selectedElements);

    this.checkElement.emit(args);

    if (this.selection === 'Single') {
      this.popup.close();
      this.spanSpin.nativeElement.classList.remove('e-spin-up');
      this.spanSpin.nativeElement.classList.add('e-spin-down');
      this.focusOut(this.input.nativeElement);
    }
  }

  onElementSelected(args: TxObjectCheckEventArgs) {
    this.onElementChecked(args);
  }
}
