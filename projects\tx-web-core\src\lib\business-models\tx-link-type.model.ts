import { TxConcept } from './tx-concept.model';

export enum LinkTypeFilteringType {
  undefined = 'lftUndefined',
  none = 'lftNone',
  parent = 'lftParent',
  or = 'lftOr',
  and = 'lftAnd',
  andButNotEmpty = 'lftAndButNotEmpty',
}

export interface TxLinkType extends TxConcept {
  idDestinationObjectType: number;
  idSourceObjectType: number;
  isAssociative: boolean;
  idFilteringObject: number;
  multiplicity: boolean;
  multiplicityInv: boolean;
  isStrongFiltered: boolean;
  isStrongFilteredInv: boolean;
  isTransposed: boolean;
  isTransposeInv: boolean;
  filteringType: LinkTypeFilteringType;
  filteringTypeInv: LinkTypeFilteringType;
}
