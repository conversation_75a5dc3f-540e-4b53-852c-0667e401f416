import { animate, state, style, transition, trigger } from '@angular/animations';
import { SelectionModel } from '@angular/cdk/collections';
import { NestedTreeControl } from '@angular/cdk/tree';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  ContentChild,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import { MatTreeModule, MatTreeNestedDataSource } from '@angular/material/tree';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { TxTreeNodeTemplate } from './directives/tree-node.directive';
import {
  TxTreeViewChecked,
  TxTreeViewChildren,
  TxTreeViewExpanded,
  TxTreeViewSelect,
} from './tree-view.interface';

@Component({
  selector: 'tx-tree-view',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule, TranslateModule, MatTreeModule, MatCheckboxModule],
  templateUrl: './tree-view.component.html',
  styleUrl: './tree-view.component.scss',
  animations: [
    // Define animations for the sidebar expansion and collapse
    trigger('expandCollapse', [
      state('collapsed', style({ height: '0px', overflow: 'hidden', display: 'none' })),
      state('expanded', style({ height: '*', overflow: 'hidden', display: 'block' })),

      transition('collapsed => expanded', [
        style({ display: 'block' }),
        animate('{{expandDuration}} ease-out', style({ height: '*' })),
      ]),

      transition('expanded => collapsed', [
        animate('{{collapseDuration}} ease-in', style({ height: '0px' })),
        style({ display: 'none' }),
      ]),
    ]),

    // Define animations for the chevron rotation
    trigger('rotateChevron', [
      state('expanded', style({ transform: 'rotate(90deg)' })), // Chevron pointing down
      state('collapsed', style({ transform: 'rotate(0deg)' })), // Chevron pointing right
      transition('collapsed <=> expanded', animate('300ms ease-in-out')),
    ]),
  ],
})
export class TxTreeViewComponent<T extends TxTreeViewChildren<T>>
  implements OnInit, OnChanges, AfterViewInit
{
  @Input({ required: true }) primaryKey!: keyof T;
  @Input() parentMapping: keyof T = 'idParent' as keyof T;
  @Input() hasChildren: keyof T = 'hasChildren' as keyof T;
  @Input({ required: true }) data!: T[];
  @Input() checkedIds: T[keyof T][] = [];
  @Input() onInitTreeExpanded: boolean = false;
  @Input() enableCheckBox: boolean = false;
  @Input() enableNodeClickToggle: boolean = false;
  @Input() enableNodeSelection: boolean = true;
  @Input() animationsDisable: boolean = false;
  @Input() expandDuration: string = '400ms';
  @Input() collapseDuration: string = '300ms';

  @Output() dataBound: EventEmitter<void> = new EventEmitter();
  @Output() nodeChecked: EventEmitter<TxTreeViewChecked<T>> = new EventEmitter();
  @Output() nodeExpanded: EventEmitter<TxTreeViewExpanded<T>> = new EventEmitter();
  @Output() nodeSelecting: EventEmitter<TxTreeViewSelect<T>> = new EventEmitter();
  @Output() nodeSelected: EventEmitter<TxTreeViewSelect<T>> = new EventEmitter();
  @Output() nodeDragStart: EventEmitter<TxTreeViewChildren<T>> = new EventEmitter();
  @ContentChild(TxTreeNodeTemplate) nodeTemplate!: TxTreeNodeTemplate;

  private expandedNodes: Set<T[keyof T]> = new Set();
  public selection = new SelectionModel<T[keyof T]>(true, []);
  public selectedRowPKValue: T[keyof T] | undefined;
  public showAnimations: boolean = true;
  treeControl = new NestedTreeControl<T>((node) => node.children);
  dataSource = new MatTreeNestedDataSource<T>();

  constructor() {}
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.data?.currentValue) {
      this.dataSource.data = this.loadData([...this.data]);
      this.updateDataSource();
    }
    if (changes.checkedIds?.currentValue) {
      this.selection.clear();
      this.selection.select(...this.checkedIds);
    }
  }

  ngOnInit(): void {
    if (this.animationsDisable) {
      this.showAnimations = false;
    }
  }
  ngAfterViewInit(): void {
    this.dataBound.emit();
    this.initDataSource();
  }
  hasChild = (_: number, node: T) => !!node.children && node.children.length > 0;
  isCheckBoxDisplayed(node: T) {
    return this.enableCheckBox && !node.isFolder;
  }
  handleSelection(data: T, event: MatCheckboxChange): void {
    this.toggleSelection(data[this.primaryKey]);
    this.nodeChecked.emit({ data, isChecked: event.checked });
  }
  onNodeClick(node: T) {
    const isSelected = this.selectedRowPKValue === node[this.primaryKey];

    const event = { nodeData: node, isSelected: !isSelected, cancel: false };
    this.nodeSelecting.emit(event);

    if (this.enableNodeSelection && !isSelected && !event.cancel) {
      this.selectedRowPKValue = node[this.primaryKey];
      this.nodeSelected.emit(event);
    }

    if (this.enableNodeClickToggle) {
      this.onNodeToggle(node);
    }
  }
  onNodeDblClick(node: T) {
    if (!this.enableNodeClickToggle) {
      this.onNodeToggle(node);
    }
  }
  onNodeToggle(node: T) {
    this.treeControl.toggle(node);
    const isExpanded = this.treeControl.isExpanded(node);
    if (isExpanded) {
      this.expandedNodes.add(node[this.primaryKey]);
      this.nodeExpanded.emit({ nodeData: node });
    } else {
      this.expandedNodes.delete(node[this.primaryKey]);
    }
  }
  getAllCheckedNodes() {
    let flattenData = this.flattenTreeData(this.dataSource.data);
    return flattenData.filter((item) => this.getSelectedElements().includes(item[this.primaryKey]));
  }

  isNodeChecked(node: T): boolean {
    if (!this.enableCheckBox) return false;
    return this.isSelected(node[this.primaryKey]);
  }

  restoreExpandedNodes(): void {
    const recurse = (nodes: T[]) => {
      for (let node of nodes) {
        if (this.expandedNodes.has(node[this.primaryKey])) {
          this.treeControl.expand(node);
        }
        if (node.children) {
          recurse(node.children);
        }
      }
    };
    recurse(this.dataSource.data);
  }
  addChildrenToNode(children: T[], parentNode?: T): void {
    debugger;
    const addDataToId = (data: T[], id: T[keyof T], children: T[]) => {
      data.forEach((item) => {
        if (item[this.primaryKey] == id) {
          const existingIds = new Set(item.children?.map((child) => child[this.primaryKey]));
          const filteredChildren = children.filter(
            (child) => !existingIds.has(child[this.primaryKey])
          );
          item.children = [...(item.children || []), ...filteredChildren];
        } else {
          addDataToId(item.children || [], id, children);
        }
      });
    };
    if (parentNode && parentNode[this.primaryKey]) {
      addDataToId(this.dataSource.data, parentNode[this.primaryKey], children);
      this.expandedNodes.add(parentNode[this.primaryKey]);
    } else {
      this.dataSource.data.concat(children);
    }
    this.updateDataSource();
  }

  private readonly getSelectedElements = () => this.selection.selected;
  private readonly isSelected = (element: T[keyof T]) => this.selection.isSelected(element);
  private toggleSelection(element: T[keyof T]) {
    this.selection.toggle(element);
  }
  private updateDataSource() {
    this.showAnimations = false;
    this.dataSource.data = JSON.parse(JSON.stringify(this.dataSource.data));
    this.treeControl.dataNodes = this.dataSource.data;
    this.restoreExpandedNodes();
    setTimeout(() => {
      if (!this.animationsDisable) {
        this.showAnimations = true;
      }
    }, 0);
  }
  private loadData(data: T[]): T[] {
    if (!this.primaryKey) {
      throw new Error('primaryKey is not defined');
    }
    if (!this.parentMapping) {
      throw new Error('parentMapping is not defined');
    }
    const dataMap = new Map(data.map((item) => [item[this.primaryKey], item]));
    const childIds = new Set<any>();
    data.forEach((item) => {
      if (item[this.parentMapping]) {
        const parent = dataMap.get(item[this.parentMapping]);
        if (parent) {
          parent.children ??= [];

          // Check if child is already added
          const alreadyExists = parent.children.some(
            (child: T) => child[this.primaryKey] === item[this.primaryKey]
          );
          if (!alreadyExists) {
            parent.children.push(item);
          }
          childIds.add(item[this.primaryKey]);
        }
      }
    });
    return data.filter((item) => !childIds.has(item[this.primaryKey]));
  }
  private flattenTreeData(data: T[]): T[] {
    const result: T[] = [];
    data.forEach((item) => {
      const { children, ...rest } = item; // Destructure to exclude children from the result
      result.push(rest as T);
      if (item.children) {
        result.push(...this.flattenTreeData(item.children));
      }
    });
    return result;
  }
  private initDataSource() {
    this.dataSource.data = this.loadData([...(this.data || [])]);
    this.treeControl.dataNodes = this.dataSource.data;

    this.selection.clear();
    this.selection.select(...this.checkedIds);

    this.expandedNodes.clear();
    if (this.onInitTreeExpanded) {
      this.treeControl.expandAll();
      //save expanded nodes to set
      const treeExpandedNodes = this.treeControl.expansionModel.selected;
      this.expandedNodes = new Set(treeExpandedNodes.map((node) => node[this.primaryKey]));
    }
  }
}
