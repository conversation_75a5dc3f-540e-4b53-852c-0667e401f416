@use '@angular/material' as mat;

@mixin object-type-tree-view-theme($theme) {
  $primary: map-get($theme, primary);
  .selected-node {
    background-color: mat.m2-get-color-from-palette($primary, 500, 0.25);
    color: mat.m2-get-color-from-palette($primary, if(map-get($theme, is-dark), 200, 500));

    &:hover {
      background-color: mat.m2-get-color-from-palette($primary, 500, 0.2) !important;
    }
  }

  .single-selection-hover:hover {
    background-color: mat.m2-get-color-from-palette($primary, 500, 0.2) !important;
  }
}
