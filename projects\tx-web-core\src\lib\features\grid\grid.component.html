<div
  class="table-top-area filter-search-pane border-grey"
  *ngIf="enableSearching || enableFiltering || headerTemplate || headerTest">
  <div class="filter-template-wrapper">
    <div class="filters" *ngIf="enableFiltering">
      <tx-grid-filter-menu
        *ngIf="enableFiltering"
        #gridFilterMenu
        [columns]="filterColumns"
        [optionsForListOptionCol]="filterOptions"
        [disabled]="false"
        [newFilters]="newFilters"
        (filter)="filterBy($event)"
        (deleteFilter)="onDeleteFilter($event)"
        (deleteAllFilters)="onDeleteAllFilters()">
      </tx-grid-filter-menu>
    </div>
    <ng-container
      *ngIf="headerTemplate"
      [ngTemplateOutlet]="headerTemplate.template"></ng-container>
    <ng-container *ngIf="headerTest" [ngTemplateOutlet]="headerTest.template"></ng-container>
  </div>
  <tx-input-search
    #gridSearch
    *ngIf="enableSearching"
    [placeholderValue]="
      inputPlaceholder ? inputPlaceholder : ('txWebCore.input.searchByNameTagId' | translate)
    "
    [inputValue]="inputValue"
    [isFiltered]="searchInputIsFiltered"
    (searchChanged)="searchItem($event)"
    (filterChanged)="filterBy($event)"
    (searchOrFilterCleared)="removeSearchItem()">
  </tx-input-search>
</div>
<mat-table
  id="grid-table"
  #table
  [dataSource]="dataSource"
  class="tx-grid-table border-grey"
  [ngClass]="enablePagination ? 'paginated-table' : ''"
  cdkDropList
  (cdkDropListDropped)="dropTableRow($event)"
  [cdkDropListConnectedTo]="droppedTargetContainer"
  [cdkDropListData]="dataSource.dataList"
  [cdkDropListDisabled]="dragDisabled"
  matSort
  [trackBy]="trackByTable"
  (contentChanged)="onDataBind()"
  (click)="onTableClick($event)">
  <ng-container matColumnDef="select">
    <mat-header-cell *matHeaderCellDef class="table-row-header-cell">
      <mat-checkbox
        (change)="masterToggle($event)"
        *ngIf="dataSource?.dataList?.length > 0"
        [checked]="
          dataSource.selection.hasValue() &&
          memoIsAllSelected(dataSource.getSelectedElements().length)
        "
        [indeterminate]="
          dataSource.selection.hasValue() &&
          !memoIsAllSelected(dataSource.getSelectedElements().length)
        ">
      </mat-checkbox>
    </mat-header-cell>
    <mat-cell *matCellDef="let element; let i = index" class="table-row-cell">
      <mat-checkbox
        (click)="$event.stopPropagation()"
        (change)="$event ? checkBoxSelect(element, $event, i) : null"
        [checked]="dataSource.selection.isSelected(element)"
        *ngIf="dataSource?.dataList?.length > 0">
      </mat-checkbox>
    </mat-cell>
  </ng-container>
  <ng-container matColumnDef="drag-drop">
    <mat-header-cell *matHeaderCellDef class="table-row-header-cell"> </mat-header-cell>
    <mat-cell
      *matCellDef="let element"
      class="table-row-cell"
      [matBadge]="
        dragElement &&
        dragElement[primaryKey ?? ''] == element[primaryKey ?? ''] &&
        !dataSource.selection.isEmpty()
          ? dataSource.getSelectedElements().length
          : ''
      "
      [matBadgeHidden]="!dragOption?.multiple">
      <fa-icon
        [icon]="['fal', 'bars']"
        size="lg"
        class="drag-cursor"
        (mousedown)="onDragStart(element)"
        (touchstart)="onDragStart(element)"
        (mouseup)="onDragStopped()"
        (touchend)="onDragStopped()"></fa-icon>
    </mat-cell>
  </ng-container>
  <ng-content></ng-content>
  <ng-container [matColumnDef]="column.field" *ngFor="let column of columns; let i = index">
    <mat-header-cell
      [matTooltipDisabled]="column.headerTooltip ? false : true"
      matTooltipPosition="above"
      [style.height]="rowHeight"
      [matTooltip]="column.headerTooltip | translate"
      [ngStyle]="getColumnStyle(column)"
      *matHeaderCellDef
      mat-sort-header
      [disabled]="column.sorting ? false : true"
      class="table-row-header-cell"
      [ngClass]="{ 'column-resize-handle': enableColumnResize && column.resize }">
      <span
        *ngIf="cellPrefixTemplate && cellPrefixTemplate.fieldNames.includes(column?.field)"
        [ngStyle]="{ width: '36px' }"></span>
      <ng-container
        [ngTemplateOutlet]="columnTemplates.headers[column?.field] || noHeaderTemplate"
        [ngTemplateOutletContext]="{
          $implicit: (column.headerText | translate),
          searchValue: searchValue
        }">
      </ng-container>
      <ng-template #noHeaderTemplate>
        <span>{{ column.headerText | translate }}</span>
      </ng-template>
      <span
        (click)="$event.stopPropagation()"
        class="resize-handle"
        [ngClass]="column.resize ? 'cursor-resize' : ''"
        (mousedown)="column.resize && onResizeColumn($event, i)"
        *ngIf="
          enableColumnResize && renderedColumns.at(-1) != column.field && column.headerText !== ''
        "></span>
    </mat-header-cell>
    <mat-cell
      #matCell
      class="table-row-cell"
      [ngStyle]="getColumnStyle(column)"
      *matCellDef="let element"
      (click)="handlerCellClick($event, column, element)"
      (dblclick)="onCellDblClick(element, $event, i, column)"
      [ngClass]="column | gridCellClass : element : matCell">
      <ng-container
        *ngIf="cellPrefixTemplate && cellPrefixTemplate.fieldNames.includes(column?.field)"
        [ngTemplateOutlet]="cellPrefixTemplate.template"
        [ngTemplateOutletContext]="{
          $implicit: element,
          column,
          i,
          searchValue: searchValue
        }"></ng-container>

      <ng-container
        [ngTemplateOutlet]="
          (allowEditing ||
            (allowCellEditOnDblClick && column.editable && column.field === cellEditColumn)) &&
          element.isEditActive
            ? editable
            : columnTemplates.columns[column?.field] || noTemplate
        "
        [ngTemplateOutletContext]="{
          $implicit: element,
          column: column,
          searchValue: searchValue
        }">
      </ng-container>
      <ng-template #noTemplate>
        <span
          *ngIf="!element.isEditActive"
          [matTooltip]="
            enableRowTooltip && column.type === DataType.DATE
              ? (element[column.field] | date : localeDateFormat)
              : element[column.field]
          "
          [ngClass]="{
            'm-right': column.textAlign === 'right',
            'm-left m-right': column.textAlign === 'center',
            'hide-text-overflow': enableRowTooltip
          }"
          [innerHTML]="
            enableSearchHighlight
              ? column.type === DataType.DATE
                ? (element[column.field] | date : localeDateFormat)
                : (element[column.field]
                  | escapeHtml
                  | highlightSearch : (searchValue | escapeHtml))
              : (element[column.field] | escapeHtml)
          ">
        </span>
      </ng-template>
      <ng-template #editable>
        <ng-container [ngSwitch]="column.editType" *ngIf="element.isEditActive">
          <mat-form-field *ngSwitchCase="TxGridEditType.DropDown" class="no-label-input">
            <mat-select
              [(value)]="editingElement[column.field]"
              (selectionChange)="changeSelection($event)"
              [disabled]="!column.editable"
              [ngClass]="
                (allowEditing || allowCellEditOnDblClick) &&
                !column.editable &&
                element.isEditActive
                  ? 'not-allowed-cursor'
                  : 'auto-cursor'
              ">
              <mat-option
                class="mat-option-padding"
                *ngFor="let option of column.editOption.params.dataSource"
                [value]="option.value">
                {{ option.text }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-checkbox
            *ngSwitchCase="TxGridEditType.Boolean"
            [checked]="editingElement[column.field]"
            (change)="toggleEditCheckBox($event)"
            [disabled]="!column.editable"
            [ngClass]="
              (allowEditing || allowCellEditOnDblClick) && !column.editable && element.isEditActive
                ? 'not-allowed-cursor'
                : 'auto-cursor'
            ">
          </mat-checkbox>
          <mat-form-field *ngSwitchCase="TxGridEditType.AutoComplete" class="no-label-input">
            <input
              matInput
              cdkTrapFocus
              [cdkTrapFocusAutoCapture]="true"
              [type]="column.type"
              [ngModel]="
                column.type == DataType.DATE
                  ? (editingElement[column.field] | date : 'yyyy-MM-dd')
                  : (editingElement | fetchNestedProperty : column.field) ?? ''
              "
              (keyup.enter)="
                (allowEditing && disableEditing()) ||
                  (allowCellEditOnDblClick && disableCellEditing($event))
              "
              (ngModelChange)="onCellChange($event, column.field)"
              [disabled]="!column.editable"
              [ngClass]="
                (allowEditing || allowCellEditOnDblClick) &&
                !column.editable &&
                element.isEditActive
                  ? 'not-allowed-cursor'
                  : 'auto-cursor'
              "
              floatLabelType="Auto"
              [matAutocomplete]="autoComplete" />
            <mat-autocomplete [hideSingleSelectionIndicator]="true" #autoComplete="matAutocomplete">
              <mat-option
                *ngFor="let option of column.editOption.params.dataSource"
                [value]="option.value">
                {{ option.text }}
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>

          <mat-form-field *ngSwitchDefault class="no-label-input">
            <input
              matInput
              cdkTrapFocus
              [cdkTrapFocusAutoCapture]="true"
              [type]="column.type"
              [value]="
                column.type == DataType.DATE
                  ? (editingElement[column.field] | date : 'yyyy-MM-dd')
                  : (editingElement | fetchNestedProperty : column.field) ?? ''
              "
              (input)="onCellInput($event, column.field)"
              (keyup.enter)="
                (allowEditing && disableEditing()) ||
                  (allowCellEditOnDblClick && disableCellEditing($event))
              "
              [disabled]="!column.editable"
              [ngClass]="
                (allowEditing || allowCellEditOnDblClick) &&
                !column.editable &&
                element.isEditActive
                  ? 'not-allowed-cursor'
                  : 'auto-cursor'
              " />
          </mat-form-field>
        </ng-container>
      </ng-template>
    </mat-cell>
  </ng-container>
  <mat-header-row
    *matHeaderRowDef="renderedColumns; sticky: true"
    [style.height]="rowHeight"
    class="table-row-header"></mat-header-row>
  <mat-row
    *matRowDef="let row; columns: renderedColumns; let i = index"
    cdkDrag
    tabindex="0"
    (keyup)="onRowKeyUp($event)"
    [cdkDragData]="row"
    [style.height]="rowHeight"
    class="table-row"
    [ngClass]="getRowClasses(row)"
    (click)="onRowClick(row, $event, i)"
    (dblclick)="handleDoubleClick(row, $event, i)"
    (contextmenu)="handleContextmenu(row, $event, i)"
    [attr.data-id]="primaryKey ? row[primaryKey] : null"></mat-row>

  <!-- Group header -->
  <ng-container matColumnDef="groupHeader">
    <mat-cell colspan="999" *matCellDef="let group">
      <ng-container
        *ngIf="groupTemplate; else noGroupTemplate"
        [ngTemplateOutlet]="groupTemplate.template"
        [ngTemplateOutletContext]="{
          $implicit: group,
          key: groupByField,
          searchValue: searchValue
        }"></ng-container>
      <ng-template #noGroupTemplate>
        <span class="group-header text-color" [matTooltip]="groupByField && group[groupByField]">{{
          groupByField && group[groupByField]
        }}</span>
      </ng-template>
      <div
        class="expand-btn"
        [matTooltip]="group.expanded ? 'expanded' : 'collapsed'"
        (click)="groupHeaderClick(group)">
        <fa-icon *ngIf="!group.expanded" [icon]="['fas', 'chevron-up']"></fa-icon>
        <fa-icon *ngIf="group.expanded" [icon]="['fas', 'chevron-down']"></fa-icon>
      </div>
    </mat-cell>
  </ng-container>

  <mat-row *matRowDef="let row; columns: ['groupHeader']; when: isGroup"> </mat-row>

  <!-- Custom template for when there are no data rows -->
  <ng-container *matNoDataRow>
    <div class="empty-record-message">{{ emptyRecordMessage | translate }}</div>
  </ng-container>
</mat-table>
<mat-paginator
  class="table-paginator"
  *ngIf="enablePagination"
  [pageSize]="pageSize"
  [hidePageSize]="true"
  [showFirstLastButtons]="true">
</mat-paginator>
