import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthenticationService } from '@bassetti-group/tx-web-core/src/lib/data-access/authentication';
import { AbstractSessionService } from '@bassetti-group/tx-web-core/src/lib/data-access/session';

@Component({
  selector: 'app-auth-callback',
  template: '',
})
export class AuthCallbackComponent implements OnInit {
  constructor(
    private authenticationService: AuthenticationService,
    private route: ActivatedRoute,
    private sessionService: AbstractSessionService
  ) {}

  ngOnInit(): void {
    const languageCode = this.route.snapshot.queryParams.lgCode; // could be undefined
    this.authenticationService
      .retrieveToken()
      .subscribe(() => this.sessionService.load(languageCode));
  }
}
