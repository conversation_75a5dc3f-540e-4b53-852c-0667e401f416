import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TxColorPickerFieldComponent } from './tx-color-picker-field.component';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { MockComponent } from 'ng-mocks';
import { TxColorPickerComponent } from '../../color-picker';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { Component } from '@angular/core';

@Component({
  template: `<tx-color-picker-field [formControl]="controlColor"></tx-color-picker-field>`,
})
class HostComponent {
  controlColor = new FormControl<string>('#ffffff');
}

describe('TxColorPickerFieldComponent', () => {
  let component: TxColorPickerFieldComponent;
  let hostComponent: HostComponent;
  let hostFixture: ComponentFixture<HostComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [HostComponent, MockComponent(TxColorPickerComponent)],
      imports: [
        NoopAnimationsModule,
        TxColorPickerFieldComponent,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        TranslateTestingModule.withTranslations({
          en: {},
        }),
      ],
    });
    hostFixture = TestBed.createComponent(HostComponent);
    hostComponent = hostFixture.componentInstance;
    component = hostFixture.debugElement.children[0].componentInstance;
    hostFixture.detectChanges();
  });

  it('should create', () => {
    expect(hostComponent).toBeTruthy();
    expect(component).toBeTruthy();
  });
});
