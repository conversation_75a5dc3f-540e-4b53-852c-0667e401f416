import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxGridComponent } from '../grid.component';
import { of } from 'rxjs';
import { By } from '@angular/platform-browser';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { OverlayContainer } from '@angular/cdk/overlay';
import { TestbedHarnessEnvironment } from '@angular/cdk/testing/testbed';
import { MatButtonHarness } from '@angular/material/button/testing';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatCheckboxHarness } from '@angular/material/checkbox/testing';

// Mock ConceptType for the test

describe('TxGridComponent', () => {
  let component: TxGridComponent<object>;
  let fixture: ComponentFixture<TxGridComponent<object>>;
  let overlayContainer: OverlayContainer;
  let overlayContainerElement: HTMLElement;
  const data = of([
    { id: 1, name: 'Node 1' },
    { id: 2, name: 'Node 2' },
    { id: 3, name: 'Node 3' },
  ]);
  const TRANSLATIONS = {
    en: {},
    fr: {},
  };
  beforeEach(async () => {
    (window as any).PointerEvent = class PointerEvent extends MouseEvent {};
    await TestBed.configureTestingModule({
      imports: [
        TxGridComponent,
        TranslateTestingModule.withTranslations(TRANSLATIONS),
        FontAwesomeTestingModule,
        NoopAnimationsModule,
        MatCheckboxModule,
      ],
      providers: [OverlayContainer],
    }).compileComponents();

    fixture = TestBed.createComponent(TxGridComponent);
    component = fixture.componentInstance;
    overlayContainer = TestBed.inject(OverlayContainer);
    overlayContainerElement = overlayContainer.getContainerElement();
    component.columns = [
      { field: 'id', header: 'ID' },
      { field: 'name', header: 'Name' },
    ] as any;
    component.primaryKey = 'id' as any;
    component.data = data;
    component.enableMultiSelect = true;
    component.allowRowDragAndDrop = true;
    fixture.detectChanges();
  });

  it('should set data input', () => {
    fixture.detectChanges();
    const matRowDes = fixture.debugElement.queryAll(By.css('.mat-mdc-row'));
    const matRowEls = matRowDes.map((row) => row.nativeElement);
    expect(matRowEls.length).toStrictEqual(3);
  });

  it('should set filter DOM testing', async () => {
    component.enableFiltering = true;
    fixture.detectChanges();
    await fixture.whenStable();

    fixture.detectChanges();
    const matRowDes = fixture.debugElement.queryAll(By.css('.filters'));
    const matRowEls = matRowDes.map((row) => row.nativeElement);

    const loader = TestbedHarnessEnvironment.loader(fixture);
    const menuButton = await loader.getHarness(
      MatButtonHarness.with({ selector: '.mat-mdc-menu-trigger' })
    );
    await menuButton.click();

    fixture.detectChanges();
    await fixture.whenStable();

    const resultingElement = overlayContainerElement;
    const resultingClass = resultingElement.className;
    expect(resultingClass).toBe('cdk-overlay-container');
    expect(matRowEls.length).toStrictEqual(1);
  });

  it('should handle select row', () => {
    component.isRowSelectable = true;
    fixture.detectChanges();
    const matRowDes = fixture.debugElement.queryAll(By.css('.mat-mdc-row'));
    const matRowEls = matRowDes.map((row) => row.nativeElement);
    matRowEls[0].click();
    fixture.detectChanges();
    expect(component.selectedRowPKValue).toStrictEqual(1);
  });

  it('should handle multi-select row: select All', async () => {
    fixture.detectChanges();
    await fixture.whenStable();
    const loader = TestbedHarnessEnvironment.loader(fixture);
    const checkboxes = await loader.getAllHarnesses(
      MatCheckboxHarness.with({ selector: '.mat-mdc-checkbox' })
    );
    expect(checkboxes.length).toBeGreaterThan(0);
    await checkboxes[0].check(); // clicking on master checkbox.
    fixture.detectChanges();
    const matRowDes = fixture.debugElement.queryAll(By.css('.mat-mdc-row'));
    matRowDes.forEach((rowDebugElement) => {
      const checkmark = rowDebugElement.query(By.css('.mdc-checkbox--selected'));
      expect(checkmark).toBeTruthy();
    });
  });

  it('should handle drag-option row', async () => {
    component.allowRowDragAndDrop = true;
    component.renderedColumns = ['drag-drop', ...component.renderedColumns];
    fixture.detectChanges();
    await fixture.whenStable();
    const matRowDes = fixture.debugElement.queryAll(By.css('.drag-cursor'));
    const matRowEls = matRowDes.map((row) => row.nativeElement);
    expect(matRowEls.length).toBeGreaterThan(0);
  });
});
