<mat-chip-option
  class="file-field-box__data-chip"
  *ngIf="file"
  [matTooltip]="file.name"
  [matTooltipShowDelay]="500"
  matTooltipPosition="above"
  [disableRipple]="true"
  [selectable]="false"
  [removable]="true"
  (removed)="removeFile()">
  <div class="file-field-box__chip-content">
    <div class="file-field-box__box">
      <fa-icon
        class="file-field-box__chip-icon file-field-box__chip-icon--left"
        [icon]="icon"></fa-icon>
      <div
        class="file-field-box__file-name-text-container"
        [ngClass]="{ 'file-field-box__file-name-text-container--without-view-toggle': hideToggle }">
        <span class="file-field-box__file-name-text">{{ file.name }}</span>
      </div>
      <div *ngIf="fileUploading; else uploaded" class="file-field-box__progress">
        <mat-progress-bar
          class="file-field-box__progress-bar"
          mode="determinate"
          [value]="file.uploadProgress"></mat-progress-bar>
      </div>
      <ng-template #uploaded>
        <div class="file-field-box__size-and-toggle-container">
          <span *ngIf="file.file" class="file-field-box__field-form-hint">{{ getFileSize() }}</span>
          <mat-slide-toggle
            *ngIf="!hideVisualisationToggle"
            class="file-field-box__form-switch"
            disableRipple="true"
            [checked]="file.view"
            (toggleChange)="changeView()"></mat-slide-toggle>
        </div>
      </ng-template>
      <fa-icon
        class="file-field-box__chip-icon file-field-box__chip-icon--right"
        [icon]="['fal', 'times']"
        matChipRemove></fa-icon>
    </div>
  </div>
</mat-chip-option>
