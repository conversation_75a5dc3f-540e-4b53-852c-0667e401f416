input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.point-field {
  font-size: 14px;
  padding-right: 4px;
  width: calc(100% - 54px);
}

:host ::ng-deep .mat-form-field-infix {
  width: fit-content;
}

:host ::ng-deep .mat-form-field-label-wrapper {
  overflow: visible !important;

  .mat-form-field-label {
    overflow: visible !important;
  }
}

.form-unit-select {
  min-width: 40px;
  width: max-content;
  margin-left: 8px;
  font-size: 13px;
  // float: right;
  margin-top: auto;
  margin-bottom: auto;

  .mat-form-field-wrapper {
    padding-bottom: 0px !important;
  }
}

// :host ::ng-deep .mat-form-field-label-wrapper{
//   width: 300px;
// }

// :host ::ng-deep .mat-form-field-subscript-wrapper{
//   width: 200px;
// }

.shortField {
  width: 170px;
  display: inline-block;
}

.mediumField {
  width: 400px;
}

.longField {
  width: 400px;
}

.input-field {
  width: 70px;
}

.hint-message {
  display: block;
  font-size: 10px;
}

.span-error-point {
  font-size: 10px;
}

.mat-form-label {
  font-size: 12px;
  pointer-events: auto;
  width: 260px;
}

.mat-label-tooltip {
  white-space: pre;
}

:host ::ng-deep .form-field-no-underline .mat-form-field-underline {
  display: none;
}

:host ::ng-deep .mat-form-field-subscript-wrapper {
  width: 272px;
}

.hint-number {
  display: flex;
  width: calc(100% + 50px) !important;
}

.read-form-field {
  padding-top: 0px;
  width: fit-content;
  display: flex;
}

.read-point-values-container {
  padding-top: 16px;
  width: fit-content;
  display: inline;
}

.read-point-value-separator {
  padding: 0px 4px;
  margin: auto;
}

.text-read-form-content {
  display: block;
  margin-bottom: 0px;
}

.read-point-value-container {
  display: inline-flex;
}

.read-point-value {
  display: inline-block;
  max-width: 51px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.one-unit-text {
  display: inline-block;
  font-size: 13px;
  padding-left: 4px;
}
