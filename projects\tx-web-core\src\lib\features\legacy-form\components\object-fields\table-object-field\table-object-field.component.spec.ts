import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxTableObjectFieldComponent } from './table-object-field.component';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { MockService } from 'ng-mocks';

describe('TxTableObjectFieldComponent', () => {
  let component: TxTableObjectFieldComponent;
  let fixture: ComponentFixture<TxTableObjectFieldComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxTableObjectFieldComponent],
      providers: [
        {
          provide: LegacyTxAttributesService,
          useValue: MockService(LegacyTxAttributesService),
        },
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxTableObjectFieldComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
