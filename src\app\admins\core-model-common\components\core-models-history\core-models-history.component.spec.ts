import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CoreModelsHistoryComponent } from './core-models-history.component';
import { CoreModelsHistory } from '../../models/core-models-history.model';
import { MockComponent, MockDirective, MockPipe } from 'ng-mocks';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { TRANSLATIONS_MOCK } from 'src/app/core/translation/translations.mock';
import { OverrideColumnPipe } from 'src/app/shared/pipes/override-column.pipe';
import { OverrideColumnDirective } from 'src/app/shared/directives/override-column.directive';
import { TxGridComponent, TxGridService } from '@bassetti-group/tx-web-core';

describe('CoreModelsHistoryComponent', () => {
  let component: CoreModelsHistoryComponent<CoreModelsHistory>;
  let fixture: ComponentFixture<CoreModelsHistoryComponent<CoreModelsHistory>>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        CoreModelsHistoryComponent,
        MockComponent(TxGridComponent),
        MockPipe(OverrideColumnPipe),
        MockDirective(OverrideColumnDirective),
      ],
      imports: [TranslateTestingModule.withTranslations(TRANSLATIONS_MOCK)],
      providers: [TxGridService],
    }).compileComponents();

    fixture = TestBed.createComponent(CoreModelsHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
