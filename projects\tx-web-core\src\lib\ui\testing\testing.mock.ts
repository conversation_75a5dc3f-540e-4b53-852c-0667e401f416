import { Pipe, PipeTransform, Component } from '@angular/core';
import { Observable, of, ReplaySubject } from 'rxjs';
import { User, UserGroup } from '../../data-access/session';
import { ToastComponent, ToastData } from '../toast';

/**
 * An ActivateRoute test double with a `queryParams` observable.
 * Use the `setParams()` method to add the next `queryParams` value.
 */
export class ActivatedRouteStub {
  // Use a ReplaySubject to share previous values with subscribers
  // and pump new values into the `queryParams` observable
  private subject = new ReplaySubject<any>();

  constructor(initialParams?: any) {
    this.setParams(initialParams || {});
  }

  /** The mock queryParams observable */
  readonly queryParams = this.subject.asObservable();

  /** Set the queryParams observables's next value */
  setParams(params?: any) {
    this.subject.next(params);
  }
}

/* Pipes */
@Pipe({ name: 'activeUser' })
export class ActiveUserPipe implements PipeTransform {
  transform(value: any) {
    return value;
  }
}

@Pipe({ name: 'translate' })
export class TranslatePipe implements PipeTransform {
  transform(value: any) {
    return value;
  }
}

/* Services */
export const testUsers = [
  { id: 1, name: 'Manu', login: 'ecarre', password: 'mmmmm' },
  { id: 2, name: 'Abdou', login: 'atoure', password: 'aaaaa' },
  {
    id: 3,
    name: 'Antoine',
    login: 'aarcangeloni',
    password: 'aaaaa',
    isSynchronizedWithLdap: true,
  },
];
export const testGroups = [
  {
    id: 1,
    name: 'admin',
    explanation: 'Tous les droits',
    type: 0,
    ldapInteractionType: 0,
  },
  {
    id: 2,
    name: 'dev',
    explanation: 'Droits accordés aux développeurs',
    type: 0,
    ldapInteractionType: 0,
  },
  {
    id: 3,
    name: 'dba',
    explanation: 'Droits accordés aux administrateurs Base de Données',
    type: 0,
    ldapInteractionType: 0,
  },
];
const contacts = [
  {
    creationDate: '2020-05-14T07:14:14.5730000Z',
    id: 42,
    idObjectParent: -1000,
    idObjectType: 1,
    idOwnerObject: 1,
    isFolder: false,
    isParent: false,
    name: 'Contact1',
    order: 0,
    searchName: 'CONTACT1',
    tags: '',
  },
  {
    creationDate: '2020-06-01T07:14:14.5730000Z',
    id: 43,
    idObjectParent: -1000,
    idObjectType: 1,
    idOwnerObject: 1,
    isFolder: false,
    isParent: false,
    name: 'Contact2',
    order: 0,
    searchName: 'CONTACT2',
    tags: '',
  },
  {
    creationDate: '2020-06-01T07:14:14.5730000Z',
    id: 43,
    idObjectParent: -1000,
    idObjectType: 1,
    idOwnerObject: 1,
    isFolder: false,
    isParent: false,
    name: 'Contact2',
    order: 0,
    searchName: 'CONTACT2',
    tags: '',
  },
];

export class UserServiceMock {
  loadingState() {
    return of(false);
  }
  getUsers() {
    return of(testUsers);
  }
  getGroupFromUser(id: number) {
    return of(testGroups);
  }
  getGroups() {
    return of(testGroups);
  }
  addUser(user: User) {
    return of(user);
  }
  editUser(user: User) {
    const testUser = testUsers.find((u) => u.id === user.id) as User;
    Object.assign(testUser, user);
    return of(user);
  }
  deleteUsers(users: User[]) {
    return of(testUsers[0]);
  }
  deleteUser(user: User) {
    return of(user);
  }
  addGroups(idUser: number, groups: UserGroup[]) {
    return of(null);
  }
  removeGroups(idUser: number, groups: UserGroup[]) {
    return of({
      id: 4,
      name: 'Hind',
      login: 'hsou',
      password: 'hhhhh',
      groups: [],
    });
  }
  getContactsUnbound() {
    return of(contacts);
  }
  addUsers(idGroup: number, users: UserGroup[]) {
    return of(null);
  }
  removeUsers(idGroup: number, users: UserGroup[]) {
    return of({
      id: 1,
      name: 'admin',
      explanation: 'Tous les droits',
      type: 0,
      ldapInteractionType: 0,
      users: [{ id: 1, name: 'Manu', login: 'ecarre', password: 'mmmmm' }],
    });
  }
  isLoginAlreadyUsed(login: string) {
    const res = login === 'ecarre';
    return of({ result: res });
  }

  getPasswordStrength(password: string) {
    const isPwdStrong = password === 'Str0ng_Pwd';
    return of({
      length: isPwdStrong,
      uppercase: true,
      lowercase: true,
      digit: true,
      special: isPwdStrong,
      result: isPwdStrong,
    });
  }
}

export class SessionServiceMock {
  locale = 'en';
  retrieveToken(): Observable<string> {
    return of('1234');
  }
  registerCultureFromAuth(culture: string): void {
    return undefined;
  }
  registerCulture(culture: string, reset?: boolean): void {
    this.locale = culture;
  }
  // getLanguages(): Observable<Lang[]> {
  //   return of([
  //     { id: 1, name: 'french', code: 'fr' },
  //     { id: 2, name: 'english', code: 'en' },
  //   ]);
  // }
  getLoadingState(): Observable<boolean> {
    return of(false);
  }

  getStrongPwdPolicy() {
    return false;
  }

  getDateFormatPreference() {
    return 'dd/MM/yyyy';
  }

  getDelegateAuthentication() {
    return of(false);
  }
}

export class ConfigServiceMock {
  getApiUrl(): string {
    return 'https://localhost:44336/';
  }
  getAuthUrl(): string {
    return 'https://localhost:44336/';
  }
  getFormsConfigsList() {
    return { txFormsConfigurations: [] };
  }
}

export class AppServiceMock {
  public theme = 'teexma-theme';
  public isSideNavOpen = false;

  getSideNavState() {
    return of(this.isSideNavOpen);
  }
  setSideNavState(b: boolean) {
    this.isSideNavOpen = b;
  }
  getThemeClass() {
    return this.theme;
  }
  setThemeClass(t: string) {
    this.theme = t;
  }
}

// const connectedUsers: CurrentActiveSession[] = [
//   {
//     id: 1,
//     dllHostPID: 1234,
//     ipClient: '127.001.5',
//     ipServer: '125.002.1',
//     login: 'ecarre',
//     sessionID: '1111',
//   },
//   {
//     id: 2,
//     dllHostPID: 1235,
//     ipClient: '127.001.8',
//     ipServer: '125.002.1',
//     login: 'hsoukane',
//     sessionID: '2222',
//   },
//   {
//     id: 3,
//     dllHostPID: 1236,
//     ipClient: '127.001.2',
//     ipServer: '125.002.1',
//     login: 'aarcangeloni',
//     sessionID: '3333',
//   },
// ];
const globalConnections: number[][] = [
  [new Date('2020-12-18T08:47:12Z').getTime(), 5],
  [new Date('2020-12-18T08:47:53Z').getTime(), 6],
  [new Date('2020-12-18T08:48:26Z').getTime(), 7],
  [new Date('2020-12-18T08:50:15Z').getTime(), 6],
  [new Date('2020-12-18T08:52:49Z').getTime(), 0],
];
// const serverEvents: ServerEvents[] = [
//   {
//     id: 1,
//     date: new Date('2020-12-18T08:00:00Z'),
//     ipServer: '125.002.1',
//     name: 'ApplicationStart',
//   },
//   {
//     id: 2,
//     date: new Date('2020-12-18T08:52:49Z'),
//     ipServer: '125.002.1',
//     name: 'ApplicationStop',
//   },
// ];

// export class ConnectionsServiceMock {
//   public connectedUsers: CurrentActiveSession[];

//   constructor() {
//     this.connectedUsers = connectedUsers;
//   }

//   getConnectedUsers() {
//     return of(this.connectedUsers);
//   }
//   getGlobalConnections() {
//     return of(globalConnections);
//   }
//   getServerEvents() {
//     return of(serverEvents);
//   }
// }

// export class ResourcesServiceMock {
//   public histories: FileHistory[] = [];

//   constructor() {
//     this.histories = [
//       {
//         sUsername: 'Administrator',
//         date: new Date('2020-12-16T08:47:12Z'),
//         sActionType: ActionType.atAddFile,
//         sBackup: '',
//         sRelativePath:
//           'Customer resources\\Models\\TxExtraction\\TxExtraction_03.xml',
//       },
//       {
//         sUsername: 'Administrator',
//         date: new Date('2020-12-17T08:47:12Z'),
//         sActionType: ActionType.atReplaceFile,
//         sBackup: '',
//         sRelativePath:
//           'Customer resources\\Models\\TxExtraction\\TxExtraction_02.xml',
//       },
//       {
//         sUsername: 'Benoit France',
//         date: new Date('2020-12-18T08:47:12Z'),
//         sActionType: ActionType.atReplaceFile,
//         sBackup: '',
//         sRelativePath:
//           'Customer resources\\Models\\TxExtraction\\TxExtraction_01.xml',
//       },
//     ];
//   }

//   onInitialize() {
//     return of({
//       folders: [
//         {
//           sName: 'Customer resources',
//           sPath: 'D:\\TEEXMA\\Client\\Client_INT\\Customer resources',
//           settings: {
//             bCanAddFiles: false,
//             bCanModifyFiles: false,
//             extensions: [],
//             sListingType: 'ltWhiteList',
//           },
//         },
//         {
//           sName: 'File deposite',
//           sPath: 'D:\\TEEXMA\\Client\\Client_INT\\File deposite',
//           settings: {
//             bCanAddFiles: true,
//             bCanModifyFiles: true,
//             extensions: ['.log'],
//             sListingType: 'ltBlackList',
//           },
//         },
//       ],
//     });
//   }
//   getHistories() {
//     return of(this.histories);
//   }
//   updateHistories(path: string) {}
//   onExpandingFolder(path: string) {
//     return of({
//       files: [],
//       folders: [
//         {
//           sName: 'Models',
//           sPath: 'D:\\TEEXMA\\Client\\Client_INT\\Customer resources\\Models',
//           settings: {
//             bCanAddFiles: false,
//             bCanModifyFiles: false,
//             extensions: [],
//             sListingType: 'ltWhiteList',
//           },
//         },
//       ],
//     });
//   }
//   onDownloadingFile() {
//     const fakeFile = (): File => {
//       const blob = new Blob([''], { type: 'text/html' });
//       return blob as File;
//     };
//     return of(fakeFile());
//   }
//   onUploadingFile(files: File[]) {
//     return of();
//   }
// }

export class TranslateServiceMock {
  onLangChange(lang: string): Observable<any> {
    return of({});
  }
  get(array: string[]): Observable<any> {
    return of({});
  }
  getTranslation(lang: string): Observable<any> {
    return of({
      syncFusion: {
        grid: {},
        pager: {},
        uploader: {},
        datepicker: {},
      },
    });
  }
  instant(key: string): string {
    return key;
  }
  setDefaultLang(lang: string): void {
    return undefined;
  }
  use(lang: string): void {
    return undefined;
  }
}

export const notifications: ToastData[] = [
  {
    type: 'success',
    interval: 8000,
    title: 'Title1',
    description: 'notification1',
    date: new Date(),
    isPersistent: true,
    isUnread: true,
  },
  {
    type: 'warning',
    interval: 8000,
    title: 'Title2',
    description: 'notification2',
    date: new Date(),
    isPersistent: true,
    isUnread: false,
  },
  {
    type: 'loading',
    interval: 0,
    title: 'Title3',
    description: 'notification3',
    date: new Date(),
    isPersistent: true,
    isUnread: true,
  },
];

export class ToastServiceMock {
  notifications = notifications;

  show(data: ToastData) {
    return undefined;
  }
  getNotifications(): ToastData[] {
    return this.notifications;
  }
  hasUnreadNotifications(): boolean {
    return true;
  }
}

export class ToastRefMock {
  afterDisplay() {
    return undefined;
  }
  close() {
    return undefined;
  }
}

export class MatSnackBarMock {
  open() {
    return {
      afterDismissed: () =>
        of({
          dismissedByAction: false,
        }),
    };
  }
}

export interface MockFile {
  name: string;
  body: string;
  mimeType: string;
}

export const createFileFromMockFile = (file: MockFile): File => {
  const blob = new Blob([file.body], { type: file.mimeType }) as any;
  blob.lastModifiedDate = new Date();
  blob.name = file.name;
  return blob as File;
};

export const createMockFileList = (files: MockFile[]) => {
  const fileList: FileList = {
    length: files.length,
    item(index: number): File {
      return fileList[index];
    },
  } as FileList;
  files.forEach((file, index) => (fileList[index] = createFileFromMockFile(file)));
  return fileList;
};

// export const testAudit: Audit[] = [
//   {
//     id: 0,
//     explanation: '',
//     name: 'Tagged User Requirement Lists',
//     status: 'warning',
//     result:
//       'The standard Requiremnt List "[Rights distribution] Organization Levels" is tagged (rlOLRightsDistribution). \n This is not a good practice to use User RL in settings.\n In 4.4, they will be turned into a "system" RL.',
//     isMultiLine: false,
//     allResult: '',
//     isShowAllLine: false,
//   },
//   {
//     id: 1,
//     explanation:
//       'Searching for Excel models. This functionnality will be deprecated in version 4.2.1.',
//     name: '[4.3+] Excel models',
//     status: 'ok',
//     result: 'No excel model found. ',
//     isMultiLine: false,
//     allResult: '',
//     isShowAllLine: false,
//   },
//   {
//     id: 2,
//     explanation: '',
//     name: 'Number of Object Types',
//     status: 'ok',
//     result: '86 record(s) found.',
//     isMultiLine: false,
//     allResult: '',
//     isShowAllLine: false,
//   },
// ];

// export const auditTest: Audit[] = [
//   {
//     id: 0,
//     explanation: '',
//     name: 'Tagged User Requirement Lists',
//     status: 'warning',
//     result:
//       'The standard Requiremnt List "[Rights distribution] Organization Levels" is tagged (rlOLRightsDistribution). \n This is not a good practice to use User RL in settings.\n In 4.4, they will be turned into a "system" RL.',
//     isMultiLine: false,
//     allResult: '',
//     isShowAllLine: false,
//   },
//   {
//     id: 1,
//     explanation:
//       'Searching for Excel models. This functionnality will be deprecated in version 4.2.1.',
//     name: '[4.3+] Excel models',
//     status: 'ok',
//     result: 'No excel model found. ',
//     isMultiLine: false,
//     allResult: '',
//     isShowAllLine: false,
//   },
// ];

// export const testSections: Sections[] = [
//   {
//     name: 'Migration',
//     auditPoints: [
//       {
//         id: 0,
//         explanation:
//           'Searching for Excel models. This functionnality will be deprecated in version 4.2.1.',
//         name: '[4.3+] Excel models',
//         status: '',
//         result: '',
//         isMultiLine: false,
//         allResult: '',
//         isShowAllLine: false,
//       },
//       {
//         id: 1,
//         explanation: 'Searching for Extractions v1.',
//         name: '[4.3+] Old Extraction module',
//         status: '',
//         result: '',
//         isMultiLine: false,
//         allResult: '',
//         isShowAllLine: false,
//       },
//     ],
//   },
//   {
//     name: 'Database content',
//     auditPoints: [
//       {
//         id: 2,
//         explanation: '',
//         name: 'Number of Object Types',
//         status: '',
//         result: '',
//         isMultiLine: false,
//         allResult: '',
//         isShowAllLine: false,
//       },
//     ],
//   },
// ];

// export const testSectionsReponse: Sections[] = [
//   {
//     name: 'Migration',
//     auditPoints: [
//       {
//         id: 0,
//         explanation:
//           'Searching for Excel models. This functionnality will be deprecated in version 4.2.1.',
//         name: '[4.3+] Excel models',
//         status: 'ok',
//         result: 'No excel model found. ',
//         isMultiLine: false,
//         allResult: '',
//         isShowAllLine: false,
//       },
//       {
//         id: 5,
//         explanation: 'Searching for Extractions v1.',
//         name: '[4.3+] Old Extraction module',
//         status: '',
//         result: 'No Module was found',
//         isMultiLine: false,
//         allResult: '',
//         isShowAllLine: false,
//       },
//     ],
//   },
//   {
//     name: 'Database content',
//     auditPoints: [
//       {
//         id: 3,
//         explanation: '',
//         name: 'Number of Object Types',
//         status: '',
//         result: '',
//         isMultiLine: false,
//         allResult: '',
//         isShowAllLine: false,
//       },
//     ],
//   },
// ];

// export const testSectionsExportWithRTL: Sections[] = [
//   {
//     name: 'Migration',
//     auditPoints: [
//       {
//         id: 0,
//         explanation: '',
//         name: 'Tagged User Requirement Lists',
//         status: 'warning',
//         result:
//           'The standard Requiremnt List "[Rights distribution] Organization Levels" is tagged (rlOLRightsDistribution). \n This is not a good practice to use User RL in settings.\n In 4.4, they will be turned into a "system" RL.',
//         isMultiLine: false,
//         allResult: '',
//         isShowAllLine: false,
//       },
//     ],
//   },
// ];
// export const testSectionsExportNORTL: Sections[] = [
//   {
//     name: 'Migration',
//     auditPoints: [
//       {
//         id: 1,
//         explanation: '',
//         name: 'Tagged User Requirement Lists',
//         status: 'warning',
//         result:
//           'The standard Requiremnt List "[Rights distribution] Organization Levels" is tagged (rlOLRightsDistribution).This is not a good practice to use User RL in settings.In 4.4, they will be turned into a "system" RL.',
//         isMultiLine: false,
//         allResult: '',
//         isShowAllLine: false,
//       },
//     ],
//   },
// ];

// export const testSectionsExportWithRTLResult: Worksheets = {
//   worksheets: [
//     {
//       cells: [
//         {
//           col: 1,
//           data: 'Name',
//           row: 1,
//         },
//         {
//           col: 2,
//           data: 'Status',
//           row: 1,
//         },
//         {
//           col: 3,
//           data: 'Message',
//           row: 1,
//         },
//         {
//           col: 1,
//           data: 'Tagged User Requirement Lists',
//           row: 2,
//         },
//         {
//           col: 2,
//           data: 'warning',
//           row: 2,
//         },
//         {
//           col: 3,
//           data: 'The standard Requiremnt List "[Rights distribution] Organization Levels" is tagged (rlOLRightsDistribution). ',
//           row: 2,
//         },
//         {
//           col: 1,
//           data: 'Tagged User Requirement Lists',
//           row: 3,
//         },
//         {
//           col: 2,
//           data: 'warning',
//           row: 3,
//         },
//         {
//           col: 3,
//           data: ' This is not a good practice to use User RL in settings.',
//           row: 3,
//         },
//         {
//           col: 1,
//           data: 'Tagged User Requirement Lists',
//           row: 4,
//         },
//         {
//           col: 2,
//           data: 'warning',
//           row: 4,
//         },
//         {
//           col: 3,
//           data: ' In 4.4, they will be turned into a "system" RL.',
//           row: 4,
//         },
//       ],
//       name: 'Migration',
//     },
//   ],
// };

// export const testSectionsExportNORTLResult: Worksheets = {
//   worksheets: [
//     {
//       cells: [
//         {
//           col: 1,
//           data: 'Name',
//           row: 1,
//         },
//         {
//           col: 2,
//           data: 'Status',
//           row: 1,
//         },
//         {
//           col: 3,
//           data: 'Message',
//           row: 1,
//         },
//         {
//           col: 1,
//           data: 'Tagged User Requirement Lists',
//           row: 2,
//         },
//         {
//           col: 2,
//           data: 'warning',
//           row: 2,
//         },
//         {
//           col: 3,
//           data: 'The standard Requiremnt List "[Rights distribution] Organization Levels" is tagged (rlOLRightsDistribution).This is not a good practice to use User RL in settings.In 4.4, they will be turned into a "system" RL.',
//           row: 2,
//         },
//       ],
//       name: 'Migration',
//     },
//   ],
// };

// export const testDataBaseInformation: DatabaseInformation = {
//   revision: 6666,
//   version: '4.3',
// };

// export const testInformationAuditPoint: InformationAudit = {
//   numbreAuditPoints: 2,
//   isFileUploded: true,
// };

// export class AuditsServiceMock {
//   getSections() {
//     return of(testSections);
//   }
//   getInfomation() {
//     return of(testDataBaseInformation);
//   }
//   getInformationAuditPoint() {
//     return of(testInformationAuditPoint);
//   }
//   getLoadingBar() {
//     return of(true);
//   }
//   executeQueries() {
//     return [of(testAudit[0]), of(testAudit[1]), of(testAudit[2])];
//   }
//   updateAuditPoint(audit) {}

//   postFile(file) {
//     return of('');
//   }
//   deleteAuditFiles() {
//     return of('');
//   }
//   callTxAudit() {
//     testInformationAuditPoint.isFileUploded = false;
//   }
//   exportResults() {
//     const fakeFile = (): File => {
//       const blob = new Blob([''], { type: 'text/html' });
//       return blob as File;
//     };
//     return of(fakeFile());
//   }
// }

export class RequestsServiceMock {
  getContentFile(fileToUpload: File): Observable<any> {
    return of({
      contents: 'content',
      id: 0,
    });
  }

  deleteRequestFiles(idRequest: number) {
    return of('');
  }

  execute(idRequest: number) {
    return of('');
  }
}

/* Components */
// @Component({
//   selector: 'app-user-form',
//   template: ``,
//   providers: [
//     {
//       provide: UserFormComponent,
//       useClass: UserFormMockComponent,
//     },
//   ],
// })
// export class UserFormMockComponent {
//   @Input() user!: User;
//   @Input() isEditMode!: boolean;

//   @Output() hideForm = new EventEmitter<User>();
//   @Output() addUser = new EventEmitter<{ user: User; groups: UserGroup[] }>();
//   @Output() editUser = new EventEmitter<{ user: User; groups: UserGroup[] }>();

//   constructor() {}
// }

// @Component({
//   selector: 'app-group-management',
//   template: ``,
//   providers: [
//     {
//       provide: GroupManagementComponent,
//       useClass: GroupManagementMockComponent,
//     },
//   ],
// })
// export class GroupManagementMockComponent {
//   @Input() group: UserGroup;

//   @Output() hideForm = new EventEmitter<boolean>();

//   constructor() {}
// }

// @Component({
//   selector: 'app-user-management',
//   template: ``,
//   providers: [
//     {
//       provide: UserManagementComponent,
//       useClass: UserManagementMockComponent,
//     },
//   ],
// })
// export class UserManagementMockComponent {
//   @Input() user: User;

//   constructor() {}
// }

// @Component({
//   selector: 'app-main-nav',
//   template: ``,
//   providers: [
//     {
//       provide: MainNavComponent,
//       useClass: MainNavMockComponent,
//     },
//   ],
// })
// export class MainNavMockComponent {}

// @Component({
//   selector: 'app-breadcrumd',
//   template: ``,
//   providers: [
//     {
//       provide: BreadcrumdComponent,
//       useClass: BreadcrumdMockComponent,
//     },
//   ],
// })
// export class BreadcrumdMockComponent {}

// @Component({
//   selector: 'app-history',
//   template: ``,
//   providers: [
//     {
//       provide: HistoryComponent,
//       useClass: HistoryMockComponent,
//     },
//   ],
// })
// export class HistoryMockComponent {}

// @Component({
//   selector: 'app-right-pane',
//   template: ``,
//   providers: [
//     {
//       provide: RightPaneComponent,
//       useClass: RightPaneMockComponent,
//     },
//   ],
// })
// export class RightPaneMockComponent {
//   @Input() templateContent: TemplateRef<any>;
//   @Input() width: string;

//   @Output() hide = new EventEmitter();

//   displayPane() {}
//   hidePane() {}
// }
// @Component({
//   selector: 'tx-combobox-object',
//   template: ``,
//   providers: [
//     {
//       provide: TxComboboxObjectComponent,
//       useClass: TxComboboxObjectMockComponent,
//     },
//   ],
// })
// export class TxComboboxObjectMockComponent {
//   @Input() idObjectType: number;
//   @Input() txObjects: TxObject[];
//   @Input() placeholder: string;

//   @Output() select = new EventEmitter();
//   @Output() removed = new EventEmitter();
// }

@Component({
  selector: 'app-toast',
  template: ``,
  providers: [
    {
      provide: ToastComponent,
      useClass: ToastMockComponent,
    },
  ],
})
export class ToastMockComponent {
  // constructor (data: ToastData) {}
}

// const attributesMock = [
//   new TxAttribute({
//     name: 'testGroup',
//     idAttributeParent: 2,
//     inherited: false,
//     dataType: 13,
//     idObjectType: 1,
//     id: 1,
//   }),
//   new TxAttribute({
//     name: 'testTab',
//     idAttributeParent: 0,
//     inherited: false,
//     dataType: -2,
//     idObjectType: 1,
//     id: 2,
//   }),
//   new TxAttribute({
//     name: 'testAttr1',
//     idAttributeParent: 2,
//     inherited: false,
//     dataType: 121,
//     idObjectType: 1,
//     id: 329,
//   }),
//   new TxAttribute({
//     name: 'testattr2',
//     idAttributeParent: 555,
//     inherited: false,
//     dataType: 121,
//     idObjectType: 1,
//     id: 330,
//   }),
//   new TxAttribute({
//     name: 'testattr3',
//     idAttributeParent: 1,
//     inherited: false,
//     dataType: 121,
//     idObjectType: 1,
//     id: 89,
//   }),
// ];

export class ObjectTypeMockService {
  start() {
    return undefined;
  }

  getObjectType(param: any) {
    return null;
  }

  getObjectTypeAttributes(idObjectType: number): Observable<any[]> {
    return of([
      {
        name: 'testGroup',
        idAttributeParent: 2,
        isInherited: false,
        dataType: 13,
        idObjectType: 1,
        id: 1,
      },
      {
        name: 'testTab',
        idAttributeParent: 0,
        isInherited: false,
        dataType: -2,
        idObjectType: 1,
        id: 2,
      },
      {
        name: 'testAttr1',
        idAttributeParent: 2,
        isInherited: false,
        dataType: 121,
        idObjectType: 1,
        id: 329,
      },
      {
        name: 'testattr2',
        idAttributeParent: 555,
        isInherited: false,
        dataType: 121,
        idObjectType: 1,
        id: 330,
      },
      {
        name: 'testattr3',
        idAttributeParent: 1,
        isInherited: false,
        dataType: 121,
        idObjectType: 1,
        id: 89,
      },
    ]);
  }
}

// export class FormConfigFillerMock {
//   public addTab(
//     formConfig: TxFormConfiguration,
//     tabAttr: TxAttribute,
//     idObjectType: number,
//     addChildrenAttributes: boolean,
//     treatInheritedAttributes: boolean
//   ) {
//     const tabField = new TxTabAttributeField();
//     tabField.attribute = attributesMock[1];
//     const attr1Field = new TxAttributeField();
//     attr1Field.attribute = attributesMock[2];
//     const grpField = new TxGroupAttributeField();
//     grpField.attribute = attributesMock[0];
//     const attr2Field = new TxAttributeField();
//     attr2Field.attribute = attributesMock[4];
//     tabField.children.push(attr1Field);
//     grpField.children.push(attr2Field);
//     tabField.children.push(grpField);
//     formConfig.tabs.push(tabField);
//   }

//   public setMandatories(
//     formConfig: TxFormConfiguration,
//     listMandatoriesAttrIds: number[]
//   ) {
//     formConfig.tabs[0].children[0].properties.mandatory = true;
//   }
// }
