import { Pipe, PipeTransform } from '@angular/core';
import { GridFilterValue } from './grid-filter-menu.component';
import { TxCommonUtils } from '@bassetti-group/tx-web-core/src/lib/utilities';

@Pipe({
  name: 'notEmptyFilter',
  standalone: true,
})
export class TxNotEmptyFilterPipe implements PipeTransform {
  transform(value: GridFilterValue | null | undefined): boolean {
    return TxCommonUtils.isDefined<GridFilterValue>(value);
  }
}
