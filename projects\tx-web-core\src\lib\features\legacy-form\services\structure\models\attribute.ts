import { LegacyTxFileType } from './file-type';
import { LegacyTxLinkType } from './link-type';
import { LegacyTxDataBaseAction, LegacyTxDataType } from './data';
import { TxTableType } from './table-type';
import { Subscription } from 'rxjs';

export enum LegacyTxAttributeRight {
  None = 0,
  Read = 1,
  Add = 2,
  Modify = 3,
  Structure = 4,
}

export enum LegacyTxNumericalFloatFormat {
  General = 'ffGeneral',
  Exponent = 'ffExponent',
  Fixed = 'ffFixed',
  Number = 'ffNumber',
  Currency = 'ffCurrency',
}

export enum LegacyTxLinkDisplayMode {
  ComboTree = 'ComboTree',
  Combo = 'Combo',
  Chips = 'Chips',
  List = 'List',
  Matrix = 'Matrix',
  OneFieldPerRaw = 'OneFieldPerRaw',
  ChipsRead = 'ChipsRead',
}

export class LegacyTxFile {
  constructor(
    public name: string,
    public size: number = -1,
    public view = false,
    public idArchivedFile?: number,
    public action: LegacyTxDataBaseAction = LegacyTxDataBaseAction.None,
    public file?: File,
    public uploadSub?: Subscription,
    public uploadProgress?: number,
    public icon?: string[]
  ) {}
}

export interface LegacyTxAttributeSetting {
  isTrackable?: boolean;
  description?: string;
  explanation?: string;
  color?: string;
  isUnderlined?: boolean;
}

export interface LegacyTxAttributeFileSetting extends LegacyTxAttributeListSetting {
  idFileType: number;
  extensionsFile?: string[];
}

export interface LegacyTxAttributeLongTextSetting extends LegacyTxAttributeInputSetting {
  numberOfLines?: number;
}

export interface LegacyTxAttributeShortStringSetting extends LegacyTxAttributeLongTextSetting {
  singleLine: boolean;
  pattern?: RegExp;
}

export interface LegacyTxAttributeListSetting extends LegacyTxAttributeSetting {
  isList: boolean;
}

export interface LegacyTxAttributeLinkSetting extends LegacyTxAttributeSetting {
  idLinkType: number;
  linkDisplayMode?: LegacyTxLinkDisplayMode;
}

export interface LegacyTxAttributeInputSetting extends LegacyTxAttributeSetting {
  minLength?: number;
  maxLength?: number;
}

export interface LegacyTxAttributePointSetting extends LegacyTxAttributeInputSetting {
  lowerBound: number;
  upperBound: number;
  isLBInclusive?: boolean;
  isUBInclusive?: boolean;
  idUnit?: number;
  digits?: number;
  floatFormat?: LegacyTxNumericalFloatFormat;
  precision?: number;
  isDisplayedInMainUnit?: boolean;
}

export interface LegacyTxAttributeTableSetting extends LegacyTxAttributeSetting {
  idTableType: number;
  isIndexesDisplayed?: boolean;
  isSeriesNameDisplayed?: boolean;
  isTableDisplayed?: boolean;
  isTransposed?: boolean;
  idFileType?: number;
}

export class LegacyTxAttribute {
  public id: number;
  public name: string;
  public dataType: LegacyTxDataType;
  public idObjectType: number;
  public idAttributeParent = 0;
  public tags: string[] = [];
  public right: LegacyTxAttributeRight = LegacyTxAttributeRight.None;
  public icon?: number;
  public isInherited: boolean;
  public isTrackable?: boolean;
  public isUnderlined?: boolean;
  public description?: string;
  public explanation?: string;
  public color?: string;
  public order: number;
  public idInheritedAttribute: number;
  public idLinkType: number;
  public option: any = {};

  constructor(attribute: LegacyTxAttribute) {
    this.id = attribute?.id;
    this.name = attribute.name;
    this.dataType = attribute.dataType;
    this.idObjectType = attribute.idObjectType;
    this.idAttributeParent = attribute.idAttributeParent || 0;
    this.tags = attribute.tags || [];
    this.right = attribute.right;
    this.isInherited = attribute.isInherited;
    this.icon = attribute.icon;
    this.isTrackable = attribute.isTrackable;
    this.description = attribute.description;
    this.explanation = attribute.explanation;
    this.color = attribute.color;
    this.isUnderlined = attribute.isUnderlined;
    this.order = attribute.order;
    this.idInheritedAttribute = attribute.idInheritedAttribute;
    this.idLinkType = attribute.idLinkType;
  }
}

export class LegacyTxAttributeList
  extends LegacyTxAttribute
  implements LegacyTxAttributeListSetting
{
  isList: boolean;

  constructor(attribute: LegacyTxAttributeList) {
    super(attribute);
    this.isList = attribute.isList;
  }
}

export class LegacyTxAttributeFile
  extends LegacyTxAttributeList
  implements LegacyTxAttributeFileSetting
{
  idFileType: number;
  fileType?: LegacyTxFileType;

  constructor(attribute: LegacyTxAttributeFile) {
    super(attribute);
    this.idFileType = attribute.idFileType;

    if (attribute.fileType) {
      this.fileType = new LegacyTxFileType(attribute.fileType);
    }
  }
}

export class TxAttributeMail extends LegacyTxAttributeList {
  constructor(attribute: TxAttributeMail) {
    super(attribute);
  }
}

export class TxAttributeUrl extends LegacyTxAttributeList {
  constructor(attribute: TxAttributeUrl) {
    super(attribute);
  }
}

export class TxAttributeLink extends LegacyTxAttributeList implements LegacyTxAttributeLinkSetting {
  linkDisplayMode?: any;
  linkType?: LegacyTxLinkType;

  constructor(attribute: TxAttributeLink) {
    super(attribute);
    switch (attribute.linkDisplayMode) {
      case 2:
        this.linkDisplayMode = LegacyTxLinkDisplayMode.Combo;
        break;
      default:
        this.linkDisplayMode = LegacyTxLinkDisplayMode.ComboTree;
        break;
    }

    if (attribute.linkType) {
      this.linkType = new LegacyTxLinkType(attribute.linkType);
    }
  }
}

export class TxAttributePoint extends LegacyTxAttribute implements LegacyTxAttributePointSetting {
  lowerBound: number;
  upperBound: number;
  isLBInclusive?: boolean;
  isUBInclusive?: boolean;
  idUnit?: number;
  digits?: number;
  floatFormat?: LegacyTxNumericalFloatFormat;
  precision?: number;
  isDisplayedInMainUnit?: boolean;

  constructor(attribute: TxAttributePoint) {
    super(attribute);
    this.lowerBound = attribute.lowerBound;
    this.upperBound = attribute.upperBound;
    this.isLBInclusive = attribute.isLBInclusive;
    this.isUBInclusive = attribute.isUBInclusive;
    this.idUnit = attribute.idUnit;
    this.digits = attribute.digits;
    this.floatFormat = attribute.floatFormat;
    this.precision = attribute.precision;
    this.isDisplayedInMainUnit = attribute.isDisplayedInMainUnit;
  }
}

export class LegacyTxAttributeTable
  extends LegacyTxAttribute
  implements LegacyTxAttributeTableSetting
{
  idTableType: number;
  isIndexesDisplayed?: boolean;
  isSeriesNameDisplayed?: boolean;
  isTableDisplayed?: boolean;
  isTransposed?: boolean;
  idFileType?: number;
  tableType?: TxTableType;

  constructor(attribute: LegacyTxAttributeTable) {
    super(attribute);
    this.idTableType = attribute.idTableType;
    this.isIndexesDisplayed = attribute.isIndexesDisplayed;
    this.isSeriesNameDisplayed = attribute.isSeriesNameDisplayed;
    this.isTableDisplayed = attribute.isTableDisplayed;
    this.isTransposed = attribute.isTransposed;
    this.idFileType = attribute.idFileType;

    if (attribute.tableType) {
      this.tableType = new TxTableType(attribute.tableType);
    }
  }
}
