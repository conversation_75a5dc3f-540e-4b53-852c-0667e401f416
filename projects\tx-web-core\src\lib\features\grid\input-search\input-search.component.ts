import {
  Component,
  ElementRef,
  EventEmitter,
  Output,
  OnChanges,
  Input,
  ViewChild,
  OnInit,
  OnDestroy,
  SimpleChanges,
  ChangeDetectionStrategy,
} from '@angular/core';
import { Subject, map, debounceTime, distinctUntilChanged } from 'rxjs';
import { StringUtils } from '@bassetti-group/tx-web-core/src/lib/utilities';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { AdjustWidthDirective } from '../directives/adjustWidth.directive';

export interface InputSearchEventInfo {
  event: KeyboardEvent | InputEvent;
  inputSearch: ElementRef<HTMLInputElement>;
}

@Component({
  standalone: true,
  imports: [
    MatFormFieldModule,
    CommonModule,
    MatInputModule,
    FontAwesomeModule,
    TranslateModule,
    AdjustWidthDirective,
  ],
  selector: 'tx-input-search',
  templateUrl: './input-search.component.html',
  styleUrls: ['./input-search.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InputSearchComponent implements OnChanges, OnDestroy, OnInit {
  /**
   * To specify the width of the input search.
   * This is an input of type string, therefore you can put the value and also the unit of the value.
   * example : '280px' or '100%' etc
   * note: This input is set to 280px by default.
   */
  @Input() fieldWidth = '280px';

  /**
   * To put a focus on input search upon the initialisation of the component.
   * Note: this input is set to false by default.
   */
  @Input() focusInput = false;

  /**
   * To specify a custom placeholder value.
   * Since this will be use along with the translate pipe, it is recommended to enter a valid placeholder value.
   * Example : input.search or admins.users.filterByNameLogin etc
   */
  @Input() placeholderValue = _('txWebCore.input.search');

  /**
   * Specifies the initial value for the input search field.
   */
  @Input() inputValue = '';

  /**
   * Input of type boolean which specifies the role of the input search:
   * - set to true if it is used for filtering.
   * - set to false if it is used for searching.
   * Note: This input is already set to false by default,so only use this if the input search is used for filtering.
   */
  @Input() isFiltered = false;

  /**
   * Emits an event on search input change, triggered by keyup/input events, and the event emitted includes:
   * - 'event': The associated KeyboardEvent.
   * - 'inputSearch': A reference to the HTMLInputElement.
   */
  @Output() searchChanged = new EventEmitter<InputSearchEventInfo>();

  /**
   * Emits an event when a search or filter operation is cleared.
   */
  @Output() searchOrFilterCleared = new EventEmitter<void>();

  /**
   * Emits an event when the filter is changed, triggered by input events,.
   * Emits a string representing the updated filter value.
   */
  @Output() filterChanged = new EventEmitter<string>();

  public keyupStream$ = new Subject<void>();
  private _inputSearch?: ElementRef<HTMLInputElement>;

  get inputSearch() {
    return this._inputSearch;
  }

  @ViewChild('inputSearch') set inputSearch(input: ElementRef<HTMLInputElement> | undefined) {
    if (input) {
      this._inputSearch = input;
    }
    if (this.inputValue !== '') {
      this.inputValueChanged();
    }

    if (this.focusInput) {
      this.inputSearch?.nativeElement.focus();
    }
  }

  ngOnDestroy() {
    this.keyupStream$.next();
    this.keyupStream$.complete();
  }

  ngOnInit(): void {
    if (this.isFiltered) {
      this.keyupStream$
        .pipe(
          map(() =>
            this.inputSearch
              ? StringUtils.cleanTextForSearch(this.inputSearch.nativeElement.value)
              : ''
          ), // get value
          debounceTime(700), // Time in milliseconds between key events
          distinctUntilChanged() // If previous query is different from the current
        )
        .subscribe((text: string) => {
          this.filterChanged.emit(text);
        });
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes.inputValue &&
      changes.inputValue.currentValue !== changes.inputValue.previousValue
    ) {
      this.inputValueChanged();
    }
  }

  public clearInputValue() {
    if (this.inputSearch) {
      this.inputSearch.nativeElement.value = '';
    }
    this.searchOrFilterCleared.emit();
  }

  public onInputSearchChanged(event: InputEvent): void {
    if (this.inputSearch) {
      this.searchChanged.emit({ event, inputSearch: this.inputSearch });
    }
  }

  public onKeyUpSearch(event: KeyboardEvent): void {
    if (this.inputSearch && (event.code === 'Enter' || event.code === 'NumpadEnter')) {
      this.searchChanged.emit({ event, inputSearch: this.inputSearch });
    }
  }

  private inputValueChanged() {
    if (this._inputSearch && this.inputValue !== '') {
      this._inputSearch.nativeElement.value = this.inputValue;
      this.onKeyUpSearch(
        new KeyboardEvent('keyup', {
          code: 'Enter',
        })
      );
    }
  }
}
