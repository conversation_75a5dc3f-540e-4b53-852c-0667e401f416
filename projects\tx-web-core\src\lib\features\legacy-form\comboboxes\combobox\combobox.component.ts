import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  OnChanges,
  SimpleChanges,
  Query,
} from '@angular/core';
import {
  FieldSettingsModel,
  SortOrder,
  MultiSelectChangeEventArgs,
  PopupEventArgs,
  CustomValueEventArgs,
  FilteringEventArgs,
  RemoveEventArgs,
  TaggingEventArgs,
  MultiSelectComponent,
  SelectEventArgs,
} from '@syncfusion/ej2-angular-dropdowns';
import { FloatLabelType } from '@syncfusion/ej2-angular-inputs';

export declare type ComboboxSelectionType = 'Box' | 'Delimiter' | 'Default' | 'CheckBox';

@Component({
  selector: 'tx-combobox',
  templateUrl: './combobox.component.html',
})
export class TxComboboxComponent implements OnInit, OnChanges {
  //#region syncfusion multiselect inputs
  // Allows user to add a custom value, the value which is not present in the suggestion list. Defaults to false.
  @Input() allowCustomValue = false;
  @Input() allowFiltering = true;
  // Based on the property, when item get select popup visibility state will changed. Defaults to true.
  @Input() closePopupOnSelect = true;
  @Input() cssClass!: string;
  // Accepts the list items either through local or remote service and binds it to the component.
  // It can be an array of JSON Objects or an instance of DataManager. Defaults to []
  @Input() dataSource!: object[] | string[] | number[] | boolean[];
  // Sets the delimiter character for ‘default’ and ‘delimiter’ visibility modes. Defaults to ’,’.
  @Input() delimiterChar!: string;
  @Input() enableGroupCheckBox = false;
  // Enable or disable persisting component’s state between page reloads. If enabled, following list of states will be persisted.
  @Input() enablePersistence = false;
  // Enable or disable rendering component in right to left direction.
  @Input() enableRtl = false;
  @Input() enableSelectionOrder = true;
  // Specifies a value that indicates whether the component is enabled or not. Defaults to true.
  @Input() enabled = true;
  /**
   * The fields property maps the columns of the data table and binds the data to the component.
   *   text - Maps the text column from data table for each list item.
   *   value - Maps the value column from data table for each list item.
   *   iconCss - Maps the icon class column from data table for each list item.
   *   groupBy - Group the list items with it’s related items by mapping groupBy field.
   */
  @Input() fields: FieldSettingsModel = { text: 'name', value: 'id', iconCss: 'image' };
  @Input() filterBarPlaceholder!: string;
  /**
   * Specifies whether to display the floating label above the input element. Possible values are:
   *   Never: The label will never float in the input when the placeholder is available.
   *   Always: The floating label will always float above the input.
   *   Auto: The floating label will float above the input after focusing or entering a value in the input.
   * Defaults to Syncfusion.EJ2.Inputs.FloatLabelType.Never.
   */
  @Input() floatLabelType!: FloatLabelType;
  // Accepts the template design and assigns it to the footer container of the popup list.
  @Input() footerTemplate: any;
  // Accepts the template design and assigns it to the group headers present in the popup list.
  @Input() groupTemplate: any;
  // Accepts the template design and assigns it to the header container of the popup list.
  @Input() headerTemplate: any;
  @Input() hideSelectedItem = true;
  // Gets or sets the additional attribute to HtmlAttributes property in MultiSelect, which helps to add attribute like title, name etc, input should be key value pair.
  @Input() htmlAttributes!: object;
  // ignoreAccent set to true, then ignores the diacritic characters or accents when filtering.
  @Input() ignoreAccent = true;
  // Sets case sensitive option for filter operation.
  @Input() ignoreCase = true;
  // Accepts the template design and assigns it to each list item present in the popup.
  @Input() itemTemplate: any;
  // Overrides the global culture and localization value for this component. Default global culture is ‘en-US’. Defaults to ‘en-US’
  @Input() locale!: string;
  // Sets limitation to the value selection. based on the limitation, list selection will be prevented. Defaults to 1000.
  @Input() maximumSelectionLength!: number;
  /**
   * configures visibility mode for component interaction.
   *   Box - selected items will be visualized in chip.
   *   Delimiter - selected items will be visualized in text content.
   *   Default - on focus in component will act in box mode. on blur component will act in delimiter mode.
   *   CheckBox - The ‘checkbox’ will be visualized in list item.
   * Defaults to Default
   */
  @Input() mode!: ComboboxSelectionType;
  // Whether to automatically open the popup when the control is clicked. Defaults to true.
  @Input() openOnClick = true;
  @Input() placeholder!: string;
  // Gets or sets the height of the popup list. By default it renders based on its list item. Defaults to ‘300px’
  @Input() popupHeight!: string | number;
  // Gets or sets the width of the popup list and percentage values has calculated based on input width. Defaults to ‘100%’
  @Input() popupWidth!: string | number;
  // Accepts the external Query which will execute along with the data processing.
  @Input() query!: Query;
  // Gets or sets the readonly to input or not. Once enabled, just you can copy or highlight the text however tab key action will perform. Defaults to false.
  @Input() readonly = false;
  // Specifies the selectAllText to be displayed on the component. Defaults to ‘select All’.
  @Input() selectAllText!: string;
  @Input() showClearButton = true;
  // Allows you to either show or hide the DropDown button on the component
  @Input() showDropDownIcon = true;
  // Allows you to either show or hide the selectAll option on the component
  @Input() showSelectAll = false;
  /**
   * Specifies the sortOrder to sort the data source. The available type of sort orders are
   *   None - The data source is not sorting.
   *   Ascending - The data source is sorting with ascending order.
   *   Descending - The data source is sorting with descending order.
   * Defaults to None
   */
  @Input() sortOrder!: SortOrder;
  // Selects the list item which maps the data text field in the component.
  @Input() text!: string;
  // Specifies the UnSelectAllText to be displayed on the component
  @Input() unSelectAllText!: string;
  // Selects the list item which maps the data value field in the component
  @Input() value!: number[] | string[] | boolean[];
  // Selects the list item which maps the data value field in the component
  @Input() valueTemplate: any;
  // Gets or sets the width of the component. By default, it sizes based on its parent. container dimension. Defaults to ‘100%’
  @Input() width!: string | number;
  // specifies the z-index value of the component popup element. Defaults to 1000.
  @Input() zIndex!: number;
  //#endregion

  //#region syncfusion multiselect outputs
  // Triggers before fetching data from the remote server
  @Output() actionBegin: EventEmitter<object> = new EventEmitter();
  // Triggers after data is fetched successfully from the remote server
  @Output() actionComplete: EventEmitter<object> = new EventEmitter();
  // Triggers when the data fetch request from the remote server fails.
  @Output() actionFailure: EventEmitter<object> = new EventEmitter();
  // Fires when popup opens before animation
  @Output() beforeOpen: EventEmitter<object> = new EventEmitter();
  // Event triggers when the input get focus-out
  @Output() blur: EventEmitter<object> = new EventEmitter();
  // Fires each time when selection changes happened in list items after model and input value get affected.
  @Output() change: EventEmitter<MultiSelectChangeEventArgs> = new EventEmitter();
  // Event triggers when the chip selection
  @Output() chipSelection: EventEmitter<object> = new EventEmitter();
  // Fires when popup close after animation completion
  @Output() close: EventEmitter<PopupEventArgs> = new EventEmitter();
  // Triggers when the component is created
  @Output() created: EventEmitter<object> = new EventEmitter();
  // Triggers when the customValue is selected
  @Output() customValueSelection: EventEmitter<CustomValueEventArgs> = new EventEmitter();
  // Triggers when data source is populated in the popup list
  @Output() dataBound: EventEmitter<object> = new EventEmitter();
  // Triggers when the component is destroyed
  @Output() destroyed: EventEmitter<object> = new EventEmitter();
  // Triggers event,when user types a text in search box
  @Output() filtering: EventEmitter<FilteringEventArgs> = new EventEmitter();
  // Event triggers when the input get focused
  @Output() focus: EventEmitter<object> = new EventEmitter();
  // Fires when popup opens after animation completion
  @Output() open: EventEmitter<PopupEventArgs> = new EventEmitter();
  // Fires after the selected item removed from the widget
  @Output() removed: EventEmitter<RemoveEventArgs> = new EventEmitter();
  // Fires before the selected item removed from the widget
  @Output() removing: EventEmitter<RemoveEventArgs> = new EventEmitter();
  // Triggers when an item in the popup is selected by the user either with mouse/tap or with keyboard navigation
  @Output() select: EventEmitter<SelectEventArgs> = new EventEmitter();
  // Fires after select all process completion
  @Output() selectedAll: EventEmitter<SelectEventArgs> = new EventEmitter();
  // Fires before set the selected item as chip in the component
  @Output() tagging: EventEmitter<TaggingEventArgs> = new EventEmitter();

  //#endregion

  @ViewChild('combobox') combobox!: MultiSelectComponent;

  @Input() multiple = false;

  constructor() {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes.multiple && changes.multiple.previousValue !== undefined) {
      this.multiple = changes.multiple.currentValue;
      this.updateMultipleMode();
    }
  }

  ngOnInit() {
    if (!this.mode) {
      this.updateMultipleMode();
    }
  }

  toggleMultipleMode() {
    this.multiple = !this.multiple;
    this.updateMultipleMode();
  }

  updateMultipleMode() {
    if (this.multiple) {
      this.delimiterChar = ',';
      this.mode = 'CheckBox';
      this.closePopupOnSelect = false;
    } else {
      this.delimiterChar = '';
      this.mode = 'Delimiter';
      this.closePopupOnSelect = true;
    }
  }

  //#region Syncfusion multiselect triggers
  onActionBegin(event: object) {
    this.actionBegin.emit(event);
  }

  onActionComplete(event: object) {
    this.actionComplete.emit(event);
  }

  onActionFailure(event: object) {
    this.actionFailure.emit(event);
  }

  onBeforeOpen(event: object) {
    this.beforeOpen.emit(event);
  }

  onBlur(event: object) {
    this.blur.emit(event);
  }

  onChange(event: MultiSelectChangeEventArgs) {
    // if (!this.multiple) {
    //   this.combobox.focusOut();
    // }

    this.change.emit(event);
  }

  onChipSelection(event: object) {
    this.chipSelection.emit(event);
  }

  onClose(event: PopupEventArgs) {
    this.close.emit(event);
  }

  onCreated(event: object) {
    this.created.emit(event);
  }

  onCustomValueSelection(event: CustomValueEventArgs) {
    this.customValueSelection.emit(event);
  }

  onDataBound(event: object) {
    this.dataBound.emit(event);
  }

  onDestroyed(event: object) {
    this.destroyed.emit(event);
  }

  onFiltering(event: FilteringEventArgs) {
    this.filtering.emit(event);
  }

  onFocus(event: object) {
    this.focus.emit(event);
  }

  onOpen(event: PopupEventArgs) {
    this.open.emit(event);
  }

  onRemoved(event: RemoveEventArgs) {
    this.removed.emit(event);
  }

  onRemoving(event: RemoveEventArgs) {
    this.removing.emit(event);
  }

  onSelect(event: SelectEventArgs) {
    // if (!this.multiple) {
    //   this.combobox.value = [];
    // }
    this.select.emit(event);
  }

  onSelectedAll(event: SelectEventArgs) {
    this.selectedAll.emit(event);
  }

  onTagging(event: TaggingEventArgs) {
    this.tagging.emit(event);
  }

  //#endregion
}
