<div class="panel-header background-grey5">
  <span>
    <div class="panel-title h2-section-title">{{ label }}</div>
  </span>
</div>
<mat-divider></mat-divider>
<div class="panel-content">
  <mat-chip-listbox #chipList aria-label="Chips" lass="chip-list">
    <div *ngFor="let chip of chips" class="data-chip-container">
      <mat-chip
        [matTooltip]="chip.name"
        [matTooltipShowDelay]="500"
        matTooltipPosition="above"
        class="pane-data-chip clickable">
        <div class="chip-text-container">
          <fa-icon
            *ngIf="chip.icon"
            matSuffix
            mat-icon-button
            [icon]="chip.icon"
            class="chip-icon"></fa-icon>
          <span class="chip-text">{{ chip.name }}</span>
        </div>
      </mat-chip>
    </div>
  </mat-chip-listbox>
</div>
