import { formatDate } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateService } from '@ngx-translate/core';
import { AbstractSessionService } from '@bassetti-group/tx-web-core/src/lib/data-access/session';

@Pipe({
  standalone: true,
  name: 'localizedDate',
})
export class LocalizedDatePipe implements PipeTransform {
  constructor(private session: AbstractSessionService, private translate: TranslateService) {}

  transform(
    value: any,
    format: string = 'mediumDate',
    withTime: boolean = false
  ): string | undefined {
    if (value == null) {
      return this.translate.instant(_('txWebCore.errorMessage.noDate'));
    }
    if (typeof value === 'string') {
      value = new Date(value);
    }

    if ((value as Date).getFullYear() < 1900) {
      return this.translate.instant(_('txWebCore.errorMessage.noDate'));
    }

    const formatPreference = withTime
      ? this.session.getDateAndTimeFormatPreference()
      : this.session.getDateFormatPreference();
    if (formatPreference) {
      format = formatPreference;
    }
    if (this.session.currentLang?.languageUsedCode) {
      return formatDate(value, format, this.session.currentLang.languageUsedCode);
    }
  }
}
