import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import {
  ControlValueAccessor,
  FormControl,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Subject, startWith, takeUntil } from 'rxjs';

@Component({
  selector: 'tx-slide-toggle',
  templateUrl: './tx-slide-toggle.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: TxSlideToggleComponent,
      multi: true,
    },
  ],
  imports: [MatSlideToggleModule, ReactiveFormsModule],
  standalone: true,
})
export class TxSlideToggleComponent implements OnDestroy, ControlValueAccessor {
  formControl: FormControl<boolean> | undefined;
  private _onTouched: (() => void) | undefined;
  private _destroying$ = new Subject<void>();

  writeValue(toggle: boolean): void {
    if (!this.formControl) {
      this.formControl = new FormControl(toggle, { nonNullable: true });
    } else {
      this.formControl.setValue(toggle);
    }
  }

  registerOnChange(fn: () => void): void {
    this.formControl?.valueChanges
      .pipe(takeUntil(this._destroying$), startWith(this.formControl.value))
      .subscribe(fn);
  }
  registerOnTouched(fn: () => void): void {
    this._onTouched = fn;
  }
  ngOnDestroy() {
    this._destroying$.next();
  }
}
