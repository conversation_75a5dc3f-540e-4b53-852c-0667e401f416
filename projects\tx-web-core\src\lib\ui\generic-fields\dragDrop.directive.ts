import { Directive, EventEmitter, HostListener, Output } from '@angular/core';

@Directive({
  selector: '[txDragDrop]',
  standalone: true,
})
export class TxDragDropDirective {
  constructor() {}
  @Output() fileDropped = new EventEmitter<DragEvent>();
  @Output() showIndicator = new EventEmitter<DragEvent>();
  @Output() hideIndicator = new EventEmitter<DragEvent>();

  containsFiles(evt: DragEvent) {
    if (evt.dataTransfer?.types) {
      return evt.dataTransfer.types.some((type: string) => type === 'Files');
    }
  }

  // Dragover listener
  @HostListener('dragover', ['$event']) public onDragOver(evt: DragEvent) {
    evt.preventDefault();
    evt.stopPropagation();
    this.showIndicator.emit(evt);
  }

  // Dragleave listener
  @HostListener('dragleave', ['$event']) public onDragLeave(evt: DragEvent) {
    evt.preventDefault();
    evt.stopPropagation();
    this.hideIndicator.emit(evt);
  }

  // Drop listener
  @HostListener('drop', ['$event']) public ondrop(evt: DragEvent) {
    evt.preventDefault();
    evt.stopPropagation();
    this.hideIndicator.emit(evt);
    if (this.containsFiles(evt)) {
      this.fileDropped.emit(evt);
    }
  }
}
