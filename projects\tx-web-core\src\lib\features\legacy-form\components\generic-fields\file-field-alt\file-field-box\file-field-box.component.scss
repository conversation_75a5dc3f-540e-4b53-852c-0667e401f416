.box {
  display: inline-block;
  max-height: 17px;

  .form-field-hint {
    margin-right: 8px;
  }
}

.progress {
  display: inline-block;
}

.progress-bar {
  height: 8px;
  border-radius: 4px;
  width: 80px;
}

.size-and-toggle-container {
  display: inline-block;
  vertical-align: top;
}

.box-buttons {
  display: inline-flex;
  padding-right: 12px;
}

.data-chip-container {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  float: left;
}

.data-chip {
  display: block !important;
  height: auto;
  padding: 0px !important;
  width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 4px !important;
  min-height: 24px;
  margin-left: 0px !important;
  margin-right: 16px !important;
  margin-top: 6px !important;
  font-weight: normal !important;
  span {
    font-size: 12px;
  }
}

.file-name-text-container {
  display: inline-block;
  padding-right: 8px;
  max-width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: top;
}

.file-name-text-container-without-view-toggle {
  max-width: 175px;
}

.chip-content {
  padding: 4px 12px 3px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-flex;
}

.icon-container {
  border-radius: 15px;
  height: 24px;
  width: 24px;
  transition: background-color 0.3s;
  position: absolute;
  top: -1px;
  right: 4px;
}

.chip-icon-left {
  margin-right: 8px;
  vertical-align: middle;
}

.chip-icon-right {
  margin-left: 6px;
  vertical-align: middle;
  position: absolute;
  top: 4px;
}

.form-switch {
  position: absolute;
  top: 5px;
  right: 29px;
  height: auto;
}

.form-switch {
  &.mat-checked {
    ::ng-deep.mat-slide-toggle-bar::after {
      font-family: 'Font Awesome 5 Pro';
      font-weight: 900;
      content: '\f06e';
      font-size: 10px;
      position: absolute;
      top: -5px;
      left: 3px;
    }
  }
  &:not(.mat-checked) {
    ::ng-deep.mat-slide-toggle-bar::after {
      font-family: 'Font Awesome 5 Pro';
      font-weight: 900;
      font-size: 10px;
      content: '\f070';
      position: absolute;
      top: -5px;
      left: 21px;
    }
  }
}
