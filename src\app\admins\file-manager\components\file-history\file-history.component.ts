import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateService } from '@ngx-translate/core';
import { ResourcesService } from 'src/app/admins/file-manager/services/resources.service';
import {
  ToastComponent,
  ToastType,
  ToastService,
  DataBaseRights,
  TxDialogService,
} from '@bassetti-group/tx-web-core';
import { FileHistory, FileTree, FileItemType, ActionType } from '../../models/file-models';
import { FilesUtils } from 'src/app/core/utils/files';
import { FileManagerService } from '../../services/file-manager.service';

@Component({
  selector: 'app-file-history',
  templateUrl: './file-history.component.html',
  styleUrls: ['./file-history.component.scss'],
})
export class FileHistoryComponent implements OnInit {
  @ViewChild('templateSnackBar') templateSnackBar: TemplateRef<any> | undefined;
  @Input() histories: FileHistory[] = [];
  @Input() currentFileHistory: any;
  @Input() isLoaderActive = false;
  @Output() restoreFileVersion = new EventEmitter<FileHistory>();

  public selectedNode: FileTree | undefined;
  actionType = ActionType;
  fileItemType = FileItemType;

  restoringFile: FileHistory | undefined;

  constructor(
    private resourcesService: ResourcesService,
    private translate: TranslateService,
    private toastService: ToastService,
    private fileManagerService: FileManagerService,
    private dialogConfirmService: TxDialogService
  ) {}
  ngOnInit() {
    this.selectedNode = this.fileManagerService.selectedNode;
  }

  getDownloadVersionTooltip(file: FileHistory): string {
    if (file.itemType === FileItemType.directory) {
      return _('admins.resources.folderCannotDownload');
    }
    if (this.selectedNode) {
      if (
        this.selectedNode.resource.right !== DataBaseRights.DbrRead &&
        this.selectedNode.resource.right !== DataBaseRights.DbrStructure &&
        this.selectedNode.resource.right !== DataBaseRights.DbrWrite
      ) {
        return _('admins.resources.fileRestorationNotAllow');
      } else {
        return _('button.download');
      }
    }
    return _('admins.resources.fileRestorationNotAllow');
  }

  getRestoreVersionTooltip(file: FileHistory): string {
    if (this.selectedNode) {
      if (file.itemType === FileItemType.directory) {
        return _('admins.resources.folderCannotRestored');
      }
      return this.selectedNode.resource.right === DataBaseRights.DbrStructure
        ? _('button.restore')
        : _('admins.resources.fileAddAndModifNotAllow');
    }
    return _('button.restore');
  }

  restoreVersionFile(history: FileHistory): void {
    if (this.canRestoreVersion(history)) {
      this.dialogConfirmService
        .open({
          message: this.translate.instant(_('admins.resources.restoreFile'), {
            fileName: history.itemName,
          }),
        })
        .subscribe((okClicked) => {
          if (okClicked) {
            this.restoringFile = history;
            this.confirmRestoration();
          } else {
            this.restoringFile = undefined;
          }
        });
    }
  }

  downloadVersionFile(history: FileHistory): void {
    if (this.canDownloadVersionFile(history)) {
      const toast = this.createNotification(
        'loading',
        _('admins.resources.downloadingFile'),
        true,
        0
      );
      this.resourcesService.onDownloadingFileVersion(history.guid).subscribe(
        (blob) => {
          FilesUtils.downloadBlobFile(blob, history.itemName, 'File');
          this.updateNotification(
            toast,
            'success',
            this.translate.instant(_('admins.resources.downloadComplete'), {
              fileName: history.itemName,
            })
          );
        },
        (error) => {
          this.updateNotification(toast, 'loading', '', null, true, 1);
        }
      );
    }
  }

  canRestoreVersion(file: FileHistory): boolean {
    if (file.itemType === FileItemType.directory) {
      return false;
    }
    if (this.selectedNode) {
      return this.selectedNode.resource.right === DataBaseRights.DbrStructure;
    }
    return false;
  }

  canDownloadVersionFile(file: FileHistory): boolean {
    if (file.itemType === FileItemType.directory) {
      return false;
    }
    if (this.selectedNode) {
      return (
        this.selectedNode.resource.right === DataBaseRights.DbrRead ||
        this.selectedNode.resource.right === DataBaseRights.DbrStructure ||
        this.selectedNode.resource.right === DataBaseRights.DbrWrite
      );
    }
    return false;
  }
  isFolderHistory(type: string): boolean {
    return this.fileManagerService.isFolder(type);
  }

  confirmRestoration() {
    if (this.restoringFile) {
      this.resourcesService.onRestoringFileVersion(this.restoringFile.guid).subscribe({
        next: () => {
          this.createNotification(
            'success',
            this.translate.instant(_('admins.resources.restoredFile'), {
              fileName: this.restoringFile?.itemName,
            }),
            false,
            0
          );
          if (this.restoringFile) {
            this.restoreFileVersion.emit(this.restoringFile);
          }
          this.restoringFile = undefined;
        },
        error: () => {
          this.restoringFile = undefined;
        },
      });
    }
  }

  createNotification(
    state: string,
    message: string,
    isPersistent: boolean,
    duration: number = 0
  ): ToastComponent {
    return this.toastService.show({
      template: this.templateSnackBar,
      templateContext: { test: { state, message, progress: 0 } },
      type: state as ToastType,
      title: _('admins.wording.resources'),
      description: message,
      date: new Date(),
      isUnread: true,
      isPersistent,
      interval: duration,
    });
  }

  updateNotification(
    toast: ToastComponent,
    state: string,
    message: string,
    progress?: number | null,
    close: boolean = true,
    durationTime: number = 8000
  ): void {
    toast.data.templateContext = { test: { state, message, progress } };
    toast.data.description = message;
    toast.data.type = state === 'loading' ? 'information' : (state as ToastType);
    if (close) {
      window.setTimeout(() => {
        toast.animationState = 'closing';
      }, durationTime);
    }
  }
}
