import { Subscription, take } from 'rxjs';
import {
  Component,
  HostListener,
  OnInit,
  TemplateRef,
  ViewChild,
  OnDestroy,
  AfterViewInit,
  Input,
} from '@angular/core';
import {
  TxObjectsTypeService,
  ToastComponent,
  ToastService,
  ToastType,
  TxObjectType,
  CTxAttributeSet,
  TxAttributeCheckChangeEvent,
  TxObjectsTypeDropdownComponent,
  TxDataType,
  TxConcept,
  TxDialogService,
  ArrayUtils,
} from '@bassetti-group/tx-web-core';
import { TranslateService } from '@ngx-translate/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { SessionService } from 'src/app/core/services/session/session.service';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { ActivatedRoute } from '@angular/router';
import { HelpBoxService } from 'src/app/core/services/help-box/help-box.service';
import {
  TxButton,
  ConceptList,
  ConceptsListComponent,
  TxToolBarButton,
} from 'src/app/shared/components/concepts-list/concepts-list.component';
import { TaggedConcepts } from 'src/app/shared/models/tags';
import { ConceptFormPaneComponent } from 'src/app/shared/components/form-pane/concept-form-pane/concept-form-pane.component';
import { AttributeSetsService } from 'src/app/core/services/structure/attribute-sets.service';
import { RightPaneRef } from 'src/app/core/right-pane/right-pane.ref';
import { RightPaneService } from 'src/app/core/right-pane/right-pane.service';
import { AppService } from 'src/app/core/services/app.service';

@Component({
  selector: 'app-business-views',
  templateUrl: './business-views.component.html',
  styleUrls: ['./business-views.component.scss'],
})
export class BusinessViewsComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('templateBusinessViewForm') templateBusinessViewForm: TemplateRef<any> | undefined;
  @ViewChild(ConceptsListComponent) conceptsListComponent:
    | ConceptsListComponent<TxConcept>
    | undefined;
  @ViewChild('dropdownObjectTypeFilter') dropdownObjectTypeFilter:
    | TxObjectsTypeDropdownComponent
    | undefined;

  @Input() isInsideRightPane?: boolean;

  public rightPaneRef?: RightPaneRef;
  public mainIcon = 'sitemap';
  public businessViews: CTxAttributeSet[] = [];
  public businessViewsFiltered: CTxAttributeSet[] = [];
  public businessViewsFilteredSameOT: CTxAttributeSet[] = [];
  public isLoadingBar = false;
  public isExplanationDisplayed?: boolean;
  public isLangLoading = true;
  public formSettings: {
    object?: CTxAttributeSet | null;
    isEditMode: boolean;
  } = { object: null, isEditMode: false };
  public businessViewsButtons: TxButton<CTxAttributeSet>[] = [];
  public businessViewsToolbarButtons: TxToolBarButton<CTxAttributeSet>[] = [];
  public businessViewSelected?: CTxAttributeSet | null;
  public formGroup?: UntypedFormGroup;
  public objectTypeControl = new UntypedFormControl('', [Validators.required]);
  public objectTypes: TxObjectType[] = [];
  public checkedIds: string[] = [];
  public objectTypesFilteredIds: string[] = [];
  public objectTypesDisabledIds: string[] = [];
  public objectTypesFilteredIdsTmp: string[] = [];
  public enableDragAndDrop = true;
  public rootAttributeTypesDisplayed: TxDataType[] = [
    TxDataType.Link,
    TxDataType.LinkAss,
    TxDataType.LinkDirect,
    TxDataType.LinkInv,
  ];
  public reloadAttributeOnSameOT = false;
  public showDeletionMode = false;
  public attributeNoDataCaption = _('admins.businessViews.deletionModeActive');
  public mainSubscription?: Subscription;

  public tagConcept = TaggedConcepts.AttributeSet;

  // auto save parameters
  public intervallTimer = 5000;
  public pushChangeDataIntervall?: number;
  public previousBusinessViews: CTxAttributeSet[] = [];
  public saveInProgress = false;
  public lastSaveDate?: Date;

  constructor(
    private helpboxService: HelpBoxService,
    private sessionService: SessionService,
    private toastService: ToastService,
    private translate: TranslateService,
    private attributeSetService: AttributeSetsService,
    private objectTypeService: TxObjectsTypeService,
    private route: ActivatedRoute,
    private dialogConfirmService: TxDialogService,
    private rightPaneService: RightPaneService,
    private appService: AppService
  ) {}

  @HostListener('window:beforeunload', ['$event'])
  beforeUnloadHandler(): void {
    this.pushChangedData();
  }

  ngOnInit() {
    this.sessionService.getLoadingState().subscribe((stateL) => (this.isLangLoading = !stateL));
    this.isLangLoading = false;
    this.helpboxService.getMultipleStates().subscribe(([hsState, mhsState, exp]) => {
      if (!hsState && !mhsState) {
        this.isExplanationDisplayed = false;
      } else if (exp.id === 'expBusinessViews') {
        this.isExplanationDisplayed = true;
      } else {
        this.isExplanationDisplayed = false;
      }
    });

    this.attributeSetService.isLoading$.subscribe((load) => (this.isLoadingBar = load));

    this.businessViewsButtons = [
      {
        id: 1,
        tooltip: _('button.edit'),
        icon: 'pencil-alt',
        click: this.editBusinessView.bind(this),
      },
      {
        id: 2,
        tooltip: _('button.duplicate'),
        icon: 'clone',
        click: this.duplicateBusinessView.bind(this),
      },
      {
        id: 3,
        displayTooltipFn: this.attributeSetService.getDeleteTooltipLabel.bind(
          this.attributeSetService
        ),
        disable: this.isDeleteButtonDisabled.bind(this),
        icon: 'trash-alt',
        click: this.confirmDeletionBusinessView.bind(this),
      },
    ];

    this.businessViewsToolbarButtons = [
      {
        id: 1,
        tooltip: _('admins.businessViews.multipleDeletion'),
        disable: this.isMulitpleDeleteButtonDisabled.bind(this),
        icon: 'trash-alt',
        click: this.confirmDeletionMultipleBusinessView.bind(this),
      },
    ];

    this.pushChangeDataIntervall = window.setInterval(
      () => this.pushChangedData(),
      this.intervallTimer
    );

    this.mainSubscription = this.attributeSetService
      .listBusinessViews(true)
      .subscribe((businessViews: CTxAttributeSet[]) => {
        this.businessViews = [...businessViews];
        this.reloadDropdownObjectTypeFilter();
        this.updatePreviousBusinessViews();
        this.updateBusinessViewsFiltered();
      });

    this.appService.mainFilterValue$.subscribe((value) => {
      this.onObjectTypesDropdownChange(value, true);
    });
  }

  ngAfterViewInit(): void {
    this.route.queryParams.subscribe((params) => {
      if (params.idConcept) {
        setTimeout(() => {
          const concept = this.findBusinessView(Number(params.idConcept));

          if (concept) {
            if (this.dropdownObjectTypeFilter) {
              this.dropdownObjectTypeFilter.checkOptions([]);
            }
            setTimeout(() => {
              this.conceptsListComponent?.selectItem(concept.id, true);
            }, 200);
          }
        }, 500); // need to wait a bit the loading of data (otherwise filter is not considered)
      }
    });
  }

  ngOnDestroy(): void {
    clearInterval(this.pushChangeDataIntervall);
    if (this.mainSubscription) {
      this.mainSubscription.unsubscribe();
    }
  }

  pushChangedData(): void {
    if (this.showDeletionMode) {
      return;
    }
    const businessViewsToSave: CTxAttributeSet[] = [];
    this.businessViews.forEach((bv) => {
      const previousBusinessView = this.getPreviousBusinessView(bv.id);
      if (previousBusinessView && JSON.stringify(previousBusinessView) !== JSON.stringify(bv)) {
        businessViewsToSave.push(bv);
      }
    });
    if (businessViewsToSave.length) {
      this.modifyBusinessViews(businessViewsToSave);
    }
  }

  modifyBusinessViews(businessViews: CTxAttributeSet[]) {
    this.startSave();
    this.attributeSetService.editAttributeSets(businessViews).subscribe(
      (res) => {
        this.endSave();
        this.updatePreviousBusinessViews();
      },
      (error) => {
        this.saveInProgress = false;
      }
    );
  }

  updateBusinessViewsFiltered(idObjectTypeToAdd: number | null = null) {
    if (this.objectTypesFilteredIds.length) {
      if (idObjectTypeToAdd && !this.objectTypesFilteredIds.includes('' + idObjectTypeToAdd)) {
        this.objectTypesFilteredIds.push('' + idObjectTypeToAdd);
        this.dropdownObjectTypeFilter?.checkOptions(this.objectTypesFilteredIds);
      }

      const businessViewsFiltered = this.businessViews.filter((bv) => {
        if (this.showDeletionMode) {
          return (
            this.objectTypesFilteredIds.includes('' + bv.idObjectType) ||
            (bv as ConceptList).checked
          );
        } else {
          return this.objectTypesFilteredIds.includes('' + bv.idObjectType);
        }
      });

      if (this.showDeletionMode) {
        // sort by checked
        businessViewsFiltered.sort((a: CTxAttributeSet, b: CTxAttributeSet) => {
          if (Number((a as ConceptList).checked) === Number((b as ConceptList).checked)) {
            return 0;
          } else if (Number((a as ConceptList).checked)) {
            return -1;
          } else {
            return 1;
          }
        });
      }
      this.businessViewsFiltered = businessViewsFiltered;
      if (this.businessViewsFiltered.length === 0) {
        this.businessViewSelected = null;
      } else if (
        !this.objectTypesFilteredIds.includes('' + this.businessViewSelected?.idObjectType)
      ) {
        this.selectItem(this.businessViewsFiltered[0].id);
        if (this.businessViewSelected) {
          this.conceptsListComponent?.selectItem(this.businessViewSelected.id, true);
        }
      }
    } else {
      this.businessViewsFiltered = this.businessViews;
      if (this.businessViewSelected) {
        this.conceptsListComponent?.focusItem(this.businessViewSelected.id);
      }
    }
  }

  onObjectTypesDropdownChange(args: string[], isFromMainFilter?: boolean) {
    const modifications = ArrayUtils.getItemsDifferenceBetweenArrays(
      this.objectTypesDisabledIds,
      args
    );
    if (!args?.length) {
      if (isFromMainFilter) {
        this.objectTypesDisabledIds = [];
        this.objectTypesFilteredIds = ArrayUtils.addAndRemoveItems(
          this.objectTypesFilteredIds,
          modifications.addedItems,
          modifications.removedItems
        );
      } else {
        this.objectTypesFilteredIds = modifications.removedItems;
      }
    } else if (isFromMainFilter) {
      this.objectTypesDisabledIds = args;
      this.objectTypesFilteredIds = ArrayUtils.addAndRemoveItems(
        this.objectTypesFilteredIds,
        modifications.addedItems,
        modifications.removedItems
      );
    } else {
      this.objectTypesFilteredIds = args;
    }

    this.enableDragAndDrop =
      this.objectTypesFilteredIds.length === this.objectTypes.length ||
      this.objectTypesFilteredIds.length < 1;
    this.updateBusinessViewsFiltered();
  }

  onObjectTypesDropdownBlur(args: any) {
    if (this.businessViewSelected) {
      if (!this.businessViewsFiltered.some((bvf) => this.businessViewSelected?.id === bvf.id)) {
        this.businessViewSelected = null;
      }
    }
  }

  editBusinessView(businessView: CTxAttributeSet): void {
    this.displayBusinessViewForm(false, businessView);
  }

  duplicateBusinessView(businessView: CTxAttributeSet) {
    const objectTypeName = this.objectTypeService.getName(businessView.idObjectType);
    this.dialogConfirmService
      .open({
        message: this.translate.instant(_('admins.businessViews.duplicateBusinessView'), {
          name: businessView.name,
          objectTypeName,
        }),
        okCaption: _('button.duplicate'),
      })
      .subscribe((duplicateClicked) => {
        if (duplicateClicked) {
          const newBusinessView = this.attributeSetService.duplicateAttributeSet(businessView);
          newBusinessView.name = this.translate.instant(_('admins.businessViews.copyOf'), {
            name: businessView.name,
          });
          this.attributeSetService.addAttributeSet(newBusinessView).subscribe((result) => {
            this.attributeSetService
              .listBusinessViews(false)
              .subscribe((businessViews: CTxAttributeSet[]) => {
                this.businessViews = [...businessViews];

                this.updateBusinessViewsFiltered(businessView.idObjectType);
                this.updatePreviousBusinessViews();
                this.onAfterAddBusinessView(result[0]);
              });
          });
        }
      });
  }

  isDeleteButtonDisabled(businessView: CTxAttributeSet): boolean {
    return businessView.tags.some((tag) => tag.startsWith('tx'));
  }

  isMulitpleDeleteButtonDisabled(businessViews: CTxAttributeSet[]): boolean {
    if (businessViews.length) {
      return businessViews.some((bv) => this.isDeleteButtonDisabled(bv));
    } else {
      return true;
    }
  }

  confirmDeletionBusinessView(businessView: CTxAttributeSet): void {
    if (!this.attributeSetService.hasReservedTag(businessView)) {
      this.dialogConfirmService
        .open({
          message: this.translate.instant(_('admins.businessViews.deleteBusinessView'), {
            name: businessView.name,
          }),
          okCaption: _('button.delete'),
        })
        .subscribe((deleteClicked) => {
          if (deleteClicked) {
            this.deleteBusinessViews([businessView]);
          }
        });
    }
  }

  confirmDeletionMultipleBusinessView(businessViews: CTxAttributeSet[]) {
    if (!this.isMulitpleDeleteButtonDisabled(businessViews)) {
      const message =
        businessViews.length > 1
          ? this.translate.instant(_('admins.businessViews.deleteBusinessViews'), {
              count: businessViews.length,
            })
          : this.translate.instant(_('admins.businessViews.deleteBusinessView'), {
              name: businessViews[0].name,
            });
      this.dialogConfirmService
        .open({ message: message, okCaption: _('button.delete') })
        .subscribe((deleteClicked) => {
          if (deleteClicked) {
            this.deleteBusinessViews(businessViews);
          }
        });
    }
  }

  deleteBusinessViews(businessViews: CTxAttributeSet[]): void {
    this.attributeSetService.removeAttributeSets(businessViews.map((bv) => bv.id)).subscribe(() => {
      this.attributeSetService
        .listBusinessViews(false)
        .subscribe((businessViewsArray: CTxAttributeSet[]) => {
          this.businessViews = [...businessViewsArray];
          this.previousBusinessViews = JSON.parse(JSON.stringify(this.businessViews));

          this.businessViewSelected = null;
          this.reloadDropdownObjectTypeFilter();
          this.updateBusinessViewsFiltered();
          businessViews.forEach((bv) => {
            this.showToast(
              'success',
              this.translate.instant(_('admins.businessViews.businessViewDeletedSuccessfully'), {
                name: bv.name,
              }),
              8000,
              true,
              _('admins.businessViews.businessViewDeleted')
            );
          });

          this.updateBusinessViewsOrder(this.businessViews);

          this.endSave();
        });
    });
  }

  displayBusinessViewForm(add: boolean, businessView?: CTxAttributeSet | null): void {
    this.formSettings.isEditMode = !add;

    if (add) {
      this.formSettings.object = new CTxAttributeSet({});
    } else {
      this.businessViewSelected = businessView;
      this.formSettings.object = businessView ?? null;
    }

    const idObjectType = this.getCurrentIdObjectType();

    if (idObjectType) {
      this.businessViewsFilteredSameOT = this.businessViews.filter(
        (bv) => bv.idObjectType === idObjectType
      );
    } else {
      this.businessViewsFilteredSameOT = [];
    }

    this.rightPaneRef = this.rightPaneService.showNewPaneWithTemplate({
      template: this.templateBusinessViewForm,
    });
    this.rightPaneRef?.afterClosed.pipe(take(1)).subscribe((result) => {
      if (!result.hasError && !result.cancel) {
        if (!result.editMode) {
          this.onAfterAddBusinessView(result.data);
        } else if (result.editMode) {
          this.onAfterEditingBusinessView(result.data);
        }
      }
    });
  }

  selectItem(id: number): void {
    this.reloadAttributeOnSameOT = false;
    const idOT = this.businessViewSelected ? this.businessViewSelected.idObjectType : null;
    this.businessViewSelected = this.findBusinessView(id);
    setTimeout(() => {
      this.reloadAttributeOnSameOT = idOT === this.businessViewSelected?.idObjectType;
    });
  }

  onConceptDropped(args: CdkDragDrop<any[]>) {
    this.updateBusinessViewsOrder(args.previousContainer.data);
  }

  onCheckAttribute(event: TxAttributeCheckChangeEvent) {
    if (this.businessViewSelected) {
      this.businessViewSelected.attributeSetLevels = event.attributeSetLevels;
    }
  }

  getExplanation(globalExpId: string, expId: string, active: boolean): void {
    this.helpboxService.setExplanationsFromId(globalExpId, expId, active);
    this.isExplanationDisplayed = true;
  }

  closeHelpbox() {
    this.helpboxService.closeHelpbox();
  }

  showToast(
    state: string,
    message: string,
    duration: number = 0,
    isPersistent: boolean,
    title?: string
  ): ToastComponent {
    return this.toastService.show({
      templateContext: { test: { state, message, progress: 0 } },
      type: state === 'loading' ? 'information' : (state as ToastType),
      title,
      description: message,
      date: new Date(),
      isPersistent,
      interval: duration,
    });
  }

  onAddingBusinessView(args: any) {
    args.data.idObjectType = parseInt(this.objectTypeControl.value[0]);
    this.objectTypesFilteredIdsTmp = this.objectTypesFilteredIds;

    // add the objectType to the main combo
    if (!this.objectTypes.some((ot) => ot.id === args.data.idObjectType)) {
      const objectType = this.objectTypeService.getByID(args.data.idObjectType);
      if (objectType) {
        if (this.objectTypesFilteredIdsTmp.length) {
          this.objectTypesFilteredIdsTmp.push('' + objectType?.id);
        }
      }
    }

    const newBusinessView = new CTxAttributeSet(args.data);
    newBusinessView.order = this.businessViews.length;
    this.attributeSetService.addAttributeSet(newBusinessView).subscribe(
      (result) => {
        this.attributeSetService
          .listBusinessViews(false)
          .subscribe((businessViews: CTxAttributeSet[]) => {
            this.businessViews = [...businessViews];
            args.observer.next(result[0]);
            args.observer.complete();
            // let the time to the dropDown to reload before update checked ids
            setTimeout(() => {
              this.objectTypesFilteredIds = this.objectTypesFilteredIdsTmp;
              this.reloadDropdownObjectTypeFilter();
              this.updateBusinessViewsFiltered(args.data.idObjectType);
              this.updatePreviousBusinessViews();
            }, 100);
          });
      },
      (error) => {
        args.observer.error(error);
      }
    );
  }

  onEditingBusinessView(args: any) {
    delete args.data.idObjectType;
    if (this.businessViewSelected) {
      Object.assign(this.businessViewSelected, args.data);
      this.attributeSetService.editAttributeSets([this.businessViewSelected]).subscribe(
        (businessViews) => {
          args.observer.next(businessViews[0]);
          args.observer.complete();
        },
        (error) => {
          args.observer.error(error);
        }
      );
    }
  }

  onAfterAddBusinessView(concept: ConceptList) {
    setTimeout(() => {
      this.conceptsListComponent?.selectItem(concept.id, true);
    }, 200);
    this.showToast(
      'success',
      this.translate.instant(_('admins.businessViews.businessViewAddedSuccessfully'), {
        name: concept.name,
      }),
      8000,
      true,
      _('admins.businessViews.businessViewAdded')
    );
    this.endSave();
  }

  onAfterEditingBusinessView(concept: ConceptList) {
    if (concept) {
      this.showToast(
        'success',
        this.translate.instant(_('admins.businessViews.businessViewEditedSuccessfully'), {
          name: concept.name,
        }),
        8000,
        true,
        _('admins.businessViews.businessViewEdited')
      );
      this.endSave();
    }
  }

  onFormPaneReady(args: ConceptFormPaneComponent) {
    this.formGroup = args.form;
    this.formGroup?.addControl('idObjectType', this.objectTypeControl);
    let value = '';
    this.checkedIds = [];
    const idObjectType = this.getCurrentIdObjectType();
    if (idObjectType) {
      value = '' + idObjectType;
      this.checkedIds = [value];
    }

    // let the time to the dropdowntree of syncfusion to display before update checked ids
    setTimeout(() => {
      this.objectTypeControl.setValue(this.checkedIds);
    }, 200);
  }

  onObjectTypesDropdownFormChange(args: any) {
    if (!args?.length) {
      this.objectTypeControl.setErrors({ required: true });
      this.objectTypeControl.markAsDirty();
      this.businessViewsFilteredSameOT = [];
    } else {
      let ids = args;
      this.objectTypeControl.setErrors(null);
      this.objectTypeControl.setValue(ids);

      if (args instanceof String || typeof args === 'string') {
        ids = [args];
      }
      const idObjectType = ids.map((id: any) => parseInt(id))[0];
      this.businessViewsFilteredSameOT = this.businessViews.filter(
        (bv) => bv.idObjectType === idObjectType
      );
    }
  }

  protected updatePreviousBusinessViews() {
    this.previousBusinessViews = JSON.parse(JSON.stringify(this.businessViews));
  }

  protected updateBusinessViewsOrder(businessViews: CTxAttributeSet[]) {
    businessViews.forEach((bv: CTxAttributeSet, i: number) => (bv.order = i));
    this.attributeSetService.editAttributeSets(businessViews).subscribe(() => {
      this.endSave();
    });
  }

  protected findBusinessView(id: number): CTxAttributeSet | undefined {
    return this.businessViews.find((bv) => bv.id === id);
  }

  protected getPreviousBusinessView(id: number): CTxAttributeSet | undefined {
    return this.previousBusinessViews.find((pbv) => pbv.id === id);
  }

  private reloadDropdownObjectTypeFilter() {
    const objectTypeIds = [
      ...new Map(this.businessViews.map((bv) => [bv.idObjectType, bv])).values(),
    ].map((bv) => bv.idObjectType);

    if (objectTypeIds.length) {
      this.objectTypes = this.objectTypeService.findFromIds(objectTypeIds);
      this.dropdownObjectTypeFilter?.reloadOptions(this.objectTypes);
    } else {
      this.dropdownObjectTypeFilter?.reloadOptions([]);
    }
  }

  private getCurrentIdObjectType(): number | undefined {
    let idObjectType: number | undefined;
    if (this.businessViewSelected) {
      idObjectType = this.businessViewSelected.idObjectType;
    } else if (this.objectTypesFilteredIds.length === 1) {
      idObjectType = parseInt(this.objectTypesFilteredIds[0], 10);
    }
    return idObjectType;
  }

  private startSave() {
    this.saveInProgress = true;
  }

  private endSave() {
    this.saveInProgress = false;
    this.lastSaveDate = new Date();
  }
}
