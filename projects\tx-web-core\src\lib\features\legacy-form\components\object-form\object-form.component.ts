import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  QueryList,
  ViewChildren,
  OnChanges,
  SimpleChanges,
  ViewChild,
  TemplateRef,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { LegacyTxData } from '../../services/structure/models/data';
import { LegacyTxAttributesService } from '../../services/structure/services/attributes.service';
import { LegacyTxObjectTypeService } from '../../services/structure/services/object-type.service';
import { LegacyTxObjectsService } from '../../services/structure/services/objects.service';
import { TxRightPaneComponent } from '../../right-pane/right-pane.component';
import { TxEditionMode } from '../../models/formConfiguration/businessClass/form-enum';
import { TxTabAttributeField } from '../../models/formConfiguration/businessClass/tab-attribute-field';
import { TxChip } from '../../models/formFields/setting-model';
import { LegacyTxFormsService } from '../../services/forms.service';
import { TxExtendedChipFieldComponent } from '../object-fields/extended-chip-field/extended-chip-field.component';
import { TxTextInputObjectFieldComponent } from '../object-fields/text-input-object-field/text-input-object-field.component';
import { LegacyTxObjectTabFieldComponent } from '../object-tab-field/object-tab-field.component';

@Component({
  selector: 'tx-object-form',
  templateUrl: './object-form.component.html',
  styleUrls: ['./object-form.component.scss'],
})
export class LegacyTxObjectFormComponent implements OnInit, OnChanges {
  @Input() tabs: TxTabAttributeField[] = [];
  @Input() formConfigTag!: string;
  @Input() attributesTags!: string[];
  @Input() idObject?: number;
  @Input() idObjectType!: number;
  @Input() showMode = 'full';
  @Input() formTitleFormated = '';
  @Input() editionMode = TxEditionMode.write;
  @Input() inRightPane!: boolean;
  @Input() mandatoriesIdsAtt!: number[];
  @Input() showBarAndButton = true;
  @Input() indexTabToFocusFirst = 0;

  @Output() loadEvent = new EventEmitter();
  @Output() displayPaneEvent = new EventEmitter<any>();

  @ViewChildren('tabField')
  public tabFieldComponents!: QueryList<LegacyTxObjectTabFieldComponent>;
  @ViewChild('objectNameField')
  objectNameField!: TxTextInputObjectFieldComponent;
  @ViewChild('rightPane') public rightPane!: TxRightPaneComponent;
  @ViewChild('templateEmpty') public templateEmpty!: TemplateRef<any>;
  @ViewChild('templateExtendLinkField')
  public templateExtendedLinkField!: TemplateRef<any>;
  @ViewChild('extendedChipField')
  public extendedChipField!: TxExtendedChipFieldComponent;

  activeTemplate!: TemplateRef<any>;
  labelExtendedChipPanel!: string;
  chipsExtendedChipPanel!: TxChip[];
  form: FormGroup;
  data: LegacyTxData[] = [];
  objectTypeName = '';
  objectName = 'New';
  firstAnimationDone = false;
  formTitleIcon: string[] | null = ['fas', 'plus'];
  errorNumber: any[] = [];
  readMode: boolean;

  isInitialised = false;
  formDisabled = true;

  constructor(
    public attributeService: LegacyTxAttributesService,
    public formsService: LegacyTxFormsService,
    public objectTypeService: LegacyTxObjectTypeService,
    public objectService: LegacyTxObjectsService
  ) {
    this.form = new FormGroup({});
    this.reloadForm();
    this.readMode = true; // tochange
  }

  private changeIdObject(idObject?: number) {
    this.idObject = idObject;
    if (idObject !== undefined && idObject > 0) {
      this.objectService.getObjects(this.idObject as number).subscribe((res) => {
        if (res[0]) {
          this.objectName = res[0].name;
          if (this.idObjectType !== res[0].idObjectType) {
            this.idObjectType = res[0].idObjectType as number;
            this.changeIdObjectType();
          }
        } else {
          // object not found
          this.changeIdObject(0);
        }
      });
    } else {
      this.objectName = '';
    }
    this.updateTitle();
  }

  private changeIdObjectType() {
    this.objectTypeService.listFromIds([this.idObjectType]).subscribe((res) => {
      if (res[0] && res[0].name) {
        this.objectTypeName = res[0].name;
      }
    });

    this.updateTitle();
  }

  private updateTitle() {
    if (this.readMode) {
      this.formTitleFormated = '#ObjectName#';
      this.formTitleIcon = null;
    } else if (this.idObject !== undefined && this.idObject > 0) {
      this.formTitleFormated = 'Edit #ObjectName#';
      this.formTitleIcon = ['fas', 'edit'];
    } else if (this.idObjectType) {
      this.formTitleFormated = 'Create a new #ObjectTypeName#';
      this.formTitleIcon = ['fas', 'plus'];
    }
  }

  ngOnInit() {
    this.activeTemplate = this.templateEmpty;
    this.readMode = this.editionMode.toString() === TxEditionMode.read.toString();
    if (this.idObject !== undefined && this.idObject > 0) {
      this.changeIdObject(this.idObject);
    }

    if (this.idObjectType) {
      this.changeIdObjectType();
    }

    this.tabs.forEach((tab) => {
      if (tab.attribute) {
        tab.tooltip = this.attributeService.getHint(tab.attribute.id);
      }
    });

    if (this.indexTabToFocusFirst >= this.tabs.length) {
      this.indexTabToFocusFirst = 0;
    }
    this.isInitialised = true;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.idObject && !changes.idObject.firstChange) {
      if (changes.idObject.previousValue !== changes.idObject.currentValue) {
        this.changeIdObject(changes.idObject.currentValue);
        this.reloadForm();
      }
    }

    // if (changes.idObjectType && !changes.idObjectType.firstChange && changes.idObjectType.previousValue !== changes.idObjectType.currentValue) {
    //   this.idObjectType = parseInt(changes.idObjectType.currentValue);
    //   this.changeIdObjectType();
    //   this.reloadForm();
    // }

    if (changes.attributesTags && !changes.attributesTags.firstChange) {
      if (
        changes.attributesTags.previousValue &&
        changes.attributesTags.currentValue &&
        changes.attributesTags.previousValue.toString() !==
          changes.attributesTags.currentValue.toString()
      ) {
        this.form.controls = {};
        this.reloadForm();
      }
    }
  }

  reloadForm() {
    this.data = [];
    this.form.valueChanges.subscribe((selectedValue) => {
      this.onFormChange();
    });
    this.updateNbErrorByTab();
  }

  updateNbErrorByTab() {
    const errorNumber: any[] = [];
    if (this.tabFieldComponents) {
      this.tabFieldComponents.forEach((tabComponent) => {
        errorNumber.push(tabComponent.getNbError());
      });
    }

    // to fix the ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      this.errorNumber = errorNumber;
    });
  }

  get formTitle() {
    if (this.formTitleFormated) {
      return this.formTitleFormated
        .replace('#ObjectName#', this.objectName)
        .replace('#ObjectTypeName#', this.objectTypeName);
    }
  }

  onFormChange() {
    this.updateNbErrorByTab();
    this.checkDisabled();
  }

  checkDisabled() {
    let formDisabled = true;
    if (this.form) {
      if (Object.keys(this.form.controls).length > 0) {
        formDisabled = !this.form.valid;
      } else {
        formDisabled = false;
      }
    } else {
      formDisabled = false;
    }

    // to fix the ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      this.formDisabled = formDisabled;
    });
  }

  initFromConfig() {
    /*if (this.config.attributesTags) {
      this.attributesTags = this.config.attributesTags;
    }*/
  }

  onSubmit() {
    let data: LegacyTxData[] = [];
    this.tabFieldComponents.forEach((tabComponent) => {
      data = data.concat(tabComponent.onSubmit());
    });
    console.log('data submit', data);
    return data;
  }

  onAnimationDone() {
    if (!this.firstAnimationDone) {
      this.onSelectedTabChange(this.indexTabToFocusFirst);
      if (!this.readMode) {
        this.loadMandatoriesFields();
      }
      this.firstAnimationDone = true;
    }
    this.updateNbErrorByTab();
  }

  onSelectedTabChange(indexTabSelected: number) {
    this.tabFieldComponents.forEach((tabComponent, index) => {
      if (index === indexTabSelected) {
        tabComponent.selectedTabChange();
      }
    });
  }

  loadMandatoriesFields() {
    // JREB: TO (re)DO
    // const attributeToLoad = this.mandatoriesIdsAtt;
    // this.tabFieldComponents.forEach((tabComponent) => {
    //   for (const idMandAttr of attributeToLoad) {
    //     if (tabComponent.listAttributesIds.includes(idMandAttr)) {
    //       tabComponent.loadData([idMandAttr]);
    //       const index = attributeToLoad.indexOf(idMandAttr);
    //       attributeToLoad.slice(index, 1);
    //     }}
    // });
  }

  showObjectPanel(event: { name: string; values: TxChip[] }): void {
    this.labelExtendedChipPanel = event.name;
    this.chipsExtendedChipPanel = event.values;
    this.activeTemplate = this.templateExtendedLinkField;
    this.rightPane.displayPane();
  }

  hidePanel(): void {
    this.rightPane.hidePane();
  }

  rightPaneHidden(): void {
    this.activeTemplate = this.templateEmpty;
  }
}
