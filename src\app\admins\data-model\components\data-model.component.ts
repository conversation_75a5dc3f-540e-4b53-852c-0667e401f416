import { Component, ElementRef, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { TxAttribute, TxAttributeSelectionEvent, TxObjectType } from '@bassetti-group/tx-web-core';
import { Subscription } from 'rxjs';
import { HelpBoxService } from 'src/app/core/services/help-box/help-box.service';
import { AppService } from 'src/app/core/services/app.service';
import { RightPaneService } from 'src/app/core/right-pane/right-pane.service';
import { RightPaneRef } from 'src/app/core/right-pane/right-pane.ref';
import { RightPaneWidth } from 'src/app/core/right-pane/right-pane.models';

@Component({
  selector: 'app-data-model',
  templateUrl: './data-model.component.html',
  styleUrls: ['./data-model.component.scss'],
})
export class DataModelComponent implements OnInit {
  @ViewChild('templateDataModel') public templateDataModel?: TemplateRef<any>;

  @Input() isInsideRightPane?: boolean;

  public rightPaneRef?: RightPaneRef;

  public isLoaderActive = true;
  public displayExportFunctionality = false;
  public isExplanationDisplayed?: boolean;
  public objectType?: TxObjectType;
  public attribute?: TxAttribute;
  public objectTypesFilteredIds: string[] = [];

  protected hasDescription = true;

  protected subscription?: Subscription;

  constructor(
    public el: ElementRef,
    private readonly helpboxService: HelpBoxService,
    public appService: AppService,
    private readonly rightPaneService: RightPaneService
  ) {}

  ngOnInit(): void {
    this.isLoaderActive = false;

    this.helpboxService.getMultipleStates().subscribe(([hsState, mhsState, exp]) => {
      if (!hsState && !mhsState) {
        this.isExplanationDisplayed = false;
      } else if (exp.id === 'expDataModel') {
        this.isExplanationDisplayed = true;
      } else {
        this.isExplanationDisplayed = false;
      }
    });

    this.appService.mainFilterValue$.subscribe((value) => {
      this.objectTypesFilteredIds = value;
    });
  }

  public changeObjectType(ot: TxObjectType): void {
    this.objectType = ot;
    this.attribute = undefined;
  }

  public changeAttribute(att: TxAttributeSelectionEvent): void {
    this.attribute = att.data.txObject;
  }

  public getExplanation(globalExpId: string, expId: string, active: boolean): void {
    this.helpboxService.setExplanationsFromId(globalExpId, expId, active);
    this.isExplanationDisplayed = true;
  }

  public openRightPaneExport(): void {
    this.rightPaneRef = this.rightPaneService.showNewPaneWithTemplate({
      template: this.templateDataModel,
      width: RightPaneWidth.Large,
    });
  }

  public closePanel(): void {
    this.rightPaneRef?.close();
  }
}
