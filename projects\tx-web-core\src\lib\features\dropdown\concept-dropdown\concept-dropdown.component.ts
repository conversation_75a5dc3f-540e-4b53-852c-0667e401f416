import {
  Component,
  EventEmitter,
  Input,
  Output,
  OnChanges,
  SimpleChanges,
  AfterContentInit,
  OnInit,
} from '@angular/core';
import { ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { TreeData, TreeDataOptions } from '@bassetti-group/tx-web-core/src/lib/features/trees';
import { TxDropdownTreeComponent } from '../dropdown-tree/dropdown-tree.component';
import { CommonModule } from '@angular/common';
import { TxObjectTypeIconService } from '@bassetti-group/tx-web-core/src/lib/data-access/session';

@Component({
  standalone: true,
  imports: [TxDropdownTreeComponent, CommonModule, ReactiveFormsModule],
  selector: 'tx-concept-dropdown',
  templateUrl: './concept-dropdown.component.html',
  styleUrls: ['./concept-dropdown.component.scss'],
})
export class TxConceptDropdownComponent implements OnInit, OnChanges, AfterContentInit {
  @Input() showCheckBox = true;
  @Input() multipleSelection?: boolean;
  @Input() width?: string | number = '100%';
  @Input() label?: string;
  @Input() allowFiltering = true;
  @Input() readonly = false;
  @Input() readyToLoad? = true;
  @Input() returnIds? = true;
  @Input() checkedIds: string[] = [];
  @Input() disabledIds: string[] = [];
  @Input() formGroup?: UntypedFormGroup;
  @Input() displayIconOption = true;
  @Input() changeOnBlur = false;
  @Input() showClearButton? = true;
  @Input() required = false;
  @Input() disabled = false;
  @Input() displayHint = true;
  @Input() enableRemoveSelection = true;
  @Input() control?: UntypedFormControl;
  @Input() dataSource: TreeData[] = [];
  @Input() dataOptions: TreeDataOptions = {
    idProperty: 'id',
    idParentProperty: 'idObjectTypeParent',
  };
  @Input() message: string | undefined;
  @Input() isSmallDropdown = false;
  @Input() useChipsForLabels = false;

  @Output() changeOption = new EventEmitter();
  @Output() selectOption = new EventEmitter();
  @Output() valueChange = new EventEmitter();
  @Output() blurCombo = new EventEmitter();
  @Output() createdCombo = new EventEmitter();
  @Output() reloadComboOptions = new EventEmitter<any[]>();

  public filterType = 'Contains';
  public firstValue: any;
  public isContentInit = false;
  public displayedLabel = '';

  constructor(private readonly _objectTypeService: TxObjectTypeIconService) {}

  ngOnInit() {
    this.updateLabel();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.dataSource?.currentValue) {
      this.reloadDataSource(changes.dataSource.currentValue);
      this.updateLabel();
    }
    if (changes?.checkedIds?.currentValue) {
      this.checkOptions(changes.checkedIds.currentValue);
    }
    if (changes?.disabled?.currentValue !== undefined) {
      if (changes.disabled.currentValue) {
        this.control?.disable();
      } else {
        this.control?.enable();
      }
    }
  }

  ngAfterContentInit(): void {
    if (!this.control) {
      this.control = new UntypedFormControl({
        value: '',
        disabled: this.disabled,
      });
    }

    if (!this.formGroup) {
      this.formGroup = new UntypedFormGroup({
        objectType: this.control,
      });
    }
    this.markAsInit();
  }

  checkOptions(ids: string[]) {
    this.checkedIds = [...ids];
  }

  reloadDataSource(data: any[]) {
    data = data.map((item) => {
      return {
        ...item,
        iconPath: item.icon ? this.getIconPath(item.icon) : undefined,
      };
    });
    this.dataSource = data;
  }

  onValueChange(args: string[] | TreeData[]) {
    if (this.firstValue === '' && !args.length) {
      this.firstValue = null;
      return;
    }

    if (this.returnIds) {
      this.checkedIds = [...(args as string[])];
    } else {
      this.checkedIds = args.map(
        (node) => '' + (node as TreeData)[this.dataOptions.idProperty ?? 'id']
      );
    }
    this.valueChange.emit(args);
  }

  getIconPath(iconID: string | number): string {
    return this._objectTypeService.getIconPath(iconID);
  }

  protected markAsInit() {
    this.isContentInit = true;
  }

  protected updateLabel() {
    this.displayedLabel = this.label ?? '';
  }
}
