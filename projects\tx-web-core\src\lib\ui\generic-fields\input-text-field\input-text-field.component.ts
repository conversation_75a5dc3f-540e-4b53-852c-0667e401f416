import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import {
  ControlValueAccessor,
  FormControl,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validator,
  Validators,
} from '@angular/forms';
import { Subject, startWith, takeUntil } from 'rxjs';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { concatenateErrors } from '../validators.utils';

@Component({
  selector: 'tx-input-text-field',
  standalone: true,
  imports: [
    FontAwesomeModule,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatTooltipModule,
    ReactiveFormsModule,
    TranslateModule,
  ],
  templateUrl: './input-text-field.component.html',
  styleUrls: ['./input-text-field.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: TxInputTextFieldComponent,
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: TxInputTextFieldComponent,
      multi: true,
    },
  ],
})
export class TxInputTextFieldComponent implements ControlValueAccessor, Validator, OnDestroy {
  @Input() isTextArea = false;
  @Input() icon: string | undefined;
  @Input() hintLabel = '';
  @Input() required = false;
  @Input() maxLength: number | undefined;
  @Input() label: string = '';
  @Input() labelTooltip: string = '';

  @Output() keydown = new EventEmitter<KeyboardEvent>();

  mouseInFormField = false;
  appearance = 'fill';
  disabled: boolean = false;
  control: FormControl<string | null> | undefined;
  _destroying$ = new Subject<void>();

  private _onTouched: () => void = () => {};
  onValidationChange: () => void = () => {};
  constructor(private cd: ChangeDetectorRef) {}

  writeValue(value: string): void {
    if (!this.control) {
      this.control = new FormControl(value);
    } else {
      this.control.setValue(value);
    }
  }

  registerOnChange(onChange: any): void {
    this.control?.valueChanges
      .pipe(takeUntil(this._destroying$), startWith(this.control.value))
      .subscribe(onChange);
  }

  registerOnTouched(fn: () => void): void {
    this._onTouched = fn;
  }
  setDisabledState?(isDisabled: boolean): void {
    isDisabled ? this.control?.disable() : this.control?.enable();
    this.disabled = isDisabled;
  }

  onMouseEnter() {
    this.mouseInFormField = true;
  }

  onMouseLeave() {
    this.mouseInFormField = false;
  }

  onFocus(): void {}

  onBlur(): void {
    this._onTouched();
    this.onValidationChange();
  }

  validate(control: FormControl): ValidationErrors | null {
    const requiredError = this.required ? Validators.required(control) : null;
    const maxLengthError =
      this.maxLength !== undefined ? Validators.maxLength(this.maxLength)(control) : null;
    const textFieldError =
      !requiredError && !maxLengthError ? null : concatenateErrors(requiredError, maxLengthError);
    this.control?.setErrors(textFieldError);
    this.cd.detectChanges();
    return textFieldError;
  }

  onKeyDown(event: KeyboardEvent) {
    this.keydown.emit(event);
  }

  onClearButtonClicked() {
    this.control?.setValue('');
  }
  registerOnValidatorChange?(fn: () => void): void {
    this.onValidationChange = fn;
  }
  ngOnDestroy(): void {
    this._destroying$.next();
  }
}
