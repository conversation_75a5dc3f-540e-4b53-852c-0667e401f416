import { Component } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { TxColumnNameTemplateComponent } from './column-name-template.component';

import { MatTooltipModule } from '@angular/material/tooltip';
import { By } from '@angular/platform-browser';
import { FormatConceptNameTooltipPipe, TxTreeGrid } from '@bassetti-group/tx-web-core';
import { MockPipe } from 'ng-mocks';
import { TxEscapeHtmlPipe, TxHighlightSearchPipe } from '../../../utilities';
@Component({
  selector: 'app-host-component',
  template: `<tx-column-name-template
    [data]="data"
    [hasNoIcon]="hasNoIcon"
    [searchId]="searchId"
    [dataLength]="dataLength"
    [hasMainParentObject]="hasMainParentObject"
    [inputSearchValue]="inputSearchValue"></tx-column-name-template>`,
})
class TestHostComponent {
  data: TxTreeGrid<any> = {
    id: 1,
    name: 'Contact',
    icon: '/srcOfImage',
    txObject: { explanation: 'This is explanation' },
  }; //data with TxObject
  hasNoIcon = false;
  searchId = 1; // matching searchId with data
  dataLength = 0;
  hasMainParentObject = false;
  inputSearchValue = '';
}

describe('ColumnNameTemplateComponent', () => {
  let component: TxColumnNameTemplateComponent;
  let hostComponent: TestHostComponent;
  let hostFixture: ComponentFixture<TestHostComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        TestHostComponent,
        MockPipe(TxEscapeHtmlPipe),
        MockPipe(TxHighlightSearchPipe),
        MockPipe(FormatConceptNameTooltipPipe),
      ],
      imports: [
        TxColumnNameTemplateComponent,
        NoopAnimationsModule,
        MatTooltipModule,
        FontAwesomeTestingModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    hostFixture = TestBed.createComponent(TestHostComponent);
    hostComponent = hostFixture.componentInstance;
    hostFixture.detectChanges();
    component = hostFixture.debugElement.children[0].componentInstance;
  });

  it('should create', () => {
    expect(hostComponent).toBeTruthy();
    expect(component).toBeTruthy();
  });

  describe('Usage of template parent', () => {
    it('should use template parent if both conditions are respected', () => {
      hostComponent.hasMainParentObject = true;
      hostFixture.detectChanges();
      expect(hostFixture.debugElement.query(By.css('.tree-node-parent'))).toBeTruthy();
    });

    it('should not use template parent if hasMainParentObject is set to false', () => {
      expect(hostFixture.debugElement.query(By.css('.tree-node-parent'))).toBeFalsy();
    });

    it('should not use template parent if idParent is defined', () => {
      hostComponent.data = { id: 1, idParent: -1, name: 'try', txObject: {} };
      hostComponent.hasMainParentObject = true;
      hostFixture.detectChanges();
      expect(hostFixture.debugElement.query(By.css('.tree-node-parent'))).toBeFalsy();
    });

    it('should display the correct data length', () => {
      hostComponent.hasMainParentObject = true;
      hostFixture.detectChanges();
      expect(
        hostFixture.debugElement.query(By.css('.otgrid-accordion-counter')).nativeElement
          .textContent
      ).toBe('0');
    });
  });

  describe('Search by id ', () => {
    it('should display data name in a mark tag when searchId matches data id', () => {
      const markElement = hostFixture.debugElement.nativeElement.querySelector('mark');

      expect(markElement).toBeTruthy();
      expect(markElement.textContent).toContain('Contact');
    });

    it('should display data name without the mark tag when searchId does not match data id', () => {
      hostComponent.searchId = 2;
      hostFixture.detectChanges();
      const markElement = hostFixture.debugElement.nativeElement.querySelector('mark');

      expect(markElement).toBeFalsy();
    });
  });

  describe('Icon display', () => {
    it('should display icon because hasNoIcon is set to false', () => {
      expect(hostFixture.debugElement.query(By.css('.otgrid-tree-icon'))).toBeTruthy();
    });

    it('should display the icon of the data', () => {
      expect(
        hostFixture.debugElement
          .query(By.css('.otgrid-tree-icon'))
          .nativeElement.getAttribute('src')
      ).toBe('/srcOfImage');
    });

    it('should not display any icon', () => {
      hostComponent.hasNoIcon = true;
      hostFixture.detectChanges();
      expect(hostFixture.debugElement.query(By.css('.otgrid-tree-icon'))).toBeFalsy();
    });
  });
});
