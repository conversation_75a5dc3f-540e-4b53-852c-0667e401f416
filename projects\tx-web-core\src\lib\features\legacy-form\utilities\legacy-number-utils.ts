export class LegacyNumberUtilsHelper {
  /**
   * Return a unique id
   */
  getUniqueId(): number {
    return parseInt(new Date().valueOf() + (Math.random() + '').split('.')[1], 10);
  }

  /**
   * @param n a number value
   * @return boolean : if param is null or undefined
   */
  isEmpty(n: number): boolean {
    return n === null || typeof n === undefined;
  }

  /**
   * @param n a number value
   * @param defaultVal the number to return if n is empty
   * @return n or defaultVal
   */
  getValue(n: number, defaultVal: number): number {
    return this.isEmpty(n) ? defaultVal : n;
  }
}

export const _LegacyNumberUtils = new LegacyNumberUtilsHelper();
