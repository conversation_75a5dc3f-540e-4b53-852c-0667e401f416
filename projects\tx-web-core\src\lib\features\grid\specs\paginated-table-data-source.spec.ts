import { PaginatedTableDataSource, TxGridComponent } from '../public-api';
import { BehaviorSubject, of } from 'rxjs';
import { MatSort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';
import { ElementRef } from '@angular/core';
import { fakeAsync, tick } from '@angular/core/testing';

describe('PaginatedTableDataSource', () => {
  let dataSource: PaginatedTableDataSource<any>;
  let mockMatSort: MatSort | null;
  let mockMatPaginator: MatPaginator | null;
  let mockTxGridService: any;

  const testData = [
    { id: 1, name: '<PERSON>', age: 30, city: 'New York' },
    { id: 2, name: '<PERSON>', age: 30, city: 'Los Angeles' },
    { id: 3, name: '<PERSON>', age: 30, city: 'Chicago' },
    { id: 4, name: '<PERSON>', age: 40, city: 'Houston' },
    { id: 5, name: '<PERSON>', age: 28, city: 'Phoenix' },
    { id: 6, name: '<PERSON>', age: 50, city: 'Philadelphia' },
    { id: 7, name: '<PERSON>', age: 22, city: 'San Antonio' },
    { id: 8, name: '<PERSON>', age: 33, city: 'New York' },
    { id: 9, name: 'Ivy', age: 27, city: 'Dallas' },
    { id: 10, name: 'Jack', age: 30, city: 'San Jose' },
  ];

  const setupDataSource = (data = testData) => {
    dataSource = new PaginatedTableDataSource(of(data), 'id');
    dataSource.addData(of(data));
    return dataSource;
  };

  const getFilteredData = (ds: PaginatedTableDataSource<any>): Promise<any[]> => {
    return new Promise((resolve) => {
      ds.connect().subscribe((data) => {
        resolve(data);
      });
    });
  };

  beforeEach(() => {
    mockMatSort = null;
    mockMatPaginator = {
      pageSize: 10,
      pageIndex: 0,
      page: new BehaviorSubject(null),
      initialized: new BehaviorSubject(null),
      firstPage: jest.fn(),
    } as any;

    dataSource = setupDataSource();
    dataSource.matSort = mockMatSort;
    dataSource.matPaginator = mockMatPaginator;
    mockTxGridService = {
      resetSearch: jest.fn(),
      search: jest.fn(),
    };
    dataSource.gridService = mockTxGridService;
  });

  it('should be created', () => {
    expect(dataSource).toBeTruthy();
  });

  it('should set pageSize', () => {
    dataSource.pageSize = 20;
    expect(mockMatPaginator?.pageSize).toBe(20);
  });

  it('should set data from Observable', async () => {
    const result = dataSource.dataList;
    expect(result).toEqual(testData);
  });

  it('should connect to data', fakeAsync(() => {
    tick(200);
    dataSource.connect().subscribe({
      next: (val) => {
        expect(val).toEqual(testData);
      },
    });
  }));

  it('should disconnect and unsubscribe', () => {
    const spy = jest.spyOn(dataSource['pageSubscription'], 'unsubscribe');
    dataSource.disconnect();
    expect(spy).toHaveBeenCalled();
  });

  describe('Search functionality', () => {
    const searchTestCases = [
      { desc: 'with correct parameters', inputSearch: true, grid: true, isRowSelected: false },
      { desc: 'with isRowSelected true', inputSearch: true, grid: true, isRowSelected: true },
      { desc: 'with undefined grid', inputSearch: true, grid: false, isRowSelected: false },
      { desc: 'with undefined inputSearch', inputSearch: false, grid: true, isRowSelected: false },
    ];

    searchTestCases.forEach(({ desc, inputSearch, grid, isRowSelected }) => {
      it(`should call gridService.search ${desc}`, () => {
        const mockInputSearch = inputSearch
          ? new ElementRef(document.createElement('input'))
          : undefined;
        const mockGrid = grid ? ({} as TxGridComponent<any>) : undefined;
        const mockContentElement = new ElementRef(document.createElement('div'));
        const mockEvent = new KeyboardEvent('keydown');

        dataSource.search(mockInputSearch, mockGrid, mockContentElement, mockEvent, isRowSelected);

        expect(mockTxGridService.search).toHaveBeenCalledWith(
          mockInputSearch,
          mockGrid,
          mockContentElement,
          mockEvent,
          isRowSelected
        );
      });
    });

    it('should handle InputEvent', () => {
      const mockInputSearch = new ElementRef(document.createElement('input'));
      const mockGrid = {} as TxGridComponent<any>;
      const mockContentElement = new ElementRef(document.createElement('div'));
      const mockEvent = new InputEvent('input');

      dataSource.search(mockInputSearch, mockGrid, mockContentElement, mockEvent);

      expect(mockTxGridService.search).toHaveBeenCalledWith(
        mockInputSearch,
        mockGrid,
        mockContentElement,
        mockEvent,
        false
      );
    });

    it('should reset search', () => {
      const searchId = dataSource.resetSearch();
      expect(searchId).not.toBeDefined();
      expect(mockTxGridService.resetSearch).toHaveBeenCalled();
    });
  });

  describe('Filter functionality', () => {
    it('should filter data by age correctly', fakeAsync(async () => {
      dataSource.filterBy({
        column: { field: 'age', headerText: 'Age', type: 'number' },
        operator: 'equal',
        value: 30,
      });

      const filteredData = await getFilteredData(dataSource);
      expect(filteredData.length).toBe(4);
      expect(filteredData[0].id).toBe(1);
    }));

    it('should apply multiple filters and then remove one filter', fakeAsync(async () => {
      dataSource.filterBy({
        column: { field: 'age', headerText: 'Age', type: 'number' },
        operator: 'equal',
        value: 30,
      });
      dataSource.filterBy({
        column: { field: 'name', headerText: 'Name', type: 'string' },
        operator: 'equal',
        value: 'Alice',
      });
      dataSource.removeFilteredColsByField('age');

      const filteredData = await getFilteredData(dataSource);
      expect(filteredData.length).toBe(3);
    }));
  });

  describe('Sort functionality', () => {
    const getResultIds = (items: any[]) => items.map((item) => item.id);
    const sortTestCases: { field: string; direction: 'asc' | 'desc'; expected: number[] }[] = [
      { field: 'age', direction: 'asc', expected: [7, 9, 5, 1, 2, 3, 10, 8, 4, 6] },
      { field: 'age', direction: 'desc', expected: [6, 4, 8, 1, 2, 3, 10, 5, 9, 7] },
      { field: 'name', direction: 'asc', expected: [8, 1, 3, 2, 4, 5, 6, 7, 9, 10] },
      { field: 'name', direction: 'desc', expected: [10, 9, 7, 6, 5, 4, 2, 8, 1, 3] },
    ];

    sortTestCases.forEach(({ field, direction, expected }) => {
      it(`should apply sort on ${field} field in ${direction} order`, fakeAsync(async () => {
        dataSource.sortBy({ active: field, direction });
        const sortedData = await getFilteredData(dataSource);
        expect(getResultIds(sortedData)).toEqual(expected);
      }));
    });
  });

  describe('Pagination functionality', () => {
    it('should handle page changes correctly', fakeAsync(() => {
      if (mockMatPaginator) {
        const testData = Array.from({ length: 100 }, (_, i) => ({ id: i + 1 }));

        mockMatPaginator.pageIndex = 1;
        mockMatPaginator.pageSize = 10;
        mockMatPaginator.length = 100;

        const dataSource = new PaginatedTableDataSource(
          of(testData),
          'id',
          undefined,
          undefined,
          10
        );
        dataSource.matPaginator = mockMatPaginator;
        dataSource.fetch({ pageIndex: 1, pageSize: 10, length: 100 });

        dataSource.connect().subscribe({
          next: (data) => {
            expect(data.length).toBe(10);
            expect(data[0].id).toBe(11);
          },
        });
      }
    }));
  });
});
