<!-- add config form template -->
<ng-template #addConfig>
  <form class="form-container-config" [formGroup]="addConfigForm">
    <div class="h2-section-subtitle">{{ 'generic.settings' | translate }}</div>
    <!-- Name -->
    <mat-form-field color="accent" class="form-field-config">
      <mat-label color="accent">{{ 'admins.columns.name' | translate }}</mat-label>
      <input
        #nameInputConfigForm
        formControlName="name"
        matInput
        floatLabelType="Auto"
        autocomplete="off"
        (ngModelChange)="updateConfigTagValue()"
        required />
      <mat-error *ngIf="addConfigForm.get('name')?.errors?.required" class="e-error">
        {{ 'input.fieldRequired' | translate }}
      </mat-error>
      <mat-error *ngIf="addConfigForm.get('name')?.hasError('nameAlreadyUsed')" class="e-error">
        {{ 'components.configFramework.nameAlreadyExist' | translate }}
      </mat-error>
    </mat-form-field>
    <!-- Tag -->
    <mat-form-field color="accent" class="form-field-config">
      <mat-label color="accent">{{ 'admins.columns.tag' | translate }}</mat-label>
      <input formControlName="tag" matInput floatLabelType="Auto" autocomplete="off" required />
      <mat-error *ngIf="addConfigForm.get('tag')?.errors?.required" class="e-error">
        {{ 'input.fieldRequired' | translate }}
      </mat-error>
      <mat-error *ngIf="addConfigForm.get('tag')?.hasError('tagAlreadyUsed')" class="e-error">
        {{ 'components.tagsEditor.tagExists' | translate }}
      </mat-error>
    </mat-form-field>
    <!-- Object Type -->
    <tx-objects-type-dropdown
      #dropDownConfigForm
      floatLabelType="Auto"
      [objectTypes]="objectTypes"
      [label]="'concepts.objectType' | translate"
      [filtered]="true"
      [onlyVisible]="false"
      [required]="true"
      [showClearButton]="false"
      [multipleSelection]="false"
      [disabled]="configFormOTDropDownDisabled"
      [checkedIds]="['' + addConfigForm.get('objectTypeId')?.value]"
      (valueChange)="onConfigFormOTChange($event)">
    </tx-objects-type-dropdown>
  </form>
</ng-template>

<!-- Add naming Setting form template -->
<ng-template #addSubconcept1>
  <form class="form-container" [formGroup]="addNamingSubConceptForm">
    <!-- Name -->
    <mat-form-field color="accent" class="form-field-type">
      <mat-label color="accent">{{ 'admins.columns.type' | translate }}</mat-label>
      <mat-select formControlName="type" (selectionChange)="updateSubConceptValidators()">
        <mat-option [value]="settingsType.data">{{
          'admins.automaticNaming.teexmaData' | translate
        }}</mat-option>
        <mat-option [value]="settingsType.text">{{
          'admins.automaticNaming.constantText' | translate
        }}</mat-option>
        <mat-option [value]="settingsType.username">{{
          'admins.automaticNaming.activeUserName' | translate
        }}</mat-option>
        <mat-option [value]="settingsType.date">{{
          'admins.automaticNaming.currentDateAndTime' | translate
        }}</mat-option>
        <mat-option [value]="settingsType.index">{{
          'admins.automaticNaming.index' | translate
        }}</mat-option>
      </mat-select>
    </mat-form-field>
    <div [ngSwitch]="adminSettingFormType">
      <ng-container
        *ngSwitchCase="settingsType.data"
        [ngTemplateOutlet]="teexmaDataForm"></ng-container>
      <ng-container
        *ngSwitchCase="settingsType.text"
        [ngTemplateOutlet]="constantTextForm"></ng-container>
      <ng-container
        *ngSwitchCase="settingsType.username"
        [ngTemplateOutlet]="activeUser"></ng-container>
      <ng-container
        *ngSwitchCase="settingsType.date"
        [ngTemplateOutlet]="currentDateForm"></ng-container>
      <ng-container
        *ngSwitchCase="settingsType.index"
        [ngTemplateOutlet]="indexForm"></ng-container>
    </div>

    <ng-template #teexmaDataForm>
      <div class="form-label required-label" [ngClass]="{ error: dataAttribute?.invalid }">
        {{ 'concepts.attribute' | translate }}
      </div>
      <div class="tree-grid-container">
        <tx-attributes-tree-grid
          #dataAttributeTreeGrid
          [idObjectType]="configurationSelected?.idOT"
          [enableCheckbox]="true"
          [multipleSelection]="false"
          [disableLinkLoading]="false"
          (checkChange)="onCheckDataAttribute($event)"
          [attributeSetLevels]="dataAttribute?.value ? [dataAttribute?.value] : []">
        </tx-attributes-tree-grid>
      </div>
      <div class="h2-section-subtitle" style="padding-top: 16px; padding-bottom: 8px">
        {{ 'admins.automaticNaming.settingSpecific' | translate }}
      </div>
      <div class="data-type-settings-form border-grey" [ngSwitch]="attributeTypeSelected">
        <ng-container *ngSwitchCase="null" [ngTemplateOutlet]="selectAttribute"></ng-container>
        <ng-container
          *ngSwitchCase="
            attributeTypeSelected === dataType.SingleValue ||
            attributeTypeSelected === dataType.LongText ||
            attributeTypeSelected === dataType.ShortText
              ? attributeTypeSelected
              : ''
          "
          [ngTemplateOutlet]="textNumType"></ng-container>
        <ng-container
          *ngSwitchCase="
            attributeTypeSelected === dataType.Link ||
            attributeTypeSelected === dataType.LinkInv ||
            attributeTypeSelected === dataType.LinkAss ||
            attributeTypeSelected === dataType.LinkBi ||
            attributeTypeSelected === dataType.LinkDirect
              ? attributeTypeSelected
              : ''
          "
          [ngTemplateOutlet]="linkType"></ng-container>
        <ng-container
          *ngSwitchCase="
            attributeTypeSelected === dataType.Date ||
            attributeTypeSelected === dataType.DateAndTime
              ? attributeTypeSelected
              : ''
          "
          [ngTemplateOutlet]="dateType"></ng-container>
        <ng-container *ngSwitchCase="dataType.File" [ngTemplateOutlet]="fileType"></ng-container>
        <ng-container *ngSwitchDefault [ngTemplateOutlet]="noSettings"></ng-container>
      </div>

      <ng-template #selectAttribute>
        <tx-no-record
          style="zoom: 80%"
          [noRecordText]="
            'admins.automaticNaming.noSettingsSelectAttribute' | translate
          "></tx-no-record>
      </ng-template>

      <ng-template #noSettings>
        <tx-no-record
          style="zoom: 80%"
          [noRecordText]="'admins.automaticNaming.noSpecificSettings' | translate"></tx-no-record>
      </ng-template>

      <ng-template #textNumType>
        <mat-slide-toggle formControlName="dataTypeFormatValueAsIndex" class="data-type-field">{{
          'admins.automaticNaming.formatValueAsIndex' | translate
        }}</mat-slide-toggle>
        <div *ngIf="addNamingSubConceptForm.get('dataTypeFormatValueAsIndex')?.value">
          <div class="form-button-group-field">
            <div>
              <mat-label class="form-label required-label">{{
                'admins.columns.type' | translate
              }}</mat-label>
            </div>
            <mat-button-toggle-group formControlName="dataTypeIndexType" class="form-button-group">
              <mat-button-toggle [value]="indexType.alphaLower">{{
                'admins.automaticNaming.alphaLowerCase' | translate
              }}</mat-button-toggle>
              <mat-button-toggle [value]="indexType.alphaUpper">{{
                'admins.automaticNaming.alphaUpperCase' | translate
              }}</mat-button-toggle>
              <mat-button-toggle [value]="indexType.num">{{
                'admins.automaticNaming.numerical' | translate
              }}</mat-button-toggle>
            </mat-button-toggle-group>
          </div>
          <div
            *ngIf="addNamingSubConceptForm.get('dataTypeIndexType')?.value === indexType.num"
            class="data-type-field">
            <mat-form-field color="accent" class="form-field small-field">
              <mat-label color="accent">{{
                'admins.automaticNaming.numberDigits' | translate
              }}</mat-label>
              <input
                type="number"
                formControlName="dataTypeNumberDigits"
                min="0"
                matInput
                floatLabelType="Auto"
                autocomplete="off" />
              <mat-error
                class=""
                *ngIf="addNamingSubConceptForm.controls?.dataTypeNumberDigits?.hasError('min')"
                >{{ 'input.mustBeGreaterThan' | translate : { min: 0 } }}</mat-error
              >
            </mat-form-field>
          </div>
        </div>
      </ng-template>

      <ng-template #linkType>
        <mat-checkbox class="data-type-field" formControlName="dataTypeTranslationNeeded">{{
          'admins.automaticNaming.translateIfNeeded' | translate
        }}</mat-checkbox>
      </ng-template>

      <ng-template #dateType>
        <div style="margin: 16px 0 0 16px">
          <mat-form-field color="accent" class="form-field">
            <mat-label color="accent">{{
              'admins.automaticNaming.dateTimeFormat' | translate
            }}</mat-label>
            <input formControlName="dateFormat" matInput floatLabelType="Auto" autocomplete="off" />
            <mat-error
              *ngIf="addNamingSubConceptForm.get('dateFormat')?.errors?.required"
              class="e-error">
              {{ 'input.fieldRequired' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </ng-template>

      <ng-template #fileType>
        <mat-checkbox class="data-type-field" formControlName="dataTypeRemoveExtention">{{
          'admins.automaticNaming.removeExtension' | translate
        }}</mat-checkbox>
      </ng-template>
      <div class="h2-section-subtitle" style="padding-top: 16px; padding-bottom: 8px">
        {{ 'admins.automaticNaming.generalSettings' | translate }}
      </div>
      <div class="form-field">
        <div>
          <mat-label class="form-label required-label">{{
            'admins.automaticNaming.case' | translate
          }}</mat-label>
        </div>
        <mat-button-toggle-group formControlName="dataCase" class="form-button-group">
          <mat-button-toggle [value]="caseValue.asIs">{{
            'admins.automaticNaming.asIs' | translate
          }}</mat-button-toggle>
          <mat-button-toggle [value]="caseValue.lowercase">{{
            'admins.automaticNaming.lowerCase' | translate
          }}</mat-button-toggle>
          <mat-button-toggle [value]="caseValue.uppercase">{{
            'admins.automaticNaming.upperCase' | translate
          }}</mat-button-toggle>
        </mat-button-toggle-group>
      </div>
      <div class="data-general-settings">
        <mat-form-field color="accent">
          <mat-label color="accent">{{ 'admins.automaticNaming.prefix' | translate }}</mat-label>
          <input formControlName="dataPrefix" matInput floatLabelType="Auto" autocomplete="off" />
        </mat-form-field>
        <mat-form-field color="accent">
          <mat-label color="accent">{{ 'admins.automaticNaming.suffix' | translate }}</mat-label>
          <input formControlName="dataSuffix" matInput floatLabelType="Auto" autocomplete="off" />
        </mat-form-field>
        <div class="form-field-down">
          <mat-form-field color="accent">
            <mat-label color="accent">{{
              'admins.automaticNaming.maximumSize' | translate
            }}</mat-label>
            <input
              formControlName="dataMaximumSize"
              type="number"
              min="0"
              matInput
              floatLabelType="Auto"
              autocomplete="off" />
            <mat-error
              class=""
              *ngIf="addNamingSubConceptForm.controls?.dataMaximumSize?.hasError('min')"
              >{{ 'input.mustBeGreaterThan' | translate : { min: 0 } }}</mat-error
            >
          </mat-form-field>
          <mat-checkbox formControlName="dataPerValue">{{
            'admins.automaticNaming.perValue' | translate
          }}</mat-checkbox>
        </div>
        <div class="form-field-down">
          <mat-form-field color="accent">
            <mat-label color="accent">{{
              'admins.automaticNaming.abbreviation' | translate
            }}</mat-label>
            <input
              formControlName="dataAbbreviation"
              matInput
              floatLabelType="Auto"
              autocomplete="off" />
          </mat-form-field>
          <mat-form-field color="accent">
            <mat-label color="accent">{{
              'admins.automaticNaming.valueSeparator' | translate
            }}</mat-label>
            <input
              formControlName="dataValueSeparator"
              matInput
              floatLabelType="Auto"
              autocomplete="off" />
          </mat-form-field>
        </div>
      </div>
    </ng-template>
    
    <ng-template #constantTextForm>
      <div class="h2-section-subtitle" style="padding-top: 16px; padding-bottom: 8px">
        {{ 'generic.settings' | translate }}
      </div>
      <mat-form-field color="accent" class="form-field">
        <mat-label color="accent">{{ 'admins.columns.text' | translate }}</mat-label>
        <input formControlName="constantText" matInput floatLabelType="Auto" autocomplete="off" />
        <mat-error
          *ngIf="addNamingSubConceptForm.get('constantText')?.errors?.required"
          class="e-error">
          {{ 'input.fieldRequired' | translate }}
        </mat-error>
      </mat-form-field>
      <div class="hint">
        <fa-icon class="hint-icon" [icon]="['fal', 'info-circle']"></fa-icon>
        {{ 'admins.automaticNaming.constantInformation' | translate }}
      </div>
    </ng-template>

    <ng-template #activeUser>
      <tx-no-record
        class="no-settings"
        [noRecordText]="'admins.automaticNaming.noSettings' | translate"></tx-no-record>
    </ng-template>

    <ng-template #currentDateForm>
      <div class="h2-section-subtitle" style="padding-top: 16px; padding-bottom: 8px">
        {{ 'generic.settings' | translate }}
      </div>
      <mat-form-field color="accent" class="form-field">
        <mat-label color="accent">{{
          'admins.automaticNaming.dateTimeFormat' | translate
        }}</mat-label>
        <input formControlName="dateFormat" matInput floatLabelType="Auto" autocomplete="off" />
        <mat-error
          *ngIf="addNamingSubConceptForm.get('dateFormat')?.errors?.required"
          class="e-error">
          {{ 'input.fieldRequired' | translate }}
        </mat-error>
      </mat-form-field>
    </ng-template>

    <ng-template #indexForm>
      <div class="h2-section-subtitle" style="padding-top: 16px; padding-bottom: 8px">
        {{ 'admins.automaticNaming.indexType' | translate }}
      </div>
      <div class="form-field">
        <div>
          <mat-label class="form-label required-label">{{
            'admins.columns.type' | translate
          }}</mat-label>
        </div>
        <mat-button-toggle-group
          formControlName="indexGlobalType"
          class="form-button-group"
          (valueChange)="updateSubConceptValidators()">
          <mat-button-toggle [value]="incrType.data">{{
            'admins.automaticNaming.globalIndex' | translate
          }}</mat-button-toggle>
          <mat-button-toggle [value]="incrType.link">{{
            'admins.automaticNaming.relativeIndex' | translate
          }}</mat-button-toggle>
        </mat-button-toggle-group>
      </div>
      <div class="hint">
        <fa-icon class="hint-icon" [icon]="['fal', 'info-circle']"></fa-icon>
        {{
          (addNamingSubConceptForm.get('indexGlobalType')?.value === incrType.link
            ? 'admins.automaticNaming.relativeIndexInformation'
            : 'admins.automaticNaming.globalIndexInformation'
          ) | translate
        }}
      </div>
      <div
        *ngIf="
          addNamingSubConceptForm.get('indexGlobalType')?.value === incrType.link;
          else dataIndex
        ">
        <div
          class="form-label required-label"
          [ngClass]="{ error: indexAttributeIncrement?.invalid }">
          {{ 'admins.automaticNaming.referenceIndexAttribute' | translate }}
        </div>
        <div class="tree-grid-container">
          <tx-attributes-tree-grid
            [idObjectType]="configurationSelected?.idOT"
            [enableCheckbox]="true"
            [multipleSelection]="false"
            [disableLinkLoading]="false"
            [attributeSetLevels]="
              indexAttributeIncrement?.value ? [indexAttributeIncrement?.value] : []
            "
            (checkChange)="onCheckRelativeIndexAttribute($event)">
          </tx-attributes-tree-grid>
        </div>
      </div>
      <ng-template #dataIndex>
        <div class="selection-field-container">
          <div class="form-field">
            <app-disabled-selection-field
              [label]="'admins.automaticNaming.objectName' | translate"
              [placeholder]="'admins.automaticNaming.noObjectSelected' | translate"
              [selectedConcept]="selectedObject"
              [errorMessage]="errorMessage"
              [requiredField]="true"
              (showSelection)="showIndexDataObjectSelectionField()"
              (conceptChange)="onIndexObjectChange($event)">
            </app-disabled-selection-field>
          </div>
          <div class="form-field">
            <app-disabled-selection-field
              [label]="'admins.automaticNaming.attributeName' | translate"
              [placeholder]="'admins.automaticNaming.noAttributeSelected' | translate"
              [selectedConcept]="selectedAttribute"
              [errorMessage]="errorMessage"
              [requiredField]="true"
              (showSelection)="showIndexDataAttributeSelectionField()"
              (conceptChange)="onIndexAttributeChange($event)">
            </app-disabled-selection-field>
          </div>
        </div>
      </ng-template>
      <div class="h2-section-subtitle" style="padding-top: 16px; padding-bottom: 8px">
        {{ 'generic.settings' | translate }}
      </div>
      <div class="form-field">
        <div>
          <mat-label class="form-label required-label">{{
            'admins.columns.type' | translate
          }}</mat-label>
        </div>
        <mat-button-toggle-group formControlName="indexType" class="form-button-group">
          <mat-button-toggle [value]="indexType.alphaLower">{{
            'admins.automaticNaming.alphaLowerCase' | translate
          }}</mat-button-toggle>
          <mat-button-toggle [value]="indexType.alphaUpper">{{
            'admins.automaticNaming.alphaUpperCase' | translate
          }}</mat-button-toggle>
          <mat-button-toggle [value]="indexType.num">{{
            'admins.automaticNaming.numerical' | translate
          }}</mat-button-toggle>
        </mat-button-toggle-group>
      </div>
      <div>
        <mat-form-field color="accent" class="form-field small-field">
          <mat-label color="accent">{{
            'admins.automaticNaming.startIndex' | translate
          }}</mat-label>
          <input
            formControlName="indexStartIndex"
            matInput
            floatLabelType="Auto"
            autocomplete="off" />
        </mat-form-field>
        <mat-checkbox class="form-field small-field" formControlName="indexIncrement">{{
          'admins.automaticNaming.increment' | translate
        }}</mat-checkbox>
        <mat-form-field
          *ngIf="addNamingSubConceptForm.get('indexType')?.value === indexType.num"
          color="accent"
          class="form-field small-field">
          <mat-label color="accent">{{
            'admins.automaticNaming.numberDigits' | translate
          }}</mat-label>
          <input
            type="number"
            min="0"
            formControlName="indexNumberDigits"
            matInput
            floatLabelType="Auto"
            autocomplete="off" />
          <mat-error
            class=""
            *ngIf="addNamingSubConceptForm.controls?.indexNumberDigits?.hasError('min')"
            >{{ 'input.mustBeGreaterThan' | translate : { min: 0 } }}</mat-error
          >
        </mat-form-field>
      </div>
      <div class="form-field">
        <app-disabled-selection-field
          [label]="'admins.automaticNaming.storageAttributeIndex' | translate"
          [placeholder]="'admins.automaticNaming.noAttributeSelected' | translate"
          [selectedConcept]="selectedStorageAttribute"
          (showSelection)="showStorageAttributeSelectionField()"
          (conceptChange)="onAttrStorageChange($event)">
        </app-disabled-selection-field>
      </div>
    </ng-template>

    <ng-template #objectSelection>
      <app-object-selection-form
        [idObjectTypeSelected]="getIdOTForIndexDataObjectSelect()"
        [objectsSelected]="indexObject?.value ? [indexObject?.value] : []"
        [objectTypes]="objectTypes"
        (cancel)="pane2Canceled()"
        (confirm)="onSelectIndexDataObject($event)"></app-object-selection-form>
    </ng-template>

    <ng-template #attributeSelection>
      <app-attribute-selection-form
        [idObjectTypeSelected]="getIdOTForIndexDataAttributeSelect()"
        [attrTypesDisplayed]="[dataType.SingleValue, dataType.ShortText]"
        [attributesLevelsSelected]="indexAttribute?.value ? [indexAttribute?.value] : []"
        [objectTypes]="objectTypes"
        (cancel)="pane2Canceled()"
        (confirm)="onSelectIndexDataAttribute($event)"></app-attribute-selection-form>
    </ng-template>

    <ng-template #storageAttributeSelection>
      <app-attribute-selection-form
        [idObjectTypeSelected]="objectTypeConfigSelected?.id.toString()"
        [attrTypesDisplayed]="[dataType.SingleValue, dataType.ShortText]"
        [disableObjectTypeChange]="true"
        [attributesLevelsSelected]="
          indexStorageAttribute?.value ? [indexStorageAttribute?.value] : []
        "
        [objectTypes]="objectTypes"
        (cancel)="pane2Canceled()"
        (confirm)="onSelectStorageAttribute($event)"></app-attribute-selection-form>
    </ng-template>
  </form>
</ng-template>

<div class="admin-container" style="position: relative">
  <app-breadcrumd *ngIf="!isInsideRightPane"></app-breadcrumd>
  <div class="admin-content">
    <div class="h1-title">{{ 'admins.wording.automaticNaming' | translate }}</div>
    <mat-divider class="divider-title"></mat-divider>
    <div class="automatic-naming-container">
      <div style="padding-top: 16px; height: calc(100% - 16px)">
        <app-configurations-framework
          #configFramework
          *ngIf="isConfigsLoaded; else spinner"
          [configurations]="configurations"
          [addConfigFormSettings]="addConfigFormSettings"
          [addSubconceptElementFormSettings]="addMailSettingFormSettings"
          [useModelApplications]="true"
          [modelInputOrderForConfig]="2"
          [displayHistoryTab]="false"
          [displayValidityTab]="false"
          [displayPreviewValue]="true"
          [itemNameTemplate]="itemNameTemplate"
          [previewNameTemplate]="previewNameTemplate"
          modelTag="txMdl_StrGen"
          (afterAddConfiguration)="onAfterAddConfiguration()"
          (beforeAddConfiguration)="onBeforeAddConfiguration()"
          (afterDuplicateConfiguration)="onAfterDuplicateConfiguration($event)"
          (deleteConfiguration)="onDeleteConfiguration($event)"
          (selectConfiguration)="onSelectingConfiguration($event)"
          (beforeValidateAddElementSubconcept)="onBeforeValidateMailSettings($event)"
          (beforeValidateEditElementSubconcept)="onBeforeValidateMailSettings($event)"
          (beforeEditConfiguration)="onBeforeEditConfig($event)"
          (afterEditConfiguration)="onAfterEditConfig()"
          (afterAddElementSubconcept)="onAfterAddElementSubconcept()"
          (beforeAddElementSubconcept)="onBeforeAddElementSubconcept()"
          (beforeEditElementSubconcept)="onBeforeEditSubConcept($event)"
          (afterEditElementSubconcept)="onAfterEditSubConcept()"
          (deleteElementSubconcept)="onDeleteSubConcept($event)"
          (changeElementOrder)="onChangeSubConceptOrder()"
          (itemHoveredEvent)="onHoverItem($event)"
          (itemSelectedEvent)="onSelectItem($event)">
        </app-configurations-framework>
        <ng-template #spinner>
          <app-loader [diameterLoader]="75"></app-loader>
        </ng-template>
      </div>
    </div>
  </div>
</div>

<ng-template #previewNameTemplate let-items="items">
  <div class="preview-name-field" >
    <mat-label class="form-label">
      {{ 'admins.automaticNaming.previewName' | translate }}
    </mat-label>
    <div class="value-example" [matTooltip]="previewNameExampleTooltip">
      @for (item of items; track item.id){
        <!-- Repeat span to avoid blank space after the value -->
        @if (item.type === settingsType.text || item.type === settingsType.username ){
          <span [class.highlight-item]="itemHovered?.id === item.id || itemSelected?.id === item.id" [ngClass]="{'accent': $even}">{{ item.value.replace(regexBLKValue, " ") }}</span>
        } @else if (item.type === settingsType.date) {
          <span [class.highlight-item]="itemHovered?.id === item.id || itemSelected?.id === item.id" [ngClass]="{'accent': $even}">{{ today | date:item.sDateFormat }}</span>
        } @else if (item.type === settingsType.data || item.type === settingsType.index) {
          <span [class.highlight-item]="itemHovered?.id === item.id || itemSelected?.id === item.id" [ngClass]="{'accent': $even}">{{ item.previewValue }}</span>
        }
      }
    </div>
  </div>
</ng-template>

<ng-template #itemNameTemplate let-item="item">
  <div [matTooltip]="item.type" class="column-type">
    {{ item.type }}
  </div>
  @if (item.type === settingsType.data || item.type === settingsType.index) {
    <div class="column-value">
      @for (attributeName of item.previewAttributeList; track attributeName){
        <span [matTooltip]="attributeName">
          {{ attributeName }}
        </span>
        @if (!$last){
          @if (item.type === settingsType.index && item.sIncrementationType === incrType.data) {
            <fa-icon class="item-arrow-size accent" [icon]="['fas', 'right']"></fa-icon>
          } @else {
            <fa-icon class="item-arrow-size accent" [icon]="['fas', 'chevron-right']"></fa-icon>
          }
        }
      }
    </div>
  } @else {
    <div [matTooltip]="item.value" class="column-value">
      {{ item.value }}
    </div>
  }
  @if (item.type === settingsType.date) {
    <div [matTooltip]="item.sDateFormat" class="column-format">
      {{ item.sDateFormat }}
    </div>
  } @else {
    <div [matTooltip]="item.previewFormat" class="column-format">
      {{ item.previewFormat }}
    </div>
  }
</ng-template>