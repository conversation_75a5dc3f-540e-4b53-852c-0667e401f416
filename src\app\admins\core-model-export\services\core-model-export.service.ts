import { Injectable } from '@angular/core';
import {
  BehaviorSubject,
  catchError,
  combineLatestWith,
  finalize,
  map,
  Observable,
  of,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateService } from '@ngx-translate/core';
import { ModelApplicationService } from 'src/app/core/services/structure/model-application.service';
import { ErrorMessagesService } from 'src/app/core/error-messages/error-messages.service';
import { CoreModelExportHistory } from '../models/core-model-export-history.model';
import { FilesUtils } from 'src/app/core/utils/files';
import { ErrorService } from 'src/app/core/services/errors/error.service';
import { CoreModelsExportGatewayService } from './core-model-export-gateway.service';
import { CoreModelExportHistoryDTO } from '../models/core-model-export-history-object.dto';
import { EnumUtils } from 'src/app/core/utils/enum';
import { ErrorUtils } from 'src/app/core/utils/error.utils';
import {
  CoreModelConceptOptionalIds,
  CoreModelConceptRequiredProps,
  CoreModelExportConceptDTO,
} from '../models/core-model-export-concept.dto';
import { CoreModelConceptService } from '../../core-model-common';
import {
  CoreModelExportConcept,
  CoreModelExportConceptErrorKeys,
  CoreModelExportConceptMetaData,
  MissingConfig,
} from '../models/core-model-export-concept.model';
import { ConceptType, TxCommonService, TxObjectsTypeService } from '@bassetti-group/tx-web-core';
import { HttpErrorResponse } from '@angular/common/http';

const META_DATA_HEADER = {
  idSourceObjectType: _('admins.coreModels.idSourceObjectType'),
  idDestinationObjectType: _('admins.coreModels.idDestinationObjectType'),
  idModel: _('admins.coreModels.idModel'),
  filePath: _('admins.coreModels.filePath'),
};
const UNKNOWN_LABEL = _('generic.unknown');
@Injectable({
  providedIn: 'root',
})
export class CoreModelExportService extends CoreModelConceptService<CoreModelExportConcept> {
  history$: Observable<CoreModelExportHistory[]>;
  canExport$: Observable<boolean>;
  private readonly _canExportSub = new BehaviorSubject<boolean>(false);
  private readonly _historySub = new BehaviorSubject<CoreModelExportHistory[]>([]);
  private readonly _conceptRequiredProps = Object.values(CoreModelConceptRequiredProps);
  constructor(
    objectsTypeService: TxObjectsTypeService,
    translateService: TranslateService,
    errorService: ErrorService,
    commonService: TxCommonService,
    errorMessagesService: ErrorMessagesService,
    private readonly _modelApplicationService: ModelApplicationService,
    private readonly _coreModelExportGetAway: CoreModelsExportGatewayService
  ) {
    super(translateService, objectsTypeService, errorService, errorMessagesService, commonService);
    this.history$ = this._historySub.asObservable();
    this.canExport$ = this._canExportSub.asObservable();
  }
  loadConcepts(): void {
    this._isLoadingSub.next(true);
    this._coreModelExportGetAway
      .loadCoreModelsConceptData()
      .pipe(
        take(1),
        combineLatestWith(
          this._coreModelExportGetAway.loadCoreModelsExportHistoryList(),
          this._objectsTypeService.isReady()
        ),
        map(([concept, historyDto]) => {
          const concepts = this.toCoreModelsConcepts(concept);
          const historyList = this.parseToCoreModelExportHistory(historyDto);
          return [concepts, historyList] as [CoreModelExportConcept[], CoreModelExportHistory[]];
        }),
        tap(([concepts, historyList]) => {
          this.updateConceptsAndFilterOnErrors(concepts, 'errors');
          this._historySub.next(historyList);
          this._canExportSub.next(this.canExport());
          this._isLoadingSub.next(false);
        }),
        catchError((err) => ErrorUtils.defaultHandlerObservableError(err, this._errorService)),
        finalize(() => {
          this._isLoadingSub.next(false);
        })
      )
      .subscribe();
  }

  refreshConcepts(): void {
    this.loadConcepts();
  }
  exportCoreModel(history: CoreModelExportHistoryDTO): Observable<CoreModelExportHistory[]> {
    this._isLoadingSub.next(true);
    return this._coreModelExportGetAway.exportCoreModels(history).pipe(
      take(1),
      tap((archive) => {
        FilesUtils.downloadBlobFile(archive, history.name + '_' + history.version + '.zip');
      }),
      switchMap(() => this._coreModelExportGetAway.loadCoreModelsExportHistoryList()),
      catchError((err) => this.handlerExportError(err)),
      map((historyDTOList) => {
        const historyList = this.parseToCoreModelExportHistory(historyDTOList);
        this._historySub.next(historyList);
        return historyList;
      }),
      catchError((err) => ErrorUtils.defaultHandlerObservableError(err, this._errorService)),
      finalize(() => {
        this._isLoadingSub.next(false);
      })
    );
  }
  getLastCoreModelExported(): Observable<CoreModelExportHistory | undefined> {
    const coreModelExported = this._historySub.value;
    return of(coreModelExported ? coreModelExported[coreModelExported.length - 1] : undefined);
  }

  filterOnConceptsInErrors(): void {
    this.filterOnErrors();
  }
  /**
   *
   * @param concepts
   * @returns can throw error
   */
  private toCoreModelsConcepts(concepts: CoreModelExportConceptDTO[]): CoreModelExportConcept[] {
    return concepts.map((data) => {
      const type = EnumUtils.getCorrespondingEnum(
        data.type,
        Object.values(ConceptType)
      ) as ConceptType;
      const id = data.id;
      const tags = [data.tag];
      const objectType = data.idObjectType ? this.getObjectType(data.idObjectType) : undefined;
      const name = data.name;
      const icon = this.conceptIcon(type, objectType, id);
      const metaDataList = this.getCoreModelMetaData(data);
      const explanation = data.explanation;
      const errors = this.parseToCoreModelConceptErrors(
        this.parseToMissingConfig(data.missingConfigs)
      );
      return {
        type,
        id,
        name,
        icon,
        tags,
        objectType,
        metaDataList,
        explanation,
        errors,
      };
    });
  }

  private parseToCoreModelConceptErrors(missingConfigs: MissingConfig[]): string[] {
    return missingConfigs.map((config) => {
      switch (config.key) {
        case CoreModelExportConceptErrorKeys.MissingObjectTypeTag:
        case CoreModelExportConceptErrorKeys.MissingModelTag:
        case CoreModelExportConceptErrorKeys.MissingAttributeTag:
        case CoreModelExportConceptErrorKeys.MissingUnitTag:
        case CoreModelExportConceptErrorKeys.MissingObjectTypeSourceTag:
        case CoreModelExportConceptErrorKeys.MissingObjectTypeDestinationTag:
        case CoreModelExportConceptErrorKeys.MissingFileTypeTag:
        case CoreModelExportConceptErrorKeys.MissingTableTypeTag:
        case CoreModelExportConceptErrorKeys.MissingConditionnalFilterAttributeTag:
        case CoreModelExportConceptErrorKeys.MissingAssociativeClassLinkTag:
        case CoreModelExportConceptErrorKeys.MissingAssociativeClassObjectTypeTag:
        case CoreModelExportConceptErrorKeys.MissingParentFilterObjectTag:
        case CoreModelExportConceptErrorKeys.MissingAttributeSetTag:
        case CoreModelExportConceptErrorKeys.MissingSeriesTypeUnitTag:
        case CoreModelExportConceptErrorKeys.MissingEquivalence:
        case CoreModelExportConceptErrorKeys.MissingEquivalenceSetTag:
        case CoreModelExportConceptErrorKeys.MissingObjectTag:
        case CoreModelExportConceptErrorKeys.MissingExplanation:
          return this.handleMissingTag(config);
        default:
          throw new Error(`the missingConfig ${config.key} key is unknown`);
      }
    });
  }
  private getCoreModelMetaData(
    concept: CoreModelExportConceptDTO
  ): CoreModelExportConceptMetaData[] {
    return (Object.keys(concept) as (keyof CoreModelExportConceptDTO)[]).flatMap((key) => {
      const dataValue = concept[key];
      if (this.isNotCoreModelRequiredValues(key, dataValue) && key !== 'modelName') {
        const header = this._translateService.instant(_(`admins.coreModels.${key}`));
        if (!this.isConceptId(dataValue)) {
          return [{ key, header, value: dataValue } as CoreModelExportConceptMetaData];
        }
        const value = this.getConceptValue(key, dataValue);
        return [{ key, header, value } as CoreModelExportConceptMetaData];
      }
      return [];
    });
  }
  private parseToMissingConfig(
    missingConfigs: {
      key: string;
      missingObject?: { tags: string[]; id: number } | null;
    }[]
  ): MissingConfig[] {
    return missingConfigs.map((config) => {
      const key = EnumUtils.getCorrespondingEnum(
        config.key,
        Object.values(CoreModelExportConceptErrorKeys)
      ) as CoreModelExportConceptErrorKeys;
      const missingObject = config.missingObject;
      return { key, missingObject };
    });
  }

  private getConceptValue(key: keyof CoreModelExportConceptDTO, dataValue: number) {
    switch (key) {
      case CoreModelConceptOptionalIds.IdSourceObjectType:
      case CoreModelConceptOptionalIds.IdDestinationObjectType:
        return this.getObjectType(dataValue);
      case CoreModelConceptOptionalIds.IdModel:
        return this._modelApplicationService.getByID(dataValue);
      default:
        throw new Error('this optional props does not exist in  Core Model Concept Data');
    }
  }
  private isConceptId(dataValue: string | number): dataValue is number {
    return typeof dataValue === 'number';
  }
  private isNotCoreModelRequiredValues(
    key: keyof CoreModelExportConceptDTO,
    value: CoreModelExportConceptDTO[keyof CoreModelExportConceptDTO]
  ): value is string | number {
    return !this._conceptRequiredProps.some((prop) => key === prop) && value !== undefined;
  }
  private canExport(): boolean {
    return this._concepts.length > 0 && this._conceptsNbErrorsSub.value.errors === 0;
  }

  private handleMissingTag(config: MissingConfig): string {
    if (config.missingObject) {
      const id = config.missingObject?.id;
      const idInterpolateParams = { id };
      return this._translateService.instant(
        this._errorMessagesService.getMessageByKey(config.key)?.content ?? '',
        idInterpolateParams
      );
    }

    return this._translateService.instant(
      this._errorMessagesService.getMessageByKey(config.key)?.content ?? ''
    );
  }

  private parseToCoreModelExportHistory(
    historyDTOList: CoreModelExportHistoryDTO[]
  ): CoreModelExportHistory[] {
    return historyDTOList.map((history) => ({
      ...history,
      explanation: history.explanation,
      date: new Date(history.date),
      username: history.username,
    }));
  }
  private handlerExportError(err: unknown): Observable<CoreModelExportHistoryDTO[]> {
    if (err instanceof HttpErrorResponse && !!err.error.size) {
      const reader: FileReader = new FileReader();
      reader.onloadend = (e) => {
        const exportError = JSON.parse(reader.result as string);
        this._errorService.addError({ error: exportError });
      };
      reader.readAsText(err.error);
    }
    return this._coreModelExportGetAway.loadCoreModelsExportHistoryList();
  }
}
