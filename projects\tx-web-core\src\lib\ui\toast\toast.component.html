<div
  class="toast mat-elevation-z6"
  [@fadeAnimation]="{ value: animationState, params: { fadeIn: 200, fadeOut: 500 } }"
  (@fadeAnimation.done)="onFadeFinished($event.toState)"
  [ngClass]="{
    'background-e-warning': data.type === 'warning',
    'background-e-information': data.type === 'information' || data.type === 'loading',
    'background-e-success': data.type === 'success',
    'background-e-error': data.type === 'error'
  }">
  <fa-icon [icon]="['fal', 'times']" class="close-button" (click)="onClose()"></fa-icon>
  <ng-container *ngIf="data.template; else descriptionRef">
    <ng-container *ngTemplateOutlet="data.template; context: data.templateContext"></ng-container>
  </ng-container>
  <ng-template #descriptionRef>
    <span
      *ngIf="data.progress !== undefined && data.progress >= 0 && data.displayPercent; else icons"
      >{{ data.progress | number : '1.0-0' }} %</span
    >
    <mat-progress-bar
      *ngIf="data.progress !== undefined && data.progress >= 0"
      class="loading-progressbar"
      mode="determinate"
      value="{{ data.progress }}"
      color="white"></mat-progress-bar>
    <ng-template #icons>
      <fa-icon *ngIf="data.type === 'success'" [icon]="['fas', 'check-circle']" size="lg"></fa-icon>
      <fa-icon
        *ngIf="data.type === 'information'"
        [icon]="['fas', 'info-circle']"
        size="lg"></fa-icon>
      <fa-icon
        *ngIf="data.type === 'error'"
        [icon]="['fas', 'exclamation-circle']"
        size="lg"></fa-icon>
      <fa-icon
        *ngIf="data.type === 'warning'"
        [icon]="['fas', 'exclamation-circle']"
        size="lg"></fa-icon>
      <fa-icon
        *ngIf="data.type === 'loading'"
        [icon]="['fal', 'spinner']"
        [animation]="'spin'"
        size="lg"></fa-icon>
    </ng-template>
    <div>{{  data.description !== undefined ? (data.description | translate): ''  }}</div>
  </ng-template>
</div>
