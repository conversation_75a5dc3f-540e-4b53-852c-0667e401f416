<div class="test-field">
  <div class="test-field__input-text">
    <tx-input-text-field
      [formControl]="controlTxt"
      [label]="'label txt'"
      [labelTooltip]="'ToolTip txt'"
      [isTextArea]="false"
      [icon]="'home'"
      [hintLabel]="'hint txt'"
      [required]="true"
      [maxLength]="4"></tx-input-text-field>
  </div>
  <div class="test-field__input-number-min-max">
    <tx-min-max-mean
      [formControl]="controlNumMM"
      [label]="'label num'"
      [labelTooltip]="'ToolTip num'"
      [lowerBound]="2"
      [upperBound]="1000"
      [lowerBoundIncluded]="false"
      [upperBoundIncluded]="true"
      [required]="true"
      [units]="units"
      [idUnitRef]="2">
    </tx-min-max-mean>
  </div>
  <div class="test-field__input-number-min-max-mean">
    <tx-min-max-mean
      [formControl]="controlNumMMM"
      [label]="'label num'"
      [labelTooltip]="'ToolTip num'"
      [lowerBound]="2"
      [upperBound]="1000"
      [lowerBoundIncluded]="false"
      [upperBoundIncluded]="true"
      [required]="true"
      [units]="units">
    </tx-min-max-mean>
  </div>
  <div class="test-field__chips-field">
    <tx-chips-field
      [formControl]="controlChips"
      [label]="'label chips'"
      [labelTooltip]="'ToolTip chips'"
      [actionIcon]="'home'"
      [actionIconToolTip]="'action tooltip'"
      [required]="true"
      [readMode]="false"
      [multiple]="true"
      [multipleSelection]="false"
      [removable]="true"
      [placeHolder]="'placeholder'"
      [hintLabel]="'hint chips'"
      [editable]="true"
      [maxChipDisplay]="5"
      [maxChipsMessage]="'Too many chips'"
      [chipsIcon]="'user'"
      [displayExtenderChip]="true"
      [hideTitleImageThumbnail]="false"
      (chipClick)="chipClick($event)"
      (allChipsLink)="displayPaneEvent($event)"
      (actionIconClick)="actionIconClick($event)"
      [pattern]=""></tx-chips-field>
  </div>
</div>
