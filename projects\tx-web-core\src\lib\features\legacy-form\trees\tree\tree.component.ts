import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
  AfterViewInit,
} from '@angular/core';
import {
  BeforeOpenCloseMenuEventArgs,
  ContextMenuComponent,
  DataBoundEventArgs,
  DataSourceChangedEventArgs,
  DragAndDropEventArgs,
  DrawNodeEventArgs,
  FailureEventArgs,
  FieldsSettingsModel,
  MenuEventArgs,
  MenuItemModel,
  NodeAnimationSettingsModel,
  NodeCheckEventArgs,
  NodeClickEventArgs,
  NodeEditEventArgs,
  NodeExpandEventArgs,
  NodeKeyPressEventArgs,
  NodeSelectEventArgs,
  TreeViewComponent,
} from '@syncfusion/ej2-angular-navigations';
import { _LegacyStringUtils } from '../../utilities/legacy-string-utils';

@Component({
  selector: 'tx-tree',
  templateUrl: './tree.component.html',
  styleUrls: ['./tree.component.scss'],
})
export class TxTreeComponent implements OnInit, AfterViewInit {
  //#region syncfusion tree inputs
  // see api here for more detaileds : https://ej2.syncfusion.com/angular/documentation/api/treeview/#properties
  @Input() allowDragAndDrop!: boolean; // Defaults to false
  @Input() allowEditing!: boolean; // Defaults to false
  @Input() allowMultiSelection = true; // Defaults to true
  /**
   * Specifies the type of animation applied on expanding and collapsing the nodes along with duration.
   * Defaults to {expand: { effect: ‘SlideDown’, duration: 400, easing: ‘linear’ },collapse: { effect: ‘SlideUp’, duration: 400, easing: ‘linear’ }}
   */
  @Input() animation!: NodeAnimationSettingsModel;
  @Input() autoCheck = false; // Defaults to false
  // checked nodes ids
  @Input() checkedNodes!: any[];
  @Input() cssClass!: string;
  @Input() disabled!: boolean;
  /**
   * Defines the area in which the draggable element movement will be occurring. Outside that area will be restricted for the draggable element movement.
   * By default, the draggable element movement occurs in the entire page.
   */
  @Input() dragArea!: HTMLElement | string; // Defaults to null
  // Defines whether to allow the cross-scripting site or not. Default to true.
  @Input() enableHtmlSanitizer = true;
  // Enables or disables persisting TreeView state between page reloads. If enabled, following APIs will persist. Defaults to false.
  @Input() enablePersistence!: boolean;
  // Enable or disable rendering component in right to left direction. Defaults to false.
  @Input() enableRtl!: boolean;
  /**
   * Specifies the action on which the node expands or collapses. The available actions are,
   *   - Auto - In desktop, the expand/collapse operation happens when you double-click the node, and in mobile devices it happens on single-click.
   *   - Click - The expand/collapse operation happens when you single-click the node in both desktop and mobile devices.
   *   - DblClick - The expand/collapse operation happens when you double-click the node in both desktop and mobile devices.
   *   - None - The expand/collapse operation will not happen when you single-click or double-click the node in both desktop and mobile devices.
   * Defaults to ‘Auto’.
   */
  @Input() expandOn!: string;
  /**
   * Represents the expanded nodes in the TreeView component. We can set the nodes that need to be expanded or get the ID of the nodes that are
   * currently expanded by using this property. Defaults to [].
   */
  @Input() expandedNodes!: string[];
  /**
   * Specifies the data source and mapping fields to render TreeView nodes.
   * Defaults to {
   *  id: ‘id’, text: ‘text’, dataSource: [], child: ‘child’, parentID: ‘parentID’, hasChildren: ‘hasChildren’,
   *  expanded: ‘expanded’, htmlAttributes: ‘htmlAttributes’, iconCss: ‘iconCss’, imageUrl: ‘imageUrl’, isChecked: ‘isChecked’,
   *  query: null, selected: ‘selected’, tableName: null, tooltip: ‘tooltip’, navigateUrl: ‘navigateUrl’
   * }
   */
  @Input() fields: FieldsSettingsModel = {
    id: 'id',
    text: 'name',
    dataSource: [],
    child: 'child',
    parentID: 'idParent',
    hasChildren: 'parent',
    expanded: 'expanded',
    htmlAttributes: 'htmlAttributes',
    iconCss: 'iconCss',
    imageUrl: 'image',
    isChecked: 'isChecked',
    query: undefined,
    selected: 'selected',
    tableName: undefined,
    tooltip: 'tooltip',
    navigateUrl: 'navigateUrl',
  };
  // If this property is set to true, then the entire TreeView node will be navigate-able instead of text element. Defaults to false.
  @Input() fullRowNavigable!: boolean;
  // On enabling this property, the entire row of the TreeView node gets selected by clicking a node.
  // When disabled only the corresponding node’s text gets selected. Default to false.
  @Input() fullRowSelect = false;
  // By default, the load on demand (Lazy load) is set to true. By disabling this property, all the tree nodes are rendered at the beginning itself. Defaults to true.
  @Input() loadOnDemand!: boolean;
  @Input() nodeTemplate: any;
  // selected nodes ids
  @Input() selectedNodes!: string[];
  @Input() showCheckBox!: boolean; // Defaults to false
  /**
   * Specifies a value that indicates whether the nodes are sorted in the ascending or descending order, or are not sorted at all.
   * The available types of sort order are,
   *   - None - The nodes are not sorted.
   *   - Ascending - The nodes are sorted in the ascending order.
   *   - Descending - The nodes are sorted in the ascending order.
   * Defaults to ‘None’.
   */
  @Input() sortOrder!: string;
  //#endregion

  //#region syncfusion tree outputs
  @Output() actionFailure: EventEmitter<FailureEventArgs> = new EventEmitter();
  @Output() created: EventEmitter<any> = new EventEmitter();
  @Output() dataBound: EventEmitter<DataBoundEventArgs> = new EventEmitter();
  @Output() dataSourceChanged: EventEmitter<DataSourceChangedEventArgs> = new EventEmitter();
  @Output() destroyed: EventEmitter<any> = new EventEmitter();
  @Output() drawNode: EventEmitter<DrawNodeEventArgs> = new EventEmitter();
  @Output() keyPress: EventEmitter<NodeKeyPressEventArgs> = new EventEmitter();
  @Output() nodeChecked: EventEmitter<NodeCheckEventArgs> = new EventEmitter();
  @Output() nodeChecking: EventEmitter<NodeCheckEventArgs> = new EventEmitter();
  @Output() nodeClicked: EventEmitter<NodeClickEventArgs> = new EventEmitter();
  @Output() nodeCollapsed: EventEmitter<NodeExpandEventArgs> = new EventEmitter();
  @Output() nodeCollapsing: EventEmitter<NodeExpandEventArgs> = new EventEmitter();
  @Output() nodeDragStart: EventEmitter<DragAndDropEventArgs> = new EventEmitter();
  @Output() nodeDragStop: EventEmitter<DragAndDropEventArgs> = new EventEmitter();
  @Output() nodeDragging: EventEmitter<DragAndDropEventArgs> = new EventEmitter();
  @Output() nodeDropped: EventEmitter<DragAndDropEventArgs> = new EventEmitter();
  @Output() nodeEdited: EventEmitter<NodeEditEventArgs> = new EventEmitter();
  @Output() nodeEditing: EventEmitter<NodeEditEventArgs> = new EventEmitter();
  @Output() nodeExpanded: EventEmitter<NodeExpandEventArgs> = new EventEmitter();
  @Output() nodeExpanding: EventEmitter<NodeExpandEventArgs> = new EventEmitter();
  @Output() nodeSelected: EventEmitter<NodeSelectEventArgs> = new EventEmitter();
  @Output() nodeSelecting: EventEmitter<NodeSelectEventArgs> = new EventEmitter();
  //#endregion

  // specific properties
  @Input() height!: number | string;
  @Input() width!: number | string;
  @Input() nodes: any[] = [];
  @Input() checkOnSelect = false;
  @Input() searchLimit = 400;
  @Input() seachedNodes!: any[];
  @Input() silent = false;
  @Input() cssRules!: string;

  @Output() mouseOver: EventEmitter<MouseEvent> = new EventEmitter();
  @Output() mouseOut: EventEmitter<MouseEvent> = new EventEmitter();
  @Output() mouseDown: EventEmitter<MouseEvent> = new EventEmitter();
  @Output() mouseUp: EventEmitter<MouseEvent> = new EventEmitter();
  @Output() mouseEnter: EventEmitter<MouseEvent> = new EventEmitter();
  @Output() mouseLeave: EventEmitter<MouseEvent> = new EventEmitter();
  @Output() afterInit: EventEmitter<any> = new EventEmitter();

  private previousNodes: any[] = [];
  @Input() checkedIds: any[] = [];
  uniqueId: string;
  @ViewChild('treeview') treeview!: TreeViewComponent;

  // context menu settings
  @Input() displayContextMenu!: boolean;
  @Input() menuItems!: MenuItemModel[];
  @ViewChild('contentmenutree') contentmenutree!: ContextMenuComponent;

  constructor() {
    this.uniqueId = _LegacyStringUtils.getUniqueId();
  }

  ngOnInit() {}

  ngAfterViewInit(): void {
    this.afterInit.emit(this);
  }

  //#region Syncfusion tree triggers
  onActionFailure(event: FailureEventArgs) {
    this.actionFailure.emit(event);
  }

  onCreated(event: object) {
    // init style properties
    if (this.height || this.width) {
      if (typeof this.height === 'number') {
        this.treeview.element.style.height = this.height + 'px';
      } else {
        this.treeview.element.style.height = this.height;
      }

      if (typeof this.width === 'number') {
        this.treeview.element.style.width = this.width + 'px';
      } else {
        this.treeview.element.style.width = this.width;
      }

      this.treeview.element.style.overflow = 'auto';
    }

    this.created.emit(event);
  }

  onDataBounded(event: DataBoundEventArgs) {
    this.dataBound.emit(event);
  }

  onDataSourceChanged(event: DataSourceChangedEventArgs) {
    this.dataSourceChanged.emit(event);
  }

  onDestroyed(event: any) {
    this.destroyed.emit(event);
  }

  onDrawNode(event: DrawNodeEventArgs) {
    // add onMouse event
    const raw = event.node.querySelector('.e-text-content') as HTMLElement;

    raw.addEventListener('mouseover', this.onMouseOver.bind(this));
    raw.addEventListener('mousedown', this.onMouseDown.bind(this));
    raw.addEventListener('mouseenter', this.onMouseEnter.bind(this));
    raw.addEventListener('mouseleave', this.onMouseLeave.bind(this));
    raw.addEventListener('mouseout', this.onMouseOut.bind(this));
    raw.addEventListener('mouseup', this.onMouseUp.bind(this));

    this.drawNode.emit(event);
  }

  onMouseOver(event: MouseEvent) {
    this.mouseOver.emit(event);
  }

  onMouseDown(event: MouseEvent) {
    this.mouseDown.emit(event);
  }

  onMouseEnter(event: MouseEvent) {
    this.mouseEnter.emit(event);
  }

  onMouseLeave(event: MouseEvent) {
    this.mouseLeave.emit(event);
  }

  onMouseOut(event: MouseEvent) {
    this.mouseOut.emit(event);
  }

  onMouseUp(event: MouseEvent) {
    this.mouseUp.emit(event);
  }

  onKeyPressed(event: NodeKeyPressEventArgs) {
    this.keyPress.emit(event);
  }

  private _addCheckedId(id: number) {
    const index = this.checkedIds.indexOf(id);
    if (index < 0) {
      this.checkedIds.push(id);
    }
  }

  private _removeCheckedId(id: number) {
    const index = this.checkedIds.indexOf(id);
    if (index > -1) {
      this.checkedIds = this.checkedIds.filter((id) => id != id);
    }
  }

  onNodeChecked(args: NodeCheckEventArgs) {
    const nodeId = parseInt(args.data[0].id as any, 10);
    if (args.action === 'check') {
      this._addCheckedId(nodeId);
    } else {
      this._removeCheckedId(nodeId);
    }
    this.nodeChecked.emit(args);
  }

  onNodeChecking(args: NodeCheckEventArgs) {
    const node = this.getNode(args.data[0].id);
    node.isChecking = true;

    this.nodeChecking.emit(args);
  }

  isNodeCheckable(node: any): boolean {
    return node.isChecking === undefined || !node.isChecking;
  }

  onNodeClicked(args: NodeClickEventArgs) {
    let ids: any[] = [];
    const currentNodeId = args.node.dataset.uid;
    if (this.allowMultiSelection && (args.event.ctrlKey || args.event.shiftKey)) {
      ids = this.treeview.selectedNodes;
    }

    if (!ids.some((id) => id === currentNodeId)) {
      ids.push(currentNodeId);
    }

    this.treeview.selectedNodes = ids;

    // check an element on select only if checkOnSelect is enabled and it's a right click
    if (this.checkOnSelect && args.event.button < 1) {
      const checkedNode: any = this.treeview.getNode(args.node);
      const node = this.getNode(checkedNode.id);

      if (!this.isNodeCheckable(node)) {
        delete node.isChecking;
        return;
      }

      if (checkedNode.isChecked === 'true') {
        this.treeview.uncheckAll([args.node]);
      } else {
        this.treeview.checkAll([args.node]);
      }
      delete node.isChecking;
    }

    this.nodeClicked.emit(args);
  }

  onNodeCollapsed(event: NodeExpandEventArgs) {
    this.nodeCollapsed.emit(event);
  }

  onNodeCollapsing(event: NodeExpandEventArgs) {
    this.nodeCollapsing.emit(event);
  }

  onNodeDragStart(event: DragAndDropEventArgs) {
    this.nodeDragStart.emit(event);
  }

  onNodeDragStop(event: DragAndDropEventArgs) {
    this.nodeDragStop.emit(event);
  }

  onNodeDragging(event: DragAndDropEventArgs) {
    this.nodeDragging.emit(event);
  }

  onNodeDropped(event: DragAndDropEventArgs) {
    this.nodeDropped.emit(event);
  }

  onNodeEdited(event: NodeEditEventArgs) {
    this.nodeEdited.emit(event);
  }

  onNodeEditing(event: NodeEditEventArgs) {
    this.nodeEditing.emit(event);
  }

  onNodeExpanded(event: NodeExpandEventArgs) {
    this.nodeExpanded.emit(event);
  }

  onNodeExpanding(event: NodeExpandEventArgs) {
    this.nodeExpanding.emit(event);
  }

  onNodeSelected(event: NodeSelectEventArgs) {
    this.nodeSelected.emit(event);
  }

  onNodeSelecting(event: NodeSelectEventArgs) {
    this.nodeSelecting.emit(event);
  }
  //#endregion

  clear() {
    if (!this.previousNodes) {
      this.previousNodes = this.nodes;
    }

    this.treeview.fields.dataSource = [];
    this.nodes = [];
  }

  reloadPreviousNodes() {
    this.reloadNodes(this.previousNodes);
  }

  reloadNodes(nodes: any[]) {
    function _findNode(id: number, targetedNodes: any[]): any {
      let node;

      targetedNodes.find((n: any) => {
        if (n.id === id) {
          node = n;
          return true;
        } else if (n.child) {
          node = _findNode(id, n.child);
          if (node) {
            return true;
          }
        }
      });

      return node;
    }
    this.clear();

    // build tree object in case of sub child
    const nodesToTree: any[] = [];
    nodes.forEach((node: any) => {
      if (node.idParent) {
        const parentNode = _findNode(node.idParent, nodesToTree);
        if (parentNode) {
          parentNode.child = parentNode.child || [];
          parentNode.child.push(node);
        }
      } else {
        nodesToTree.push(node);
      }
    });

    this.treeview.fields.dataSource = nodesToTree;
    this.treeview.refresh();
    this.addNodes(nodes);
  }

  addNodes(
    nodes: any[],
    idParentNode: number = 0,
    startIndex = undefined,
    preventOnExpand: boolean = false
  ) {
    const _fillParentToCheck = (idParent: any) => {
      const node = this.getNode(idParent);
      if (this.isChecked(idParent)) {
        idsParentToCheck.push('' + idParent);
      }

      if (node && node.idObjectParent) {
        _fillParentToCheck(node.idObjectParent);
      }
    };

    this.nodes = this.nodes.concat(nodes);

    const parentNode = this.treeview.getNode('' + idParentNode);
    if (parentNode) {
      parentNode.isChecked = this.isChecked(idParentNode);
    }

    nodes.forEach((n) => {
      n.isChecked = this.isChecked(n.id);
    });

    this.treeview.addNodes(nodes, idParentNode.toString(), startIndex, preventOnExpand);

    // fix unchecking parent nodes issue when we load it's children
    const idsParentToCheck: any[] = [];
    _fillParentToCheck(idParentNode);
    if (idsParentToCheck.length) {
      this.treeview.checkAll(idsParentToCheck);
    }
  }

  getNode(idNode: any) {
    return this.nodes.find((node) => node.id == idNode);
  }

  removeNode(id: number) {
    this.nodes = this.nodes.filter((node) => node.id !== id);
    this.treeview.removeNodes(['' + id]);
  }

  editNode(id: string) {
    this.treeview.beginEdit(id);
  }

  filterNodes(nodes: []) {
    this.treeview.fields = {
      dataSource: nodes,
    };
  }

  getCheckedNodes() {
    return this.checkedIds.map((id) => this.getNode(id) || id);
  }

  isChecked(id: number): boolean {
    const node = this.checkedIds.find((checkedId) => {
      if (checkedId == id) {
        return this.getNode(id);
      }
      return false;
    });
    return node !== null && node !== undefined;
  }

  setCheckedIds(ids: number[]) {
    this.checkedIds = ids;
    const idsToCheck = this.nodes.filter((n) => ids.indexOf(n.id) > -1).map((o) => '' + o.id);

    if (idsToCheck.length) {
      this.treeview.checkAll(idsToCheck);
    }
  }

  // context menu events
  onBeforeContextMenuOpen(args: BeforeOpenCloseMenuEventArgs) {}

  onContextMenuClick(args: MenuEventArgs) {
    const idClickedOption = args.item.id;
    const targetNodeId: string = this.treeview.selectedNodes[0];
  }
}
