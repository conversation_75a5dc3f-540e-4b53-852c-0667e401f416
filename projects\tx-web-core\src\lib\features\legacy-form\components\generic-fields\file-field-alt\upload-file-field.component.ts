import { HttpClient, HttpEventType, HttpHeaders } from '@angular/common/http';
import {
  Component,
  ElementRef,
  HostListener,
  Inject,
  Input,
  OnDestroy,
  OnInit,
  Optional,
  Self,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormGroup,
  NgControl,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';
import { MatFormField, MatFormFieldControl, MAT_FORM_FIELD } from '@angular/material/form-field';
import { faTreeChristmas } from '@fortawesome/pro-light-svg-icons';
import { LegacyTxFile } from '../../../services/structure/models/attribute';
import { TxFileService } from '../../../services/structure/services/file.service';
import { Observable, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { TxAttributeField } from '../../../models/formConfiguration/businessClass/attribute-field';
import { TxBaseFieldComponent } from '../base-field/base-field.component';
import { LegacyTxDataBaseAction } from '../../../services/structure/models/data';

@Component({
  selector: 'tx-upload-file-field',
  templateUrl: './upload-file-field.component.html',
  providers: [{ provide: MatFormFieldControl, useExisting: TxUploadFileFieldComponent }],
  styleUrls: ['./upload-file-field.component.scss'],
})
export class TxUploadFileFieldComponent
  extends TxBaseFieldComponent
  implements OnInit, MatFormFieldControl<LegacyTxFile[]>, ControlValueAccessor, OnDestroy
{
  @Input() apiUrl!: string;
  @Input() requiredFileType!: string;
  @Input() multiple!: boolean;
  @Input() maxMoFileSize: number = 40;
  @Input() field!: TxAttributeField;
  @Input() hideVisualisationToggle = false;

  @ViewChild('fileUpload') fileInput!: ElementRef;

  fileNameUploading = '';
  uploadProgress!: number;
  isDropzoneHovered = false;
  uploadSub!: Subscription;
  files: LegacyTxFile[] = [];
  filesShown: LegacyTxFile[] = [];
  idUploadedFiles: number[] = [];
  defaultView: boolean = true;
  nbChipsPerColumn = 4;

  constructor(
    private fileService: TxFileService,
    @Optional() @Inject(MAT_FORM_FIELD) public formField: MatFormField,
    @Optional() @Self() public ngControl: NgControl
  ) {
    super();
    if (this.ngControl != null) {
      this.ngControl.valueAccessor = this;
    }
  }
  writeValue(obj: any): void {}
  registerOnChange(fn: any): void {}
  registerOnTouched(fn: any): void {}
  setDisabledState?(isDisabled: boolean): void {}
  stateChanges!: Observable<void>;
  placeholder!: string;
  focused!: boolean;
  get empty(): boolean {
    return this.files.filter((f) => f.action < 3).length === 0;
  }
  shouldLabelFloat!: boolean;
  errorState!: boolean;
  controlType: string = 'tx-upload-file-field';
  autofilled?: boolean;
  userAriaDescribedBy?: string;
  setDescribedByIds(ids: string[]): void {}
  onContainerClick(event: MouseEvent): void {}

  ngOnInit(): void {
    if (this.control) {
      this.control.addValidators(this.noUploadInProgressValidator());
      if (this.required) {
        this.control.addValidators(this.isRequiredValidator());
      }
    }
    window.addEventListener('beforeunload', () => this.deleteUploadedFilesFromCache());
  }

  onFileSelected(event: any) {
    const files: FileList = (
      event.dataTransfer ? event.dataTransfer.files : event.target.files
    ) as FileList;

    if (files) {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (this.isValidFile(file)) {
          const txFile = this.addNewFile(file);
          if (txFile) {
            const upload$ = this.fileService
              .upload(file, this.field.attribute.id)
              .pipe(finalize(() => this.resetUpload(txFile)));
            txFile.uploadSub = upload$.subscribe(
              (event) => {
                if (event.type === HttpEventType.UploadProgress) {
                  txFile.uploadProgress = Math.round(100 * (event.loaded / event.total));
                }
                if (event.type === HttpEventType.Response) {
                  const fileId = event.body[0].id;
                  txFile.idArchivedFile = fileId;
                  txFile.view = this.defaultView;
                  txFile.name = event.body[0].name;
                  this.idUploadedFiles.push(Math.abs(fileId));
                }
              },
              (error) => {
                this.removeFile(txFile);
              }
            );
            this.control.updateValueAndValidity();
          }
        }
      }
      // if (files.length == 0 ){
      //   this.fileNameUploading = files[0].name;
      // }
      // else {
      //   this.fileNameUploading = 'files';
      // }
    }
  }

  public containsFiles(evt: any) {
    if (evt.dataTransfer) {
      if (evt.dataTransfer.types) {
        if (evt.dataTransfer.types.some((type: string) => type === 'Files')) {
          this.isDropzoneHovered = true;
        } else {
          this.isDropzoneHovered = false;
        }
      }
    }
  }

  onWheel(event: WheelEvent): void {
    if (this.isScrollable()) {
      let element: any = document
        .getElementById(this.label + 'ChipListContainer')
        ?.children.item(0)
        ?.children.item(0);
      element.scrollLeft += event.deltaY;
      event.preventDefault();
    }
  }

  isScrollable() {
    return this.getNbFiles() > this.nbChipsPerColumn * 2 - 1;
  }

  getFileIndex(file: File | LegacyTxFile): number {
    return this.files.findIndex((f) => f.name === file.name);
  }

  getNbFiles() {
    return this.filesShown.length;
  }

  fileExist(file: File | LegacyTxFile): boolean {
    return this.getFileIndex(file) > -1;
  }

  isHeadColumnFile(file: LegacyTxFile) {
    const index = this.filesShown.findIndex((f) => f.name === file.name);
    return index > 0 && index % this.nbChipsPerColumn == this.nbChipsPerColumn - 1;
  }

  addNewFile(file: File): LegacyTxFile | undefined {
    if (!this.fileExist(file) && (this.multiple || this.getNbFiles() < 1)) {
      const txFile = new LegacyTxFile(
        file.name,
        file.size,
        this.defaultView,
        undefined,
        LegacyTxDataBaseAction.Add,
        file
      );
      this.files = [txFile].concat(this.files);
      this.filesShown = [txFile].concat(this.filesShown);
      this.control.setValue(this.files);
      return txFile;
    }
  }

  addFiles(filesToAdd: LegacyTxFile[]) {
    filesToAdd.forEach((file) => {
      if (!this.fileExist(file) && (this.multiple || this.getNbFiles() < 1)) {
        file.action = LegacyTxDataBaseAction.None;
        this.files.push(file);
        this.filesShown.push(file);
        this.control.setValue(this.files);
      }
    });
  }

  removeFile(file: LegacyTxFile) {
    this.resetUpload(file);
    if (file.idArchivedFile == null || file.idArchivedFile < 0) {
      const index = this.getFileIndex(file);
      if (index > -1) {
        this.files.splice(index, 1);
      }
      this.removeFromUploadedFilesAndCache(file.idArchivedFile as number);
    } else {
      file.action = LegacyTxDataBaseAction.Delete;
    }

    const indexShown = this.filesShown.findIndex((f) => f.name === file.name);
    if (indexShown > -1) {
      this.filesShown.splice(indexShown, 1);
    }

    this.resetInput();
    this.control.updateValueAndValidity();
    this.control.setValue(this.files);
  }

  isValidFile(file: File) {
    return this.validFileSize(file);

    // Test file extention
  }

  validFileSize(file: File) {
    return file.size / (1024 * 1024) <= this.maxMoFileSize;
  }

  resetInput() {
    this.fileInput.nativeElement.value = '';
  }

  removeFromUploadedFilesAndCache(fileId: number) {
    const index = this.idUploadedFiles.indexOf(Math.abs(fileId));
    if (index > -1) {
      this.idUploadedFiles.splice(index, 1);
      this.fileService.deleteCache([Math.abs(fileId)]).subscribe();
    }
  }

  resetUpload(txFile: LegacyTxFile) {
    if (txFile.uploadProgress) {
      txFile.uploadSub?.unsubscribe();
    }
    txFile.uploadProgress = undefined;
    txFile.uploadSub = undefined;
    this.control.updateValueAndValidity();
  }

  ngOnDestroy(): void {
    this.deleteUploadedFilesFromCache();
  }

  deleteUploadedFilesFromCache() {
    if (this.idUploadedFiles.length > 0) {
      this.fileService.deleteCache(this.idUploadedFiles).subscribe();
    }
  }

  onDragLeave(event: any) {
    if (event.fromElement.classList.contains('generic-form-field')) {
      this.isDropzoneHovered = false;
    }
  }

  areAnyUploadInProgress() {
    return this.files.some((file) => file.uploadSub !== undefined && file.uploadSub !== null);
  }

  noUploadInProgressValidator(): ValidatorFn {
    return (): ValidationErrors | null => {
      const uploadInProgress = this.areAnyUploadInProgress();
      return uploadInProgress ? { UploadInProgress: { value: true } } : null;
    };
  }

  isFieldEmpty() {
    return this.files.filter((f) => f.action < 3).length === 0;
  }

  isRequiredValidator(): ValidatorFn {
    return (): ValidationErrors | null => {
      const isEmpty = this.isFieldEmpty();
      return isEmpty ? { empty: { value: true } } : null;
    };
  }
}
