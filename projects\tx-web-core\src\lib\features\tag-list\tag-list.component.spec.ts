import { ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { Component } from '@angular/core';
import { TagListComponent } from './tag-list.component';
import { ToastService } from '../../ui/toast';
import { ToastServiceMock } from '../../ui/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-host-component',
  template: `<tx-tag-list [tags]="tagstest" [tagListWidth]="300"></tx-tag-list>`,
})
class TestHostComponent {
  tagstest = ['tag1', 'tag2', 'tag3', 'tag4', 'tag5', 'tag6'];
}

describe('TagsListComponent', () => {
  let component: TagListComponent;
  let hostComponent: TestHostComponent;
  let hostFixture: ComponentFixture<TestHostComponent>;
  let toastService: ToastService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TestHostComponent],
      imports: [
        TagListComponent,
        FontAwesomeTestingModule,
        ClipboardModule,
        MatTooltipModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
      ],
      providers: [{ provide: ToastService, useClass: ToastServiceMock }],
    }).compileComponents();

    toastService = TestBed.inject(ToastService);
  });

  beforeEach(() => {
    hostFixture = TestBed.createComponent(TestHostComponent);
    hostComponent = hostFixture.componentInstance;
    component = hostFixture.debugElement.children[0].componentInstance;
  });

  it('should create', () => {
    hostFixture.detectChanges();
    expect(hostComponent).toBeTruthy();
    expect(component).toBeTruthy();
  });

  it('should properly init', () => {
    hostFixture.detectChanges();
    component.ngOnInit();
    expect(component.isTagListNeeded).toBe(true);
  });

  it('should scroll to right', fakeAsync(() => {
    component.tagList = {
      nativeElement: {
        scrollLeft: 20,
        scrollTo: jest.fn(),
      } as unknown as HTMLDivElement,
    };
    const spyScrollTo = jest.spyOn(component.tagList.nativeElement, 'scrollTo');
    component.scrollRight();
    hostFixture.detectChanges();
    expect(spyScrollTo).toHaveBeenCalledWith({ left: 140, behavior: 'smooth' });
  }));

  it('should create a toast from service', () => {
    hostFixture.detectChanges();
    const spyShow = jest.spyOn(toastService, 'show');
    component.copyTag();
    expect(spyShow).toHaveBeenCalledWith({
      templateContext: {
        test: { state: 'information', message: 'txWebCore.generic.tagCopy', progress: 0 },
      },
      type: 'information',
      description: 'txWebCore.generic.tagCopy',
      isPersistent: false,
      interval: 4000,
    });
  });
});
