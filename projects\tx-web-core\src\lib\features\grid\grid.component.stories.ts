import { applicationConfig, type Meta, type StoryObj } from '@storybook/angular';
import { importProvidersFrom } from '@angular/core';
import { MatDialogModule } from '@angular/material/dialog';
import { StorybookTranslateModule } from 'projects/tx-web-core/src/stories/storybook-translate-module';
import { TxGridComponent } from './grid.component';
const meta: Meta<TxGridComponent<{}>> = {
  component: TxGridComponent,
  title: 'Grid/TxGrid',
  tags: ['autodocs'],
  decorators: [
    applicationConfig({
      providers: [
        importProvidersFrom(MatDialogModule),
        importProvidersFrom(StorybookTranslateModule),
      ],
    }),
  ],
};
export default meta;
type Story = StoryObj<TxGridComponent<{id : number, name : string, login : string}>>;

export const Primary: Story = {
  // component doesn't display because the width is a defined as 0 
  args: {
    data : [{id : 1, name :"<PERSON>", login : "<PERSON><PERSON>" },
      {id : 2, name : "<PERSON><PERSON>", login : "SCHA"}
    ],
    columns:[{
          field: 'id',
          headerText:"Id",
          visible: false,
          isSearchable: true,
        },
        {
          field: 'name',
          headerText:"Name",
          sorting: true,
          headerTooltip: "Name",
          isSearchable: true,
        },
        {
          field: 'login',
          headerText: "Login",
          sorting: true,
          headerTooltip: "Login",
          isSearchable: true,
        },],
    primaryKey : 'id'
  },
};
