export enum LegacyTxObjectTypeType {
  Standard = 'ottStandard',
  User = 'ottUser',
  Source = 'ottSource',
  Information = 'ottInformation',
  Enumeration = 'ottEnumeration',
  Portal = 'ottPortal',
  Associativity = 'ottAssociativity',
}

export enum LegacyTxLockingType {
  Undefined = 'ltUndefined',
  None = 'ltNone',
  Auto = 'ltAuto',
  Manual = 'ltManual',
}

export class LegacyTxObjectType {
  public id: number;
  public name: string;
  public tags: string[];
  public description: string;
  public explanation: string;
  public idObjectTypeParent: number;
  public order: number;
  public icon: number;
  public isFolder: boolean;
  public type: LegacyTxObjectTypeType;
  public hasDistinctName: boolean;
  public isVisible: boolean;
  public lockingType: LegacyTxLockingType;
  public lockingDuration: number;
  public displayResultInTextSearch: boolean;

  constructor(attribute: LegacyTxObjectType) {
    this.id = attribute.id;
    this.name = attribute.name;
    this.tags = attribute.tags;
    this.description = attribute.description;
    this.explanation = attribute.explanation;
    this.idObjectTypeParent = attribute.idObjectTypeParent;
    this.order = attribute.order;
    this.icon = attribute.icon;
    this.isFolder = attribute.isFolder;
    this.type = attribute.type;
    this.hasDistinctName = attribute.hasDistinctName;
    this.isVisible = attribute.isVisible;
    this.lockingType = attribute.lockingType;
    this.lockingDuration = attribute.lockingDuration;
    this.displayResultInTextSearch = attribute.displayResultInTextSearch;
  }
}
