<div
  [formGroup]="form"
  class="numbers-input-container"
  (focusin)="onFocusIn($event)"
  (focusout)="onFocusOut($event)">
  <input
    #firstInput
    matInput
    color="accent"
    class="numbers-input-element"
    [style.width.px]="inputWidthPx"
    type="number"
    [formControl]="minFormControl"
    [placeholder]="minPlaceHolder"
    autocomplete="off"
    (keyup)="
      upperThanLower();
      betweenLowerAndUpper();
      checkBoundValue(minFormControl);
      onKeyPress.emit($event)
    " />
  <div *ngIf="withSecondInput || withThirdInput">
    <span (click)="focusFirstInput()" class="numbers-input-spacer">to</span>
    <input
      #secondInput
      matInput
      color="accent"
      class="numbers-input-element"
      [style.width.px]="inputWidthPx"
      type="number"
      [formControl]="maxFormControl"
      [placeholder]="maxPlaceHolder"
      autocomplete="off"
      (keyup)="
        upperThanLower();
        betweenLowerAndUpper();
        checkBoundValue(maxFormControl);
        onKeyPress.emit($event)
      " />
  </div>
  <div *ngIf="withThirdInput">
    <span (click)="focusSecondInput()" class="numbers-input-spacer"> / </span>
    <input
      #thirdInput
      matInput
      color="accent"
      class="numbers-input-element"
      [style.width.px]="inputWidthPx"
      [formControl]="meanFormControl"
      type="number"
      [placeholder]="meanPlaceHolder"
      autocomplete="off"
      (keyup)="betweenLowerAndUpper(); checkBoundValue(meanFormControl); onKeyPress.emit($event)" />
  </div>
</div>
