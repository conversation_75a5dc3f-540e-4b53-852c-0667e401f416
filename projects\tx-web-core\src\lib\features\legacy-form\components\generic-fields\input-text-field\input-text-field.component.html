<mat-form-field
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
  hideRequiredMarker
  #formField
  color="accent"
  class="form-field full-fields"
  [ngClass]="{ 'form-field-error': control && !control.valid && !disabled }"
  [hintLabel]="information">
  <mat-label
    class="form-label mat-form-label"
    [matTooltip]="labelTooltip"
    matTooltipClass="mat-label-tooltip mat-tooltip-multiline"
    matTooltipShowDelay="500"
    matTooltipPosition="above">
    {{ label }}
    <span
      *ngIf="required"
      [class]="
        control && control.hasError('required') ? 'span-error mat-form-label' : 'mat-form-label'
      ">
      *</span
    >
  </mat-label>
  <div *ngIf="!inTextArea">
    <input
      matInput
      #input
      color="accent"
      class="input-text"
      [value]="value"
      [formControl]="control"
      (focus)="onFocus()"
      (blur)="onBlur()"
      (keyup)="onFieldChange($event)"
      (keydown)="onKeyPress($event)"
      (input)="checkValue()"
      (focus)="focusState = true"
      (focusout)="focusState = false"
      autocomplete="off" />
  </div>
  <div *ngIf="inTextArea">
    <textarea
      matInput
      #input
      color="accent"
      [formControl]="control"
      [maxLength]="maxLength || 200"
      [style.height.px]="textAreaHeight"
      (keyup)="onFieldChange($event)"
      (keydown)="onKeyPress($event)"
      (input)="checkValue()"
      >{{ value }}</textarea
    >
  </div>

  <mat-icon *ngIf="icon" matSuffix [fontIcon]="'fas ' + icon"></mat-icon>
  <button
    mat-button
    *ngIf="mouseInFormField && control.value && !disabled && !inTextArea"
    matSuffix
    mat-icon-button
    aria-label="Clear"
    (click)="onClearButtonClicked()">
    <mat-icon>close</mat-icon>
  </button>
  <mat-hint *ngIf="withMaxLength" align="end"
    >{{ input.nativeElement.value?.length || 0 }}/{{ maxLength }}</mat-hint
  >
  <mat-error *ngIf="control && control.hasError('required')"><strong>Required</strong></mat-error>
</mat-form-field>
