import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { SizeProp } from '@fortawesome/fontawesome-svg-core';
import { TxCopyIconComponent } from './copy-icon.component';
import {
  TxEscapeHtmlPipe,
  TxHighlightSearchPipe,
} from '@bassetti-group/tx-web-core/src/lib/utilities';

@Component({
  standalone: true,
  imports: [
    CommonModule,
    MatTooltipModule,
    TxCopyIconComponent,
    TxEscapeHtmlPipe,
    TxHighlightSearchPipe,
  ],
  selector: 'tx-texts-to-copy',
  templateUrl: './texts-to-copy.component.html',
  styleUrls: ['./texts-to-copy.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TxTextsToCopyComponent {
  @Input() searchValue: string | undefined;
  private _formattedTexts: string[] = [];
  get formattedTexts() {
    return this._formattedTexts.filter((text) => text !== '');
  }
  @Input() iconSize: SizeProp = 'lg';
  @Input() set texts(values: string[] | string) {
    if (Array.isArray(values)) {
      this._formattedTexts = values;
    } else {
      this._formattedTexts = values.split('|');
    }
  }
}
