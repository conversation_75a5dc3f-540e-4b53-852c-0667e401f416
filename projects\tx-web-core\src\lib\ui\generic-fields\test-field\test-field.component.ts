import { Component, OnInit } from '@angular/core';
import { FormArray, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TxUnit } from '../../../features/legacy-form';
import { MinMaxMean, MinMax } from '../input-number/models';
import { ChipsService } from '../chips-field/chips.service';
import { TxChip, chipsOptions } from '../chips-field/models';
import { tap } from 'rxjs';
import { TxInputMinMaxMeanComponent } from '../input-number/input-min-max-mean.component';
import { TxInputTextFieldComponent } from '../input-text-field/input-text-field.component';
import { TxChipsFieldComponent } from '../chips-field/chips-field.component';
import { TxDataBaseAction, TxDataFile } from '@bassetti-group/tx-web-core/src/lib/business-models';

@Component({
  selector: 'tx-test-field',
  templateUrl: './test-field.component.html',
  styleUrls: ['./test-field.component.scss'],
  standalone: true,
  providers: [ChipsService],
  imports: [
    TxInputMinMaxMeanComponent,
    TxInputTextFieldComponent,
    TxChipsFieldComponent,
    ReactiveFormsModule,
  ],
})
export class TestFieldComponent implements OnInit {
  controlTxt = new FormControl('');
  form = new FormGroup({});
  array = new FormArray([]);
  controlChips = new FormControl<TxChip[]>([]);
  controlNumMMM = new FormControl<MinMaxMean>({
    min: null,
    max: null,
    mean: null,
    unit: null,
  });
  controlNumMM = new FormControl<MinMax>({ min: null, max: null, unit: null });
  units = [
    new TxUnit({ name: 'm', id: 1 }),
    new TxUnit({ name: 'cm', id: 2 }),
    new TxUnit({ name: 'km', id: 3 }),
  ];
  chips = [{ name: 'test1' }, { name: 'test2' }, { name: 'test3' }, { name: 'test4' }];
  data: TxDataFile = new TxDataFile(1, 1, [], TxDataBaseAction.Add);

  constructor(private chipsService: ChipsService) {}
  async ngOnInit(): Promise<void> {
    const chips = await this.chipsService.chipsFromData(
      chipsOptions({ selectable: true }),
      this.data
    );
    this.controlChips.setValue(chips);
    this.controlChips.valueChanges
      .pipe(
        tap((chip) => {
          console.log('controlChips', chip);
        })
      )
      .subscribe();
  }
  displayPaneEvent(event: any) {
    console.log('display pane event');
  }

  chipClick(event: any) {
    console.log('chipClick');
  }

  actionIconClick(event: any) {
    console.log('action icon click');
  }
}
