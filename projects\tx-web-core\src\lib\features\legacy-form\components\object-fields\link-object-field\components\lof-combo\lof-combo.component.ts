import { TxTreeObjectsComponent } from '../../../../../trees/tree-objects/tree-objects.component';
import { map } from 'rxjs/operators';
import { LegacyTxObjectsService } from '../../../../../services/structure/services/objects.service';
import { TreeViewComponent } from '@syncfusion/ej2-angular-navigations';
import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { Observable } from 'rxjs/internal/Observable';
import { TxLinkObjectFieldService } from '../../services/link-object-field.service';
import { FormControl } from '@angular/forms';
import { TxDropdownListFieldComponent } from '../../../../generic-fields/dropdown-list-field/dropdown-list-field.component';
import { LegacyTxObject } from '../../../../../services/structure/models/object';
import { _LegacyStringUtils } from '../../../../../utilities/legacy-string-utils';
import { _ArrayUtils } from '../../../../../utilities/legacy-array-utils';

@Component({
  selector: 'tx-lof-combo',
  templateUrl: './lof-combo.component.html',
  styleUrls: ['./lof-combo.component.scss'],
})
export class TxLofComboComponent {
  @Input() label!: string;
  @Input() labelToolTip!: string;
  @Input() showCheckBox = true;
  @Input() height = '250px';
  @Input() placeholder = 'Search';
  @Input() allowFiltering = true;
  @Input() popupHeight = 220;
  @Input() selectedObjects: LegacyTxObject[] = [];
  @Input() checkedIds: string[] = [];
  @Input() idObjectType = 0;
  @Input() idFilteringObject = 0;
  @Input() displayIconOption = true;
  @Input() filteringWithRequest = false;
  @Input() txCheckedObjects: LegacyTxObject[] = [];
  @Input() mode = 'Delimiter';
  @Input() imagePath = '/assets/tx-web-core/imgs/objectTypesIcons/';
  @Input() floatLabelType = 'Auto'; // Auto, Always, Never
  @Input() sortedBy = 'order';
  @Input() treeMode = true;
  @Input() mainIcon!: string;
  @Input() required: boolean = false;
  @Input() disabled: boolean = false;
  @Input() control!: FormControl;
  @Input() labelTooltip!: string;

  @Output() change = new EventEmitter();
  @Output() select = new EventEmitter();
  @Output() valueChange = new EventEmitter();
  @Output() beforeOpen = new EventEmitter();

  @ViewChild('dropDownListField')
  dropDownListField!: TxDropdownListFieldComponent;
  public spanNoData!: HTMLElement;
  public searchTree!: TxTreeObjectsComponent;

  treeview!: TreeViewComponent;
  uniqueId: string = _LegacyStringUtils.getUniqueId();
  rootNodesLoaded: boolean = false;

  public options: { [key: string]: Object }[] = [];

  @Input() field = {
    dataSource: this.options,
    value: 'id',
    text: 'name',
    child: 'child',
    hasChildren: 'isParent',
    iconCss: 'icon',
    imageUrl: 'image',
  };

  txObjects: LegacyTxObject[] = this.txCheckedObjects;

  constructor(
    public objectsServices: LegacyTxObjectsService,
    public linkObjectFieldService: TxLinkObjectFieldService
  ) {}

  onBeforeOpen() {
    this.beforeOpen.emit();
  }

  onValueChange(event: any) {
    this.valueChange.emit(event);
  }

  /**
   * Request(s)
   */
  /**
   * Load objects
   * @param idObjectParent
   * @returns
   */
  loadObjects(idObjectParent: number): Observable<any[]> {
    return this.objectsServices
      .listObjects(this.idObjectType, idObjectParent, !this.treeMode, this.treeMode, false, 0)
      .pipe(
        map((objects) => {
          if (this.sortedBy != 'order') {
            switch (this.sortedBy) {
              case 'name':
                objects = _ArrayUtils.sortByName(objects);
                break;
              case 'id':
                objects = _ArrayUtils.sortById(objects);
                break;
            }
          }

          objects = objects.filter((o) => !o.isFolder || (o.isFolder && o.isParent));

          const options = objects.map((txObject) => {
            return this.linkObjectFieldService.createOption(
              txObject,
              !this.treeMode,
              this.displayIconOption
            );
          });

          this.txObjects = this.txObjects.concat(objects);
          return options;
        })
      );
  }

  loadObjOnBeforeOpen(idFilteringObject: number) {
    this.loadObjects(idFilteringObject).subscribe((options) => {
      this.dropDownListField.setLoadedObjOnBeforeOpen(options);
    });
  }

  loadObjOnExpandingNodes(parentNode: any) {
    this.loadObjects(parentNode.id).subscribe((options) => {
      this.dropDownListField.setLoadedObjOnExpandingNodes(parentNode, options);
    });
  }

  searchObjects(searchedValue: string): Observable<any[]> {
    return this.objectsServices
      .searchObjects(searchedValue, undefined, undefined, this.idObjectType, true, false)
      .pipe(
        map((objects) => {
          objects = objects.filter((o) => !o.isFolder);
          if (this.sortedBy != 'order') {
            switch (this.sortedBy) {
              case 'name':
                objects = _ArrayUtils.sortByName(objects);
                break;
              case 'id':
                objects = _ArrayUtils.sortById(objects);
                break;
            }
          }

          return objects;
        })
      );
  }

  searchOptions(searchedValue: string): Observable<any[]> {
    return this.objectsServices
      .searchObjects(searchedValue, undefined, undefined, this.idObjectType)
      .pipe(
        map((objects) => {
          objects = objects.filter((o) => !o.isFolder);

          if (this.sortedBy != 'order') {
            switch (this.sortedBy) {
              case 'name':
                objects = _ArrayUtils.sortByName(objects);
                break;
              case 'id':
                objects = _ArrayUtils.sortById(objects);
                break;
            }
          }

          const options = this.linkObjectFieldService.createOptions(objects, true);
          return options;
        })
      );
  }
}
