import { TestBed } from '@angular/core/testing';
import { CoreModelsImportHttpService } from './core-models-import.http.service';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TxConfigService, MOCK_TX_CONFIG_SERVICE } from '@bassetti-group/tx-web-core';
import { MockProvider } from 'ng-mocks';
import {
  IMPORT_HISTORY,
  VALID_ARCHIVE_FILE,
  ARCHIVE_SUMMARY,
  CONCEPT_DTO,
  IMPORT_CONCEPT_DTO,
} from './core-models-import.http.service.mock';
import {
  CoreModelImportHistoryDTO,
  CoreModelsArchiveSummaryDTO,
  ImportedDTO,
  TestedImportDTO,
} from '../models/core-models-import.dto';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

describe('CoreModelsImportHttpService', () => {
  let service: CoreModelsImportHttpService;
  let httpTestingController: HttpTestingController;
  beforeEach(() => {
    TestBed.configureTestingModule({
    imports: [],
    providers: [
        CoreModelsImportHttpService,
        MockProvider(TxConfigService, MOCK_TX_CONFIG_SERVICE, 'useValue'),
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
    ]
});
    service = TestBed.inject(CoreModelsImportHttpService);
    httpTestingController = TestBed.inject(HttpTestingController);
  });

  it('CoreModelsImportHttpService', () => {
    expect(service).toBeTruthy();
  });
  it('Should load history', () => {
    service.loadHistory().subscribe((history) => {
      expect(history).toStrictEqual<CoreModelImportHistoryDTO['importHistory']>(IMPORT_HISTORY);
    });
    const testReq = httpTestingController.expectOne(
      'https://localhost:44336/api/CoreModel/import/history'
    );
    testReq.flush(IMPORT_HISTORY);
  });
  it('Should validate archive', () => {
    service.validateArchive(VALID_ARCHIVE_FILE).subscribe((archive) => {
      expect(archive).toStrictEqual<CoreModelsArchiveSummaryDTO>(ARCHIVE_SUMMARY);
    });
  });
  it('Should test import', () => {
    service.testImport(VALID_ARCHIVE_FILE).subscribe((concept) => {
      expect(concept).toStrictEqual<TestedImportDTO>(CONCEPT_DTO);
    });
  });
  it('Should import ', () => {
    service.import(VALID_ARCHIVE_FILE).subscribe((concept) => {
      expect(concept).toStrictEqual<ImportedDTO>(IMPORT_CONCEPT_DTO);
    });
  });
});
