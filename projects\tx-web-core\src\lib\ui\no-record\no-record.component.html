<div class="no-record">
  <fa-icon class="no-record__icon" [icon]="['fal', icon]" size="lg"></fa-icon>
  <span class="no-record__text-placeholder">
    <div>{{ noRecordText | translate }}</div>
    <div *ngIf="additionalText">{{ additionalText | translate }}</div>
  </span>
  <button mat-flat-button *ngIf="displayAction" color="accent" (click)="clickAction.emit()">
    {{ 'txWebCore.generic.modify' | translate }}
  </button>
</div>
