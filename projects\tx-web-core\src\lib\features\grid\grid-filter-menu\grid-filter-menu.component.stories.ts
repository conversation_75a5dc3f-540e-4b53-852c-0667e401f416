import type { Meta, StoryObj } from '@storybook/angular';
import { TxGridFilterMenuComponent } from './grid-filter-menu.component';
import {
  DataBaseRights,
  TxLockingType,
  TxObjectTypeType,
} from '@bassetti-group/tx-web-core/src/lib/business-models';

const meta: Meta<TxGridFilterMenuComponent> = {
  component: TxGridFilterMenuComponent,
  title: 'GridFilterMenuComponent',
  tags: ['autodocs'],
};
export default meta;
type Story = StoryObj<TxGridFilterMenuComponent>;

export const Primary: Story = {
  args: {
    disabled: false,
    columns: [
      { field: 'Name', headerText: 'Name', type: 'string' },
      { field: 'Tags', headerText: 'Tags', type: 'string' },
      { field: 'Type', headerText: 'Type', type: 'string' },
      { field: 'ID', headerText: 'ID', type: 'string' },
    ] as any[],
    optionsForListOptionCol: [],
    newFilters: [
      {
        column: { field: 'Name' },
        operator: 'operator',
        value: [
          {
            icon: 1,
            isFolder: false,
            type: TxObjectTypeType.Standard,
            hasDistinctName: true,
            isVisible: true,
            lockingType: TxLockingType.Auto,
            lockingDuration: 2,
            displayResultInTextSearch: true,
            right: DataBaseRights.DbrRead,
            order: 1,
            id: 1,
            name: 'name test',
            tags: [],
          },
        ],
      },
    ],
  },
};
