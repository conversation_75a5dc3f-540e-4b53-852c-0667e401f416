import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxCopyIconComponent } from '../copy-icon.component';
import { ToastService } from '../../toast';
import { ToastServiceMock } from '../../../ui/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { TRANSLATIONS_MOCK } from '../../../utilities';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';

describe('CopyIconComponent', () => {
  let component: TxCopyIconComponent;
  let fixture: ComponentFixture<TxCopyIconComponent>;
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TxCopyIconComponent,
        FontAwesomeTestingModule,
        TranslateTestingModule.withTranslations(TRANSLATIONS_MOCK),
      ],
      providers: [{ provide: ToastService, useClass: ToastServiceMock }],
    }).compileComponents();
    fixture = TestBed.createComponent(TxCopyIconComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
