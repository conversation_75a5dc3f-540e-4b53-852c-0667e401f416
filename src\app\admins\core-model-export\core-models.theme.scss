@use '@angular/material' as mat;

@mixin core-models-theme($theme) {
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);

  .is-in-error {
    background-color: mat.m2-get-color-from-palette($background, pastel-red);
    color: mat.m2-get-color-from-palette($foreground, base);
  }
  .e-grid td.e-active.is-in-error,
  .is-in-error {
    background-color: mat.m2-get-color-from-palette($background, pastel-red);
    color: mat.m2-get-color-from-palette($foreground, base);
  }
  .e-grid td.e-active.is-ok,
  .is-ok {
    background-color: mat.m2-get-color-from-palette($background, pastel-green);
    color: mat.m2-get-color-from-palette($foreground, base);
  }
  app-core-models-export .mat-mdc-tab-body-content {
    overflow: hidden;
  }
}
