import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import {
  DataBaseRights,
  TxContextMenuEventArgs,
  TxContextMenuItem,
  TxObjectsService,
  TxTreeViewComponent,
} from '@bassetti-group/tx-web-core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateService } from '@ngx-translate/core';
import {
  BeforeOpenCloseMenuEventArgs,
  ContextMenuComponent,
  DragAndDropEventArgs,
  NodeClickEventArgs,
  NodeEditEventArgs,
  NodeExpandEventArgs,
  NodeSelectEventArgs,
} from '@syncfusion/ej2-angular-navigations';
import { ResourcesService } from 'src/app/admins/file-manager/services/resources.service';
import { FilesUtils } from 'src/app/core/utils/files';
import {
  FileDescription,
  FileItem,
  FileItemType,
  FileTree,
  FilesManager,
  FilesState,
  MoveFileParams,
} from '../../models/file-models';
import { FileManagerService } from '../../services/file-manager.service';
import { FileManagerComponent } from '../file-manager.component';

@Component({
  selector: 'app-file-tree',
  templateUrl: './file-tree.component.html',
  styleUrls: ['./file-tree.component.scss'],
})
export class FileTreeComponent implements OnInit, OnChanges {
  @ViewChild('fileTree') public fileTree: TxTreeViewComponent<FileTree> | undefined;
  @ViewChild('contentMenuTree') contentMenuTree: ContextMenuComponent | undefined;
  @ViewChild('containerTree') containerTree: ElementRef | undefined;
  @Input() isLangLoading = true;
  @Input() dragExternalFile = {
    active: false,
    type: 0,
    message: '',
    node: '',
    emplacement: '',
  };
  @Input() isGridUpdatable = true;
  @Output() setChild = new EventEmitter<{ node: FileTree; children?: FileDescription[] }>();
  @Output() setFileAfterSuccessMovingFromTree = new EventEmitter<{
    destinationFolder: FileTree;
    file: FileDescription;
    parentNodeMoved: FileTree;
    nodeMoved: FileTree;
  }>();
  @Output() selectNodeInTree = new EventEmitter();
  @Output() deleteFakeNodeFromTree = new EventEmitter();
  @Output() activateLoaderGridFromTree = new EventEmitter();
  @Output() disableLoaderGridFromTree = new EventEmitter();
  @Output() showFromTree = new EventEmitter<{
    fileName: string;
    newName: string;
    path: string;
    alias: string;
    node?: FileTree;
    selectedRowGrid?: FileDescription;
  }>();
  @Output() openHistoryFromTree = new EventEmitter<{
    alias: string;
    fileName: string;
    path: string;
  }>();
  @Output() renameItemFromTree = new EventEmitter<{
    oldName: string;
    newName: string;
    folder: FileTree;
    path: string;
    alias: string;
    rowGrid?: FileDescription;
  }>();
  @Output() indicatorHidden = new EventEmitter();
  @Output() indicatorShow = new EventEmitter();
  @Output() displayDialog = new EventEmitter();
  @Output() displayDialogReplaceFile = new EventEmitter();
  @Output() createFolderFromTree = new EventEmitter();
  @Output() addNewFolderFromTree = new EventEmitter();
  @Output() folderDeleteInTree = new EventEmitter<{
    parentNode: FileTree | undefined;
    childNodeToDelete: FileTree;
  }>();
  @Output() getFilesInFolderFromTree = new EventEmitter<{ treeNode: FileTree; files: any }>();
  @Output() currentFileHistoryChange = new EventEmitter<any>();
  @Output() updateFilesInFolder = new EventEmitter<any>();
  @Output() dropToTree = new EventEmitter<any>();

  onCellDragStop = (event: any): void => {
    this.dropToTree.emit(event);
  };

  // eslint-disable-next-line @typescript-eslint/member-ordering
  public nbFilesMoved = 0;
  public nbFilesTreated = 0;
  public menuItems: TxContextMenuItem[] = [];
  public disableMenuItems: string[] = [];

  //defined the array of data
  public field: { dataSource: FileTree[]; id: string; text: string; child: string } | undefined;

  public treeAnimation = {
    expand: { effect: 'SlideDown', duration: 200, easing: 'linear' },
    collapse: { effect: 'SlideUp', duration: 200, easing: 'linear' },
  };

  private _treeData: FileTree[] = [];
  private _filesInFolder: FileDescription[] = [];

  constructor(
    private readonly fileManagerService: FileManagerService,
    private readonly fileManagerComponent: FileManagerComponent,
    private readonly resourcesService: ResourcesService,
    private readonly translate: TranslateService,
    private readonly objService: TxObjectsService
  ) {}

  get filesInFolder(): FileDescription[] {
    return this._filesInFolder;
  }
  @Input() set filesInFolder(filesInFolder: FileDescription[] | undefined) {
    if (filesInFolder) {
      this._filesInFolder = filesInFolder;
    }
  }
  // eslint-disable-next-line @typescript-eslint/member-ordering
  get treeData(): FileTree[] {
    return this._treeData;
  }
  @Input() set treeData(treeData: FileTree[] | undefined) {
    if (treeData) {
      this._treeData = treeData;
      console.log(treeData);
      this.field = { dataSource: treeData, id: 'id', text: 'name', child: 'children' };
      if (this.fileTree) {
        this.refreshTree();
      }
    }
  }

  ngOnInit(): void {
    this.translate.onLangChange.subscribe(() => {
      this.menuItems = this.getMenuItemsTree();
    });
    this.menuItems = this.getMenuItemsTree();
  }
  ngOnChanges(changes: SimpleChanges) {
    if (changes.changedSelectedNodes?.currentValue && this.fileTree) {
      // this.fileTree.selectedNodes = changes.changedSelectedNodes.currentValue;
    }
  }
  fileDropTree(event: DragEvent) {
    const target = event.target;
    const files = event.dataTransfer?.files;
    const targetAncestor = (target as Element | null)?.closest('.e-list-item');
    let node:
      | {
          [key: string]: unknown;
        }
      | undefined;
    if (targetAncestor) {
      // node = this.fileTree?.getNode(targetAncestor);
    }
    const treeNode =
      this.dragExternalFile.type === 1
        ? this.fileManagerService.findNode(node?.id as string, this.treeData) // folder
        : this.fileManagerService.findParentNode(node?.id as string, this.treeData); // leaf

    if (this.fileTree && treeNode) {
      // this.fileTree.selectedNodes = [treeNode.id];
    }
    this.fileManagerService.selectedNode = treeNode;
    if (this.isGridUpdatable && treeNode) {
      this.getFilesInFolderFromTree.emit({ treeNode, files });
    }
  }
  hideIndicator(event: DragEvent) {
    this.indicatorHidden.emit(event);
  }
  showIndicator(event: DragEvent) {
    this.indicatorShow.emit(event);
  }

  renameWithNameInTree(data: { name: string; node: FileTree }): void {
    // data.node.name = data.name;
    // data.node.resource.alias = data.name;
    // this.fileTree?.updateNode(data.node.id, data.name);
  }
  getMenuItemsTree(): TxContextMenuItem[] {
    return [
      { label: this.translate.instant(_('button.add')), id: 'add' },
      { label: this.translate.instant(_('admins.resources.newFolder')), id: 'newFolder' },
      { label: this.translate.instant(_('button.delete')), id: 'delete' },
      { label: this.translate.instant(_('admins.resources.rename')), id: 'rename' },
      { label: this.translate.instant(_('admins.resources.history')), id: 'history' },
      { label: this.translate.instant(_('admins.resources.download')), id: 'download' },
    ];
  }
  deleteFileInTree(): void {
    if (this.fileManagerService.selectedNode) {
      const deletedNode: FileTree = { ...this.fileManagerService.selectedNode };
      let message = '';
      if (deletedNode.isFolder) {
        message = this.translate.instant(_('admins.resources.folderDelete'), {
          name: deletedNode.name,
        });
      } else {
        message = this.translate.instant(_('admins.resources.fileDelete'), {
          name: deletedNode.name,
        });
      }
      this.displayDialog.emit({
        message,
        annotation: null,
        object: {
          files: deletedNode,
          isDeletingFromTree: true,
          parentNode: this.fileManagerService.findParentNode(deletedNode.id, this.treeData),
        },
      });
    }
  }

  public menuClick(args: TxContextMenuEventArgs) {
    if (args.item.label === this.translate.instant(_('button.delete'))) {
      this.deleteFileInTree();
      this.updateFilesInFolder.emit([]);
    } else if (
      args.item.label === this.translate.instant(_('admins.resources.rename')) &&
      this.fileManagerService.selectedNode
    ) {
      // this.fileTree?.beginEdit(this.fileManagerService.selectedNode.id);
    } else if (args.item.label === this.translate.instant(_('button.add'))) {
      this.addNewFolderFromTree.emit();
    } else if (args.item.label === this.translate.instant(_('admins.resources.newFolder'))) {
      this.createFolderFromTree.emit(this.filesInFolder);
    } else if (args.item.label === this.translate.instant(_('admins.resources.history'))) {
      this.openHistoryInTree();
    } else if (args.item.label === this.translate.instant(_('admins.resources.download'))) {
      this.downloadParentDirectory();
    }
  }

  downloadParentDirectory() {
    const selectedNode = this.fileManagerService.selectedNode;
    const itemToDownload: FileItem = {
      alias: this.fileManagerService.getAlias(selectedNode?.id),
      itemName: '',
      path: selectedNode ? this.fileManagerService.findPath(selectedNode) : '',
    };
    const toast = this.fileManagerService.createNotification(
      'loading',
      this.translate.instant(_('admins.resources.downloadingFolder'), { name: selectedNode?.name }),
      false,
      0
    );
    this.resourcesService.onDownloadingFolder(itemToDownload).subscribe({
      next: (blob) => {
        if (selectedNode) {
          FilesUtils.downloadBlobFile(blob, selectedNode.name, FileItemType.directory);
          this.fileManagerService.afterDownloadFolder(selectedNode.name, toast);
        }
      },
      error: () => {
        this.fileManagerService.afterErrorDownloadFile(toast);
      },
    });
  }

  createNode(file: File | FileDescription, selectedNode: FileTree, isFolder: boolean): void {
    const originalNode = this.fileManagerService.findNode(selectedNode.id, this.treeData);
    let childNode: FileTree | undefined;
    if (originalNode?.children) {
      childNode = originalNode.children.find((child) => child.name === file.name);
    } else if (originalNode && !originalNode?.children) {
      originalNode.children = [];
    }
    // if the file correspond to a children of the selected node : update this child
    if (childNode) {
      childNode.isTemp = false;
    } else if (originalNode) {
      const nodeRight = this.fileManagerService.getRight(originalNode.id);
      originalNode.children = originalNode.children?.concat(
        this.fileManagerService.formatTreeData(
          [file].map((newFile) => ({
            hasSubFolders: false,
            alias: newFile.name,
            right: nodeRight,
            isFolder,
            isTemp: false,
          })),
          false
        )
      );
    }
    this.refreshTree();
  }
  // To delete when the bug of the API has been fixed :
  // (Bug : we can only expand the nodes of the tree if the first one is expanded (= if the first has children))
  public createFakeNode(node: FileTree | undefined): void {
    if (node && !node?.children) {
      node.children = [
        {
          id: '-1',
          name: 'test',
          isFolder: false,
          resource: this.treeData[0].resource,
          expanded: false,
        },
      ];
      this.refreshTree();
    }
  }

  getTreeIcon(data: FileTree): string {
    if (data.isFolder) {
      return data.expanded ? 'folder-open' : 'folder';
    } else {
      return 'file';
    }
  }

  public beforeOpen(args: BeforeOpenCloseMenuEventArgs) {
    this.disableMenuItems = [];
    // focus the element click
    const listElement = (args.event.target as HTMLElement).closest('li.e-list-item');
    if (listElement) {
      this.onNodeSelected({
        action: 'select',
        cancel: false,
        isInteracted: false,
        node: undefined,
        nodeData: { id: listElement.getAttribute('data-uid') },
      } as unknown as NodeSelectEventArgs);
    }

    if (!this.canDeleteFileInTree()) {
      this.disableMenuItems = [...this.disableMenuItems, 'delete'];
    }
    if (!this.canAddFileInTreeOrGrid()) {
      this.disableMenuItems = [...this.disableMenuItems, 'add'];
    }
    if (!this.canRenameFileInTree()) {
      this.disableMenuItems = [...this.disableMenuItems, 'rename'];
    }
    if (!this.canCreateFolderInTree()) {
      this.disableMenuItems = [...this.disableMenuItems, 'newFolder'];
    }
  }
  canDeleteFileInTree(): boolean {
    if (!this.fileManagerService.selectedNode) {
      return false;
    }
    const parentNode = this.fileManagerService.findParentNode(
      this.fileManagerService.selectedNode.id,
      this.treeData
    );
    return (parentNode && parentNode.resource.right === DataBaseRights.DbrStructure) ?? false;
  }

  canAddFileInTreeOrGrid(): boolean {
    if (!this.fileManagerService.selectedNode) {
      return false;
    }
    let node: FileTree | undefined = this.fileManagerService.selectedNode;
    if (!this.fileManagerService.selectedNode.isFolder) {
      node = this.fileManagerService.findParentNode(
        this.fileManagerService.selectedNode.id,
        this.treeData
      );
    }
    return (node && node.resource.right === DataBaseRights.DbrStructure) ?? false;
  }

  canCreateFolderInTree(): boolean {
    if (!this.fileManagerService.selectedNode) {
      return false;
    }
    return this.fileManagerService.selectedNode.resource.right === DataBaseRights.DbrStructure;
  }

  canRenameFileInTree(): boolean {
    if (
      !this.fileManagerService.selectedNode ||
      !this.fileManagerService.findParentNode(
        this.fileManagerService.selectedNode.id,
        this.treeData
      )
    ) {
      return false;
    }
    return (
      this.fileManagerService.selectedNode.resource.right === DataBaseRights.DbrStructure ||
      this.fileManagerService.selectedNode.resource.right === DataBaseRights.DbrWrite
    );
  }

  onNodeDragStart(event: DragAndDropEventArgs): void {
    if (event.draggedNodeData) {
      const nodeMoved = this.fileManagerService.findNode(
        event.draggedNodeData.id as string,
        this.treeData
      );
      if (nodeMoved?.resource.right !== DataBaseRights.DbrStructure) {
        event.cancel = true;
      }
    }
  }

  onNodeDrag(event: DragAndDropEventArgs): void {
    this.hideIndicator(event as unknown as DragEvent);

    if (event.draggedNodeData) {
      const nodeMoved = this.fileManagerService.findNode(
        event.draggedNodeData.id as string,
        this.treeData
      );
      if (nodeMoved?.resource.right !== DataBaseRights.DbrStructure) {
        event.cancel = true;
      }
    }
    if (event.droppedNodeData === null || event.position !== 'Inside') {
      event.dropIndicator = 'e-no-drop';
    } else {
      event.droppedNode.children.item(1)?.classList.add('border-accent-dashed');
    }
  }
  controlFilesTreeToDrag(
    files: FileTree[],
    selectedNode: FileTree,
    filesInFolder: FileDescription[]
  ): FilesState {
    const result = {
      goodFiles: [] as FileDescription[],
      sameFiles: [] as FileDescription[],
      existingFiles: [] as FileDescription[],
    };

    // iterate over each files selected by user to upload
    Array.from(files).forEach((file) => {
      // if addition and modification is not allowed
      if (selectedNode.resource.right !== DataBaseRights.DbrStructure) {
        this.fileManagerService.createNotification(
          'error',
          this.translate.instant(_('admins.resources.fileAddAndModifNotAllow')),
          false,
          8000
        );
        return;
      }
      if (file.resource.size === undefined) {
        throw new Error('file.resource.size === undefined');
      }
      const newFile: FileDescription = {
        name: file.name,
        type: FileItemType.directory,
        lastWriteTime: file.resource.lastModification,
        extension: '',
        length: file.resource.size,
      };
      const existingRowGrid = filesInFolder.find((child) => child.name === file.name);
      if (existingRowGrid) {
        result.existingFiles.push(existingRowGrid);
        result.sameFiles.push(newFile);
      } else {
        result.goodFiles.push(newFile);
      }
    });
    return result;
  }

  onNodeDragStop(event: DragAndDropEventArgs): void {
    // Cancel the event, to avoid the movement animation implemented by syncfusion on the tree :
    event.cancel = true; // do not delete this line
    this.hideIndicator(event as unknown as DragEvent);

    if (!event.droppedNodeData || event.position !== 'Inside') {
      event.cancel = true;
      return;
    }

    const nodeMoved = this.fileManagerService.findNode(
      event.draggedNodeData?.id as string,
      this.treeData
    );
    const aliasNodeMoved = this.fileManagerService.getAlias(nodeMoved?.id);
    const destinationFolder = this.fileManagerService.findNode(
      event.droppedNodeData?.id as string,
      this.treeData
    );
    const aliasFolder = this.fileManagerService.getAlias(destinationFolder?.id);
    const parentNodeMoved = nodeMoved
      ? this.fileManagerService.findParentNode(nodeMoved?.id, this.treeData)
      : undefined;

    // trying to move the folder to another alias
    if (aliasNodeMoved !== aliasFolder) {
      event.cancel = true;
      this.fileManagerService.createNotification(
        'information',
        this.translate.instant(_('admins.resources.folderNotMovedAlias'), {
          alias: aliasNodeMoved,
        }),
        false,
        8000
      );
      return;
    }
    const folderPath = destinationFolder ? this.fileManagerService.findPath(destinationFolder) : '';
    let nodePath = nodeMoved ? this.fileManagerService.findPath(nodeMoved) : '';
    nodePath = this.fileManagerService.removeLastElementPathIfFolder(
      nodePath,
      nodeMoved?.isFolder ?? false
    );

    // trying to move in the same folder where it comes from
    if (folderPath === nodePath) {
      event.cancel = true;
      return;
    }

    if (!this.isGridUpdatable) {
      return;
    }

    // expand the folder to have the informations of the files and folder inside destination folder
    if (parentNodeMoved) {
      this.resourcesService
        .onExpandingFolder(
          this.fileManagerService.getAlias(destinationFolder?.id),
          destinationFolder ? this.fileManagerService.findPath(destinationFolder) : ''
        )
        .subscribe((data) => {
          if (destinationFolder) {
            const filesControled = this.controlFilesTreeToDrag(
              [nodeMoved ?? []].flat(),
              destinationFolder,
              data
            );
            this.moveFilesInTree(
              filesControled,
              parentNodeMoved,
              destinationFolder,
              [nodeMoved ?? []].flat()
            );
          }
        });
    }
  }

  onNodeExpanding(event: NodeExpandEventArgs): void {
    // -----------------------------------------------------------------------------------------------------------
    // To delete when the bug of the API has been fixed :
    // (Bug : we can only expand the nodes of the tree if the first one is expanded (= if the first has children))
    // -----------------------------------------------------------------------------------------------------------
    const foundNode = this.fileManagerService.findNode(this.treeData[0].id, this.treeData);
    if (this.treeData?.[0] && event.nodeData.id !== this.treeData[0].id && foundNode) {
      this.createFakeNode(foundNode);
    }
    if (event.nodeData.id === this.treeData[0].id) {
      this.deleteFakeNodeFromTree.emit(
        this.fileManagerService.findNode(this.treeData[0].id, this.treeData)
      );
    }
    // -----------------------------------------------------------------------------------------------------------
    const dataUidAttribute = event.nodeData.id as string;
    if (this.fileTree) {
      this.fileTree.selection.isSelected(dataUidAttribute);
      // this.fileTree.selectedNodes = [event.nodeData.id ?? []].flat();
    }
    this.fileManagerService.selectedNode = dataUidAttribute
      ? this.fileManagerService.findNode(dataUidAttribute, this.treeData)
      : undefined;
    const idNode = event.nodeData.id as string;
    if (!this.fileManagerService.selectedNode?.expanded) {
      this._expandNode(idNode);
    }
  }

  _expandNode(idNode: string): void {
    const node = this.fileManagerService.findNode(idNode, this.treeData);

    if (node?.hasChildren) {
      this.fileManagerComponent.getFilesInFolder(node).subscribe();
    }
  }

  onNodeExpanded(event: NodeExpandEventArgs): void {
    const idNode = event.nodeData.id as string;
    const node = this.fileManagerService.findNode(idNode, this.treeData);
    if (node) {
      if (node.children && node.children.length > 0) {
        node.expanded = true;
      }
    }
  }

  moveFilesInTree(
    files: FilesState,
    sourceParentNode: FileTree,
    destinationParentNode: FileTree,
    nodeMoved?: FileTree[]
  ) {
    if (files.sameFiles.length > 0) {
      this.resourcesService
        .onExpandingFolder(
          this.fileManagerService.getAlias(destinationParentNode.id),
          this.fileManagerService.findPath(destinationParentNode)
        )
        .subscribe((data) => {
          // ask for replacing same files
          this.displayDialogReplaceFile.emit({
            files,
            filesInFolder: this.filesInFolder,
            annotation: true,
            sourceParentNode,
            destinationParentNode,
            data,
            nodeMoved,
          });
        });
    } else {
      this.doMoveFilesInTree({
        filesToAdd: files.goodFiles,
        filesToReplace: [],
        sourceParentNode,
        destinationParentNode,
        node: nodeMoved,
      });
    }

    // if (this.fileTree) {
    //   this.fileTree.selectedNodes = [destinationParentNode.id];
    // }
  }

  moveFile(moveFileParams: MoveFileParams) {
    const {
      aliasDestinationFolder,
      file,
      nodePath,
      pathDestinationFolder,
      destinationFolder,
      parentNodeMoved,
      nodeMoved,
      nbTotalFiles,
      toast,
    } = moveFileParams;

    this.resourcesService
      .onMovingFile(
        aliasDestinationFolder,
        file.name,
        file.newName ? file.newName : file.name,
        nodePath,
        pathDestinationFolder
      )
      .subscribe(
        () => {
          this.nbFilesMoved++;
          this.nbFilesTreated++;
          this.setFileAfterSuccessMovingFromTree.emit({
            destinationFolder,
            file,
            parentNodeMoved,
            nodeMoved,
          });
          if (!parentNodeMoved.children || parentNodeMoved.children.length === 0) {
            this.setChild.emit({ node: parentNodeMoved, children: undefined });
          }
          this.fileManagerService.afterMoveFile(
            this.nbFilesMoved,
            this.nbFilesTreated,
            nbTotalFiles,
            toast,
            destinationFolder
          );
        },
        (error) => {
          this.nbFilesTreated++;
          this.fileManagerService.afterErrorMoveFile(
            this.nbFilesMoved,
            this.nbFilesTreated,
            nbTotalFiles,
            toast,
            file,
            destinationFolder
          );
        }
      );
    this.nbFilesMoved = 0;
    this.nbFilesTreated = 0;
  }
  doMoveFilesInTree(args: {
    filesToAdd: FileDescription[];
    filesToReplace: FileDescription[];
    sourceParentNode: FileTree;
    destinationParentNode: FileTree;
    node?: FileTree[];
  }) {
    const nbTotalFiles = args.filesToAdd.length + args.filesToReplace.length;
    const destinationFolder = args.destinationParentNode;
    const parentNodeMoved = args.sourceParentNode;
    const pathDestinationFolder = this.fileManagerService.findPath(destinationFolder);
    const aliasDestinationFolder = this.fileManagerService.getAlias(destinationFolder.id);
    let nodeMoved = parentNodeMoved;
    const nodePath = this.fileManagerService.findPath(nodeMoved);

    if (nbTotalFiles > 0) {
      // eslint-disable-next-line prefer-const
      let nbFilesMoved = 0;
      // eslint-disable-next-line prefer-const
      let nbFilesTreated = 0;
      const toast = this.fileManagerService.createNotification(
        'loading',
        this.translate.instant(_('admins.resources.movingFiles'), {
          number: nbFilesMoved,
          total: nbTotalFiles,
        }),
        false,
        0,
        nbTotalFiles > 1 ? 0 : undefined
      );
      // Upload new files
      args.filesToAdd.forEach((file) => {
        if (file.type === 'Directory' && args.node) {
          nodeMoved =
            this.fileManagerService.findNodeByName(file.name, args.node) ?? parentNodeMoved;
        }
        this.moveFile({
          aliasDestinationFolder,
          file,
          nodePath,
          pathDestinationFolder,
          destinationFolder,
          parentNodeMoved,
          nodeMoved,
          nbFilesMoved,
          nbFilesTreated,
          nbTotalFiles,
          toast,
        });
      });
      // Replace files
      args.filesToReplace.forEach((file) => {
        if (file.type === 'Directory' && args.node) {
          nodeMoved =
            this.fileManagerService.findNodeByName(file.name, args.node) ?? parentNodeMoved;
        }
        this.moveFile({
          aliasDestinationFolder,
          file,
          nodePath,
          pathDestinationFolder,
          destinationFolder,
          parentNodeMoved,
          nodeMoved,
          nbFilesMoved,
          nbFilesTreated,
          nbTotalFiles,
          toast,
        });
      });
    }
  }

  onNodeCollapsed(event: NodeExpandEventArgs): void {
    const idNode = event.nodeData.id as string;
    const node = this.fileManagerService.findNode(idNode, this.treeData);
    if (node) {
      node.expanded = false;
      node.children = undefined;

      // -----------------------------------------------------------------------------------------------------------
      // To delete when the bug of the API has been fixed :
      // (Bug : we can only expand the nodes of the tree if the first one is expanded (= if the first has children))
      // -----------------------------------------------------------------------------------------------------------

      const firstData: FileTree | undefined = this.treeData[0];
      let foundedNode: FileTree | undefined;
      if (firstData?.id === idNode) {
        foundedNode = this.fileManagerService.findNode(firstData.id, this.treeData);
        this.createFakeNode(foundedNode);
      }
      // -----------------------------------------------------------------------------------------------------------

      // check if selected node exists again
      const selectedNode = this.fileManagerService?.selectedNode?.id
        ? this.fileManagerService.findNode(this.fileManagerService.selectedNode.id, this.treeData)
        : undefined;
      if (!selectedNode) {
        this.fileManagerService.selectedNode = undefined;
      }

      this.refreshTree();
    }
  }

  //This method is due to a bug (when container is bigger than 500px : the scroll is lost after a refresh)
  refreshTree(keepScroll: boolean = true, actions?: () => any) {
    const containerTreeScroll = this.containerTree?.nativeElement.scrollTop ?? 0;
    if (keepScroll && containerTreeScroll > 0 && this.containerTree) {
      setTimeout(() => {
        // this.fileTree?.refresh();
        if (actions) {
          actions();
        }
        if (this.containerTree) {
          this.containerTree.nativeElement.scrollTo({
            top: containerTreeScroll,
          });
        }
      }, 1);
    } else {
      // this.fileTree?.refresh();
      if (actions) {
        actions();
      }
    }
  }
  onNodeEditing(args: NodeEditEventArgs): void {
    // not edit root node
    if (args.node.parentNode?.parentNode?.nodeName !== 'LI') {
      args.cancel = true;
      this.fileManagerService.createNotification(
        'information',
        _('admins.resources.rootFolderNotRenamed'),
        false,
        5000
      );
    } else if (args.nodeData) {
      const nodeEdited = this.fileManagerService.findNode(
        args.nodeData.id as string,
        this.treeData
      );
      if (
        nodeEdited?.resource.right !== DataBaseRights.DbrStructure &&
        nodeEdited?.resource.right !== DataBaseRights.DbrWrite
      ) {
        args.cancel = true;
        this.fileManagerService.createNotification(
          'information',
          this.translate.instant(_('admins.resources.noRightsToRenameFolder'), {
            name: nodeEdited?.name,
          }),
          false,
          8000
        );
      }
    }
  }
  onNodeEdited(args: NodeEditEventArgs): void {
    // if (args.newText.trim() === '') {
    //   args.cancel = true;
    //   this.fileManagerService.createNotification(
    //     'information',
    //     _('admins.resources.folderNameNotEmpty'),
    //     false,
    //     5000
    //   );
    // } else if (args.newText !== args.oldText) {
    //   // file is rename
    //   const idNode = args.nodeData.id as string;
    //   const node = this.fileManagerService.findNode(idNode, this.treeData);
    //   if (node && args.newText !== node.name) {
    //     // in case of revert file renaming
    //     this._isFileNameExistInTree(args.newText, args.oldText, node);
    //   }
    // }
    // if (!this.fileManagerService.selectedNode) {
    //   const idSelectedNode = args.nodeData.id as string;
    //   this.fileManagerService.selectedNode = this.fileManagerService.findNode(
    //     idSelectedNode,
    //     this.treeData
    //   );
    //   this.selectNodeInTree.emit(this.fileManagerService.selectedNode);
    //   if (this.fileTree) {
    //     this.fileTree.selectedNodes = [this.fileManagerService?.selectedNode?.id ?? []].flat();
    //   }
    // }
  }

  public onNodeSelected(event: NodeSelectEventArgs): void {
    debugger;
    const idSelectedNode = event.nodeData.id as string;
    const nodeSelected = this.fileManagerService.findNode(idSelectedNode, this.treeData);
    // don't send the request again if the node is already selected (the children are already in node.children) or if the node is already expanded
    if (
      (this.fileManagerService.selectedNode &&
        this.fileManagerService.selectedNode.id !== idSelectedNode) ||
      !this.fileManagerService.selectedNode // don't send the request if the node has not been selected
    ) {
      this.fileManagerService.selectedNode = nodeSelected;
      if (this.fileTree) {
        this.fileTree.selection.isSelected(idSelectedNode);
        // this.fileTree.selectedNodes = [idSelectedNode];
      }
      this.selectNodeInTree.emit(this.fileManagerService.selectedNode);
    }
  }
  public onNodeClicked(args: NodeClickEventArgs): void {
    //handle tree closing
    // const nodeAttribute = args.node.getAttribute('data-uid');
    // let nodeClicked: FileTree | undefined;
    // if (nodeAttribute) {
    //   nodeClicked = this.fileManagerService.findNode(nodeAttribute, this.treeData);
    // }
    // if (nodeClicked?.expanded) {
    //   this.activateLoaderGridFromTree.emit();
    //   if (this.fileTree) {
    //     this.fileTree.selectedNodes = [nodeClicked.id];
    //   }
    //   this.fileManagerService.selectedNode = nodeClicked;
    //   this.resourcesService
    //     .onExpandingFolder(
    //       this.fileManagerService.getAlias(this.fileManagerService.selectedNode.id),
    //       this.fileManagerService.findPath(this.fileManagerService.selectedNode)
    //     )
    //     .subscribe((d) => {
    //       this.fileManagerService.sortGrid(d);
    //       this.updateFilesInFolder.emit(d);
    //       this.disableLoaderGridFromTree.emit();
    //     });
    // }
  }

  deleteFromTree(dataDelete: { file: FileTree; parentNode: FileTree }): void {
    // const toast = this.fileManagerService.createNotification(
    //   'loading',
    //   this.translate.instant(_('admins.resources.itemsDeletingNoProgression'), { number: 1 }),
    //   false,
    //   0
    // );
    // this.resourcesService
    //   .onDeletingFile(
    //     this.fileManagerService.getAlias(dataDelete.parentNode.id),
    //     dataDelete.file.name,
    //     this.fileManagerService.removeLastElementPathIfFolder(
    //       this.fileManagerService.findPath(dataDelete.file),
    //       dataDelete.file.isFolder
    //     )
    //   )
    //   .subscribe({
    //     next: (data) => {
    //       const parentNode = this.fileManagerService.findParentNode(
    //         dataDelete.file.id,
    //         this.treeData
    //       );
    //       this.folderDeleteInTree.emit({ parentNode, childNodeToDelete: dataDelete.file });
    //       this.fileManagerService.afterDeleteFiles(1, toast);
    //       this.activateLoaderGridFromTree.emit();
    //       if (this.fileTree) {
    //         this.fileTree.selectedNodes = [dataDelete.parentNode.id];
    //       }
    //       this.fileManagerService.selectedNode = dataDelete.parentNode;
    //       this.resourcesService
    //         .onExpandingFolder(
    //           this.fileManagerService.getAlias(this.fileManagerService.selectedNode.id),
    //           this.fileManagerService.findPath(this.fileManagerService.selectedNode)
    //         )
    //         .subscribe((d) => {
    //           this.fileManagerService.sortGrid(d);
    //           this.updateFilesInFolder.emit(d);
    //           this.disableLoaderGridFromTree.emit();
    //         });
    //     },
    //     error: (error) => {
    //       this.fileManagerService.afterErrorDeleteFiles(toast, false);
    //     },
    //   });
  }

  openHistoryInTree(): void {
    if (this.fileManagerService.selectedNode) {
      const fileName =
        this.fileManagerService.selectedNode.name ===
        this.fileManagerService.getAlias(this.fileManagerService.selectedNode.id)
          ? ''
          : this.fileManagerService.selectedNode.name;
      const currentFileHistory = {
        name: this.fileManagerService.selectedNode.name,
        type:
          this.fileManagerService.selectedNode.isFolder === true
            ? FileItemType.directory
            : FileItemType.file,
      };
      this.currentFileHistoryChange.emit(currentFileHistory);
      let path = this.fileManagerService.findPath(this.fileManagerService.selectedNode);
      path = this.fileManagerService.removeLastElementPathIfFolder(
        path,
        this.fileManagerService.selectedNode.isFolder
      );
      this.openHistoryFromTree.emit({
        alias: this.fileManagerService.getAlias(this.fileManagerService.selectedNode.id),
        fileName,
        path,
      });
    }
  }
  setFileAfterSuccessMovingTree(
    destinationFolder: FileTree,
    file: FileDescription,
    parentNodeMoved: FileTree,
    nodeMoved: FileTree
  ) {
    // if (file.type === 'Directory') {
    //   destinationFolder.hasChildren = true;
    // }
    // if (parentNodeMoved.children) {
    //   // remove child folder (=source folder) from source parent folder
    //   const index = parentNodeMoved.children.findIndex((f) => f.name === nodeMoved.name);
    //   if (index !== -1) {
    //     parentNodeMoved.children.splice(index, 1);
    //   }
    //   // update name of the file if needed :
    //   if (file.newName) {
    //     file.name = file.newName;
    //   }
    // }
    // if (file.type === FileItemType.directory && destinationFolder.children) {
    //   this.createNode(file, destinationFolder, true);
    //   if (nodeMoved === this.fileManagerService.selectedNode && this.fileTree) {
    //     this.fileManagerService.selectedNode = undefined;
    //     this.fileTree.selectedNodes = [];
    //     this.updateFilesInFolder.emit([]);
    //   }
    // }
  }
  private _isFileNameExistInTree(name: string, oldName: string, node: FileTree) {
    const parentNode = this.fileManagerService.findParentNode(node.id, this.treeData);
    // assuming parentNode exist because root elements cannot be renamed
    if (parentNode) {
      this.fileManagerComponent.getFilesInFolder(parentNode).subscribe((result) => {
        // update children
        // get new node
        const newNode = parentNode.children?.find((child) => child.name === oldName);
        if (!newNode) {
          // node was renamed, moved or deleted
          this.fileManagerService.createNotification(
            'information',
            this.translate.instant(_('admins.resources.fileNotExistOrRenamed'), { name: oldName }),
            false,
            8000
          );
          return;
        }
        const path = this.fileManagerService.removeLastElementPathIfFolder(
          this.fileManagerService.findPath(newNode),
          newNode.isFolder
        );
        const alias = this.fileManagerService.getAlias(newNode.id);

        if (parentNode.children?.some((child) => child.name.toLowerCase() === name.toLowerCase())) {
          name = FilesManager.createUniqueFileNameTree(
            name,
            this.fileManagerService.findParentNode(newNode.id, this.treeData)?.children ?? [],
            2
          );
          this.showFromTree.emit({ fileName: oldName, newName: name, path, alias, node: newNode });
        } else {
          this.renameItemFromTree.emit({
            oldName,
            newName: name,
            folder: newNode,
            path,
            alias,
          });
        }
      });
    }
  }
}
