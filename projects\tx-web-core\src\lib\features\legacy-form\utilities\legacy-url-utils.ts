import { LegacyFileUtils } from './legacy-file-utils';

export class UrlUtilsHelper {
  isDirectoryUrl(ulr: string): boolean {
    return ulr.substring(0, 2) === '\\\\';
  }

  isValidUrl(url: string): boolean {
    var sUrl = url.toLowerCase(),
      sExt = LegacyFileUtils.extractFileExt(sUrl),
      bOk = ['html', 'htm', 'asp', 'php', 'php3', 'php4', 'mht', 'fr', 'com', 'org'].includes(sExt);

    if (!bOk)
      bOk =
        sUrl.substring(0, 4) == 'http' ||
        sUrl.substring(0, 3) == 'www' ||
        sUrl.substring(0, 3) == 'ftp' ||
        sUrl.substring(0, 2) == '\\';

    return bOk;
  }

  completeUrl(url: string) {
    if (
      !url.startsWith('ftp://') &&
      !url.startsWith('https://') &&
      !url.startsWith('http://') &&
      !url.startsWith('..') &&
      !url.startsWith('\\')
    )
      url = 'https://' + url;
    return url;
  }
}
