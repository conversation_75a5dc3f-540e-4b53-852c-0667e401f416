import { TestBed } from '@angular/core/testing';
import { TxFormatTooltipFilterPipe } from '../grid-filter-menu/format-tooltip-filter.pipe';
import { TranslateService } from '@ngx-translate/core';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { GridFilter } from '../grid-filter-menu/grid-filter-menu.component';
import { TxObjectType } from '@bassetti-group/tx-web-core';
import MockDate from 'mockdate';
import { AbstractSessionService, MockSessionService } from '../../../data-access/session';

describe('FormatTooltipFilterPipe', () => {
  let pipe: TxFormatTooltipFilterPipe;
  let translateService: TranslateService;
  let sessionService: AbstractSessionService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        TranslateTestingModule.withTranslations({
          en: {},
          fr: {},
        }),
      ],
      providers: [{ provide: AbstractSessionService, useClass: MockSessionService }],
    });
    translateService = TestBed.inject(TranslateService);
    sessionService = TestBed.inject(AbstractSessionService);
    pipe = new TxFormatTooltipFilterPipe(translateService, sessionService);
  });

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return a transformed string of the date filter value', () => {
    MockDate.set('1/20/2022');
    const dateValue = new Date();
    expect(
      pipe.transform({ column: { type: 'date' }, value: dateValue } as GridFilter, dateValue)
    ).toEqual('20/01/2022');
    MockDate.reset();
  });

  it('should return the value of the option selected', () => {
    expect(
      pipe.transform(
        {
          column: {
            filterOptions: [
              { value: 'test1', text: 'test 1' },
              { value: 'test2', text: 'test 2' },
            ],
          },
          value: ['test1'],
        } as GridFilter,
        'test1'
      )
    ).toEqual('test 1');
  });

  it('should return the concatenation of the value of the 2 options selected', () => {
    expect(
      pipe.transform(
        {
          column: {
            filterOptions: [
              { value: 'test1', text: 'test 1' },
              { value: 'test2', text: 'test 2' },
            ],
          },
          value: ['test1', 'test2'],
        } as GridFilter,
        'test1'
      )
    ).toEqual('test 1, test 2');
  });

  it('should return the default value when none option is selected', () => {
    expect(
      pipe.transform(
        {
          column: {
            filterOptions: [
              { value: 'test1', text: 'test 1' },
              { value: 'test2', text: 'test 2' },
            ],
            field: 'column',
          },
          value: [],
          operator: 'equal',
        } as GridFilter,
        'test1'
      )
    ).toEqual('txWebCore.syncFusion.grid.all');
  });

  it('should return the value of the option selected for Object Type', () => {
    expect(
      pipe.transform(
        {
          column: {
            filterOptions: [],
            filterType: 'objectType',
          },
          value: [
            { id: 1, name: 'Contacts' } as TxObjectType,
            { id: 5, name: 'Equipments' } as TxObjectType,
          ],
        } as GridFilter,
        'objectType2'
      )
    ).toEqual('Contacts, Equipments');
  });

  it('should return the value in case of string value', () => {
    expect(pipe.transform({ column: {}, value: 'test' } as GridFilter, 'test')).toEqual('test');
  });

  it('should return the value in case of number value', () => {
    expect(pipe.transform({ column: {}, value: 10 } as GridFilter, 10)).toEqual('10');
  });

  it('should return the value in case of boolean value', () => {
    expect(pipe.transform({ column: {}, value: true } as GridFilter, true)).toEqual('true');
  });
});
