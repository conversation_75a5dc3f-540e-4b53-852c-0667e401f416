import { Directive, Input } from '@angular/core';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';
import { TxObjectType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { DataSourceInitialized, GridDirective, TxGridColumn } from '@bassetti-group/tx-web-core/src/lib/features/grid';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { Observable, of } from 'rxjs';

@Directive({
  selector: '[txTreeGrid]',
  standalone: true,
})
export abstract class TxDataTreeGridDirective<T extends {}> extends GridDirective<T> {
  @Input() columns!: TxGridColumn<T>[];
  @Input() objectType?: TxObjectType;
  @Input() idObjectType?: number;
  @Input() multipleSelection: boolean = true;
  @Input() enableCheckbox: boolean = false;
  showSpinner$: Observable<boolean> = of(false);
  constructor() {
    super();
  }

  // if showSelectionToggle has the same implementation in all its subclasses. We need to transform the abstract class into a concrete class that factorizes the implementation here.
  abstract showSelectionToggle(state: MatSlideToggleChange): void;
  abstract handleSelection(element: T, event: MatCheckboxChange): void;
  // if showSelectionToggle has the same implementation in all its subclasses. We need to transform the abstract class into a concrete class that factorizes the implementation here.
  abstract handleMasterCheckbox(): void;
  protected abstract initDataSource(): DataSourceInitialized<T>;
}
