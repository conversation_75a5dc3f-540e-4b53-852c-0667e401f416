import { TestBed } from '@angular/core/testing';
import { TRANSLATIONS_MOCK } from 'src/app/core/translation/translations.mock';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { ModelApplicationService } from 'src/app/core/services/structure/model-application.service';
import {
  CommonServiceMock,
  ErrorServiceMock,
  ModelApplicationServiceMock,
  ObjectsTypeServiceMock,
} from 'src/app/app.testing.mock';
import { NbErrorsPipe } from 'src/app/shared/pipes/nb-errors.pipe';
import { ErrorService } from 'src/app/core/services/errors/error.service';
import { MockProvider } from 'ng-mocks';
import { ErrorMessagesService } from 'src/app/core/error-messages/error-messages.service';
import { ERROR_MESSAGE_SERVICE_MOCK } from 'src/app/core/error-messages/error-messages.service.mock';
import { CoreModelConceptService } from './core-model-concept.service';
import { TxCommonService, TxObjectsTypeService, TxConcept } from '@bassetti-group/tx-web-core';

describe('CoreModelsConcept Service', () => {
  let service: CoreModelConceptService<TxConcept>;
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [TranslateTestingModule.withTranslations(TRANSLATIONS_MOCK)],
      providers: [
        {
          provide: ModelApplicationService,
          useClass: ModelApplicationServiceMock,
        },
        NbErrorsPipe,
        {
          provide: TxCommonService,
          useClass: CommonServiceMock,
        },
        {
          provide: ErrorService,
          useClass: ErrorServiceMock,
        },
        MockProvider(ErrorMessagesService, ERROR_MESSAGE_SERVICE_MOCK, 'useValue'),
        { provide: TxObjectsTypeService, useClass: ObjectsTypeServiceMock },
      ],
    });
    service = TestBed.inject(CoreModelConceptService);
  });

  it('should create', () => {
    expect(service).toBeTruthy();
  });
  it('removeFilterOnConceptsInErrors', () => {
    service.removeFilterOnConceptsInErrors();
    service.concepts$.subscribe((concepts) => {
      expect(concepts).toEqual([]);
    });
    service.showOnlyErrors$.subscribe((show) => {
      expect(show).toEqual(false);
    });
  });
});
