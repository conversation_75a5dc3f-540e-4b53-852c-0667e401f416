import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import {
  TxObject,
  TxObjectType,
  TxObjectsService,
  TxObjectsTypeService,
  TxDialogService,
  TxGridChecked,
} from '@bassetti-group/tx-web-core';

@Component({
  selector: 'app-object-selection-form',
  templateUrl: './object-selection-form.component.html',
  styleUrls: ['./object-selection-form.component.scss'],
})
export class ObjectSelectionFormComponent implements OnInit {
  @Input() idObjectTypeSelected?: string | null;
  @Input() objectTypes?: TxObjectType[];
  @Input() objectsSelected: TxObject[] = [];
  @Input() disableObjectTypeChange = false;
  @Input() multipleSelection = false;
  @Input() selectButtonCaption = this.translate.instant(_('button.select'));
  @Input() title = this.translate.instant(_('admins.automaticNaming.objectSelection'));

  @Output() cancel = new EventEmitter();
  @Output() confirm = new EventEmitter<TxObject[]>();

  public validForm = false;

  constructor(
    private readonly translate: TranslateService,
    private readonly objectTypeService: TxObjectsTypeService,
    private readonly objectService: TxObjectsService,
    private readonly dialogConfirmService: TxDialogService
  ) {}

  ngOnInit(): void {
    if (!this.objectTypes || this.objectTypes.length < 1) {
      this.objectTypeService.listAll().subscribe((ots) => {
        this.objectTypes = ots;
      });
    }
    if (this.objectsSelected && this.objectsSelected.length > 0) {
      this.validForm = true;
    }
  }

  onCancel() {
    this.cancel.emit();
  }

  beforeConfirm() {
    if (!this.objectsSelected?.[0].tags || this.objectsSelected[0].tags.length < 1) {
      this.dialogConfirmService
        .open({
          message: this.translate.instant(_('admins.automaticNaming.autoTagObject')),
          okCaption: _('button.create'),
        })
        .subscribe((createClicked) => {
          if (createClicked) {
            this.objectService.autotag([this.objectsSelected[0].id]).subscribe((objsTagged) => {
              this.objectsSelected = objsTagged;
              this.afterConfirm();
            });
          }
        });
    } else {
      this.afterConfirm();
    }
  }

  afterConfirm() {
    this.confirm.emit(this.objectsSelected);
  }

  getObjectTypeSelected() {
    if (this.idObjectTypeSelected) {
      return this.objectTypeService.getByID(Number(this.idObjectTypeSelected));
    }
  }
  getIdObjectTypeSelected() {
    if (this.idObjectTypeSelected) {
      return Number(this.idObjectTypeSelected);
    }
  }

  objectTypeChange(args: string[]) {
    this.validForm = false;
    if (!args || args.length < 1) {
      this.idObjectTypeSelected = null;
    } else {
      this.idObjectTypeSelected = args[0];
    }
  }

  onTreeGridCheckChange(args: TxGridChecked<TxObject>) {
    let objects = args.objects;
    this.validForm = objects && objects.length > 0;
    this.objectsSelected = objects;
  }
}
