import { Directive, ElementRef, Input, OnChanges, Renderer2, SimpleChanges } from '@angular/core';

@Directive({
  selector: '[appAdjustWidth]',
  standalone: true,
})
export class AdjustWidthDirective implements OnChanges {
  @Input('appAdjustWidth') placeholderText: string = '';

  constructor(private readonly el: ElementRef, private readonly renderer: Renderer2) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['placeholderText'] && this.placeholderText) {
      this.updateWidth();
    }
  }

  private updateWidth(): void {
    const span = this.renderer.createElement('span');
    this.renderer.setStyle(span, 'visibility', 'hidden');
    this.renderer.setStyle(span, 'position', 'absolute');
    this.renderer.appendChild(span, this.renderer.createText(this.placeholderText));
    this.renderer.appendChild(document.body, span);

    const textWidth = span.offsetWidth;

    const maxWidth = textWidth + 60 + 'px';
    this.renderer.setStyle(this.el.nativeElement, 'max-width', maxWidth);
    this.renderer.setStyle(this.el.nativeElement, 'min-width', '200px');
    this.renderer.removeChild(document.body, span);
  }
}
