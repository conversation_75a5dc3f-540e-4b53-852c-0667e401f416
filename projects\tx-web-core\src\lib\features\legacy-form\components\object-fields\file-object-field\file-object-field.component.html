<div
  *ngIf="!readMode"
  class="form-field"
  [ngClass]="{
    'form-field-error': control && !control.valid && !disabled,
    'read-field': readMode
  }">
  <mat-label
    class="form-label mat-form-label"
    [matTooltip]="label"
    matTooltipClass="mat-label-tooltip mat-tooltip-multiline"
    matTooltipShowDelay="500"
    matTooltipPosition="above">
    {{ label }}
    <span
      *ngIf="required"
      [class]="
        control && (control.hasError('required') || control.hasError('empty'))
          ? 'span-error mat-form-label'
          : 'mat-form-label'
      ">
      *</span
    >
  </mat-label>
  <tx-upload-file-field
    #uploadeFileField
    [form]="form"
    [value]="value"
    [formControl]="control"
    [control]="control"
    [label]="label"
    [multiple]="multiple"
    [field]="field"
    [required]="required"
    [hideVisualisationToggle]="hideVisualisationToggle"></tx-upload-file-field>
</div>

<tx-chips-field
  #chipsField
  *ngIf="readMode"
  [field]="field"
  [label]="label"
  [labelTooltip]="labelTooltip"
  [control]="control"
  [disabled]="disabled"
  [required]="required"
  [visible]="true"
  [selectable]="false"
  [addOnBlur]="true"
  [information]="information"
  [value]="value"
  [data]="data"
  [readMode]="readMode"
  [fieldType]="field.attribute.dataType"
  [fullWidthChip]="true"
  [hideTitleImageThumbnail]="hideTitleImageThumbnail"
  (chipClick)="downloadFile($event)"
  (actionIconClick)="downloadFileList($event)">
</tx-chips-field>
