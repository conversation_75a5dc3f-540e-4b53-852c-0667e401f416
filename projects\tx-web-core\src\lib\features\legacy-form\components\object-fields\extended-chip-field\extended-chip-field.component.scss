.data-chip-container {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  float: left;
}

.pane-data-chip {
  display: block !important;
  height: auto;
  padding: 0px !important;
  max-width: 100%;
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 4px !important;
  min-height: 24px;
  margin-left: 0px !important;
  margin-right: 16px !important;
  margin-top: 6px !important;
  font-weight: normal !important;
}

.panel-content {
  padding: 32px 24px;
}

.chip-icon {
  margin-right: 8px;
}

.action-icon {
  float: right;
  cursor: pointer;
}

:host ::ng-deep .mat-chip-list-wrapper {
  display: inline-table !important;
  width: calc(100% + 20px);
  margin: 0px !important;
}

.chip-text-container {
  padding: 4px 16px 3px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-flex;
}

.chip-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
