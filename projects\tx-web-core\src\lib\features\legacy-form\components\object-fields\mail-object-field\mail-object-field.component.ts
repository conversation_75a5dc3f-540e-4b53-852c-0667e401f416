import { Component, Input, ViewChild } from '@angular/core';
import { TxAttributeMail } from '../../../services/structure/models/attribute';
import { LegacyTxDataTab, LegacyTxDataType } from '../../../services/structure/models/data';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { TxChip } from '../../../models/formFields/setting-model';
import { TxChipsFieldComponent } from '../../generic-fields/chips-field/chips-field.component';
import { TxInputObjectFieldComponent } from '../_system/input-object-field/input-object-field.component';

@Component({
  selector: 'tx-mail-object-field',
  templateUrl: './mail-object-field.component.html',
  styleUrls: ['./mail-object-field.component.scss'],
})
export class TxMailObjectFieldComponent extends TxInputObjectFieldComponent {
  @Input() placeHolder = 'Ex. <EMAIL>';
  @Input() pattern =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9_]+\.)+[a-zA-Z]{2,}))$/;
  @Input() removable!: boolean;
  @Input() multiple!: boolean;

  @ViewChild('chipsField') chipsField!: TxChipsFieldComponent;
  declare attribute: TxAttributeMail;
  maxChipsMessage = "Nombre d'email(s) max atteint";
  dataType = LegacyTxDataType.Email;

  constructor(public attributeService: LegacyTxAttributesService) {
    super(attributeService);
  }

  initProperties() {
    super.initProperties();
    if (this.attribute) {
      this.multiple = this.attribute.isList;
      this.removable = !this.readMode;
    }
  }

  initValue() {
    // if (this.data) {
    //   this.field.value = this.data.value;
    // }
    // this.field.classes = 'mediumField';
  }

  getData(): LegacyTxDataTab {
    const values = this.control.value;
    return new LegacyTxDataTab(this.idObject, this.idAttribute, values);
  }

  clear() {
    this.chipsField.clearField();
  }

  setData(data: LegacyTxDataTab) {
    if (this.chipsField) {
      this.chipsField.data = data;
      this.chipsField.setData();
    } else {
      this.control.setValue(data.value);
    }
  }

  sendMail(chip: TxChip) {
    window.location.href = 'mailto:' + chip.name;
  }

  sendMailList(chips: TxChip[]) {
    let allMail = 'mailto:';
    for (const chip of chips) {
      if (allMail !== 'mailto:') {
        allMail += ', ';
      }
      allMail += chip.name;
    }
    if (allMail !== 'mailto:') {
      window.location.href = allMail;
    }
  }
}
