import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { NodeCheckEventArgs, NodeSelectEventArgs } from '@syncfusion/ej2-angular-navigations';
import { LegacyTxObject } from '../../../../../../services/structure/models/object';
import { TxTreeObjectsComponent } from '../../../../../../trees/tree-objects/tree-objects.component';
import { LegacyTxObjectTypeService } from '../../../../../../services/structure/services/object-type.service';

@Component({
  selector: 'tx-escf-tree',
  templateUrl: './escf-tree.component.html',
  styleUrls: ['./escf-tree.component.scss'],
})
export class LegacyTxEscfTreeComponent implements OnInit {
  @Input() multiple!: boolean;
  @Input() elements!: any[];
  @Input() selectedElementsValues!: any[];
  @Input() idObjectType!: number;
  @Input() height = 300;

  @Output() elementChecked = new EventEmitter<NodeCheckEventArgs>();
  @Output() elementSelected = new EventEmitter<NodeSelectEventArgs>();

  @ViewChild('treeObject') treeObject!: TxTreeObjectsComponent;

  objectTypeName!: string;
  txObjects: LegacyTxObject[] = [];
  initialized = false;

  constructor(public objectTypeService: LegacyTxObjectTypeService) {}

  ngOnInit() {
    this.objectTypeName = this.objectTypeService.getName(this.idObjectType);
  }

  onElementChecked(args: NodeCheckEventArgs) {
    this.elementChecked.emit(args);
  }

  onElementSelected(args: NodeSelectEventArgs) {
    this.elementSelected.emit(args);
  }

  load() {
    if (this.initialized) {
      return;
    }
    this.treeObject.loadBranch();
    this.initialized = true;
  }

  onMouseEnter(event: MouseEvent) {}

  onMouseLeave(event: MouseEvent) {}
}
