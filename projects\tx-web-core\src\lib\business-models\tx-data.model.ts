import { TxFile } from './tx-attribute.model';
import { TxObject } from './tx-object.model';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';

export enum TxDataBaseAction {
  None,
  Add,
  Modify,
  Delete,
}

export enum TxDataType {
  Boolean = 1,
  ShortText = 3,
  Listing = 4,
  Decimal = 5,
  Table = 6,
  LongText = 9,
  LinkAss = 11,
  Link = 12,
  Group = 13,
  SingleValue = 50,
  Range = 51,
  RangeMeanValue = 52,
  Series = 61,
  TableValue = 62,
  ArchivedGraphic = 63,
  Date = 80,
  DateAndTime = 81,
  File = 100,
  Email = 110,
  Url = 111,
  LinkDirect = 121,
  LinkInv = 122,
  LinkBi = 123,
  Tab = -2,
  TrashIdObject = -1000,
  ConceptTypeCriterion = -181,
  ConceptTypeAppliedInputOutput = -161,
  ConceptTypeInputOutput = -151,
  ConceptTypeSeriesType = -101,
  ConceptTypeExportation = -57,
  ConceptTypeMDLog = -55,
  ConceptTypeEqObjectAttribute = -44,
  ConceptTypeEqAttributeSId = -43,
  ConceptTypeEqAttributeId = -42,
  ConceptTypeEqObjectSId = -41,
  ConceptTypeEqObjectId = -40,
  ConceptTypeEquivalenceSet = -39,
  ConceptTypeObjectTypeTranslation = -33,
  ConceptTypeTranslation = -32,
  ConceptTypeLanguage = -31,
  ConceptTypeAttributeSet = -29,
  ConceptTypeQuestion = -20,
  ConceptTypeChoiceGuide = -19,
  ConceptTypeRequirementList = -18,
  ConceptTypeModelApplication = -16,
  ConceptTypeModel = -15,
  ConceptTypeFileType = -14,
  ConceptTypeRight = -13,
  ConceptTypeUsersGroup = -12,
  ConceptTypeUser = -11,
  ConceptTypeTableType = -10,
  ConceptTypeSource = -9,
  ConceptTypeLinkType = -8,
  ConceptTypeUnit = -6,
  ConceptTypeConversion = -5,
  ConceptTypeAttribute = -4,
  ConceptTypeObject = -3,
  ConceptTypeTab = -2,
  ConceptTypeObjectType = -1,
}

const TX_DATATYPE = {
  Boolean: _('txWebCore.dataType.1'),
  ShortText: _('txWebCore.dataType.3'),
  Listing: _('txWebCore.dataType.4'),
  Decimal: _('txWebCore.dataType.5'),
  Table: _('txWebCore.dataType.6'),
  LongText: _('txWebCore.dataType.9'),
  LinkAss: _('txWebCore.dataType.11'),
  Link: _('txWebCore.dataType.12'),
  Group: _('txWebCore.dataType.13'),
  SingleValue: _('txWebCore.dataType.50'),
  Range: _('txWebCore.dataType.51'),
  RangeMeanValue: _('txWebCore.dataType.52'),
  Series: _('txWebCore.dataType.61'),
  TableValue: _('txWebCore.dataType.62'),
  ArchivedGraphic: _('txWebCore.dataType.63'),
  Date: _('txWebCore.dataType.80'),
  DateAndTime: _('txWebCore.dataType.81'),
  File: _('txWebCore.dataType.100'),
  Email: _('txWebCore.dataType.110'),
  Url: _('txWebCore.dataType.111'),
  LinkDirect: _('txWebCore.dataType.121'),
  LinkInv: _('txWebCore.dataType.122'),
  LinkBi: _('txWebCore.dataType.123'),
  Tab: _('txWebCore.dataType.-2'),
  TrashIdObject: _('txWebCore.dataType.-1000'),
  ConceptTypeCriterion: _('txWebCore.dataType.-181'),
  ConceptTypeAppliedInputOutput: _('txWebCore.dataType.-161'),
  ConceptTypeInputOutput: _('txWebCore.dataType.-151'),
  ConceptTypeSeriesType: _('txWebCore.dataType.-101'),
  ConceptTypeExportation: _('txWebCore.dataType.-57'),
  ConceptTypeMDLog: _('txWebCore.dataType.-55'),
  ConceptTypeEqObjectAttribute: _('txWebCore.dataType.-44'),
  ConceptTypeEqAttributeSId: _('txWebCore.dataType.-43'),
  ConceptTypeEqAttributeId: _('txWebCore.dataType.-42'),
  ConceptTypeEqObjectSId: _('txWebCore.dataType.-41'),
  ConceptTypeEqObjectId: _('txWebCore.dataType.-40'),
  ConceptTypeEquivalenceSet: _('txWebCore.dataType.-39'),
  ConceptTypeObjectTypeTranslation: _('txWebCore.dataType.-33'),
  ConceptTypeTranslation: _('txWebCore.dataType.-32'),
  ConceptTypeLanguage: _('txWebCore.dataType.-31'),
  ConceptTypeAttributeSet: _('txWebCore.dataType.-29'),
  ConceptTypeQuestion: _('txWebCore.dataType.-20'),
  ConceptTypeChoiceGuide: _('txWebCore.dataType.-19'),
  ConceptTypeRequirementList: _('txWebCore.dataType.-18'),
  ConceptTypeModelApplication: _('txWebCore.dataType.-16'),
  ConceptTypeModel: _('txWebCore.dataType.-15'),
  ConceptTypeFileType: _('txWebCore.dataType.-14'),
  ConceptTypeRight: _('txWebCore.dataType.-13'),
  ConceptTypeUsersGroup: _('txWebCore.dataType.-12'),
  ConceptTypeUser: _('txWebCore.dataType.-11'),
  ConceptTypeTableType: _('txWebCore.dataType.-10'),
  ConceptTypeSource: _('txWebCore.dataType.-9'),
  ConceptTypeLinkType: _('txWebCore.dataType.-8'),
  ConceptTypeUnit: _('txWebCore.dataType.-6'),
  ConceptTypeConversion: _('txWebCore.dataType.-5'),
  ConceptTypeAttribute: _('txWebCore.dataType.-4'),
  ConceptTypeObject: _('txWebCore.dataType.-3'),
  ConceptTypeTab: _('txWebCore.dataType.-2'),
  ConceptTypeObjectType: _('txWebCore.dataType.-1'),
};

export enum DataBaseRights {
  DbrNone = 'dbrNone',
  DbrRead = 'dbrRead',
  DbrWrite = 'dbrWrite',
  DbrStructure = 'dbrStructure',
}

export class TxData {
  action: TxDataBaseAction;
  constructor(
    public idObject: number,
    public idAttribute: number,
    action: TxDataBaseAction = TxDataBaseAction.Add
  ) {
    this.action = action;
  }
  public static removedData(idObject: number, idAttribute: number): TxData {
    return new TxData(idObject, idAttribute, TxDataBaseAction.Delete);
  }
}

export class TxDataString extends TxData {
  constructor(
    public idObject: number,
    public idAttribute: number,
    public value: string,
    action?: TxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
  }
}

export class TxDataBoolean extends TxData {
  constructor(
    public idObject: number,
    public idAttribute: number,
    public value: boolean,
    action?: TxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
  }
}

export class TxDataNumeric extends TxData {
  constructor(
    public idObject: number,
    public idAttribute: number,
    public min: number,
    public max?: number,
    public mean?: number,
    public idUnit?: number,
    action?: TxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
  }
}

export class TxDataFile extends TxData {
  constructor(
    public idObject: number,
    public idAttribute: number,
    public files: TxFile[],
    action: TxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
  }
}

export class TxDataTable extends TxData {
  constructor(
    public idObject: number,
    public idAttribute: number,
    public series: [],
    action: TxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
  }
}

export class TxDataLink extends TxData {
  constructor(
    public idObject: number,
    public idAttribute: number,
    public linkedIds: number[],
    public linkedObjects: TxObject[],
    action: TxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
  }
}

export class TxDataTab extends TxData {
  constructor(
    public idObject: number,
    public idAttribute: number,
    public value: string | string[],
    action: TxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
    if (!Array.isArray(value)) {
      this.value = value?.split(/\r\n/);
    }
  }
}

export class TxDataUrl extends TxData {
  constructor(
    public idObject: number,
    public idAttribute: number,
    public value: string | string[],
    public boolVisualisation: boolean,
    action?: TxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
    if (!Array.isArray(value)) {
      this.value = value?.split(/\r\n/);
    }
  }
}

export interface TxDataWithLinkedObjects {
  data?: TxData[];
  linkedObjects?: TxObject[];
}
