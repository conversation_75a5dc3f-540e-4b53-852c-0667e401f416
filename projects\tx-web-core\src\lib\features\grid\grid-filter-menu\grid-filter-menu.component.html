<div class="button-and-chips-container">
  <button
    class="toolbarBtn"
    mat-button
    [matMenuTriggerFor]="menu"
    (click)="setFilterableCols()"
    [disabled]="disabled">
    <fa-icon class="icon-in-button" [icon]="['fal', 'filter']"></fa-icon>
    <span>{{ 'txWebCore.syncFusion.grid.FilterButton' | translate }}</span>
  </button>
  <div class="filter-chips-container">
    <mat-chip-listbox aria-label="Filtered columns list">
      <ng-container *ngFor="let filter of filterList; let index = index">
        <mat-chip
          highlighted
          *ngIf="filter.value | notEmptyFilter"
          color="accent"
          [removable]="true"
          (removed)="removeFilter(index)"
          [matTooltip]="
            'txWebCore.gridFilters.chipFilterTooltip'
              | translate
                : {
                    filteredColumn: filter.column?.headerText | translate,
                    operator: translate.instant(
                      'txWebCore.gridFilters.filterTypes.' + filter.operator
                    ),
                    filterValue: filter | formatTooltipFilter : filter.value
                  }
          ">
          {{ filter.column?.headerText | translate }}
          <fa-icon
            [icon]="['fal', 'times-circle']"
            matChipRemove
            class="contrasted-accent"></fa-icon>
        </mat-chip>
      </ng-container>
    </mat-chip-listbox>
  </div>
</div>

<mat-menu #menu="matMenu" class="filters-mat-menu" yPosition="above">
  <div class="filters-main-container" (click)="$event.stopImmediatePropagation()">
    <div class="filter-list">
      <div *ngIf="filterList.length > 0; else noFilterBlock">
        <ng-container *ngFor="let filter of filterList; let index = index">
          <div class="filter-container">
            <fa-icon
              [icon]="['fal', 'times']"
              class="remove-filter-icon"
              (click)="removeFilter(index)"
              matTooltip="{{ 'txWebCore.gridFilters.removeFilter' | translate }}"></fa-icon>
            <mat-form-field class="filter-field" color="accent">
              <mat-label>{{ 'txWebCore.gridFilters.filterOn' | translate }}</mat-label>
              <mat-select
                (selectionChange)="changeFilterColumn($event, index)"
                [value]="filter.column?.field">
                <ng-container *ngFor="let column of filterableCols">
                  <mat-option
                    [value]="column.field"
                    [ngStyle]="{ display: isColumnAlreadyFiltered(column.field) ? 'none' : '' }">
                    {{ column.headerText | translate }}
                  </mat-option>
                </ng-container>
              </mat-select>
            </mat-form-field>
            <mat-form-field *ngIf="!filter.hideFilterType" class="filter-field" color="accent">
              <mat-label>{{ 'txWebCore.gridFilters.filterType' | translate }}</mat-label>
              <mat-select
                (selectionChange)="changeFilterType($event, index)"
                [value]="filter.operator">
                <ng-container
                  *ngFor="
                    let filterType of getFilterTypesByColType(
                      filter.column?.type,
                      !!filter.column?.filterOptions
                    )
                  ">
                  <mat-option [value]="filterType">
                    {{ 'txWebCore.gridFilters.filterTypes.' + filterType | translate }}
                  </mat-option>
                </ng-container>
              </mat-select>
            </mat-form-field>
            <mat-form-field
              *ngIf="
                !filter.column?.type ||
                ((filter.column?.type === 'string' ||
                  filter.column?.type === 'number' ||
                  filter.column?.type === 'boolean') &&
                  !filter.column?.filterOptions &&
                  filter.column?.filterType !== 'objectType')
              "
              class="filter-field"
              color="accent">
              <mat-label>{{ 'txWebCore.gridFilters.filterValue' | translate }}</mat-label>
              <input (change)="applyFilter(index)" matInput [(ngModel)]="filter.value" />
            </mat-form-field>
            <div *ngIf="filter.column?.type && filter.column?.type === 'date'" class="filter-field">
              <tx-datepicker
                [label]="'txWebCore.gridFilters.selectFilterDateValue' | translate"
                [value]="filter.value"
                (dateChange)="onFilterDateChange($event, filter, index)"></tx-datepicker>
            </div>
            <div
              *ngIf="filter.column?.filterType && filter.column?.filterType === 'objectType'"
              class="filter-field">
              <tx-objects-type-dropdown
                #dropdownOT
                [width]="'376px'"
                [label]="'txWebCore.gridFilters.selectFilterValue' | translate"
                [types]="getObjectTypeTypes(index)"
                [onlyVisible]="getOnlyVisibleObjectType(index)"
                [showCheckBox]="true"
                [returnIds]="false"
                (valueChange)="applyDropDownTreeFilter(index, $event)"
                (reloadComboOptions)="onDropDownObjectTypeOptionsReloaded(index, $event)">
              </tx-objects-type-dropdown>
            </div>
            <div
              *ngIf="filter.column?.filterType && filter.column?.filterType === 'conceptDropDown'"
              class="filter-field">
              <tx-concept-dropdown
                #dropdown
                [width]="'376px'"
                [label]="'txWebCore.gridFilters.selectFilterValue' | translate"
                [dataSource]="filter.column?.filterOptions"
                [showCheckBox]="true"
                (valueChange)="applyDropDownTreeFilter(index, $event)">
              </tx-concept-dropdown>
            </div>
            <mat-form-field
              width="376"
              [class]="'filter-field' + (filter.hideFilterType ? ' filter-select-large' : '')"
              color="accent"
              *ngIf="
                filter.column?.filterOptions &&
                !['conceptDropDown', 'objectType'].includes(filter.column?.filterType)
              ">
              <mat-label>{{ 'txWebCore.gridFilters.selectFilterValue' | translate }}</mat-label>
              <mat-select
                [multiple]="true"
                (selectionChange)="applyFilter(index)"
                [(value)]="filter.value">
                <mat-option
                  *ngFor="let option of filter.column.filterOptions"
                  [value]="option.value">
                  {{ option.text | translate }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </ng-container>
      </div>
      <ng-template #noFilterBlock>
        <span class="no-filter-span" *ngIf="true">No filters applied to this view</span>
      </ng-template>
    </div>
    <button
      class="add-filter-button"
      mat-button
      color="accent"
      (click)="addNewFilter()"
      [disabled]="filterList.length === filterableCols.length">
      <fa-icon class="icon-in-button" [icon]="['fal', 'plus']"></fa-icon>
      <span>{{ 'txWebCore.gridFilters.button.addFilter' | translate }}</span>
    </button>
    <button
      *ngIf="filterList.length > 0"
      class="remove-filters-button"
      mat-button
      (click)="removeAllFilters()">
      <fa-icon class="icon-in-button" [icon]="['fal', 'trash']"></fa-icon>
      <span>{{ 'txWebCore.gridFilters.button.removeAllFilters' | translate }}</span>
    </button>
  </div>
</mat-menu>
