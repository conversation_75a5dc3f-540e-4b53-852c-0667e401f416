import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AutomaticNamingComponent } from './components/automatic-naming.component';
import { AccessRightsGuard } from 'src/app/core/guards/access-rights.guard';
import { AuthenticationGuard } from 'src/app/core/guards/authentication.guard';
import { AdminRights } from '@bassetti-group/tx-web-core';

const routes: Routes = [
  {
    path: '',
    component: AutomaticNamingComponent,
    data: {
      breadcrumb: 'admins.wording.automaticNaming',
      adminRights: AdminRights.CanAdministrateRights,
    },
    canActivate: [AuthenticationGuard, AccessRightsGuard],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AutomaticNamingRoutingModule {}
