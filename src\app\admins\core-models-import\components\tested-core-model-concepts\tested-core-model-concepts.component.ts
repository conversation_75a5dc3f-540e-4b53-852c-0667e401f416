import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { NbErrors, SUPPORTED_CONCEPTS } from 'src/app/admins/core-model-common';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import {
  FlatTestedCoreModelConcept,
  TestedCoreModelConceptFieldEnum,
} from '../../models/tested-core-model-concept.model';
import { FormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { TextUtils } from 'src/app/core/utils/text.utils';
import {
  FilterOptionsList,
  GridFilterType,
  InputSearchEventInfo,
  TxGridColumn,
  TxGridComponent,
  TxGridDataType,
  TxObjectTypeType,
} from '@bassetti-group/tx-web-core';
import { FlatCoreModelExportConcept } from 'src/app/admins/core-model-export/models/core-model-export-concept.model';

const TABLE_HEADERS = {
  modificationType: _('admins.columns.translatedModificationType'),
  translatedConflicts: _('admins.columns.translatedConflicts'),
  conflicts: _('admins.columns.conflicts'),
};
@Component({
  selector: 'app-tested-core-model-concepts',
  templateUrl: './tested-core-model-concepts.component.html',
  styleUrls: ['./tested-core-model-concepts.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TestedCoreModelConceptsComponent implements OnInit {
  private readonly defaultActiveFilter = true;
  private _coreModelsGrid?: TxGridComponent<FlatCoreModelExportConcept>;
  @Input() flatConcepts: FlatTestedCoreModelConcept[] = [];
  public inputSearchValue: string = '';
  public searchById: number | undefined;
  @Input() nbErrors: NbErrors | null | undefined = null;

  @Output() filterOnConceptsInConflicts = new EventEmitter<boolean>();
  slideToggleControl = new FormControl<boolean>(false, {
    nonNullable: true,
  });
  @ViewChild('coreModelGrid') set coreModelsGrid(
    value: TxGridComponent<FlatCoreModelExportConcept> | undefined
  ) {
    if (value !== undefined && this._coreModelsGrid !== value) {
      this._coreModelsGrid = value;
    }
  }
  get coreModelsGrid() {
    return this._coreModelsGrid;
  }
  columns: TxGridColumn<FlatTestedCoreModelConcept>[] = Object.values(
    TestedCoreModelConceptFieldEnum
  ).map((field) => {
    if (field === TestedCoreModelConceptFieldEnum.Id) {
      return {
        field,
        headerText: _(`admins.columns.${field}`),
        type: TxGridDataType.NUMBER,
        visible: false,
      };
    } else if (field === TestedCoreModelConceptFieldEnum.Tags) {
      return {
        field,
        headerText: _(`admins.coreModels.tag`),
        type: TxGridDataType.TEXT,
        width: this.getColumnWidth(field),
        sorting: true,
        isSearchable: true,
      };
    } else if (field === TestedCoreModelConceptFieldEnum.Type) {
      return {
        field,
        headerText: _(`admins.columns.type`),
        type: TxGridDataType.TEXT,
        visible: false,
      };
    } else if (
      field === TestedCoreModelConceptFieldEnum.Conflicts ||
      field === TestedCoreModelConceptFieldEnum.ModificationType
    ) {
      return {
        field,
        headerText: _(`admins.columns.${field}`),
        type: TxGridDataType.TEXT,
        sorting: true,
        isSearchable: field === TestedCoreModelConceptFieldEnum.ModificationType,
        width: this.getColumnWidth(field),
      };
    }
    return {
      field,
      headerText: _(`admins.columns.${field}`),
      type: TxGridDataType.TEXT,
      width: this.getColumnWidth(field),
      sorting: true,
      isSearchable: true,
    };
  });

  filterColumns: TxGridColumn<FlatTestedCoreModelConcept>[] = this.columns.filter(
    (data) => data.field !== TestedCoreModelConceptFieldEnum.Id
  );
  gridFilterOptions: FilterOptionsList[] = [
    {
      column: TestedCoreModelConceptFieldEnum.Type,
      options: Object.values(SUPPORTED_CONCEPTS).map((type) => ({
        value: type,
        text: this.translate.instant(_(`concepts.${TextUtils.firstLetterLowercase(type)}`)),
      })),
      hideFilterType: true,
      filterType: GridFilterType.FilterSelectLarge,
    },
    {
      column: TestedCoreModelConceptFieldEnum.ObjectType,
      hideFilterType: true,
      filterType: GridFilterType.ObjectType,
      settings: {
        types: [TxObjectTypeType.Standard, TxObjectTypeType.User, TxObjectTypeType.Portal],
        onlyVisible: false,
      },
      options: [],
    },
  ];
  TestedCoreModelConceptFieldEnum = TestedCoreModelConceptFieldEnum;
  conflictLabel = _('admins.columns.conflict');
  constructor(private readonly translate: TranslateService) {}
  get conflictsColumns(): TxGridColumn<FlatTestedCoreModelConcept> {
    return this.columns[5];
  }
  @Input() set showOnlyErrors(value: boolean | undefined) {
    if (value !== undefined) {
      this.slideToggleControl.setValue(value);
    }
  }
  ngOnInit(): void {
    this.slideToggleControl.valueChanges.pipe().subscribe((show) => {
      this.filterOnConceptsInConflicts.emit(show);
    });
  }

  private getColumnWidth(field: TestedCoreModelConceptFieldEnum): string | undefined {
    switch (field) {
      case TestedCoreModelConceptFieldEnum.Tags:
        return '15%';
      case TestedCoreModelConceptFieldEnum.Name:
        return '18%';
      case TestedCoreModelConceptFieldEnum.ObjectType:
        return '20%';
      case TestedCoreModelConceptFieldEnum.Conflicts:
        return '33%';
      default:
        return undefined;
    }
  }
  searchItem(inputSearchEventInfo: InputSearchEventInfo): void {
    if (this.coreModelsGrid) {
      this.inputSearchValue = inputSearchEventInfo.inputSearch.nativeElement.value;
      const event = inputSearchEventInfo.event;
      const eventCode = event instanceof KeyboardEvent ? event.code : '';
      if (event.type === 'keyup' && (eventCode === 'Enter' || eventCode === 'NumpadEnter')) {
        this.coreModelsGrid.searchByTextSelect(this.inputSearchValue);
      }
    }
  }
}
