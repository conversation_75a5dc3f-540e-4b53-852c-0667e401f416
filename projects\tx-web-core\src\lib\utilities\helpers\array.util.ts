export class ArrayUtils {
  /**
   * Sort an array with a given property (ascending order)
   *
   * @param array The array to sort
   * @param property The name of the property
   */
  public static sortByProperty(array: any[], property: string = ''): any[] {
    return array.sort((a, b) => {
      const valueA = a[property] instanceof String ? a[property].toLowerCase() : a[property];
      const valueB = b[property] instanceof String ? b[property].toLowerCase() : b[property];
      if (valueA < valueB) {
        return -1;
      }
      if (valueA > valueB) {
        return 1;
      }
      return 0;
    });
  }

  /**
   * Sort an array with name property (ascending order)
   *
   * @param array The array to sort
   */
  public static sortByName(array: any[]): any[] {
    return array.sort((a, b) => {
      if (a.name.toLowerCase() < b.name.toLowerCase()) {
        return -1;
      }
      if (a.name.toLowerCase() > b.name.toLowerCase()) {
        return 1;
      }
      return 0;
    });
  }

  public static equalsCheck(a: any[], b: any[]): boolean {
    return JSON.stringify(a) === JSON.stringify(b);
  }

  public static equalsCheckString(a: string[], b: string[]): boolean {
    if (a.length !== b.length) {
      return false;
    }
    // sort the arrays
    const firstArray = [...a].sort((valueA, valueB) => valueA.localeCompare(valueB));
    const secondArray = [...b].sort((valueA, valueB) => valueA.localeCompare(valueB));
    // check equality
    return firstArray.every((val, index) => val === secondArray[index]);
  }

  /**
   * Returns the differences between two arrays
   * @param baseArray The basic array
   * @param compareArray The array to compare
   * @returns an object with added items and and removed items in compareArray compared to baseArray
   */
  public static getItemsDifferenceBetweenArrays(
    baseArray: string[],
    compareArray: string[]
  ): { addedItems: string[]; removedItems: string[] } {
    const addedItems: string[] = compareArray.filter((id) => !baseArray.includes(id));
    const removedItems: string[] = baseArray.filter((id) => !compareArray.includes(id));
    return { addedItems: addedItems, removedItems: removedItems };
  }

  /**
   * Add and remove elements from an array
   * @param array the array to add and remove from
   * @param add the elements to add
   * @param remove the elements to remove
   * @returns the new array with the elements added and removed
   */
  public static addAndRemoveItems(
    array: string[],
    addedItems: string[],
    removedItems: string[]
  ): string[] {
    let newArray = [...array];
    addedItems.forEach((id) => {
      if (!newArray.includes(id)) {
        newArray.push(id);
      }
    });
    newArray = newArray.filter((id) => !removedItems.includes(id));
    return newArray;
  }

  /**
   * according to a mergeFn logic
   * Merge objects that have the same keys together according to mergeFn param
   * warning: CompactErrors must be called with empty result for this to work
   * @param objects
   * @returns
   */
  public static compact<T>(
    key: keyof T,
    mergeFn: (el1: T, el2: T) => [T],
    objects: T[],
    result: T[] = []
  ): T[] {
    if (objects.length === 0) {
      return result;
    }
    if (objects.length === 1) {
      return result.concat(objects);
    }
    const [el1, el2, ...remain] = objects;
    if (el1[key] === el2[key]) {
      return this.compact(key, mergeFn, mergeFn(el1, el2).concat(remain), result);
    }
    const foundError = this.findWithPropKey(key, el1, remain, null);
    return foundError === null
      ? this.compact(key, mergeFn, [el2].concat(remain), result.concat(el1))
      : this.compact(key, mergeFn, mergeFn(el1, foundError[0]).concat(foundError[1], el2), result);
  }

  private static findWithPropKey<T>(
    key: keyof T,
    obj: T,
    objects: T[],
    result: [T, T[]] | null
  ): [T, T[]] | null {
    if (objects.length === 0) {
      return null;
    }
    const [el1, ...remain] = objects;
    return obj[key] === el1[key]
      ? [el1, result ? result[1].concat(remain) : remain]
      : this.findWithPropKey(key, obj, remain, [el1, result ? result[1].concat(el1) : [el1]]);
  }
}
