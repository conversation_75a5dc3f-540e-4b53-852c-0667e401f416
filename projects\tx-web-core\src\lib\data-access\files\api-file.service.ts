import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { throwError, Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class TxApiFileService {
  private apiUrl = '';
  public productionMode = true;

  constructor(private httpClient: HttpClient) {}

  start(apiUrl: string, productionMode = true) {
    this.apiUrl = apiUrl;
    this.productionMode = productionMode;
  }

  sendGet(url: string, header?: any): Observable<any> {
    if (this.checkApiUrlValid()) {
      return this.httpClient.get(this.apiUrl + url, header).pipe(catchError(this.handleError));
    } else {
      return new Observable();
    }
  }

  sendPost(url: string, args: any, header?: any): Observable<any> {
    if (this.checkApiUrlValid()) {
      return this.httpClient
        .post(this.apiUrl + url, args, header)
        .pipe(catchError(this.handleError));
    } else {
      return new Observable();
    }
  }

  downloadFile(id: number) {
    return this.sendGet(`api/File/download/${id}`, { responseType: 'blob' });
  }

  uploadFile(files: FormData, idAttribute: number) {
    return this.sendPost(`api/File/upload?idAttribute=${idAttribute}`, files, {
      reportProgress: true,
      observe: 'events',
    });
  }

  deleteCacheFile(idsFile: number[]) {
    return this.sendPost(`api/File/delete/cache`, idsFile);
  }

  private handleError(error: HttpErrorResponse) {
    let errorMessage: string;
    if (error.error instanceof ErrorEvent) {
      // Client-side errors
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side errors
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
    }
    return throwError(errorMessage);
  }

  private checkApiUrlValid() {
    if (!this.apiUrl) {
      return false;
    }

    return true;
  }
}
