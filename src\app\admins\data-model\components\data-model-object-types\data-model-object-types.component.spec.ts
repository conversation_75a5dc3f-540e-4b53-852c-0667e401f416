import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';
import {
  DataBaseRights,
  TxLockingType,
  TxObjectType,
  TxObjectTypeType,
  TxObjectTypesTreeGridComponent,
  TxObjectsTypeService,
} from '@bassetti-group/tx-web-core';
import { ObjectsTypeServiceMock, RightPaneServiceMock } from 'src/app/app.testing.mock';
import { RightPaneService } from 'src/app/core/right-pane/right-pane.service';
import { DataModelObjectTypesComponent } from './data-model-object-types.component';
import { MockComponent } from 'ng-mocks';

const TRANSLATIONS = {
  en: {},
  fr: {},
};

const objectTypeMock: TxObjectType = {
  id: 1,
  name: 'Contact',
  order: 0,
  icon: 0,
  isFolder: false,
  type: TxObjectTypeType.Standard,
  hasDistinctName: false,
  isVisible: false,
  tags: [],
  lockingType: TxLockingType.Auto,
  lockingDuration: 0,
  displayResultInTextSearch: true,
  right: DataBaseRights.DbrStructure,
  description: '',
  explanation: '',
  idObjectTypeParent: 0,
  options: undefined,
};

describe('DataModelObjectTypesComponent', () => {
  let component: DataModelObjectTypesComponent;
  let fixture: ComponentFixture<DataModelObjectTypesComponent>;
  let rightPaneService: RightPaneService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DataModelObjectTypesComponent, MockComponent(TxObjectTypesTreeGridComponent)],
      imports: [
        FontAwesomeTestingModule,
        MatTooltipModule,
        TranslateTestingModule.withTranslations(TRANSLATIONS),
      ],
      providers: [
        { provide: TxObjectsTypeService, useClass: ObjectsTypeServiceMock },
        { provide: RightPaneService, useClass: RightPaneServiceMock },
      ],
    }).compileComponents();
    rightPaneService = TestBed.inject(RightPaneService);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DataModelObjectTypesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  beforeEach(() => {
    component.objectType = { ...objectTypeMock };
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Tootlips', () => {
    it('should return add tooltip', () => {
      expect(component.getAddOTTooltip()).toBe('button.add');
    });

    it('should return delete tooltip', () => {
      expect(component.getDeleteOTTooltip()).toBe('button.delete');
    });

    it('should return edit tooltip', () => {
      expect(component.getEditOTTooltip()).toBe('button.edit');
    });

    it('should return editNotAllowed tooltip with DataBaseRights.dbrRead', () => {
      if (component.objectType) {
        component.objectType.right = DataBaseRights.DbrRead;
      }
      expect(component.getEditOTTooltip()).toBe('admins.dataModel.editNotAllowed');
    });

    it('should return editTooltipSelected tooltip with none object type', () => {
      component.objectType = null;
      expect(component.getEditOTTooltip()).toBe('admins.dataModel.editTooltipSelected');
    });

    it('should return edition not allowed tooltip', () => {
      component.objectType = null;
      expect(component.getEditOTTooltip()).toBe('admins.dataModel.editTooltipSelected');
    });
  });

  describe('Autorizations on buttons', () => {
    it('should return true to add OT', () => {
      expect(component.canAddOT()).toBe(true);
    });
    it('should return true to canEditOT OT', () => {
      expect(component.canAddOT()).toBe(true);
    });
  });

  describe('Add object type', () => {
    it('should call displayPane', () => {
      const spyDisplayPane = jest.spyOn(rightPaneService, 'showNewPaneWithTemplate');
      component.addObjectType();
      expect(spyDisplayPane).toHaveBeenCalled();
    });

    it('should assign new values to component.settingFormObjectType', () => {
      component.addObjectType();
      expect(component.settingFormObjectType).toStrictEqual({
        object: {
          id: 0,
          name: '',
          tags: [],
          idObjectTypeParent: 0,
          type: TxObjectTypeType.Standard,
          isFolder: false,
        },
        isEditMode: false,
      });
    });
  });

  describe('Edit object type', () => {
    it('should call displayPane', () => {
      const spyDisplayPane = jest.spyOn(rightPaneService, 'showNewPaneWithTemplate');
      component.editObjectType();
      expect(spyDisplayPane).toHaveBeenCalled();
    });

    it('should assign new values to component.settingFormObjectType', () => {
      component.editObjectType();
      expect(component.settingFormObjectType).toStrictEqual({
        object: objectTypeMock,
        isEditMode: true,
      });
    });
  });
});
