import { TestBed } from '@angular/core/testing';
import { CoreModelsImportService } from './core-models-import.service';
import { MockProvider } from 'ng-mocks';
import {
  TESTED_CORE_MODELS_CONCEPTS_MOCK,
  CORE_MODEL_IMPORT_DATA_SERVICE_MOCK,
  VALID_ARCHIVE_FILE,
  IMPORTED_CONCEPTS,
} from './core-models-import.http.service.mock';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { TRANSLATIONS_MOCK } from 'src/app/core/translation/translations.mock';
import { ModelApplicationService } from 'src/app/core/services/structure/model-application.service';
import {
  CommonServiceMock,
  ErrorServiceMock,
  ModelApplicationServiceMock,
  ObjectsTypeServiceMock,
} from 'src/app/app.testing.mock';
import { NbErrorsPipe } from 'src/app/shared/pipes/nb-errors.pipe';
import { ErrorService } from 'src/app/core/services/errors/error.service';
import { ErrorMessagesService } from 'src/app/core/error-messages/error-messages.service';
import { ERROR_MESSAGE_SERVICE_MOCK } from 'src/app/core/error-messages/error-messages.service.mock';
import { SessionService } from 'src/app/core/services/session/session.service';
import { sessionServiceMock } from 'src/app/core/services/session/session.service.mock';
import { CoreModelImportGatewayService } from './core-model-import-gateway.service';
import { CoreModelImportHistory } from '../models/import-history.model';
import { ModificationTypeEnum } from '../enums/modification.enum';
import { ArchiveData, ArchiveStatus } from '../models/archive-info.model';
import { TestedCoreModelConcept } from '../models/tested-core-model-concept.model';
import { ImportedCoreModelConcept } from '../models/imported-core-model-concept.model';
import { ConceptType, TxCommonService, TxObjectsTypeService } from '@bassetti-group/tx-web-core';

const historyStored: CoreModelImportHistory[] = [
  {
    id: 'id_1',
    date: new Date(),
    status: 'Success',
    username: 'user',
    name: 'name',
    version: '0.0.1',
    explanation: 'explanation',
    comment: 'comment',
    concepts: [
      {
        type: ConceptType.Attribute,
        id: 1,
        tags: ['tags'],
        icon: 1,
        name: 'name',
        modificationType: ModificationTypeEnum.Creation,
        translatedConflicts: ['string'],
        conflicts: ['conflicts'],
        translatedModificationType: 'Add',
      },
    ],
  },
];
const ARCHIVE_STORED: Omit<ArchiveData, 'archiveInfo'> = {
  id: 'string',
  cacheId: 1,
  file: VALID_ARCHIVE_FILE,
  status: ArchiveStatus.Valid,
  history: [],
};
describe('CoreModelsImportDataService', () => {
  let service: CoreModelsImportService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [TranslateTestingModule.withTranslations(TRANSLATIONS_MOCK)],
      providers: [
        CoreModelsImportService,
        {
          provide: ModelApplicationService,
          useClass: ModelApplicationServiceMock,
        },
        NbErrorsPipe,
        {
          provide: TxCommonService,
          useClass: CommonServiceMock,
        },
        {
          provide: ErrorService,
          useClass: ErrorServiceMock,
        },
        { provide: TxObjectsTypeService, useClass: ObjectsTypeServiceMock },
        MockProvider(ErrorMessagesService, ERROR_MESSAGE_SERVICE_MOCK, 'useValue'),
        MockProvider(SessionService, sessionServiceMock, 'useValue'),
        MockProvider(
          CoreModelImportGatewayService,
          CORE_MODEL_IMPORT_DATA_SERVICE_MOCK,
          'useValue'
        ),
      ],
    });
    service = TestBed.inject(CoreModelsImportService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should load history', () => {
    service.loadHistory();
    service.historyList$.subscribe((history) => {
      expect(history[0].name).toBe(historyStored[0].name);
      expect(history[0].status).toBe(historyStored[0].status);
      expect(history[0].version).toBe(historyStored[0].version);
    });
  });
  it('should validate archive', () => {
    service.validateArchive(VALID_ARCHIVE_FILE);
    service.archiveData$.subscribe((archive) => {
      expect(archive?.file).toStrictEqual<File>(ARCHIVE_STORED.file);
    });
  });
  it('should test import', () => {
    service.testImport();
    service.concepts$.subscribe((concepts) => {
      expect(concepts).toStrictEqual<TestedCoreModelConcept[]>(TESTED_CORE_MODELS_CONCEPTS_MOCK);
    });
    service.archiveData$.subscribe((archive) => {
      expect(archive).toStrictEqual<Omit<ArchiveData, 'archiveInfo'>>({
        ...ARCHIVE_STORED,
        status: ArchiveStatus.TestImportValid,
      });
    });
  });
  it('should import core model', () => {
    service.import();
    service.concepts$.subscribe((concepts) => {
      expect(concepts).toStrictEqual<ImportedCoreModelConcept[]>(IMPORTED_CONCEPTS);
    });
    service.archiveData$.subscribe((archive) => {
      expect(archive).toStrictEqual<Omit<ArchiveData, 'archiveInfo'>>({
        ...ARCHIVE_STORED,
        status: ArchiveStatus.Imported,
      });
    });
  });
  it('should cancel archive', () => {
    service.cancelArchive();
    service.concepts$.subscribe((concepts) => {
      expect(concepts).toStrictEqual<ImportedCoreModelConcept[]>([]);
    });
    service.archiveData$.subscribe((archive) => {
      expect(archive).toStrictEqual<null>(null);
    });
  });
  it('should filter concepts on conflicts', () => {
    service.testImport();
    service.filterOnConceptsInConflicts();
    service.concepts$.subscribe((concepts) => {
      expect(concepts).toStrictEqual<TestedCoreModelConcept[]>([
        TESTED_CORE_MODELS_CONCEPTS_MOCK[0],
      ]);
    });
  });
  it('should remove filter concepts on conflicts', () => {
    service.testImport();
    service.filterOnConceptsInConflicts();
    service.removeFilterOnConceptsInConflicts();
    service.concepts$.subscribe((concepts) => {
      expect(concepts).toStrictEqual<TestedCoreModelConcept[]>(TESTED_CORE_MODELS_CONCEPTS_MOCK);
    });
  });
});
