import { Injectable } from '@angular/core';
import { BehaviorSubject, forkJoin, Observable } from 'rxjs';
import { registerLocaleData } from '@angular/common';
import localeUS from '@angular/common/locales/en';
import localeGB from '@angular/common/locales/en-GB';
import localeFrench from '@angular/common/locales/fr';
import localeDeutsch from '@angular/common/locales/de';
import localeItalian from '@angular/common/locales/it';
import localeEspana from '@angular/common/locales/es';
import localeChinese from '@angular/common/locales/zh';
import { HttpClient } from '@angular/common/http';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { L10n, setCulture } from '@syncfusion/ej2-base';
import { Lang } from '../../main-nav/main-nav.component';
import { tap, catchError, mergeMap, filter, combineLatestWith, map } from 'rxjs/operators';
import { About } from '../../about/about';
import { TranslationParser } from '../../translation/translation-parser';
import { TranslateService } from '@ngx-translate/core';
import {
  ConnectedUser,
  ConnectedUserDTO,
  TxConfigService,
  AdminRights,
  AbstractSessionService,
  StringUtils,
  DatabaseDocType,
  AdministrationSettings,
  ConfigurationSettings,
  PreferencesSettings,
  TxApplicationAccessMode,
  LocaleService,
  ConnectedUserState,
} from '@bassetti-group/tx-web-core';
import { EnvironmentConstant } from 'src/app/shared/models/environment-constant';
import { EnvironmentConstantsService } from '../structure/environment-constants.service';
import {
  CORE_MODELS_EXPORT_ENV_CONSTANT,
  CORE_MODELS_IMPORT_ENV_CONSTANT,
} from 'src/app/shared/models/env-constants';
import { ActivatedRouteSnapshot, Route, Router, UrlTree } from '@angular/router';
import { environment } from 'src/environments/environment';
import { RouteUtils } from '../../utils/route.utils';

@Injectable({
  providedIn: 'root',
})
export class SessionService extends AbstractSessionService {
  public currentLang?: Lang;
  public about?: About;
  public documentsStorageType: DatabaseDocType = DatabaseDocType.directory;
  public connectedUserRights: AdminRights[] = [];
  public connectedUser$: Observable<ConnectedUserState>;
  public connectedUserSub = new BehaviorSubject<ConnectedUserState>(undefined);
  private apiUrl?: string;
  private defaultLang?: Lang;
  private langs: Lang[] = [];
  private langsSub: BehaviorSubject<Lang[]> = new BehaviorSubject<Lang[]>([]);
  private loadingLangSub: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private defLangSub: BehaviorSubject<Lang> = new BehaviorSubject<Lang>({
    id: 0,
    name: _('lang.default'),
    code: 'en',
    languageUsedCode: 'en',
  });
  private loadedLangs: string[] = [];
  private strongPwdPolicy = false;
  private administrationSettings?: AdministrationSettings;
  private sessionTimeoutSub: BehaviorSubject<number> = new BehaviorSubject(0);
  private isAuthenticationDelegated: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private NB_OT_ICONS_BASE = 300;

  constructor(
    private http: HttpClient,
    private configService: TxConfigService,
    private translate: TranslateService,
    private localeService: LocaleService,
    private environmentConstants: EnvironmentConstantsService,
    private router: Router
  ) {
    super();
    this.connectedUser$ = this.connectedUserSub.asObservable();
  }

  init() {
    this.apiUrl = this.configService.getApiUrl();
  }

  load(codeLang?: string) {
    this.http
      .get<ConfigurationSettings>(`${this.apiUrl}api/Configuration`)
      .pipe(
        tap((config) => {
          this.langs = config.languages;
          // Map tp prepare language to manage fake languages (with random codelang)
          // The code is the language code given by back-end. The languageUsedCode is the code that will be used for translation if the first code is fake.
          // Example : ForTreeView language code : 'FT', languageUsedCode: 'fr'
          // This languageUsedCode is the code of default language or the "en" if there is no default language.
          // If the language have real code lang the both will be the same. Example : name : Français, code : fr, languageUsedCode : fr
          this.langs.forEach((lang) => {
            lang.code = lang.code.toLowerCase();
            lang.languageUsedCode = lang.code;
          });

          this.defaultLang = config.defaultLanguageCode
            ? {
                id: 0,
                name: _('lang.default'),
                code: config.defaultLanguageCode,
                languageUsedCode: config.defaultLanguageCode,
              }
            : { id: 0, name: _('lang.default'), code: 'en', languageUsedCode: 'en' };

          const subTranslation = this.translate
            .getTranslation(this.defaultLang.code)
            .subscribe((response: any) => {
              if (response === 'error') {
                // if trad not found for default language, consider EN as default language
                this.defaultLang = {
                  id: 0,
                  name: _('lang.default'),
                  code: 'en',
                  languageUsedCode: 'en',
                };
              }
              subTranslation.unsubscribe();
              if (this.defaultLang) {
                this.defLangSub.next(this.defaultLang);
              }
              this.doLoadLanguage(codeLang);
            });

          if (config.authenticationModes.internal) {
            this.strongPwdPolicy = config.authenticationModes.internal.useStrongPasswordPolicy;
          }
          this.isAuthenticationDelegated.next(
            Boolean(
              config.authenticationModes.ldap?.enabled ||
                config.authenticationModes.saml?.enabled ||
                config.authenticationModes.windows?.enabled
            )
          );

          this.preferences.next(config.preferences);
          this.administrationSettings = config.administration;
          //"documentsStorageType": "dsmDirectory"
          this.documentsStorageType = config.documentsStorageMode;
          this.sessionTimeoutSub.next(config.administration.timeoutDuration || 120);
          this.sessionTimeoutSub.complete();
        }),
        catchError((error: any) => {
          this.defaultLang = { id: 0, name: _('lang.default'), code: 'en', languageUsedCode: 'en' };
          this.doLoadLanguage('en');
          return error;
        }),
        mergeMap(() =>
          this.http.get<ConnectedUserDTO>(`${this.apiUrl}api/Users/<USER>/current`).pipe(
            combineLatestWith(
              this.environmentConstants
                .getConstants()
                .pipe(
                  filter(
                    (constants: EnvironmentConstant[] | null): constants is EnvironmentConstant[] =>
                      constants !== null
                  )
                )
            ),
            tap(([user, constants]) => {
              user.isLoaded = true;
              const connectedUser = this.getConnectedUserWithRights(user, constants);
              this.connectedUserSub.next(connectedUser);
            })
          )
        )
      )
      .subscribe();
  }

  isFolderStorageType(): boolean {
    return this.documentsStorageType === DatabaseDocType.directory;
  }

  doLoadLanguage(codeLang?: string): void {
    if (this.defaultLang && this.defaultLang.code !== 'en') {
      (this.translate.currentLoader as TranslationParser).setDefaultTranslation(
        this.defaultLang.code
      );
    }

    // add default language if no language is set or if no langs with same code as default language
    this.addDefaultLanguage();

    // define the initial language
    this.defineInitialLanguage(codeLang);

    this.setAvailableLangs();
  }

  getLoadingState(): Observable<boolean> {
    return this.loadingLangSub.asObservable();
  }

  getLanguages(): Observable<Lang[]> {
    return this.langsSub.asObservable();
  }

  getDefaultLanguage(): Observable<Lang> {
    return this.defLangSub.asObservable();
  }

  getVersionsInformation(reload: boolean = false): Observable<About> {
    return new Observable<About>((observer) => {
      if (reload || !this.about) {
        this.http.get<any>(`${this.apiUrl}api/Configuration/about`).subscribe((version) => {
          this.about = About.assign(version);
          observer.next(this.about);
          observer.complete();
        });
      } else {
        observer.next(this.about);
        observer.complete();
      }
    });
  }

  getConnectedUser(): Observable<ConnectedUser> {
    return this.connectedUser$.pipe(
      filter((user: ConnectedUser | undefined | null): user is ConnectedUser => user != null)
    );
  }

  getSessionTimeout(): Observable<number> {
    return this.sessionTimeoutSub.asObservable();
  }

  getPreferences(): PreferencesSettings | undefined {
    return this.preferences.value;
  }

  getAdministrationSettings(): AdministrationSettings | undefined {
    return this.administrationSettings;
  }

  getPreference(preferenceName: keyof PreferencesSettings): string | undefined {
    return this.preferences.value ? this.preferences.value[preferenceName] : undefined;
  }

  getDateFormatPreference() {
    return this.preferences.value?.sShortDateFormat;
  }

  getDateAndTimeFormatPreference() {
    return this.preferences.value?.sDateAndTimeFormat;
  }

  getDelegateAuthentication() {
    return this.isAuthenticationDelegated.asObservable();
  }

  getStrongPwdPolicy(): boolean {
    return this.strongPwdPolicy;
  }

  isNavigationToTEEXMAllowed(): boolean {
    if (!this.administrationSettings?.teexmaAccessSettings) {
      return false;
    }

    if (
      this.administrationSettings?.teexmaAccessSettings?.accessMode ===
      TxApplicationAccessMode.Disabled
    ) {
      return false;
    }

    if (
      this.administrationSettings?.teexmaAccessSettings.accessMode ===
        TxApplicationAccessMode.Absolute &&
      !this.administrationSettings?.teexmaAccessSettings.url
    ) {
      return false;
    }

    return true;
  }

  getTEEXMAUrl(): string {
    let url = '';
    switch (this.administrationSettings?.teexmaAccessSettings.accessMode) {
      case TxApplicationAccessMode.Absolute:
        if (this.administrationSettings.teexmaAccessSettings.url) {
          url = StringUtils.getExternalUrlFormatted(
            this.administrationSettings.teexmaAccessSettings.url
          );
        }
        break;
      case TxApplicationAccessMode.Relative:
        url = window.origin;
        break;
    }

    return url;
  }

  registerCulture(lang: Lang, reset?: boolean) {
    if (!lang) {
      return;
    } else if (reset) {
      this.currentLang = lang;
      this.loadingLangSub.next(false);
      return this.loadLanguage();
    }

    this.currentLang = lang;
    localStorage.setItem('userLang', this.currentLang.code);

    setCulture(this.currentLang.languageUsedCode);
    this.localeService.setLocale(this.currentLang.languageUsedCode);
    this.translate.use(this.currentLang.languageUsedCode);

    if (
      this.preferences.value?.sShortDateFormat &&
      this.preferences.value.sShortDateFormat !== ''
    ) {
      this.localeService.setDateFormat(this.preferences.value.sShortDateFormat);
    } else {
      this.localeService.setDateFormat(null);
    }

    // Register locale data since only the en-US locale data comes with Angular
    switch (this.currentLang.languageUsedCode) {
      case 'fr':
      case 'fr-FR': {
        registerLocaleData(localeFrench);
        break;
      }
      case 'en':
      case 'en-US': {
        registerLocaleData(localeUS);
        break;
      }
      case 'en-GB': {
        registerLocaleData(localeGB);
        break;
      }
      case 'de':
      case 'de-DE': {
        registerLocaleData(localeDeutsch);
        break;
      }
      case 'it':
      case 'it-IT': {
        registerLocaleData(localeItalian);
        break;
      }
      case 'es':
      case 'es-ES': {
        registerLocaleData(localeEspana);
        break;
      }
      case 'zh':
      case 'zh-ZH': {
        registerLocaleData(localeChinese);
        break;
      }
    }
  }

  setAvailableLangs(): void {
    forkJoin(
      this.langs.map((lang) =>
        this.translate.getTranslation(lang.code).pipe(
          tap((response: any) => {
            if (this.defaultLang && response === 'error') {
              lang.languageUsedCode = this.defaultLang.code;
            }
          })
        )
      )
    ).subscribe(() => {
      this.langsSub.next(this.langs);
      this.loadLanguage();
    });
  }

  accessAdminPage(
    route: ActivatedRouteSnapshot | Route
  ): Observable<boolean | UrlTree> | boolean | UrlTree {
    const adminRights = route?.data?.adminRights;
    if (route?.data?.hidden) {
      return this.router.parseUrl(`/${RouteUtils.DASHBOARD_PATH}`);
    } else if (this.isAdminRights(adminRights)) {
      return this.getConnectedUser().pipe(
        filter(
          (user: ConnectedUser | null | undefined): user is ConnectedUser | null =>
            user !== undefined
        ),
        map((user) =>
          user && this.hasRightsOn(adminRights, user.adminRights)
            ? true
            : this.router.parseUrl(`/${RouteUtils.DASHBOARD_PATH}`)
        )
      );
    } else if (route?.data?.developOnly) {
      return environment.production ? this.router.parseUrl(`/${RouteUtils.DASHBOARD_PATH}`) : true;
    }
    return false;
  }

  loadLanguage() {
    if (this.currentLang) {
      this.translate.getTranslation(this.currentLang.languageUsedCode).subscribe((translations) => {
        if (this.currentLang) {
          if (!this.loadedLangs.some((l) => l === this.currentLang?.languageUsedCode)) {
            const lang: any = {};
            lang[this.currentLang.languageUsedCode] = this.getObjectLanguage(translations);
            L10n.load(lang);
            this.loadedLangs.push(this.currentLang.languageUsedCode);
          }

          this.registerCulture(this.currentLang);
          setTimeout(() => {
            this.loadingLangSub.next(true);
          }, 100);
        }
      });
    }
  }

  getTranslation(translation: { [x: string]: any }, key: string) {
    const keys = key.split('.');
    let result = translation[keys[0]];
    keys.shift();

    if (result) {
      keys.forEach((element: string | number) => {
        result = result[element];
      });

      return result;
    } else {
      return key;
    }
  }

  getObjectLanguage(translations: { [x: string]: any }) {
    return {
      grid: {
        emptyRecord: this.getTranslation(translations, _('syncFusion.grid.EmptyRecord')),
      },
      pager: {
        currentPageInfo: this.getTranslation(translations, _('syncFusion.pager.currentPageInfo')),
        totalItemsInfo: this.getTranslation(translations, _('syncFusion.pager.totalItemsInfo')),
        firstPageTooltip: this.getTranslation(translations, _('syncFusion.pager.firstPageTooltip')),
        lastPageTooltip: this.getTranslation(translations, _('syncFusion.pager.lastPageTooltip')),
        nextPageTooltip: this.getTranslation(translations, _('syncFusion.pager.nextPageTooltip')),
        previousPageTooltip: this.getTranslation(
          translations,
          _('syncFusion.pager.previousPageTooltip')
        ),
        nextPagerTooltip: this.getTranslation(translations, _('syncFusion.pager.nextPagerTooltip')),
        previousPagerTooltip: this.getTranslation(
          translations,
          _('syncFusion.pager.previousPagerTooltip')
        ),
      },
      uploader: {
        delete: this.getTranslation(translations, _('syncFusion.uploader.delete')),
        remove: this.getTranslation(translations, _('syncFusion.uploader.remove')),
        uploadSuccessMessage: this.getTranslation(
          translations,
          _('syncFusion.uploader.uploadSuccessMessage')
        ),
        uploadFailedMessage: this.getTranslation(
          translations,
          _('syncFusion.uploader.uploadFailedMessage')
        ),
        invalidFileType: this.getTranslation(
          translations,
          _('syncFusion.uploader.invalidFileType')
        ),
      },
      datepicker: {
        today: this.getTranslation(translations, _('syncFusion.datepicker.today')),
      },
    };
  }
  hasRightsOn(mandatoryRights: AdminRights | AdminRights[], userRights: AdminRights[]): boolean {
    return [mandatoryRights]
      .flat()
      .some((right) => userRights.some((userRight) => userRight === right));
  }

  getSpecificIconsZip(): Observable<Blob> {
    return this.http.get(`${this.apiUrl}api/Configuration/getSpecificIcons`, {
      responseType: 'blob',
    });
  }
  //private methods used in doLoadLanguages
  private isAdminRights(
    adminRights: AdminRights | AdminRights[] | undefined
  ): adminRights is AdminRights | AdminRights[] {
    return adminRights !== undefined && adminRights !== null;
  }

  private addDefaultLanguage() {
    if (this.langs.length === 0 || !this.langs.some((l) => l.code === this.defaultLang?.code)) {
      if (this.defaultLang) {
        this.langs.unshift(this.defaultLang);
      }
    } else {
      const dLang = this.langs.find((l) => l.code === this.defaultLang?.code);
      if (dLang) {
        dLang.nameBis = dLang?.name;
        dLang.name = _('lang.specific');
        // put default lang at first position
        const compareLangNames = (x: Lang, y: Lang) => {
          if (x.name === dLang.name) {
            return -1;
          } else if (y.name === dLang.name) {
            return 1;
          } else {
            return 0;
          }
        };
        this.langs.sort(compareLangNames);
      }
    }
  }

  private defineInitialLanguage(codeLang: string | undefined) {
    if (codeLang && this.langs.some((l) => l.code === codeLang)) {
      this.currentLang = this.langs.find((l) => l.code === codeLang);
    } else if (
      localStorage.getItem('userLang') !== null &&
      this.langs.some((l) => l.code === localStorage.getItem('userLang'))
    ) {
      this.currentLang = this.langs.find((l) => l.code === localStorage.getItem('userLang'));
    } else {
      this.currentLang = this.defaultLang;
    }
  }

  private getConnectedUserWithRights(
    user: ConnectedUserDTO,
    constants: EnvironmentConstant[]
  ): ConnectedUser {
    const adminRights = [
      user.isAdmin ? [AdminRights.IsAdmin] : [],
      user.canAdministrateRights ? [AdminRights.CanAdministrateRights] : [],
      user.canAdministrateStructure ? [AdminRights.CanAdministrateStructure] : [],
      this.hasCoreModelsExportAccessRights(constants) && user.isAdmin
        ? [AdminRights.CoreModelsExport]
        : [],
      this.hasCoreModelsImportAccessRights(constants) && user.isAdmin
        ? [AdminRights.CoreModelsImport]
        : [],
      user.canDoMassImportation ? [AdminRights.CanDoMassImportation] : [],
      user.canExportAndExtract ? [AdminRights.CanExportAndExtract] : [],
      user.canExecuteMCSAndChoiceguide ? [AdminRights.CanExecuteMCSAndChoiceguide] : [],
      user.canDoDataMining ? [AdminRights.CanDoDataMining] : [],
    ].flat();
    return {
      name: user.name,
      lastConnectionDate: user.lastConnectionDate,
      adminRights,
      isLoaded: user.isLoaded,
    };
  }

  private hasCoreModelsExportAccessRights(constants: EnvironmentConstant[]) {
    return constants.some(
      (constant) =>
        constant.key === CORE_MODELS_EXPORT_ENV_CONSTANT.key &&
        constant.value === CORE_MODELS_EXPORT_ENV_CONSTANT.value
    );
  }
  private hasCoreModelsImportAccessRights(constants: EnvironmentConstant[]) {
    return constants.some(
      (constant) =>
        constant.key === CORE_MODELS_IMPORT_ENV_CONSTANT.key &&
        constant.value === CORE_MODELS_IMPORT_ENV_CONSTANT.value
    );
  }
}
