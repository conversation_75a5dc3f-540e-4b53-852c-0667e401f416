import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  Input,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  UntypedFormGroup,
  UntypedFormControl,
  AbstractControl,
  ValidationErrors,
  Validators,
  ValidatorFn,
} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TxAutomaticNamingService } from 'src/app/admins/automatic-naming/services/automatic-naming.service';
import {
  Configuration,
  ConfigurationSubConcept,
  ConfigurationSubConceptElement,
  FrameworkConfigFormSettings,
} from 'src/app/shared/components/configurations-framework/models/configurations-framework-model';
import { ConfigurationsFrameworkComponent } from 'src/app/shared/components/configurations-framework/configurations-framework.component';
import {
  NamingSettingsCaseValue,
  NamingSettingsIndexType,
  NamingSettingsIncrementationType,
  NamingSettings,
  NamingSettingsType,
} from '../models/naming-settings';
import { Observable, Subject, takeUntil } from 'rxjs';
import { SessionService } from 'src/app/core/services/session/session.service';
import {
  TxAttributesService,
  TxAttribute,
  TxObjectsService,
  TxObjectsTypeService,
  StringUtils,
  ToastComponent,
  ToastService,
  ToastType,
  AttributeTreeGridComponent,
  TxObjectType,
  TxObjectTypeType,
  TxAttributeCheckChangeEvent,
  CTxAttributeSetLevel,
  TxObjectsTypeDropdownComponent,
  TxDataType,
  TxObject,
  TxDialogService,
  ConnectedUser,
} from '@bassetti-group/tx-web-core';
import { CustomValidators } from 'src/app/core/utils/validators';
import { AttributeSetsService } from 'src/app/core/services/structure/attribute-sets.service';
import { NumberUtils } from 'src/app/core/utils/number';
import { RightPaneRef } from 'src/app/core/right-pane/right-pane.ref';
import { RightPaneService } from 'src/app/core/right-pane/right-pane.service';


@Component({
  selector: 'app-automatic-naming',
  templateUrl: './automatic-naming.component.html',
  styleUrls: ['./automatic-naming.component.scss'],
})
export class AutomaticNamingComponent
  implements OnInit, AfterViewInit, AfterViewChecked, OnDestroy
{
  @ViewChild('addConfig') public addConfig?: TemplateRef<unknown>;
  @ViewChild('specificAMFields') public specificAMFields?: TemplateRef<unknown>;
  @ViewChild('nameInputConfigForm') public nameInputConfigForm?: ElementRef<HTMLInputElement>;
  @ViewChild('addSubconcept1') public addSubconcept1?: TemplateRef<unknown>;
  @ViewChild('objectSelection') public objectSelectionTemplate?: TemplateRef<unknown>;
  @ViewChild('attributeSelection') public attributeSelectionTemplate?: TemplateRef<unknown>;
  @ViewChild('storageAttributeSelection')
  public storageAttributeSelectionTemplate?: TemplateRef<unknown>;

  @ViewChild('dataAttributeTreeGrid') public dataAttributeTreeGrid?: AttributeTreeGridComponent;
  @ViewChild('relativeIndexAttributeTreeGrid')
  public relativeIndexAttributeTreeGrid?: AttributeTreeGridComponent;
  @ViewChild('configFramework') public configFramework?: ConfigurationsFrameworkComponent;
  @ViewChild('oTDropDownConfigForm') public oTDropDownConfigForm?: TxObjectsTypeDropdownComponent;

  @Input() isInsideRightPane?: boolean;

  private rightPaneRef?: RightPaneRef;
  public configurations: Configuration[] = [];
  public configurationSelected?: Configuration;
  public dataTypeSelected?: string;
  public buttons = [];
  public isConfigsLoaded = false;
  public objectTypes: TxObjectType[] = [];
  public configFormOTDropDownDisabled = false;
  public addConfigForm = new UntypedFormGroup({
    id: new UntypedFormControl(0),
    name: new UntypedFormControl('New Configuration', [
      Validators.required,
      this.isNameUniqueValidator(),
    ]),
    tag: new UntypedFormControl('NewConfiguration', [
      Validators.required,
      this.isTagUniqueValidator(),
    ]),
    objectTypeId: new UntypedFormControl(1, Validators.required),
  });
  public attributeTypeSelected?: TxDataType | null;
  public dataType = TxDataType;
  public objectTypeConfigSelected?: TxObjectType;
  public objectSelectionObjectType?: TxObjectType;
  public caseValue = NamingSettingsCaseValue;
  public indexType = NamingSettingsIndexType;
  public incrType = NamingSettingsIncrementationType;
  public subConceptEdited?: NamingSettings;
  public settingsType = NamingSettingsType;
  public idsAttributesUntagged: { dataAttribute: number[]; indexAttributeIncrement: number[] } = {
    dataAttribute: [],
    indexAttributeIncrement: [],
  };
  public attributesAutotagRequired = false;
  public addNamingSubConceptForm = new UntypedFormGroup({
    id: new UntypedFormControl(0),
    type: new UntypedFormControl(NamingSettingsType.data),
    constantText: new UntypedFormControl(''),
    dateFormat: new UntypedFormControl('dd/MM/yyyy'),
    dataAttribute: new UntypedFormControl(''),
    dataCase: new UntypedFormControl(NamingSettingsCaseValue.asIs),
    dataPrefix: new UntypedFormControl(''),
    dataSuffix: new UntypedFormControl(''),
    dataMaximumSize: new UntypedFormControl('', Validators.min(0)),
    dataPerValue: new UntypedFormControl(false),
    dataAbbreviation: new UntypedFormControl('...'),
    dataValueSeparator: new UntypedFormControl(''),
    dataTypeTranslationNeeded: new UntypedFormControl(false),
    dataTypeFormatValueAsIndex: new UntypedFormControl(false),
    dataTypeIndexType: new UntypedFormControl(NamingSettingsIndexType.alphaLower),
    dataTypeNumberDigits: new UntypedFormControl(2, Validators.min(0)),
    dataTypeRemoveExtention: new UntypedFormControl(false),
    indexGlobalType: new UntypedFormControl(NamingSettingsIncrementationType.data),
    indexObject: new UntypedFormControl(''),
    indexAttribute: new UntypedFormControl(''),
    indexType: new UntypedFormControl(NamingSettingsIndexType.alphaLower),
    indexStartIndex: new UntypedFormControl(''),
    indexIncrement: new UntypedFormControl(false),
    indexNumberDigits: new UntypedFormControl(0, Validators.min(0)),
    indexAttributeIncrement: new UntypedFormControl(''),
    indexStorageAttribute: new UntypedFormControl(''),
  });

  public addConfigFormSettings?: FrameworkConfigFormSettings;
  public addMAOptionalFields?: FrameworkConfigFormSettings;
  public addMailSettingFormSettings?: FrameworkConfigFormSettings;
  public isLangLoading = true;

  public selectedObject?: TxObject;
  public selectedAttribute?: TxAttribute;
  public selectedStorageAttribute?: TxAttribute;
  public errorMessage?: string;

  public previewNameExampleTooltip = '';
  public today = new Date();
  public itemHovered?: ConfigurationSubConceptElement;
  public itemSelected?: ConfigurationSubConceptElement;

  public connectedUser?: ConnectedUser;
  public regexBLKValue = /#BLK#/g;

  private readonly _destroying = new Subject<void>();

  private listObjects?: TxObject[];

  constructor(
    private readonly cdRef: ChangeDetectorRef,
    private readonly objectTypeService: TxObjectsTypeService,
    private readonly attributeService: TxAttributesService,
    private readonly attributeSetService: AttributeSetsService,
    private readonly toastService: ToastService,
    private readonly objectService: TxObjectsService,
    private readonly namingConfigurationService: TxAutomaticNamingService,
    private readonly translate: TranslateService,
    private readonly sessionService: SessionService,
    private readonly dialogConfirmService: TxDialogService,
    private readonly rightPaneService: RightPaneService
  ) {}

  public get adminSettingFormType() {
    return this.addNamingSubConceptForm.get('type')?.value;
  }

  get dataAttribute() {
    return this.addNamingSubConceptForm.get('dataAttribute');
  }
  get indexAttribute() {
    return this.addNamingSubConceptForm.get('indexAttribute');
  }
  get indexStorageAttribute() {
    return this.addNamingSubConceptForm.get('indexStorageAttribute');
  }
  get indexAttributeIncrement() {
    return this.addNamingSubConceptForm.get('indexAttributeIncrement');
  }
  get indexObject() {
    return this.addNamingSubConceptForm.get('indexObject');
  }
  get indexDataObjectIcon() {
    if (!this.indexObject?.value) {
      return '';
    }
    return this.objectTypeService.getIconPath(this.indexObject.value.idObjectType);
  }
  get indexDataAttributeIcon() {
    const attributeLevelSelected = this.indexAttribute?.value;
    if (!attributeLevelSelected) {
      return '';
    }
    return this.attributeService.getIconPath(attributeLevelSelected.idAttribute);
  }

  ngOnInit(): void {
    this.previewNameExampleTooltip = this.translate.instant(_('admins.automaticNaming.previewNameExample'));
    this.loadConfigurations();
    this.attributeService.listAll().subscribe((ots: TxAttribute[]) => {
      this.objectService.getTaggedObjects().subscribe((obj) => {
        this.listObjects = obj;
        if (this.configurationSelected){
          this.setElementPreview();
        }
      });
    });
    
    this.objectTypeService
      .listAll()
      .pipe(takeUntil(this._destroying))
      .subscribe((ots: TxObjectType[]) => {
        this.objectTypes = ots.filter(
          (ot) => ot.type === TxObjectTypeType.Standard || ot.type === TxObjectTypeType.User
        );
      });
    this.initConfigForm();
    this.initNamingSettingForm();
    this.sessionService
      .getLoadingState()
      .pipe(takeUntil(this._destroying))
      .subscribe((state) => {
        this.isLangLoading = !state;
      });

    this.addNamingSubConceptForm
      .get('indexObject')
      ?.valueChanges.pipe(takeUntil(this._destroying))
      .subscribe((value: TxObject | undefined) => {
        this.selectedObject = value;
      });

    this.addNamingSubConceptForm
      .get('indexAttribute')
      ?.valueChanges.pipe(takeUntil(this._destroying))
      .subscribe((value: CTxAttributeSetLevel | null) => {
        if (value) {
          this.selectedAttribute = this.attributeService.getByID(value.idAttribute);
        } else {
          this.selectedAttribute = undefined;
        }
      });

    this.addNamingSubConceptForm
      .get('indexStorageAttribute')
      ?.valueChanges.pipe(takeUntil(this._destroying))
      .subscribe((value: CTxAttributeSetLevel | null) => {
        if (value) {
          this.selectedStorageAttribute = this.attributeService.getByID(value.idAttribute);
        } else {
          this.selectedStorageAttribute = undefined;
        }
      });

    this.addNamingSubConceptForm
      .get('indexAttribute')
      ?.statusChanges.pipe(takeUntil(this._destroying))
      .subscribe(() => {
        if (this.indexAttribute?.hasError('sameOT')) {
          this.errorMessage = _('admins.automaticNaming.objAttrSameOTError');
        } else {
          this.errorMessage = '';
        }
      });

    this.sessionService.getConnectedUser().subscribe((user) => {
      this.connectedUser = user;
    });
  }

  ngAfterViewInit(): void {
    this.addConfigFormSettings = {
      formGroup: this.addConfigForm,
      template: this.addConfig,
    };

    this.addMailSettingFormSettings = {
      formGroup: this.addNamingSubConceptForm,
      template: this.addSubconcept1,
      addFormTitle: this.translate.instant(_('admins.automaticNaming.addNamingSetting')),
      checkBeforeValidate: true,
    };
  }

  ngAfterViewChecked() {
    this.cdRef.detectChanges();
  }

  ngOnDestroy(): void {
    this._destroying.next();
    this._destroying.complete();
  }

  updateConfigNameValue(newName: string) {
    if (!this.addConfigForm.get('name')?.dirty && !this.addConfigForm.get('tag')?.disabled) {
      this.addConfigForm.get('name')?.setValue(newName);
    }
  }

  updateConfigTagValue() {
    if (!this.addConfigForm.get('tag')?.dirty && !this.addConfigForm.get('tag')?.disabled) {
      this.addConfigForm
        .get('tag')
        ?.setValue(StringUtils.toCamelCaseExt(this.addConfigForm.get('name')?.value));
    }
  }

  showToast(
    state: string,
    message: string,
    duration: number = 8000,
    isPersistent: boolean = true,
    title?: string
  ): ToastComponent {
    return this.toastService.show({
      templateContext: { test: { state, message, progress: 0 } },
      type: state === 'loading' ? 'information' : (state as ToastType),
      title,
      description: message,
      date: new Date(),
      isPersistent,
      interval: duration,
    });
  }

  ///* Configurations Part *///

  updateConfigList() {
    this.configurations = [...this.configurations];
    let id = 1;
    this.configurations = this.configurations.map((c) => {
      c.id = id;
      id++;
      return c;
    });
  }

  loadConfigurations() {
    this.namingConfigurationService.getConfigurations().subscribe((result) => {
      if (result?.txStrGenConfiguration) {
        this.configurations = (result.txStrGenConfiguration as any[]).map<Configuration>((r) => {
          const config = r as Configuration;
          config.name = r.sName;
          config.tag = r.sTag;
          config.objectTypeTag = r.sOTTag;
          config.subConcept = this.createSubConcept(r.items);
          return config;
        });
      }
      this.isConfigsLoaded = true;
    });
  }

  initConfigForm() {
    this.addConfigForm.markAllAsTouched();
  }

  resetConfigForm() {
    this.addConfigForm.get('id')?.setValue(0);
    const objType = this.objectTypes.find((ot) => ot.id === 1);
    if (objType) {
      const newName = StringUtils.generateAutoIncrement<Configuration>(
        objType.name,
        this.configurations,
        'name'
      );
      this.addConfigForm.get('name')?.setValue(newName);
      this.addConfigForm.get('tag')?.setValue(StringUtils.toCamelCaseExt(newName));
      this.addConfigForm.get('objectTypeId')?.setValue(1);
      this.addConfigForm.get('tag')?.enable();
      this.addConfigForm.get('objectTypeId')?.enable();
      this.configFormOTDropDownDisabled = false;
    }
  }

  onConfigFormOTChange(idOt: string[]) {
    if (idOt && idOt.length > 0) {
      this.addConfigForm.get('objectTypeId')?.setValue(Number(idOt[0]));
      const ot = this.objectTypeService.getByID(Number(idOt[0]));
      if (ot) {
        this.updateConfigNameValue(ot.name);
      }
      this.updateConfigTagValue();
    } else {
      this.addConfigForm.get('objectTypeId')?.setValue(null);
    }
  }

  onDeleteConfiguration(configToRemove: Configuration) {
    const i = this.configurations.findIndex((config) => config.id === configToRemove.id);
    this.namingConfigurationService.deleteConfiguration(configToRemove.tag).subscribe(() => {
      if (i > -1) {
        this.configurations.splice(i, 1);
        this.configurations = [...this.configurations];
      }
      this.showToast(
        'success',
        this.translate.instant(_('components.configFramework.notif.configSuccessfullyDeleted'), {
          name: configToRemove.name,
        }),
        8000,
        true,
        this.translate.instant(_('components.configFramework.notif.configDeleted'), {
          adminName: this.translate.instant(_('admins.wording.automaticNaming')),
        })
      );
      setTimeout(() => {
        // wait configurations list update
        this.configFramework?.selectConcept(undefined);
      }, 50);
    });
  }

  onAfterAddConfiguration(): void {
    const config = this.createConfigFromForm();
    this.doAddConfiguration(config);
  }

  doAddConfiguration(config: Configuration) {
    this.namingConfigurationService.addConfiguration(config).subscribe(() => {
      this.configurations.splice(-1, 0, config);
      this.updateConfigList();
      this.showToast(
        'success',
        this.translate.instant(_('components.configFramework.notif.configSuccessfullyAdded'), {
          name: config.name,
        }),
        8000,
        true,
        this.translate.instant(_('components.configFramework.notif.configAdded'), {
          adminName: this.translate.instant(_('admins.wording.automaticNaming')),
        })
      );
      setTimeout(() => {
        // wait configurations list update
        this.configFramework?.selectConcept(config, true);
      }, 50);
    });
  }

  onBeforeAddConfiguration() {
    this.resetConfigForm();
    setTimeout(() => {
      // wait form initialization
      if (this.nameInputConfigForm) {
        this.nameInputConfigForm.nativeElement.select();
      }
    }, 500);
  }

  onAfterDuplicateConfiguration(event: Configuration) {
    const newConfig: Configuration = JSON.parse(JSON.stringify(event));
    newConfig.id = this.configFramework && this.configFramework.maxID++;
    newConfig.modelApplications = [];
    this.namingConfigurationService.addConfiguration(newConfig).subscribe(() => {
      this.configurations.splice(-1, 0, newConfig);
      this.updateConfigList();
      this.showToast(
        'success',
        this.translate.instant(_('components.configFramework.notif.configSuccessfullyAdded'), {
          name: newConfig.name,
        }),
        8000,
        true,
        this.translate.instant(_('components.configFramework.notif.configDuplicated'), {
          adminName: this.translate.instant(_('admins.wording.automaticNaming')),
        })
      );
    });
  }

  onBeforeEditConfig(config: Configuration) {
    this.resetConfigForm();
    this.addConfigForm.get('id')?.setValue(config.id);
    this.addConfigForm.get('name')?.setValue(config.name);
    this.addConfigForm.get('tag')?.setValue(config.tag);
    this.addConfigForm.get('tag')?.disable();
    if (config.idOT !== -1) {
      this.addConfigForm.get('objectTypeId')?.setValue(config.idOT);
      this.addConfigForm.get('objectTypeId')?.disable();
      this.configFormOTDropDownDisabled = true;
    }
    setTimeout(() => {
      // wait form initialization
      if (this.nameInputConfigForm) {
        this.nameInputConfigForm.nativeElement.select();
      }
    }, 500);
  }

  onAfterEditConfig() {
    const tag = this.addConfigForm.get('tag')?.value;
    const config = this.configurations.find((c) => c.tag === tag);

    if (config) {
      config.name = this.addConfigForm.get('name')?.value;
      const idOT = this.addConfigForm.get('objectTypeId')?.value;
      config.idOT = idOT;
      config.imagePath = this.objectTypeService.getIconPath(idOT);
      this.addConfigForm.get('tag')?.enable();
      const objectType = this.objectTypeService.getByID(idOT);
      if (objectType) {
        config.objectTypeTag = objectType.tags[0];
      }
      this.namingConfigurationService.setConfiguration(config).subscribe(() =>
        this.showToast(
          'success',
          this.translate.instant(_('components.configFramework.notif.configSuccessfullyEdited'), {
            name: config?.name,
          }),
          8000,
          true,
          this.translate.instant(_('components.configFramework.notif.configEdited'), {
            adminName: this.translate.instant(_('admins.wording.automaticNaming')),
          })
        )
      );
    }
  }

  onSelectingConfiguration(idConfiguration: number | undefined): void {
    this.configurationSelected = this.configurations.find((c) => c.id === idConfiguration);
    if (this.configurationSelected?.idOT) {
      this.objectTypeConfigSelected = this.objectTypeService.getByID(
        this.configurationSelected.idOT
      );
      this.setElementPreview();
      this.itemSelected = undefined;
    }
  }

  public onHoverItem(item?: ConfigurationSubConceptElement) {
    this.itemHovered = item;
  }

  public onSelectItem(item?: ConfigurationSubConceptElement) {
    this.itemSelected = item;
  }

  createConfigFromForm(): Configuration {
    const ot = this.objectTypeService.getByID(this.addConfigForm.get('objectTypeId')?.value);
    const configTag = this.addConfigForm.get('tag')?.value;
    const config: Configuration = {
      name: this.addConfigForm.get('name')?.value,
      tag: configTag,
      objectTypeTag: ot?.tags[0] ?? '',
      data: {},
      id: this.configFramework && this.configFramework.maxID++,
      otName: ot?.name,
      idOT: ot?.id,
      imagePath: ot && this.objectTypeService.getIconPath(ot.id),
      modelApplications: [],
      subConcept: this.createSubConcept([]),
      tags: [configTag],
    };
    return config;
  }

  isNameUniqueValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null =>
      CustomValidators.isNameUnique(
        control,
        this.configurations,
        this.addConfigForm?.get('id')?.value
      );
  }

  isTagUniqueValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null =>
      CustomValidators.isTagUnique(control, this.configurations);
  }

  ///* SubConcept Part *///

  createSubConcept(items: any[]): ConfigurationSubConcept {
    const result: ConfigurationSubConcept = {
      name: this.translate.instant(_('admins.automaticNaming.configurationSettings')),
      icon: 'info-circle',
      elements: [],
    };
    if (items) {
      items.forEach((item, i) => {
        const element = this.createSubConceptElement(item, i);
        result.elements?.push(element);
      });
    }
    return result;
  }

  createSubConceptElement(element: NamingSettings, i: number): NamingSettings {
    element.id = i;
    element.name = element.type;
    element.icon = this.getSubConceptIcon(element.type);
    return element;
  }

  getSubConceptIcon(elementType: NamingSettingsType): string {
    switch (elementType) {
      case NamingSettingsType.data :
        return 'database';
      case NamingSettingsType.username :
        return 'user';
      case NamingSettingsType.date :
        return 'calendar';
      case NamingSettingsType.index :
        return 'list-ol';
      default:
        return 'text';
    }
  }

  initNamingSettingForm() {
    this.addNamingSubConceptForm.markAllAsTouched();
    this.updateSubConceptValidators();
  }

  resetSubConceptNamingForm() {
    this.addNamingSubConceptForm.reset({
      type: this.settingsType.data,
      dateFormat: 'dd/MM/yyyy',
      dataCase: NamingSettingsCaseValue.asIs,
      dataPerValue: false,
      dataAbbreviation: '...',
      dataTypeTranslationNeeded: false,
      dataTypeRemoveExtention: false,
      dataTypeFormatValueAsIndex: false,
      indexType: NamingSettingsIndexType.alphaLower,
      dataTypeIndexType: NamingSettingsIndexType.alphaLower,
      dataTypeNumberDigits: 2,
      indexGlobalType: NamingSettingsIncrementationType.data,
      indexIncrement: false,
    });
    this.attributeTypeSelected = null;
    if (this.dataAttributeTreeGrid) {
      this.dataAttributeTreeGrid.attributeSetLevels = [];
    }
    if (this.relativeIndexAttributeTreeGrid) {
      this.relativeIndexAttributeTreeGrid.attributeSetLevels = [];
    }
    this.initNamingSettingForm();
  }

  updateSubConceptValidators() {
    this.clearAllSubConceptFormValidators();
    switch (this.addNamingSubConceptForm.get('type')?.value) {
      case 'Text':
        this.addNamingSubConceptForm.get('constantText')?.addValidators(Validators.required);
        break;
      case 'Date':
        this.addNamingSubConceptForm.get('dateFormat')?.addValidators(Validators.required);
        break;
      case 'Data':
        this.dataAttribute?.addValidators(Validators.required);
        this.addNamingSubConceptForm.get('dataCase')?.addValidators(Validators.required);
        break;
      case 'Index':
        this.addNamingSubConceptForm.get('indexGlobalType')?.addValidators(Validators.required);
        this.updateSubConceptIndexTypeValidators();
        break;
      case 'Username':
      default:
        break;
    }
    this.updateValueAndValiditySubConceptForm();
  }

  updateSubConceptIndexTypeValidators() {
    if (
      this.addNamingSubConceptForm.get('indexGlobalType')?.value ===
      NamingSettingsIncrementationType.data
    ) {
      this.indexObject?.addValidators(Validators.required);
      this.indexAttribute?.addValidators([Validators.required, this.sameObjectTypeValidator()]);
      this.addNamingSubConceptForm.get('indexType')?.addValidators(Validators.required);
      this.addNamingSubConceptForm.get('indexObject')?.addValidators(this.isIndexObjectValid());
      this.addNamingSubConceptForm
        .get('indexAttribute')
        ?.addValidators(this.isIndexAttributeValid());
    } else {
      this.addNamingSubConceptForm
        .get('indexAttributeIncrement')
        ?.addValidators(Validators.required);
    }
    this.updateValueAndValiditySubConceptForm();
  }

  clearAllSubConceptFormValidators() {
    this.addNamingSubConceptForm.get('constantText')?.clearValidators();
    this.addNamingSubConceptForm.get('dateFormat')?.clearValidators();
    this.dataAttribute?.clearValidators();
    this.addNamingSubConceptForm.get('dataCase')?.clearValidators();
    this.addNamingSubConceptForm.get('indexGlobalType')?.clearValidators();
    this.indexObject?.clearValidators();
    this.addNamingSubConceptForm.get('indexAttribute')?.clearValidators();
    this.addNamingSubConceptForm.get('indexType')?.clearValidators();
    this.addNamingSubConceptForm.get('indexAttributeIncrement')?.clearValidators();
  }

  updateValueAndValiditySubConceptForm() {
    this.addNamingSubConceptForm.get('constantText')?.updateValueAndValidity();
    this.addNamingSubConceptForm.get('dateFormat')?.updateValueAndValidity();
    this.dataAttribute?.updateValueAndValidity();
    this.addNamingSubConceptForm.get('dataCase')?.updateValueAndValidity();
    this.addNamingSubConceptForm.get('indexGlobalType')?.updateValueAndValidity();
    this.indexObject?.updateValueAndValidity();
    this.addNamingSubConceptForm.get('indexAttribute')?.updateValueAndValidity();
    this.addNamingSubConceptForm.get('indexType')?.updateValueAndValidity();
    this.addNamingSubConceptForm.get('indexObject')?.updateValueAndValidity();
    this.addNamingSubConceptForm.get('indexAttributeIncrement')?.updateValueAndValidity();
  }

  onBeforeValidateMailSettings(args: any) {
    this.checkAttributesTags().subscribe(() => args());
  }

  checkAttributesTags(): Observable<any> {
    return new Observable((observer) => {
      const idsWithMissingTags: number[] = this.getAttributesToAutotag();
      if (idsWithMissingTags?.length > 0) {
        this.dialogConfirmService
          .open({
            message: this.translate.instant(_('components.configFramework.autoTagAttributes')),
            okCaption: _('button.create'),
          })
          .subscribe((createClicked) => {
            if (createClicked) {
              this.attributeService.autoTag(idsWithMissingTags).subscribe(() => {
                observer.next();
                observer.complete();
              });
            }
          });
      } else {
        observer.next();
        observer.complete();
      }
    });
  }

  onAfterAddElementSubconcept(): void {
    const namingSettings = this.getDataFromNamingSubConceptForm();
    this.resetSubConceptNamingForm();
    const id =
      this.configurationSelected?.subConcept?.elements &&
      NumberUtils.getNextId(this.configurationSelected.subConcept.elements);
    if (id) {
      this.configurationSelected?.subConcept?.elements?.push(
        this.createSubConceptElement(namingSettings, id)
      );
    }
    if (this.configurationSelected) {
      this.namingConfigurationService.setConfiguration(this.configurationSelected).subscribe(() =>
        this.showToast(
          'success',
          this.translate.instant(_('admins.automaticNaming.namingSettingSuccessfullyAdded'), {
            type: namingSettings.type,
            name: this.configurationSelected?.name,
          }),
          8000,
          true
        )
      );
    }
  }

  onBeforeAddElementSubconcept() {
    this.resetSubConceptNamingForm();
  }

  onBeforeEditSubConcept(element: NamingSettings) {
    this.fillSubConceptForm(element);
    this.subConceptEdited = element;
  }

  onAfterEditSubConcept() {
    const settingEdited = this.getDataFromNamingSubConceptForm();
    if (this.configurationSelected?.subConcept?.elements) {
      const index = this.configurationSelected.subConcept.elements.findIndex(
        (sc) => sc.id === this.subConceptEdited?.id
      );
      if (this.subConceptEdited) {
        settingEdited.id = this.subConceptEdited.id;
        settingEdited.icon = this.getSubConceptIcon(settingEdited.type);
      }
      settingEdited.name = settingEdited.type;
      this.configurationSelected.subConcept.elements[index] = settingEdited;
    }
    this.subConceptEdited = settingEdited;
    if (this.configurationSelected) {
      this.namingConfigurationService.setConfiguration(this.configurationSelected).subscribe(() =>
        this.showToast(
          'success',
          this.translate.instant(_('admins.automaticNaming.namingSettingSuccessfullyEdited'), {
            type: settingEdited.type,
            name: this.configurationSelected?.name,
          }),
          8000,
          true
        )
      );
    }
  }

  onDeleteSubConcept(element: NamingSettings) {
    const index =
      this.configurationSelected?.subConcept?.elements?.findIndex((e) => e.id === element.id) ?? -1;
    if (index > -1) {
      this.configurationSelected?.subConcept?.elements?.splice(index, 1);
      if (this.configurationSelected) {
        this.namingConfigurationService.setConfiguration(this.configurationSelected).subscribe(() =>
          this.showToast(
            'success',
            this.translate.instant(_('admins.automaticNaming.namingSettingSuccessfullyDeleted'), {
              type: element.type,
              name: this.configurationSelected?.name,
            }),
            8000,
            true
          )
        );
      }
    }
  }

  onChangeSubConceptOrder() {
    if (this.configurationSelected) {
      this.namingConfigurationService
        .setConfiguration(this.configurationSelected)
        .subscribe(() =>
          this.showToast(
            'success',
            this.translate.instant(
              _('admins.automaticNaming.namingSettingsOrderSuccessfullyEdited'),
              { name: this.configurationSelected?.name }
            ),
            8000,
            true
          )
        );
    }
  }

  getAttributesToAutotag(): number[] {
    const typeSubConcept = this.addNamingSubConceptForm.get('type')?.value;
    if (typeSubConcept === 'Data') {
      return this.idsAttributesUntagged.dataAttribute;
    }
    if (typeSubConcept === 'Index') {
      if (
        this.addNamingSubConceptForm.get('indexGlobalType')?.value ===
        NamingSettingsIncrementationType.link
      ) {
        return this.idsAttributesUntagged.indexAttributeIncrement;
      }
    }
    return [];
  }

  getDataFromNamingSubConceptForm(): NamingSettings {
    const form = this.addNamingSubConceptForm;
    const type = form.get('type')?.value;
    const data = new Object() as NamingSettings;
    data.type = type;
    switch (type) {
      case 'Text':
        data.value = form.get('constantText')?.value;
        break;
      case 'Date':
        data.sDateFormat = form.get('dateFormat')?.value;
        break;
      case 'Data':
        this.setPropertiesForDataType(data, form);
        this.setDataPreview(data);
        break;
      case 'Index':
        this.setPropertiesForIndexType(data, form);
        this.objectService.getTaggedObjects().subscribe((obj) => {
          this.listObjects = obj;
          this.setIndexPreview(data);
        });
        break;
      case 'Username':
        data.value = this.connectedUser?.name;
        break;
      default:
        break;
    }
    return data;
  }

  fillSubConceptForm(data: NamingSettings) {
    this.resetSubConceptNamingForm();
    const form = this.addNamingSubConceptForm;
    form.get('type')?.setValue(data.type);
    switch (data.type) {
      case NamingSettingsType.text:
        form.get('constantText')?.setValue(data.value);
        break;
      case NamingSettingsType.date:
        form.get('dateFormat')?.setValue(data.sDateFormat);
        break;
      case NamingSettingsType.data: {
        const dataAttrSetLevel =
          data.sTags_Att &&
          this.attributeSetService.stringAttributesTagToAttributeSetLevels(data.sTags_Att);
        this.onCheckDataAttribute(
          dataAttrSetLevel ? { attributeSetLevels: [dataAttrSetLevel] } : undefined
        );
        form.get('dataCase')?.setValue(data.sCase);
        form.get('dataMaximumSize')?.setValue(data.iMaxSize);
        form.get('dataPerValue')?.setValue(data.bPerData);
        form.get('dataValueSeparator')?.setValue(data.sDataSeparator);
        form.get('dataPrefix')?.setValue(data.sPrefix);
        form.get('dataSuffix')?.setValue(data.sSuffix);
        form.get('dataTypeRemoveExtention')?.setValue(data.bDeleteExtension);
        form.get('dataAbbreviation')?.setValue(data.sAbbreviation);
        form.get('dateFormat')?.setValue(data.sDateFormat);
        form.get('dataTypeIndexType')?.setValue(data.sIndexFormat);
        form.get('dataTypeNumberDigits')?.setValue(data.iNumberOfCharacters);
        form.get('dataTypeFormatValueAsIndex')?.setValue(data.bConsiderAsIndex);
        form.get('dataTypeTranslationNeeded')?.setValue(data.bTranslateIfNeeded);
        break;
      }
      case NamingSettingsType.index:
        form.get('indexGlobalType')?.setValue(data.sIncrementationType);
        if (data.sTags_Att_ReferenceIndex) {
          form
            .get('indexAttributeIncrement')
            ?.setValue(
              this.attributeSetService.stringAttributesTagToAttributeSetLevels(
                data.sTags_Att_ReferenceIndex
              )
            );
        }
        form.get('indexType')?.setValue(data.sIndexFormat);
        form.get('indexStartIndex')?.setValue(data.sInitialIndex);
        form.get('indexIncrement')?.setValue(data.bIncrement);
        if (data.sTag_Att_Index) {
          form
            .get('indexStorageAttribute')
            ?.setValue(
              this.attributeSetService.stringAttributesTagToAttributeSetLevels(data.sTag_Att_Index)
            );
        }
        if (data.sTag_Att_Data) {
          form
            .get('indexAttribute')
            ?.setValue(
              this.attributeSetService.stringAttributesTagToAttributeSetLevels(data.sTag_Att_Data)
            );
        }
        if (data.sTag_Obj_Data) {
          this.objectService.getObjectByTag(data.sTag_Obj_Data).subscribe((obj) => {
            form.get('indexObject')?.setValue(obj);
            form.get('indexObject')?.updateValueAndValidity();
          });
        }
        form.get('indexNumberDigits')?.setValue(data.iNumberOfCharacters);
        break;
      case NamingSettingsType.username:
      default:
        break;
    }
    this.updateSubConceptValidators();
  }

  onCheckDataAttribute(args: TxAttributeCheckChangeEvent | undefined) {
    this.idsAttributesUntagged.dataAttribute = [];
    if (args && args.attributeSetLevels.length > 0) {
      const parentAttrSetSelected: CTxAttributeSetLevel = args.attributeSetLevels[0];
      this.dataAttribute?.setValue(parentAttrSetSelected);
      let attrSetLevelSelected = parentAttrSetSelected;
      let attribute = this.attributeService.getByID(attrSetLevelSelected.idAttribute);
      if (attribute && attribute.tags.length < 1) {
        //update untagged attribute list
        this.idsAttributesUntagged.dataAttribute.push(attrSetLevelSelected.idAttribute);
      }
      while (attrSetLevelSelected.childLevels && attrSetLevelSelected.childLevels.length > 0) {
        attrSetLevelSelected = attrSetLevelSelected.childLevels[0];
        attribute = this.attributeService.getByID(attrSetLevelSelected.idAttribute);
        if (attribute && attribute.tags.length < 1) {
          this.idsAttributesUntagged.dataAttribute.push(attrSetLevelSelected.idAttribute);
        }
      }
      this.attributeTypeSelected = this.attributeService.getByID(
        attrSetLevelSelected.idAttribute
      )?.dataType;
    } else {
      this.attributeTypeSelected = null;
      this.dataAttribute?.setValue('');
    }
  }

  onCheckRelativeIndexAttribute(args: TxAttributeCheckChangeEvent) {
    this.idsAttributesUntagged.indexAttributeIncrement = [];
    if (args && args.attributeSetLevels.length > 0) {
      let attrSetLevelSelected: CTxAttributeSetLevel = args.attributeSetLevels[0];
      const attribute = this.attributeService.getByID(attrSetLevelSelected.idAttribute);
      if (attribute && attribute.tags.length < 1) {
        //update untagged attribute list
        this.idsAttributesUntagged.indexAttributeIncrement.push(attrSetLevelSelected.idAttribute);
      }
      while (attrSetLevelSelected.childLevels && attrSetLevelSelected.childLevels.length > 0) {
        attrSetLevelSelected = attrSetLevelSelected.childLevels[0];
        if (attribute && attribute.tags.length < 1) {
          this.idsAttributesUntagged.indexAttributeIncrement.push(attrSetLevelSelected.idAttribute);
        }
      }
      this.addNamingSubConceptForm
        .get('indexAttributeIncrement')
        ?.setValue(args.attributeSetLevels[0]);
    } else {
      this.addNamingSubConceptForm.get('indexAttributeIncrement')?.setValue('');
    }
  }

  ///* Objet and Attribute selection part (for SubConcept Form) *///

  pane2Canceled() {
    this.rightPaneRef?.close();
  }

  showIndexDataObjectSelectionField() {
    this.rightPaneRef = this.rightPaneService.showNewPaneWithTemplate({
      template: this.objectSelectionTemplate,
    });
  }

  showIndexDataAttributeSelectionField() {
    this.rightPaneRef = this.rightPaneService.showNewPaneWithTemplate({
      template: this.attributeSelectionTemplate,
    });
  }

  showStorageAttributeSelectionField() {
    this.rightPaneRef = this.rightPaneService.showNewPaneWithTemplate({
      template: this.storageAttributeSelectionTemplate,
    });
  }

  onSelectIndexDataObject(objects: TxObject[]) {
    if (objects && objects.length > 0) {
      this.indexObject?.setValue(objects[0]);
    } else {
      this.indexObject?.setValue(null);
    }
    this.indexAttribute?.updateValueAndValidity();
    this.rightPaneRef?.close();
  }

  onSelectIndexDataAttribute(attributeLevels: CTxAttributeSetLevel[]) {
    if (attributeLevels && attributeLevels.length > 0) {
      this.indexAttribute?.setValue(attributeLevels[0]);
    } else {
      this.indexAttribute?.setValue(null);
    }
    this.rightPaneRef?.close();
  }

  onSelectStorageAttribute(attributeLevels: CTxAttributeSetLevel[]) {
    if (attributeLevels && attributeLevels.length > 0) {
      this.indexStorageAttribute?.setValue(attributeLevels[0]);
    } else {
      this.indexStorageAttribute?.setValue(null);
    }
    this.rightPaneRef?.close();
  }

  getIdOTForIndexDataAttributeSelect() {
    const attributeLevelSelected = this.indexAttribute?.value;
    if (!attributeLevelSelected) {
      if (!this.indexObject?.value) {
        return '1';
      }
      return this.indexObject?.value.idObjectType.toString();
    }
    return this.attributeService
      .getByID(attributeLevelSelected.idAttribute)
      ?.idObjectType.toString();
  }

  getIdOTForIndexDataObjectSelect() {
    if (!this.indexObject?.value) {
      const attributeLevelSelected = this.indexAttribute?.value;
      if (!attributeLevelSelected) {
        return '1';
      }
      return this.attributeService
        .getByID(attributeLevelSelected.idAttribute)
        ?.idObjectType.toString();
    }
    return this.indexObject.value.idObjectType.toString();
  }

  sameObjectTypeValidator() {
    return (): ValidationErrors | null => {
      const attributeLevelSelected: CTxAttributeSetLevel = this.addNamingSubConceptForm
        ? this.indexAttribute?.value
        : null;
      let sameOT = true;
      if (this.indexObject?.value && attributeLevelSelected) {
        sameOT =
          this.attributeService.getByID(attributeLevelSelected.idAttribute)?.idObjectType ===
          this.indexObject?.value.idObjectType;
      }
      return sameOT ? null : { sameOT: true };
    };
  }

  isIndexAttributeValid() {
    return (): ValidationErrors | null => {
      const isValid = this.addNamingSubConceptForm ? this.indexAttribute?.valid : true;
      return isValid ? null : { notValid: true };
    };
  }

  isIndexObjectValid() {
    return (): ValidationErrors | null => {
      const isValid = this.addNamingSubConceptForm ? this.indexObject?.valid : true;
      return isValid ? null : { notValid: true };
    };
  }

  onIndexAttributeChange(attribute: TxAttribute | undefined) {
    if (attribute === undefined) {
      this.onSelectIndexDataAttribute([]);
    }
  }

  onIndexObjectChange(object: TxObject | undefined) {
    if (object === undefined) {
      this.onSelectIndexDataObject([]);
    }
  }

  onAttrStorageChange(attribute: TxAttribute | undefined) {
    if (attribute === undefined) {
      this.onSelectStorageAttribute([]);
    }
  }

  //private methods which are used in getDataFromNamingSubConceptForm

  private setPropertiesForDataType(data: NamingSettings, form: UntypedFormGroup) {
    data.sTags_Att = this.attributeSetService.attributeSetLevelsToStringAttributeTags(
      form.get('dataAttribute')?.value
    );
    data.sCase = form.get('dataCase')?.value;
    data.sCaseSpecified = data.sCase ? true : false;
    data.iMaxSize = form.get('dataMaximumSize')?.value ?? 0;
    data.iMaxSizeSpecified = data.iMaxSize ? true : false;
    data.bPerData = form.get('dataPerValue')?.value;
    data.bPerDataSpecified = data.bPerData;
    data.sDataSeparator = form.get('dataValueSeparator')?.value;
    data.sPrefix = form.get('dataPrefix')?.value;
    data.sSuffix = form.get('dataSuffix')?.value;
    data.bDeleteExtension = form.get('dataTypeRemoveExtention')?.value;
    data.bDeleteExtensionSpecified = data.bDeleteExtension;
    data.sAbbreviation = form.get('dataAbbreviation')?.value;
    data.sDateFormat = form.get('dateFormat')?.value;
    data.sIndexFormat = form.get('dataTypeIndexType')?.value;
    data.sIndexFormatSpecified = data.sIndexFormat ? true : false;
    data.iNumberOfCharacters = form.get('dataTypeNumberDigits')?.value ?? 0;
    data.iNumberOfCharactersSpecified = data.iNumberOfCharacters ? true : false;
    data.bConsiderAsIndex = form.get('dataTypeFormatValueAsIndex')?.value;
    data.bTranslateIfNeeded = form.get('dataTypeTranslationNeeded')?.value;
    data.bConsiderAsIndexSpecified = data.bConsiderAsIndex;
    data.bTranslateIfNeededSpecified = data.bTranslateIfNeeded;
  }

  private setPropertiesForIndexType(data: NamingSettings, form: UntypedFormGroup) {
    data.sIncrementationType = form.get('indexGlobalType')?.value;
    data.sIncrementationTypeSpecified = true;
    if (form.get('indexStorageAttribute')?.value) {
      data.sTag_Att_Index = this.attributeSetService.attributeSetLevelsToStringAttributeTags(
        form.get('indexStorageAttribute')?.value
      );
    }
    if (data.sIncrementationType === NamingSettingsIncrementationType.data) {
      data.sTag_Att_Data = this.attributeSetService.attributeSetLevelsToStringAttributeTags(
        form.get('indexAttribute')?.value
      );
      data.sTag_Obj_Data = (form.get('indexObject')?.value as TxObject).tags[0];
    } else {
      data.sTags_Att_ReferenceIndex =
        this.attributeSetService.attributeSetLevelsToStringAttributeTags(
          form.get('indexAttributeIncrement')?.value
        );
    }
    data.sIndexFormat = form.get('indexType')?.value;
    data.sIndexFormatSpecified = true;
    data.sInitialIndex = form.get('indexStartIndex')?.value;
    data.bIncrement = form.get('indexIncrement')?.value;
    data.bIncrementSpecified = data.bIncrement;
    data.iNumberOfCharacters = form.get('indexNumberDigits')?.value ?? 0;
    data.iNumberOfCharactersSpecified = data.iNumberOfCharacters ? true : false;
  }

  // preview methods

  private setElementPreview(): void {
    for (let element of this.configurationSelected?.subConcept?.elements as NamingSettings[]) {
      if(element.type == NamingSettingsType.data){
        this.setDataPreview(element);
      } else if(element.type == NamingSettingsType.index){
        this.setIndexPreview(element);
      }
    }
  }

  private setDataPreview(element: NamingSettings): void {
    this.setDataPreviewAttrList(element);
    this.setDataPreviewFormat(element);
    this.setDataPreviewValue(element);
  }

  private setIndexPreview(element: NamingSettings): void {
    this.setIndexPreviewAttrList(element);
    this.setIndexPreviewFormat(element);
    this.setIndexPreviewValue(element);
  }

  private setDataPreviewAttrList(element: NamingSettings): void {
    if (element.sTags_Att){
      const listTags = element.sTags_Att.split(";");
      let list = [];
      for (const tag of listTags) {
        list.push(this.attributeService.tagToName(tag)!);
      }
      element.previewAttributeList = list;
    }
  }

  private setIndexPreviewAttrList(element: NamingSettings): void {
    let list = [];
    const typeIsData = element.sIncrementationType == NamingSettingsIncrementationType.data;

    if (typeIsData) {
      const object = this.listObjects?.find((o) => o.tags.includes(element.sTag_Obj_Data!));
      list.push(object?.name ?? '');
      list.push(this.attributeService.tagToName(element.sTag_Att_Data!) ?? '');
    } else if (element.sTags_Att_ReferenceIndex){
      const listTags = element.sTags_Att_ReferenceIndex.split(";");
      for (const tag of listTags) {
        list.push(this.attributeService.tagToName(tag) ?? '');
      }
    }

    element.previewAttributeList = list;
  }

  //must be called after setDataPreviewAttrList
  private setDataPreviewValue(element: NamingSettings): void {
    let preview = '';
    if (!element.previewAttributeList){
      return;
    }
    let attr = element.previewAttributeList[element.previewAttributeList?.length-1];

    if (element.sPrefix) {
      preview += element.sPrefix;
    }

    if (element.sCase === NamingSettingsCaseValue.uppercase) {
      attr = attr?.toLocaleUpperCase();
    } else if (element.sCase === NamingSettingsCaseValue.lowercase) {
      attr = attr?.toLocaleLowerCase();
    }
    preview += attr;
    if (element.sSuffix) {
      preview += element.sSuffix;
    }
    element.previewValue = preview;
  }

  private setIndexPreviewValue(element: NamingSettings): void {
    if (element.sIndexFormat == NamingSettingsIndexType.alphaLower) {
      element.previewValue = element.sInitialIndex ?? 'a';
    } else if (element.sIndexFormat == NamingSettingsIndexType.alphaUpper) {
      element.previewValue = element.sInitialIndex ? element.sInitialIndex.toLocaleUpperCase() : 'A';
    } else { //numerical
      let indexNum = element.sInitialIndex ?? '1';
      if (element.iNumberOfCharactersSpecified) {
        indexNum = indexNum.padStart(element.iNumberOfCharacters!, '0');
      }
      element.previewValue = indexNum;
    }
  }

  private setDataPreviewFormat(element: NamingSettings): void {
    let formatStr = '';
    if (!element.sTags_Att){
      element.previewFormat = formatStr;
      return;
    }
    const lastTag = element.sTags_Att.split(";").pop()!;
    const attribute = this.attributeService.getByTag(lastTag);
    let formatList: string[] = [];
    
    const isTextType = attribute?.dataType == TxDataType.ShortText || attribute?.dataType == TxDataType.LongText || attribute?.dataType == TxDataType.SingleValue;
    if (isTextType && element.bConsiderAsIndex) {
      formatStr += this.translate.instant(_('admins.automaticNaming.index')) + ": " + this.getIndexFormatString(element.sIndexFormat!);
    } else if (attribute?.dataType == TxDataType.Date || attribute?.dataType == TxDataType.DateAndTime) {
      formatStr += element.sDateFormat;
    } else if (attribute?.dataType == TxDataType.File) {
      formatStr += this.translate.instant(_('admins.automaticNaming.extension')) + " ";
      formatStr += element.bDeleteExtension ? this.translate.instant(_('admins.automaticNaming.no')) : this.translate.instant(_('admins.automaticNaming.yes'));
    } else if (attribute?.dataType == TxDataType.LinkDirect || attribute?.dataType == TxDataType.LinkInv || attribute?.dataType == TxDataType.LinkBi) {
      formatStr += this.translate.instant(_('admins.automaticNaming.translate')) + " ";
      formatStr += element.bTranslateIfNeeded ? this.translate.instant(_('admins.automaticNaming.yes')) : this.translate.instant(_('admins.automaticNaming.no'));
    }
    formatList.push(formatStr);

    if (element.sPrefix) {
      formatList.push(this.translate.instant(_('admins.automaticNaming.prefix')) + ": " + element.sPrefix);
    }
    if (element.sSuffix) {
      formatList.push(this.translate.instant(_('admins.automaticNaming.suffix')) + ": " + element.sSuffix);
    }
    
    element.previewFormat = formatList.join(' | ');
  }

  private getIndexFormatString(indexType : NamingSettingsIndexType): string {
    const numIndex = "012";
    const alphaIndex = "aab";
    if (indexType == NamingSettingsIndexType.num) {
      return numIndex;
    } else {
      return ((indexType == NamingSettingsIndexType.alphaLower) ? alphaIndex : alphaIndex.toUpperCase());
    }
  }

  private setIndexPreviewFormat(element: NamingSettings): void {
    element.previewFormat = this.getIndexFormatString(element.sIndexFormat!);
  }
}
