<div id="modal-content-wrapper">
  <header id="modal-header">
    <div class="dialog-header background-accent">
      <fa-icon [icon]="['fal', data.icon]" size="lg"></fa-icon>
      <span>{{ data.title ?? '' | translate }}</span>
    </div>
  </header>
  <section id="modal-body">
    <div class="dialog-content-container">
      <div class="user-message">{{ data.message | translate }}</div>
      <div *ngIf="data.annotation && data.annotation !== ''" class="user-message annotation">
        {{ data.annotation | translate }}
      </div>
      <div class="additional-input-container" *ngIf="data.template">
        <ng-container
          [ngTemplateOutlet]="data.template"
          [ngTemplateOutletContext]="{ data: data.dummyData }"></ng-container>
      </div>
    </div>
  </section>
  <footer id="modal-footer">
    <ng-container [ngSwitch]="data.type">
      <div *ngSwitchCase="'confirm'" class="button-container">
        <button
          mat-flat-button
          color="accent"
          mat-dialog-close
          (click)="onConfirm()"
          [disabled]="actionButtonDisabled">
          {{ data.okCaption ?? '' | translate }}
        </button>
        <button mat-stroked-button mat-dialog-close (click)="onCancel()">
          {{ 'txWebCore.window.cancel' | translate }}
        </button>
      </div>
      <div *ngSwitchCase="'display'" class="button-container">
        <button
          mat-flat-button
          color="accent"
          mat-dialog-close
          (click)="onConfirm()"
          [disabled]="actionButtonDisabled">
          {{ data.okCaption ?? '' | translate }}
        </button>
      </div>
    </ng-container>
  </footer>
</div>
