import { DateUtils } from '@bassetti-group/tx-web-core/src/lib/utilities';
import { CONDITIONS_FUNCTIONS, TxGridFilterValueType, TxGroupByValue } from '../grid.const';
import { FilterOperators, TxGridFilterByColumn, TxGridFilterOnData } from '../grid.interface';
import { InjectionToken } from '@angular/core';
/**
 * Enables a custom filter predicate for the PaginatedTableDataSource.
 * @memberof TxGridComponent
 */

export type FilterQuery<T> = TxGridFilterByColumn<T> | TxGridFilterOnData<T> | undefined;
type FilterValueType = string | string[] | null;
export const INITIAL_FILTER_QUERY = new InjectionToken<FilterQuery<unknown>>('InitialFilterQuery');
const transformFilterValue = (value: string): string => value.trim().toLowerCase();

const generateDataString = <T>(item: T, fields: (keyof T)[]): string =>
  fields
    .reduce((currentTerm: string, key: keyof T) => {
      return currentTerm + item[key] + '◬';
    }, '')
    .toLowerCase();

const filterByData = <T>(
  item: T & { isTxGroup?: boolean },
  query: TxGridFilterOnData<T>
): boolean => {
  const dataStr = generateDataString(
    item,
    query.fields.length !== 0 ? query.fields : (Object.keys(item) as (keyof T)[])
  );
  const transformedFilter = transformFilterValue(query.value);
  return dataStr.indexOf(transformedFilter) !== -1;
};

const isDate = (value: any): value is Date => value instanceof Date;

const checkCondition = <T>(
  item: T & { isTxGroup?: boolean },
  query: TxGridFilterByColumn<T>,
  field: keyof T,
  forGroup?: boolean
): boolean => {
  const filterOperators = query.conditions?.[field];
  const dataValue: T[keyof T] | Date = isDate(item[field])
    ? DateUtils.getDateWithoutTime(item[field] as Date)
    : item[field];

  const filterValue: FilterValueType = query.values ? (query.values[field] as string) : null;

  if (item.isTxGroup) {
    return !!forGroup;
  }
  if (Array.isArray(filterValue)) {
    if (filterValue.length === 0 && field !== TxGroupByValue) {
      return true;
    }
    const uniqueFilterValue = Array.from(new Set(filterValue));
    return uniqueFilterValue.some(
      (value) =>
        CONDITIONS_FUNCTIONS[filterOperators as NonNullable<FilterOperators<T>[keyof T]>](
          dataValue as TxGridFilterValueType,
          value
        ) === true
    );
  } else if (filterOperators && filterValue && query.values?.[field] != null) {
    return (
      CONDITIONS_FUNCTIONS[filterOperators](dataValue as TxGridFilterValueType, filterValue) ===
      true
    );
  }

  return true;
};

const filterByConditions = <T>(
  item: T & { isTxGroup?: boolean },
  query: TxGridFilterByColumn<T>,
  forGroup?: boolean
): boolean => {
  const fields = Object.keys(query.values ?? item) as (keyof T)[];
  return fields.every((field) => checkCondition(item, query, field, forGroup));
};

export const clientSideFilterBy = <T extends {}>(
  data: T[],
  query: TxGridFilterByColumn<T> | TxGridFilterOnData<T>,
  forGroup?: boolean
): T[] => {
  return data.filter((item: T & { isTxGroup?: boolean }) => {
    if (isFilterOnData(query)) {
      return filterByData(item, query);
    } else {
      return filterByConditions(item, query, forGroup);
    }
  });
};

function isFilterOnData<T>(
  query: TxGridFilterByColumn<T> | TxGridFilterOnData<T>
): query is TxGridFilterOnData<T> {
  return (query as TxGridFilterOnData<T>).fields !== undefined;
}
