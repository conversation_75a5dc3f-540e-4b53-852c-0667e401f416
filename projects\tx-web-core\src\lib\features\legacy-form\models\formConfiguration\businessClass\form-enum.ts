export enum TxFormType {
  fullObjectType,
  attributesTags,
  attributeIds,
  attributesSet,
  runConfiguration,
  buildConfiguration,
}

export enum TxShowMode {
  show,
  hide,
  empty,
}

export enum TxEditionMode {
  read,
  write,
  readAndWrite,
}

export enum TxConfigurationObjectTypeDependency {
  one,
  none,
}

export enum TxFormRightManagement {
  frmUndefined,
  frmRespectRights,
  frmForceWriteMode,
  frmForceReadMode,
  frmForceLockedMode,
}

export enum TxValidationState {
  valid,
  unvalid,
  undefined,
}

export enum TxDBRight {
  dbaNone,
  dbaRead,
  dbaAdd,
  dbaModif,
  dbaStructure,
}

export enum TxLinkAttributeFilteringType {
  undefined,
  none,
  parent,
  or,
  and,
  andButNotEmpty,
}

export enum TxFieldSize {
  Single = 'Single',
  Double = 'Double',
  Full = 'Full',
}
