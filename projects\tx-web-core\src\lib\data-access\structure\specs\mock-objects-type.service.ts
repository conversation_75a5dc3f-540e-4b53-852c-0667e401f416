import { MockService } from 'ng-mocks';
import { TxObjectsTypeService } from '../objects-type.service';
import { Observable, of } from 'rxjs';
import { DataBaseRights, TxLockingType, TxObjectType, TxObjectTypeType } from '@bassetti-group/tx-web-core/src/lib/business-models';


export const MOCK_OBJECTS: TxObjectType[] = [
  {
    id: 1,
    idObjectTypeParent: 0,
    icon: 262,
    isFolder: false,
    type: TxObjectTypeType.Standard,
    hasDistinctName: true,
    isVisible: true,
    lockingType: TxLockingType.Auto,
    lockingDuration: 0,
    displayResultInTextSearch: true,
    right: DataBaseRights.DbrRead,
    order: 1,
    name: 'Départements',
    tags: []
  },
  {
    id: 2,
    idObjectTypeParent: 1,
    icon: 232,
    isFolder: false,
    type: TxObjectTypeType.Standard,
    hasDistinctName: false,
    isVisible: false,
    lockingType: TxLockingType.Manual,
    lockingDuration: 30,
    displayResultInTextSearch: false,
    right: DataBaseRights.DbrWrite,
    order: 2,
    name: 'Services',
    tags: []
  },
];

export const MOCK_OBJECTS_TYPE_SERVICE = MockService(TxObjectsTypeService, {
  isReady: () => of(true),
  listAll: () => of([]),
  filter: (types: TxObjectTypeType[]): Observable<TxObjectType[]> => of(MOCK_OBJECTS)
});