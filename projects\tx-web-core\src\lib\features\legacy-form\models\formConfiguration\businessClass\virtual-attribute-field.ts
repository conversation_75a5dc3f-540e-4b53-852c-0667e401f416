import { LegacyTxAttribute } from '../../../services/structure/models/attribute';
import { TxIdConcept } from '../../generics/Id-concept';
import { TxShowMode } from './form-enum';
import { TxGroupAttributeField } from './group-attribute-field';

export class TxVirtualAttributeField extends TxIdConcept {
  showMode!: TxShowMode;
  attribute!: LegacyTxAttribute;
  parent!: TxGroupAttributeField;
  properties: any;
  classes: string = '';
  name!: string;

  static assign(virtualField: Partial<TxVirtualAttributeField>): TxVirtualAttributeField {
    const f = new TxVirtualAttributeField();
    f.assign(virtualField);
    return f;
  }

  constructor() {
    super();
  }

  reset() {
    super.reset();
    this.properties = {};
    this.name = '';
    this.classes = '';
    this.showMode = TxShowMode.show;
  }

  assign(object?: Partial<TxVirtualAttributeField>) {
    super.assign(object as Partial<TxIdConcept>);
    this.showMode = object?.showMode || this.showMode;
    this.attribute = object?.attribute || this.attribute;
    this.properties = object?.properties || this.properties;
    this.name = this.attribute?.name || this.name;
  }
}
