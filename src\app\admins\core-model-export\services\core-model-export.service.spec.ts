import { TestBed } from '@angular/core/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';
import {
  CommonServiceMock,
  ErrorServiceMock,
  ModelApplicationServiceMock,
  ObjectsTypeServiceMock,
} from 'src/app/app.testing.mock';
import { ModelApplicationService } from 'src/app/core/services/structure/model-application.service';
import {
  CORE_MODELS_EXPORT_SERVICE_MOCK,
  CORE_MODEL_CONCEPTS,
  CORE_MODEL_EXPORT_HISTORY,
} from './core-model-export.http.service.mock';
import { CoreModelExportService } from './core-model-export.service';
import { TRANSLATIONS_MOCK } from 'src/app/core/translation/translations.mock';
import { MockProvider } from 'ng-mocks';
import { sessionServiceMock } from 'src/app/core/services/session/session.service.mock';
import { SessionService } from 'src/app/core/services/session/session.service';
import { NbErrorsPipe } from 'src/app/shared/pipes/nb-errors.pipe';
import { ErrorService } from 'src/app/core/services/errors/error.service';
import { CoreModelsExportGatewayService } from './core-model-export-gateway.service';
import { CoreModelExportConcept } from '../models/core-model-export-concept.model';
import { CoreModelExportHistoryDTO } from '../models/core-model-export-history-object.dto';
import { CoreModelExportHistory } from '../models/core-model-export-history.model';
import { TxCommonService, TxObjectsTypeService } from '@bassetti-group/tx-web-core';

describe('CoreModelsExportService', () => {
  let service: CoreModelExportService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [TranslateTestingModule.withTranslations(TRANSLATIONS_MOCK)],
      providers: [
        MockProvider(CoreModelsExportGatewayService, CORE_MODELS_EXPORT_SERVICE_MOCK, 'useValue'),
        {
          provide: ModelApplicationService,
          useClass: ModelApplicationServiceMock,
        },
        NbErrorsPipe,
        {
          provide: TxCommonService,
          useClass: CommonServiceMock,
        },
        {
          provide: ErrorService,
          useClass: ErrorServiceMock,
        },
        { provide: TxObjectsTypeService, useClass: ObjectsTypeServiceMock },
        MockProvider(SessionService, sessionServiceMock, 'useValue'),
        MockProvider(CoreModelsExportGatewayService, CORE_MODELS_EXPORT_SERVICE_MOCK, 'useValue'),
      ],
    });
    service = TestBed.inject(CoreModelExportService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
  it('should load concepts', () => {
    service.loadConcepts();
    service.concepts$.subscribe((concepts) => {
      expect(concepts[0]).toStrictEqual<CoreModelExportConcept>(CORE_MODEL_CONCEPTS[0]);
      expect(concepts[0].errors[0]).toStrictEqual<string>(CORE_MODEL_CONCEPTS[0].errors[0]);
    });
  });

  it('Should refresh concepts', () => {
    service.refreshConcepts();
    service.concepts$.subscribe((concepts) => {
      expect(concepts[0]).toStrictEqual<CoreModelExportConcept>(CORE_MODEL_CONCEPTS[0]);
      expect(concepts[0].errors[0]).toStrictEqual<string>(CORE_MODEL_CONCEPTS[0].errors[0]);
    });
  });

  it('Should export core model', () => {
    const toExport = {
      version: '0.0.2',
      name: 'Core Model export',
      explanation: 'export test',
      comment: 'export test comment',
      date: new Date().toString(),
      username: 'Joe Doe',
    };
    service.exportCoreModel(toExport).subscribe((history) => {
      expect(history).toStrictEqual<CoreModelExportHistoryDTO[]>(CORE_MODEL_EXPORT_HISTORY);
    });
  });

  it('Should get last core model exported', () => {
    service.getLastCoreModelExported().subscribe((lastHistory) => {
      expect(lastHistory).toStrictEqual<CoreModelExportHistory | undefined>({
        version: '0.0.1',
        name: 'Core Model Test',
        explanation: 'history description',
        comment: 'comment',
        date: new Date(),
        username: 'Joe Doe',
      });
    });
  });
  it('Should filter on concepts in errors', () => {
    service.filterOnConceptsInErrors();
    service.concepts$.subscribe((concepts) => {
      expect(concepts[0]).toStrictEqual<CoreModelExportConcept>(CORE_MODEL_CONCEPTS[1]);
    });
  });
});
