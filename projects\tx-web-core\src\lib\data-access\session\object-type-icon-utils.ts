import { unzip } from 'unzipit';
import { SpecificIconsUrls } from './object-type-icon.model';

enum FileExtension {
  Svg = 'svg',
  Png = 'png',
}
/**
 * Throw error
 * @param zip
 * @returns
 */
export async function toSpecificIconsUrls(zip: Blob): Promise<SpecificIconsUrls> {
  const zipInfo = await unzip(zip);
  const files = await Promise.all(
    Object.values(zipInfo.entries).reduce((files, entry) => {
      return [...files, entry.blob()];
    }, [] as Promise<Blob>[])
  );
  const specificIconsUrls = Object.keys(zipInfo.entries).reduce(
    (specificIconsUrls, name, index) => {
      const iconTitle = name.split('.');
      const iconIndex = parseInt(iconTitle[0]);
      if (isNaN(iconIndex)) {
        throw new Error(iconIndex + ' icon index is Invalid');
      }
      const nameExtension = name.split('.')[1];
      if (nameExtension.toLowerCase() === FileExtension.Svg) {
        return {
          ...specificIconsUrls,
          iconShocks: specificIconsUrls.iconShocks.set(
            iconIndex,
            URL.createObjectURL(files[index])
          ),
        };
      }
      return {
        ...specificIconsUrls,
        txIcons: specificIconsUrls.txIcons.set(iconIndex, URL.createObjectURL(files[index])),
      };
    },
    {
      iconShocks: new Map<number, string>(),
      txIcons: new Map<number, string>(),
    }
  );
  return specificIconsUrls;
}
