import { MatDividerModule } from '@angular/material/divider';
import {
  ComponentFixture,
  fakeAsync,
  flush,
  TestBed,
  tick,
  waitForAsync,
} from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { AuditsComponent } from './audits.component';
import { AuditsService } from 'src/app/admins/audits/services/audits.service';
import {
  createFileFromMockFile,
  ErrorServiceMock,
  SessionServiceMock,
  testSections,
  AuditsServiceMock,
  BreadcrumdMockComponent,
  ToastServiceMock,
  ConfigServiceMock,
} from 'src/app/app.testing.mock';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { TreeGridModule } from '@syncfusion/ej2-angular-treegrid';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { SessionService } from 'src/app/core/services/session/session.service';
import { ErrorService } from 'src/app/core/services/errors/error.service';
import { About } from 'src/app/core/about/about';
import { merge, of } from 'rxjs';
import { Audit, DatabaseInformation, InformationAudit } from 'src/app/admins/audits/models/audit';
import {
  ToastComponent,
  ToastService,
  TxConfigService,
  TxEscapeHtmlPipe,
  TxGridColumnTemplate,
  TxTreeGridComponent,
} from '@bassetti-group/tx-web-core';
import { FilesUtils } from 'src/app/core/utils/files';
import { MockComponent, MockDirective, MockProvider } from 'ng-mocks';
import { DropFileComponent } from 'src/app/shared/components/drop-file/drop-file.component';
import { LoaderComponent } from 'src/app/shared/components/loader/loader.component';

const TRANSLATIONS = {
  en: {},
  fr: {},
};

describe('AuditsComponent', () => {
  let component: AuditsComponent;
  let fixture: ComponentFixture<AuditsComponent>;
  let auditsService: AuditsService;
  let toastService: ToastService;
  let sessionService: SessionService;
  let errorService: ErrorService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        AuditsComponent,
        BreadcrumdMockComponent,
        MockComponent(DropFileComponent),
        MockComponent(LoaderComponent),
        MockComponent(TxTreeGridComponent),
        MockDirective(TxGridColumnTemplate),
        TxEscapeHtmlPipe,
      ],
      imports: [
        NoopAnimationsModule,
        MatTooltipModule,
        MatProgressBarModule,
        TreeGridModule,
        FontAwesomeTestingModule,
        MatDividerModule,
        TranslateTestingModule.withTranslations(TRANSLATIONS),
      ],
      providers: [
        { provide: AuditsService, useClass: AuditsServiceMock },
        { provide: ToastService, useClass: ToastServiceMock },
        { provide: SessionService, useClass: SessionServiceMock },
        { provide: ErrorService, useClass: ErrorServiceMock },
        MockProvider(TxConfigService, ConfigServiceMock, 'useClass'),
      ],
    }).compileComponents();

    auditsService = TestBed.inject(AuditsService);
    toastService = TestBed.inject(ToastService);
    sessionService = TestBed.inject(SessionService);
    errorService = TestBed.inject(ErrorService);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AuditsComponent);
    component = fixture.debugElement.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Initial state', () => {
    it('should register "queryresult" request to error service', () => {
      const spyRegister = jest.spyOn(errorService, 'registerUnhandledRequestURL');

      fixture.detectChanges(); // initialize component

      expect(spyRegister).toBeCalledWith('https://localhost:44336/api/TxAudit/queryresult', true);
    });

    it('should get the state of execution', () => {
      auditsService.isAlreadyExecuted = jest.fn().mockReturnValue(true);

      fixture.detectChanges(); // initialize component

      expect(component.isExecuteMode).toBe(true);
    });

    it('should get the version of TEEXMA (version.revision)', () => {
      const aboutInformation: About = {
        teexma: { version: '5.0.0', revision: '54123', generatedOn: new Date() },
        txAdministration: { version: '1.5' },
        txBusinessRest: { version: '1.3' },
      };
      sessionService.getVersionsInformation = jest.fn().mockReturnValue(of(aboutInformation));

      fixture.detectChanges(); // initialize component

      expect(component.version).toEqual(
        `${aboutInformation.teexma.version}.${aboutInformation.teexma.revision}`
      );
    });

    it('should get the the sections of audit', () => {
      auditsService.getSections = jest.fn().mockReturnValue(of(testSections));

      fixture.detectChanges(); // initialize component

      expect(component.sections.length).toBe(2);
    });

    it('should get the revision of the Database', () => {
      const dbInfo: DatabaseInformation = {
        revision: 54123,
        version: '5.0.0',
      };
      auditsService.getInfomation = jest.fn().mockReturnValue(of(dbInfo));

      fixture.detectChanges(); // initialize component

      expect(component.revision).toBe(dbInfo.revision);
    });

    it('should get file audit information', () => {
      const fileInfo: InformationAudit = {
        numberAuditPoints: 2,
        isFileUploaded: true,
      };
      auditsService.getInformationAuditPoint = jest.fn().mockReturnValue(of(fileInfo));

      fixture.detectChanges(); // initialize component

      expect(component.numberAuditPoints).toBe(fileInfo.numberAuditPoints);
    });

    it('should set file as uploaded', () => {
      const fileInfo: InformationAudit = {
        numberAuditPoints: 2,
        isFileUploaded: true,
      };
      auditsService.getInformationAuditPoint = jest.fn().mockReturnValue(of(fileInfo));

      fixture.detectChanges(); // initialize component

      expect(component.isFileUpload).toBe(fileInfo.isFileUploaded);
    });

    it('should update the loading state', () => {
      auditsService.getLoadingBar = jest.fn().mockReturnValue(of(false));

      fixture.detectChanges(); // initialize component

      expect(component.isLoadingBar).toBe(false);
    });
  });

  describe('Window unload', () => {
    it('should delete audit file', () => {
      const spyDeleteFile = jest.spyOn(auditsService, 'deleteAuditFiles');

      window.dispatchEvent(new Event('beforeunload'));

      expect(spyDeleteFile).toBeCalled();
    });
  });

  describe('Execute audit', () => {
    beforeEach(() => {
      fixture.detectChanges();
      if (component.treegrid) {
        const gridInstance = {
          ...component.treegrid.gridComponent,
          hideColumns: jest.fn(),
          showColumns: jest.fn(),
        };
        component.treegrid.gridComponent = gridInstance as any;
      }
      component.createNotification = jest.fn();
      component.updateNotification = jest.fn();
      component.updateProgress = jest.fn();
      component.isExecuteMode = false;
    });

    it('should call "executeAudit"', () => {
      const spyExecute = jest.spyOn(component, 'executeAudit');

      fixture.debugElement
        .query(By.css('#audit-execute-button'))
        .triggerEventHandler('click', null);
      fixture.detectChanges();

      expect(spyExecute).toBeCalled();
    });

    it('should hide columns in treegrid', () => {
      let spyHide;
      if (component.treegrid) {
        spyHide = jest.spyOn(component.treegrid, 'hideColumns');
      }
      component.executeAudit();

      expect(spyHide).toBeCalledWith(['explanation']);
    });

    it('should show columns in treegrid', () => {
      let spyShow;
      if (component.treegrid) {
        spyShow = jest.spyOn(component.treegrid, 'showColumns');
      }

      component.executeAudit();

      expect(spyShow).toBeCalledWith(['name', 'status', 'result']);
    });

    it('should change "isExecuteMode" to true', () => {
      component.executeAudit();

      expect(component.isExecuteMode).toBe(true);
    });

    it('should create a new notification', () => {
      const spyCrNotification = jest.spyOn(component, 'createNotification');

      component.executeAudit();

      expect(spyCrNotification).toBeCalledWith('loading', `admins.audit.executionInProgress`, 0, 0);
    });

    it('should call "executeQueries"', () => {
      const spyExecuteQuery = jest.spyOn(auditsService, 'executeQueries');

      component.executeAudit();

      expect(spyExecuteQuery).toBeCalled();
    });

    it('should update the notification previously created', () => {
      const spyUpdateNotif = jest.spyOn(component, 'updateNotification');
      component.numberAuditPoints = 3;
      auditsService.executeQueries = jest
        .fn()
        .mockReturnValue(merge(...([of(1), of(2), of(3)] as const)));

      component.executeAudit();

      expect(spyUpdateNotif).toBeCalledTimes(1);
    });

    it('should hide execute button', () => {
      component.executeAudit();
      fixture.detectChanges();

      expect(fixture.debugElement.query(By.css('#audit-execute-button'))).toBeFalsy();
    });

    it('should show export button', () => {
      component.executeAudit();
      fixture.detectChanges();

      expect(fixture.debugElement.query(By.css('#audit-export-button'))).toBeTruthy();
    });
  });

  describe('Reset audit', () => {
    beforeEach(() => {
      fixture.detectChanges();
      if (component.treegrid) {
        const gridInstance = {
          ...component.treegrid.gridComponent,
          hideColumns: jest.fn(),
          showColumns: jest.fn(),
        };
        component.treegrid.gridComponent = gridInstance as any;
      }
      component.updateNotification = jest.fn();
    });

    it('should call "resetAudit"', () => {
      const spyReset = jest.spyOn(component, 'reset');
      component.setResetMode = jest.fn();

      fixture.debugElement.query(By.css('#audit-reset-button')).triggerEventHandler('click', null);
      fixture.detectChanges();

      expect(spyReset).toBeCalled();
    });

    it('should call "setResetMode" in reset method', () => {
      const spyReset = jest.spyOn(component, 'setResetMode').mockImplementation(jest.fn());

      component.reset();

      expect(spyReset).toBeCalled();
    });

    it('should call "resetAuditPoints" in setResetMode method', () => {
      const spyUpdateAudit = jest.spyOn(auditsService, 'updateAuditPoints');

      component.setResetMode();

      expect(spyUpdateAudit).toBeCalled();
    });

    it('should hide columns in treegrid', () => {
      let spyHide;
      if (component.treegrid) {
        spyHide = jest.spyOn(component.treegrid, 'hideColumns');
      }

      component.setResetMode();

      expect(spyHide).toBeCalledWith(['status', 'result']);
    });

    it('should show columns in treegrid', () => {
      let spyShow;
      if (component.treegrid) {
        spyShow = jest.spyOn(component.treegrid, 'showColumns');
      }

      component.setResetMode();

      expect(spyShow).toBeCalledWith(['name', 'explanation']);
    });

    it('should reset parameters "isExecuteMode"', () => {
      component.isExecuteMode = true;

      component.setResetMode();

      expect(component.isExecuteMode).toBe(false);
    });

    it('should reset parameters "progress"', () => {
      component.progress = 100;

      component.setResetMode();

      expect(component.progress).toBe(0);
    });

    it('should update notification with an error', () => {
      const spyUpdateNotif = jest.spyOn(component, 'updateNotification');
      const toast = {} as ToastComponent;
      auditsService.toastProgress = toast;

      component.setResetMode();

      expect(spyUpdateNotif).toBeCalledWith(toast, 'error', 'admins.audit.executionInterrupted');
    });

    it('should NOT update notification', () => {
      const spyUpdateNotif = jest.spyOn(component, 'updateNotification');
      auditsService.toastProgress = null;

      component.setResetMode();

      expect(spyUpdateNotif).not.toBeCalled();
    });

    it('should be possible to reset the audit', () => {
      component.isExecuteMode = true;

      expect(component.cannotReset()).toBe(false);
    });

    it('should NOT be possible to reset the audit', () => {
      component.isExecuteMode = false;

      expect(component.cannotReset()).toBe(true);
    });
  });

  describe('Export audit', () => {
    beforeEach(() => {
      FilesUtils.downloadBlobFile = jest.fn();
      fixture.detectChanges(); // ngOnInit
      component.isExecuteMode = true; // active export
      fixture.detectChanges(); // update view
    });

    it('should call "exportAudit"', () => {
      const spyExport = jest.spyOn(component, 'export');

      fixture.debugElement.query(By.css('#audit-export-button')).triggerEventHandler('click', null);
      fixture.detectChanges();

      expect(spyExport).toBeCalled();
    });

    it('should call "exportResults" from service', () => {
      const spyExportResults = jest.spyOn(auditsService, 'exportResults');
      FilesUtils.downloadBlobFile = jest.fn();

      component.export();

      expect(spyExportResults).toBeCalled();
    });

    it('should download File', () => {
      const spyDownloadBlob = jest
        .spyOn(FilesUtils, 'downloadBlobFile')
        .mockImplementation(jest.fn());

      component.export();

      expect(spyDownloadBlob).toBeCalled();
    });

    it('should be possible to export the audit', () => {
      auditsService.toastProgress = null;

      expect(component.cannotExport()).toBe(false);
    });

    it('should NOT be possible to export the audit', () => {
      auditsService.toastProgress = {} as ToastComponent;

      expect(component.cannotExport()).toBe(true);
    });
  });

  describe('Change audit file', () => {
    beforeEach(() => {
      fixture.detectChanges(); // ngOnInit
    });

    describe('Handle file input', () => {
      let fileExml: File;
      beforeEach(() => {
        fileExml = createFileFromMockFile({
          body: 'test',
          mimeType: 'text/plain',
          name: 'TxAudit.exml',
        });
        component.uploadFileToActivity = jest.fn();
        component.createNotification = jest.fn();
      });

      it('should delete old files if extention is EXML', () => {
        const spyDelete = jest.spyOn(auditsService, 'deleteAuditFiles');
        FilesUtils.extractFileExt = jest.fn().mockReturnValue('exml');

        component.handleFileInput({ isFileUploaded: true, file: fileExml });

        expect(spyDelete).toBeCalled();
      });

      it('should upload new file if extention is EXML', () => {
        const spyUpload = jest.spyOn(component, 'uploadFileToActivity');
        FilesUtils.extractFileExt = jest.fn().mockReturnValue('exml');

        component.handleFileInput({ isFileUploaded: true, file: fileExml });

        expect(spyUpload).toBeCalledWith(fileExml);
      });
    });

    describe('Upload audit file', () => {
      let fileToUpload: File;
      beforeEach(() => {
        fileToUpload = { name: 'TxAudit.exml' } as File;
        component.setResetMode = jest.fn();
        component.createNotification = jest.fn();
      });

      it('should call "setResetMode" when upload file', () => {
        const spyReset = jest.spyOn(component, 'setResetMode');

        component.uploadFileToActivity(fileToUpload);

        expect(spyReset).toBeCalled();
      });

      it('should upload file', () => {
        const spyPostFile = jest.spyOn(auditsService, 'postFile');

        component.uploadFileToActivity(fileToUpload);

        expect(spyPostFile).toBeCalledWith(fileToUpload);
      });

      it('should create a notification', () => {
        const spyCreateNotif = jest.spyOn(component, 'createNotification');

        component.uploadFileToActivity(fileToUpload);

        expect(spyCreateNotif).toBeCalledWith('success', 'admins.audit.fileUploaded', 8000);
      });
    });

    describe('drag file', () => {
      it('should call "containsFiles" when a file is dragged', () => {
        const spyContainsFiles = jest
          .spyOn(component, 'containsFiles')
          .mockImplementation(jest.fn());

        fixture.debugElement
          .query(By.css('#fileControl'))
          .triggerEventHandler('dragover', { dataTransfer: { types: ['Files'] } });

        expect(spyContainsFiles).toBeCalledWith({ dataTransfer: { types: ['Files'] } });
      });

      it('should activate drop zone when a file is dragged', () => {
        component.isDropzoneHovered = false;

        component.containsFiles({ dataTransfer: { types: ['Files'] } } as unknown as DragEvent);

        expect(component.isDropzoneHovered).toBe(true);
      });

      it('should NOT activate drop zone when something else than a file is dragged', () => {
        component.isDropzoneHovered = true;

        component.containsFiles({
          dataTransfer: { types: ['text/plain'] },
        } as unknown as DragEvent);

        expect(component.isDropzoneHovered).toBe(false);
      });
    });
  });

  describe('Delete audit file', () => {
    beforeEach(() => {
      fixture.detectChanges(); // ngOnInit
      component.setResetMode = jest.fn();
    });

    it('should call "setResetMode" in deleteFile method', () => {
      const spyReset = jest.spyOn(component, 'setResetMode');

      component.deleteFile();

      expect(spyReset).toBeCalled();
    });

    it('should delete the file from server', () => {
      const spyDelete = jest.spyOn(auditsService, 'deleteAuditFiles');

      component.deleteFile();

      expect(spyDelete).toBeCalled();
    });

    it('should load the default Audit file', () => {
      const spyLoadAudit = jest.spyOn(auditsService, 'callTxAudit');

      component.deleteFile();

      expect(spyLoadAudit).toBeCalled();
    });
  });

  describe('Manage results in grid', () => {
    let audit: Audit;
    beforeEach(() => {
      fixture.detectChanges(); // ngOnInit
      audit = {
        id: 0,
        isShowAllLine: false,
        explanation: '',
        name: '',
        status: 'warning',
        result: '',
        isMultiLine: false,
        allResult: '',
      };
    });

    it('should make all lines visible', () => {
      component.showAllLine(audit);

      expect(audit.isShowAllLine).toBe(true);
    });

    it('should make all lines NOT visible', () => {
      component.hideLine(audit);

      expect(audit.isShowAllLine).toBe(false);
    });
  });

  describe('Manage notifications', () => {
    beforeEach(() => {
      fixture.detectChanges(); // ngOnInit
    });

    it('should create a notification', () => {
      const spyToast = jest.spyOn(toastService, 'show');

      component.createNotification('loading', 'message', 8000);

      expect(spyToast).toBeCalled();
    });

    describe('Update notifications', () => {
      let type: string;
      let message: string;
      let toast: ToastComponent;
      beforeEach(() => {
        type = 'success';
        message = 'message';
        toast = component.createNotification('loading', '', 0);
      });

      it('should update the notification content', () => {
        component.updateNotification(toast, type, message);

        expect(toast.data.templateContext).toEqual({ test: { state: type, message } });
      });

      it('should update the notification type', () => {
        component.updateNotification(toast, type, message);

        expect(toast.data.type).toBe('success');
      });

      it('should update with "information" type when loading is fill', () => {
        component.updateNotification(toast, 'loading', message);

        expect(toast.data.type).toBe('information');
      });

      it('should keep the notification displayed after 2.5sec', fakeAsync(() => {
        component.updateNotification(toast, type, message);
        tick(2500);

        expect(toast.animationState).not.toBe('closing');
        flush();
      }));

      it('should dismiss the notification after 3sec', fakeAsync(() => {
        component.updateNotification(toast, type, message);
        tick(3001);

        expect(toast.animationState).toBe('closing');
        flush();
      }));
    });
  });
});
