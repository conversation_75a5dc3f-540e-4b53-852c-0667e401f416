import {
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  Optional,
  Output,
  Self,
  ViewChild,
} from '@angular/core';
import { AbstractControl, ControlValueAccessor, FormGroup, NgControl } from '@angular/forms';
import { MatFormField, MatFormFieldControl, MAT_FORM_FIELD } from '@angular/material/form-field';
import { Subject } from 'rxjs';
import { TxAttributeField } from '../../../../models/formConfiguration/businessClass/attribute-field';

@Component({
  selector: 'tx-input-numbers-control',
  templateUrl: './input-numbers-control.component.html',
  styleUrls: ['./input-numbers-control.component.scss'],
  providers: [
    {
      provide: MatFormFieldControl,
      useExisting: TxInputNumbersControlComponent,
    },
  ],
  host: {
    '[class.example-floating]': 'shouldLabelFloat',
  },
})
export class TxInputNumbersControlComponent
  implements MatFormFieldControl<number>, ControlValueAccessor
{
  @Input() form!: FormGroup;
  @Input() minFormControl!: AbstractControl;
  @Input() maxFormControl!: AbstractControl;
  @Input() meanFormControl!: AbstractControl;

  @Input() minPlaceHolder = 'min';
  @Input() maxPlaceHolder = 'max';
  @Input() meanPlaceHolder = 'mean';

  @Input() minLength!: number;
  @Input() maxLength!: number;
  @Input() lowerBoundValue: any;
  @Input() upperBoundValue: any;
  @Input() lowerBoundInclude!: boolean;
  @Input() upperBoundInclude!: boolean;

  @Input() withSecondInput = false;
  @Input() withThirdInput = false;

  @Input() inputWidthPx = 54;

  @Output() errorEvent = new EventEmitter();
  @Output() focusEvent = new EventEmitter();
  @Output() unfocusEvent = new EventEmitter();
  @Output() onKeyPress = new EventEmitter();

  @ViewChild('firstInput') firstInput!: ElementRef;
  @ViewChild('secondInput') secondInput!: ElementRef;
  @ViewChild('thirdInput') thirdInput!: ElementRef;

  maxField!: TxAttributeField;
  meanField!: TxAttributeField;

  maxFieldId!: string;
  meanFieldId!: string;

  value2UpperThanValue1 = true;
  meanBetweenMinAndMax = true;
  valueRequiredUnassigned = false;

  mainUnit: any;
  secondUnits!: any[];
  idUnitSelected!: number;

  stateChanges = new Subject<void>();
  @Input()
  get placeholder(): string {
    return this._placeholder;
  }
  set placeholder(value: string) {
    this._placeholder = value;
  }
  private _placeholder!: string;

  focused!: boolean;
  get errorState(): boolean {
    return this.minFormControl.invalid;
  }
  controlType = 'app-inputs-number-field';
  autofilled?: boolean;
  userAriaDescribedBy?: string;

  constructor(
    @Optional() @Inject(MAT_FORM_FIELD) public formField: MatFormField,
    @Optional() @Self() public ngControl: NgControl
  ) {
    if (this.ngControl != null) {
      this.ngControl.valueAccessor = this;
    }
  }

  value!: number;
  id!: string;
  required!: boolean;
  disabled!: boolean;
  writeValue(obj: any): void {}

  registerOnChange(fn: any): void {}
  registerOnTouched(fn: any): void {}
  setDisabledState?(isDisabled: boolean): void {}
  setDescribedByIds(ids: string[]): void {}
  onContainerClick(event: MouseEvent): void {}

  get empty() {
    const minFieldHasValue =
      this.minFormControl &&
      this.minFormControl.value !== null &&
      this.minFormControl.value !== undefined;
    const maxFieldHasValue =
      this.maxFormControl &&
      this.maxFormControl.value !== null &&
      this.maxFormControl.value !== undefined;
    const meanFieldHasValue =
      this.meanFormControl &&
      this.meanFormControl.value !== null &&
      this.meanFormControl.value !== undefined;

    return !minFieldHasValue && !maxFieldHasValue && !meanFieldHasValue;
  }

  get shouldLabelFloat() {
    return this.focused || !this.empty;
  }

  onFocusIn(event: FocusEvent) {
    this.focused = true;
    this.focusEvent.emit(true);
  }

  onFocusOut(event: FocusEvent) {
    this.focused = false;
    this.autocompleteValues();
    this.focusEvent.emit(false);
  }

  // initId() {
  //   if (this.field && (this.field.attribute as TxAttributePoint).idUnit) {
  //     // to change : get unit from back
  //     this.mainUnit = {id: 1, name: 'Kg'};
  //     // get seconds unit from
  //     this.secondUnits = [this.mainUnit, {id: 2, name: 'g'}, {id: 3, name: 'lbs'}, {id: 4, name: 'vehicule/an'}];
  //     this.idUnitSelected = this.mainUnit.id;
  //   }
  // }

  autocompleteValues() {
    if (this.maxFormControl && this.maxFormControl.value === null) {
      this.maxFormControl.setValue(this.minFormControl.value);
    }
    if (this.meanFormControl && this.meanFormControl.value === null) {
      this.meanFormControl.setValue(this.minFormControl.value);
    }
    this.checkValuesValidity();
  }

  focusFirstInput() {
    this.firstInput.nativeElement.focus();
  }

  focusSecondInput() {
    this.secondInput.nativeElement.focus();
  }

  upperThanLower(): boolean | undefined {
    if (!this.maxFormControl) {
      return true;
    }

    const minValue = this.minFormControl ? parseFloat(this.minFormControl.value) : null;
    const maxValue = this.maxFormControl ? parseFloat(this.maxFormControl.value) : null;

    let value2UpperThanValue1 = true;
    if ((minValue || minValue === 0) && (maxValue || maxValue === 0)) {
      value2UpperThanValue1 = minValue <= maxValue;
    }
    // const matched = true;
    setTimeout(() => {
      if (!value2UpperThanValue1) {
        this.minFormControl.setErrors({ minUpperThanMax: true });
      }
    }, 100);
    this.value2UpperThanValue1 = value2UpperThanValue1;
  }

  betweenLowerAndUpper(): boolean | undefined {
    if (!this.maxFormControl || !this.meanFormControl) {
      return true;
    }

    const minValue = this.minFormControl ? parseFloat(this.minFormControl.value) : null;
    const maxValue = this.maxFormControl ? parseFloat(this.maxFormControl.value) : null;
    const meanValue = this.meanFormControl ? parseFloat(this.meanFormControl.value) : null;

    let meanBetweenMinAndMax = true;
    if (
      (minValue || minValue === 0) &&
      (maxValue || maxValue === 0) &&
      (meanValue || meanValue === 0)
    ) {
      meanBetweenMinAndMax = meanValue <= maxValue && meanValue >= minValue;
    }
    // const matched = true;
    setTimeout(() => {
      if (!meanBetweenMinAndMax) {
        this.minFormControl.setErrors({
          meanOutOfMinAndMax: true,
        });
      }
    }, 100);

    this.meanBetweenMinAndMax = meanBetweenMinAndMax;
  }

  changeLowerBoundValue(value: number) {
    this.lowerBoundValue = value;
  }

  changeUpperBoundValue(value: number) {
    this.upperBoundValue = value;
  }

  checkBoundValue(FieldFormControl: AbstractControl) {
    const fieldValue = FieldFormControl ? parseFloat(FieldFormControl.value) : null;
    if (fieldValue || fieldValue === 0) {
      if (
        this.lowerBoundValue &&
        (this.lowerBoundInclude
          ? fieldValue < this.lowerBoundValue
          : fieldValue <= this.lowerBoundValue)
      ) {
        this.minFormControl.setErrors({ min: this.lowerBoundValue });
      } else if (
        this.upperBoundValue &&
        (this.upperBoundInclude
          ? fieldValue > this.upperBoundValue
          : fieldValue >= this.upperBoundValue)
      ) {
        this.minFormControl.setErrors({ max: this.upperBoundValue });
      } else if (FieldFormControl === this.meanFormControl) {
        this.checkBoundValue(this.maxFormControl);
      } else if (FieldFormControl === this.maxFormControl) {
        this.checkBoundValue(this.minFormControl);
      } else {
        this.minFormControl.updateValueAndValidity();
        this.checkValuesValidity();
      }
    }
  }

  checkValuesValidity() {
    this.upperThanLower();
    this.betweenLowerAndUpper();
  }
}
