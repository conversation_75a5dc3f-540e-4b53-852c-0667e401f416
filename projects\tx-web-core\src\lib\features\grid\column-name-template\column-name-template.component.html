<ng-container
  *ngIf="data && hasMainParentObject && data.idParent === undefined; else templateChild">
  <div class="tree-node-parent">
    <fa-icon [icon]="['fal', 'folder']" class="otgrid-accordion-icon" size="lg"></fa-icon>
    {{ data.name | translate }}
    <span class="otgrid-accordion-counter background-grey40-half">{{ dataLength }}</span>
  </div>
</ng-container>

<ng-template #templateChild>
  <ng-container *ngIf="data">
    <img
      *ngIf="!hasNoIcon && data.icon !== null; else noIcon"
      class="otgrid-tree-icon"
      [ngClass]="{ 'disabled-icon': data.isDisabled }"
      [src]="data?.icon"
      alt="Concept icon" />
    <div
      *ngIf="searchId === data.id; else defaultSearch"
      [matTooltip]="data | formatConceptNameTooltip : showMoreTooltips : hideTooltipId"
      matTooltipClass="tooltip-line-break"
      class="d-inline">
      <mark>{{ data?.name | translate }}</mark>
    </div>
  </ng-container>
</ng-template>

<ng-template #defaultSearch>
  <ng-container *ngIf="data">
    <div
      [ngClass]="{ 'color-grey80': data.isDisabled }"
      [matTooltip]="data | formatConceptNameTooltip : showMoreTooltips : hideTooltipId"
      matTooltipClass="tooltip-line-break"
      class="d-inline"
      [innerHTML]="
        data?.name | translate | escapeHtml | highlightSearch : (inputSearchValue | escapeHtml)
      "></div>
  </ng-container>
</ng-template>

<ng-template #noIcon>
  <fa-icon
    *ngIf="!hasNoIcon"
    [icon]="['fal', 'comment']"
    size="lg"
    class="otgrid-tree-icon"></fa-icon>
</ng-template>
