import { FilterQuery } from '@bassetti-group/tx-web-core/src/lib/features/grid';
import { of } from 'rxjs';
import { TxTreeExpandState } from '../../tree-grid.interface';
import { PaginatedTreeGridDataSource } from '../paginated-tree-grid-data-source';

describe('PaginatedTreeGridDataSource', () => {
  let dataSource: PaginatedTreeGridDataSource<any>;
  interface TreeNode {
    id: number;
    name: string;
    children?: TreeNode[];
  }
  let mockData: TreeNode[];

  beforeEach(() => {
    mockData = [
      {
        id: 1,
        name: 'Parent 1',
        children: [
          { id: 2, name: 'Child 1.1' },
          { id: 3, name: 'Child 1.2' },
        ],
      },
      {
        id: 4,
        name: 'Parent 2',
        children: [{ id: 5, name: 'Child 2.1' }],
      },
    ];

    // Initialize datasource with mock observable data
    dataSource = new PaginatedTreeGridDataSource(
      of(mockData),
      'id',
      null,
      {} as FilterQuery<TreeNode>,
      10
    );
  });

  describe('initialization', () => {
    it('should create instance with correct default values', () => {
      expect(dataSource).toBeDefined();
      expect(dataSource.expandedNodes.size).toBe(0);
      expect(dataSource.expandState).toBe(TxTreeExpandState.ExpandedFirst);
      expect(dataSource.buildTree).toBe(false);
      expect(dataSource.autoReloadCollapse).toBe(true);
    });
  });

  describe('addData', () => {
    it('should add data and assign levels', () => {
      const data = of(mockData);
      dataSource.addData(data);

      // Wait for data to be processed
      return new Promise((resolve) => {
        dataSource.connect().subscribe((result) => {
          expect(result[0].level).toBeDefined();
          expect(result[0].children[0].level).toBeDefined();
          resolve(null);
        });
      });
    });

    it('should throw error when buildTree is true but mappings are undefined', () => {
      dataSource.buildTree = true;
      expect(() => {
        dataSource.addData(mockData);
      }).toThrow('idMapping or parentMapping is missing to build tree hierarchies');
    });
  });

  describe('toggleNode', () => {
    it('should toggle node expansion state', () => {
      const node = mockData[0];
      dataSource.toggleNode(node);
      expect(dataSource.isExpanded(node)).toBe(true);

      dataSource.toggleNode(node);
      expect(dataSource.isExpanded(node)).toBe(false);
    });

    it('should throw error when primaryKey is undefined', () => {
      dataSource = new PaginatedTreeGridDataSource(of(mockData));
      expect(() => {
        dataSource.toggleNode(mockData[0]);
      }).toThrow('primaryKey not defined');
    });
  });

  describe('handleExpandCollapseNode', () => {
    it('should expand all nodes when state is Expanded', () => {
      dataSource.handleExpandCollapseNode(TxTreeExpandState.Expanded);

      return new Promise((resolve) => {
        dataSource.connect().subscribe((result) => {
          expect(dataSource.expandedNodes.size).toBeGreaterThan(0);
          expect(dataSource.isExpanded(mockData[0])).toBe(true);
          expect(dataSource.isExpanded(mockData[1])).toBe(true);
          resolve(null);
        });
      });
    });

    it('should expand only first node when state is ExpandedFirst', () => {
      dataSource.handleExpandCollapseNode(TxTreeExpandState.ExpandedFirst);

      return new Promise((resolve) => {
        dataSource.connect().subscribe((result) => {
          expect(dataSource.expandedNodes.size).toBe(1); // first node & its children
          expect(dataSource.isExpanded(mockData[0])).toBe(true);
          expect(dataSource.isExpanded(mockData[1])).toBe(false);
          resolve(null);
        });
      });
    });

    it('should collapse all nodes when state is Collapsed', () => {
      // First expand all nodes
      dataSource.handleExpandCollapseNode(TxTreeExpandState.Expanded);
      // Then collapse all
      dataSource.handleExpandCollapseNode(TxTreeExpandState.Collapsed);

      expect(dataSource.expandedNodes.size).toBe(0);
    });
  });

  describe('searchByData', () => {
    it('should search through flattened tree data', () => {
      const searchableKeys = ['name'];

      return new Promise((resolve) => {
        dataSource.connect().subscribe(() => {
          const result = dataSource.searchByData('child', undefined, searchableKeys);
          expect(result.selected).toBeDefined(); // Should find all child nodes
          resolve(null);
        });
      });
    });
  });

  describe('filterTree', () => {
    it('should return filtered tree based on expanded nodes', () => {
      const node = mockData[0];
      dataSource.toggleNode(node);

      return new Promise((resolve) => {
        dataSource.connect().subscribe((result) => {
          expect(result.length).toBeGreaterThan(1); // Should include expanded node's children
          resolve(null);
        });
      });
    });
  });

  describe('tree building', () => {
    // Test the configuration
    test('should properly configure tree building settings', () => {
      dataSource.buildTree = true;
      dataSource.idMapping = 'id';
      dataSource.parentMapping = 'parentId';

      expect(dataSource.buildTree).toBe(true);
      expect(dataSource.idMapping).toBe('id');
      expect(dataSource.parentMapping).toBe('parentId');
    });

    // Test data preparation
    test('should prepare flat data structure', () => {
      const flatData = [
        { id: 1, name: 'Parent 1', parentId: null },
        { id: 2, name: 'Child 1.1', parentId: 1 },
        { id: 3, name: 'Child 1.2', parentId: 1 },
      ];

      expect(Array.isArray(flatData)).toBe(true);
      expect(flatData.length).toBe(3);
      expect(flatData[0].parentId).toBeNull();
      expect(flatData[1].parentId).toBe(1);
    });
  });
});
