import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxFileFieldBoxComponent } from './file-field-box.component';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { MatIconModule } from '@angular/material/icon';

describe('TxFileFieldBoxComponent', () => {
  let component: TxFileFieldBoxComponent;
  let fixture: ComponentFixture<TxFileFieldBoxComponent>;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [FontAwesomeTestingModule, MatIconModule, TxFileFieldBoxComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxFileFieldBoxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
