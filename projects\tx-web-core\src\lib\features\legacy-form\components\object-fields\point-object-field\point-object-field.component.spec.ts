import { LegacyTxDataType } from '../../../services/structure/models/data';
import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxPointObjectFieldComponent } from './point-object-field.component';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { AttributesMockService, UnitMockService } from '../../../testing.mock';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatInputModule } from '@angular/material/input';
import {
  AbstractControl,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatChipsModule } from '@angular/material/chips';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import {
  LegacyTxAttributeRight,
  TxAttributePoint,
} from '../../../services/structure/models/attribute';
import { LegacyTxDataNumeric } from '../../../services/structure/models/data';
import { TxUnit } from '../../../services/structure/models/unit';
import { TxUnitService } from '../../../services/structure/services/unit.service';
import { MockComponent } from 'ng-mocks';
import { TxInputNumbersFieldComponent } from '../../generic-fields/input-numbers-field/input-numbers-field.component';

describe('TxPointObjectFieldComponent', () => {
  let component: TxPointObjectFieldComponent;
  let fixture: ComponentFixture<TxPointObjectFieldComponent>;
  let element;
  let attributesService: LegacyTxAttributesService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxPointObjectFieldComponent, MockComponent(TxInputNumbersFieldComponent)],
      providers: [
        { provide: LegacyTxAttributesService, useClass: AttributesMockService },
        { provide: TxUnitService, useClass: UnitMockService },
      ],
      imports: [
        MatChipsModule,
        MatTooltipModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatInputModule,
        BrowserAnimationsModule,
      ],
    }).compileComponents();
    attributesService = TestBed.inject(LegacyTxAttributesService);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxPointObjectFieldComponent);
    component = fixture.componentInstance;
    element = fixture.nativeElement;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('testing isValid...', () => {
    beforeEach(() => {
      const minControl = new FormControl(0);
      const maxControl = new FormControl(0);
      const meanControl = new FormControl(0);
      minControl.setValidators(Validators.max(50));
      maxControl.setValidators(Validators.max(50));
      meanControl.setValidators(Validators.max(50));
      component.control = minControl;
      component.maxFormControl = maxControl;
      component.meanFormControl = meanControl;
    });

    it('should return true if three controls are valid', () => {
      expect(component.isValid).toBeTruthy();
    });

    it('should return false if min control unvalid', () => {
      component.control.setValue(100);
      expect(component.isValid).toBeFalsy();
    });

    it('should return false if max control unvalid', () => {
      component.maxFormControl.setValue(100);
      expect(component.isValid).toBeFalsy();
    });

    it('should return false if mean control unvalid', () => {
      component.meanFormControl.setValue(100);
      expect(component.isValid).toBeFalsy();
    });
  });

  describe('testing notEmpty...', () => {
    beforeEach(() => {
      const minControl = new FormControl();
      const maxControl = new FormControl();
      const meanControl = new FormControl();
      component.control = minControl;
      component.maxFormControl = maxControl;
      component.meanFormControl = meanControl;
    });

    it('should return false if three controls are empty', () => {
      expect(component.notEmpty).toBeFalsy();
    });

    it('should return true if min control has a value', () => {
      component.control.setValue(100);
      expect(component.notEmpty).toBeTruthy();
    });

    it('should return true if max control has a value', () => {
      component.maxFormControl.setValue(100);
      expect(component.notEmpty).toBeTruthy();
    });

    it('should return true if mean control has a value', () => {
      component.meanFormControl.setValue(100);
      expect(component.notEmpty).toBeTruthy();
    });
  });

  describe('testing component contructor...', () => {
    it('should set inputType to number', () => {
      expect(component.inputType).toBe('number');
    });
  });

  describe('testing ngAfterViewInit...', () => {
    it('should call updateBoundValue if field has bound', () => {
      const spyUpdateBound = jest.spyOn(component, 'updateBoundValue');
      component.withBounds = true;
      component.ngAfterViewInit();
      expect(spyUpdateBound).toHaveBeenCalled();
    });

    it('should not call updateBoundValue if field has not bound', () => {
      const spyUpdateBound = jest.spyOn(component, 'updateBoundValue');
      component.withBounds = false;
      component.ngAfterViewInit();
      expect(spyUpdateBound).not.toHaveBeenCalled();
    });
  });

  describe('testing initUnitProperties...', () => {
    // TODO when unit service work
  });

  describe('testing initBoundProperties...', () => {
    let attribute: TxAttributePoint;
    beforeEach(() => {
      attribute = new TxAttributePoint({
        lowerBound: -20,
        upperBound: 20,
        id: -1,
        name: '',
        dataType: LegacyTxDataType.Boolean,
        idObjectType: 0,
        idAttributeParent: 0,
        isInherited: false,
        tags: [],
        right: LegacyTxAttributeRight.None,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      });
      component.attribute = attribute;
    });

    it('should not update lowerBoundValue if already set', () => {
      component.lowerBoundValue = -25;
      component.attribute = attribute;
      component.initBoundProperties();
      expect(component.lowerBoundValue).toBe(-25);
    });

    it('should not update upperBoundValue if already set', () => {
      component.upperBoundValue = 25;
      component.attribute = attribute;
      component.initBoundProperties();
      expect(component.upperBoundValue).toBe(25);
    });

    it('should update lowerBoundValue if not set', () => {
      component.attribute = attribute;
      component.initBoundProperties();
      expect(component.lowerBoundValue).toBe(-20);
    });

    it('should update upperBoundValue if not set', () => {
      component.attribute = attribute;
      component.initBoundProperties();
      expect(component.upperBoundValue).toBe(20);
    });

    it('should set withBound to true if bound are set', () => {
      component.initBoundProperties();
      expect(component.withBounds).toBeTruthy();
    });

    it('should set withBound tp false id no bound set', () => {
      attribute = new TxAttributePoint({
        name: 'test',
        dataType: LegacyTxDataType.DecUnique,
        lowerBound: -20,
        upperBound: 20,
        id: -1,
        idObjectType: 0,
        idAttributeParent: 0,
        isInherited: false,
        tags: [],
        right: LegacyTxAttributeRight.None,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      });
      expect(component.withBounds).toBeFalsy();
    });
  });

  describe('testing initControlsProperties...', () => {
    let simpleAttribute = new TxAttributePoint({
      name: 'test',
      dataType: LegacyTxDataType.DecUnique,
      lowerBound: -20,
      upperBound: 20,
      id: -1,
      idObjectType: 0,
      idAttributeParent: 0,
      isInherited: false,
      tags: [],
      right: LegacyTxAttributeRight.None,
      order: 0,
      idInheritedAttribute: 0,
      idLinkType: 0,
      option: {},
    });
    let rangeAttribute = new TxAttributePoint({
      dataType: LegacyTxDataType.DecRange,
      name: 'test',
      lowerBound: -20,
      upperBound: 20,
      id: -1,
      idObjectType: 0,
      idAttributeParent: 0,
      isInherited: false,
      tags: [],
      right: LegacyTxAttributeRight.None,
      order: 0,
      idInheritedAttribute: 0,
      idLinkType: 0,
      option: {},
    });
    let mmmAttribute = new TxAttributePoint({
      dataType: LegacyTxDataType.DecRangeMean,
      name: 'test',
      lowerBound: -20,
      upperBound: 20,
      id: -1,
      idObjectType: 0,
      idAttributeParent: 0,
      isInherited: false,
      tags: [],
      right: LegacyTxAttributeRight.None,
      order: 0,
      idInheritedAttribute: 0,
      idLinkType: 0,
      option: {},
    });

    beforeEach(() => {
      //  component.id= '1';
      component.attribute = simpleAttribute;
    });

    it('should set label from attribute name', () => {
      component.initControlsProperties();
      expect(component.label).toBe('test');
    });

    it('should not call addMaxFormControl for simple point attribute', () => {
      const spyAddMaxCtrl = jest.spyOn(component, 'addMaxFormControl');
      component.initControlsProperties();
      expect(spyAddMaxCtrl).not.toHaveBeenCalled();
    });

    it('should not call addMeanFormControl for simple point attribute', () => {
      const spyAddMeanCtrl = jest.spyOn(component, 'addMeanFormControl');
      component.initControlsProperties();
      expect(spyAddMeanCtrl).not.toHaveBeenCalled();
    });

    it('should call addMaxFormControl for range point attribute', () => {
      component.attribute = rangeAttribute;
      const spyAddMaxCtrl = jest.spyOn(component, 'addMaxFormControl');
      component.initControlsProperties();
      expect(spyAddMaxCtrl).toHaveBeenCalled();
    });

    it('should not call addMeanFormControl for range point attribute', () => {
      component.attribute = rangeAttribute;
      const spyAddMeanCtrl = jest.spyOn(component, 'addMeanFormControl');
      component.initControlsProperties();
      expect(spyAddMeanCtrl).not.toHaveBeenCalled();
    });

    it('should call addMaxFormControl for rangeMean point attribute', () => {
      component.attribute = mmmAttribute;
      const spyAddMaxCtrl = jest.spyOn(component, 'addMaxFormControl');
      component.initControlsProperties();
      expect(spyAddMaxCtrl).toHaveBeenCalled();
    });

    it('should call addMeanFormControl for rangeMean point attribute', () => {
      component.attribute = mmmAttribute;
      const spyAddMeanCtrl = jest.spyOn(component, 'addMeanFormControl');
      component.initControlsProperties();
      expect(spyAddMeanCtrl).toHaveBeenCalled();
    });
  });

  describe('testing initPropertiesFromAttributes...', () => {
    let simpleAttribute = new TxAttributePoint({
      name: 'test',
      dataType: LegacyTxDataType.DecUnique,
      lowerBound: -20,
      upperBound: 20,
      id: -1,
      idObjectType: 0,
      idAttributeParent: 0,
      isInherited: false,
      tags: [],
      right: LegacyTxAttributeRight.None,
      order: 0,
      idInheritedAttribute: 0,
      idLinkType: 0,
      option: {},
    });
    beforeEach(() => {
      //  component.id= '1';
      component.attribute = simpleAttribute;
    });

    it('should call initUnitProperties with simpleAttribute', () => {
      const spyInitUnitProperties = jest.spyOn(component, 'initUnitProperties');
      component.initPropertiesFromAttribute();
      expect(spyInitUnitProperties).toHaveBeenCalled();
    });

    it('should call initBoundProperties with simpleAttribute', () => {
      const spyInitBoundProperties = jest.spyOn(component, 'initBoundProperties');
      component.initPropertiesFromAttribute();
      expect(spyInitBoundProperties).toHaveBeenCalled();
    });

    it('should call initControlsProperties with simpleAttribute', () => {
      const spyInitControlsProperties = jest.spyOn(component, 'initControlsProperties');
      component.initPropertiesFromAttribute();
      expect(spyInitControlsProperties).toHaveBeenCalled();
    });
  });

  describe('testing addMaxFormControl', () => {
    let simpleAttribute = new TxAttributePoint({
      id: 2,
      name: 'test',
      dataType: LegacyTxDataType.DecUnique,
      lowerBound: -20,
      upperBound: 20,
      idObjectType: 0,
      idAttributeParent: 0,
      isInherited: false,
      tags: [],
      right: LegacyTxAttributeRight.None,
      order: 0,
      idInheritedAttribute: 0,
      idLinkType: 0,
      option: {},
    });

    beforeEach(() => {
      component.attribute = simpleAttribute;
    });
    it('should call createFormControl', () => {
      const spyCreateFormCtrl = jest.spyOn(component, 'createFormControl');
      component.addMaxFormControl();
      expect(spyCreateFormCtrl).toHaveBeenCalled();
    });
  });

  describe('testing addMeanFormControl', () => {
    let simpleAttribute = new TxAttributePoint({
      id: 2,
      name: 'test',
      dataType: LegacyTxDataType.DecUnique,
      lowerBound: -20,
      upperBound: 20,
      idObjectType: 0,
      idAttributeParent: 0,
      isInherited: false,
      tags: [],
      right: LegacyTxAttributeRight.None,
      order: 0,
      idInheritedAttribute: 0,
      idLinkType: 0,
      option: {},
    });

    beforeEach(() => {
      component.attribute = simpleAttribute;
    });
    it('should call createFormControl', () => {
      const spyCreateFormCtrl = jest.spyOn(component, 'createFormControl');
      component.addMeanFormControl();
      expect(spyCreateFormCtrl).toHaveBeenCalled();
    });
  });

  describe('testing setData...', () => {
    let data: LegacyTxDataNumeric;
    let simpleAttribute = new TxAttributePoint({
      name: 'test',
      dataType: LegacyTxDataType.DecUnique,
      lowerBound: -20,
      upperBound: 20,
      id: -1,
      idObjectType: 0,
      idAttributeParent: 0,
      isInherited: false,
      tags: [],
      right: LegacyTxAttributeRight.None,
      order: 0,
      idInheritedAttribute: 0,
      idLinkType: 0,
      option: {},
    });
    let rangeAttribute = new TxAttributePoint({
      dataType: LegacyTxDataType.DecRange,
      name: 'test',
      lowerBound: -20,
      upperBound: 20,
      id: -1,
      idObjectType: 0,
      idAttributeParent: 0,
      isInherited: false,
      tags: [],
      right: LegacyTxAttributeRight.None,
      order: 0,
      idInheritedAttribute: 0,
      idLinkType: 0,
      option: {},
    });
    let mmmAttribute = new TxAttributePoint({
      dataType: LegacyTxDataType.DecRangeMean,
      name: 'test',
      lowerBound: -20,
      upperBound: 20,
      id: -1,
      idObjectType: 0,
      idAttributeParent: 0,
      isInherited: false,
      tags: [],
      right: LegacyTxAttributeRight.None,
      order: 0,
      idInheritedAttribute: 0,
      idLinkType: 0,
      option: {},
    });

    beforeEach(() => {
      data = new LegacyTxDataNumeric(0, 0, 5, 15, 7);
      component.maxFormControl = new FormControl();
      component.meanFormControl = new FormControl();
    });

    it('should set min value to control', () => {
      component.attribute = simpleAttribute;
      component.setData(data);
      expect(component.control.value).toBe(5);
    });

    it('should set max value to maxControl value', () => {
      component.attribute = rangeAttribute;
      component.setData(data);
      expect(component.maxFormControl.value).toBe(15);
    });

    it('should set mean value to meanControl value', () => {
      component.attribute = mmmAttribute;
      component.setData(data);
      expect(component.meanFormControl.value).toBe(7);
    });
  });

  describe('testing getData...', () => {
    it('should return corresponding data', () => {
      component.idObject = 1;
      component.idAttribute = 2;
      component.control.setValue(5);
      component.maxFormControl = new FormControl();
      component.meanFormControl = new FormControl();
      component.unitFormControl = new FormControl();
      component.maxFormControl.setValue(15);
      component.meanFormControl.setValue(7);
      component.unitFormControl.setValue(1);
      component.mainUnit = new TxUnit({ id: 1, name: 'kg' });
      const data = new LegacyTxDataNumeric(1, 2, 5, 15, 7, 1);
      expect(component.getData()).toEqual(data);
    });
  });

  describe('testing chechBoundValue', () => {
    let control: AbstractControl;

    beforeEach(() => {
      control = new FormControl();
      component.control = new FormControl();
      component.lowerBoundValue = 5;
      component.upperBoundValue = 100;
    });

    it('should set min error to control if control value is lower than lower bound value', () => {
      control.setValue(3);
      component.checkBoundValue(control);
      expect(component.control.hasError('min')).toBeTruthy();
    });

    it('should not set max error to control if control value is lower than lower bound value', () => {
      control.setValue(3);
      component.checkBoundValue(control);
      expect(component.control.hasError('max')).toBeFalsy();
    });

    it('should set max error to control if control value is upper than lower bound value', () => {
      control.setValue(103);
      component.checkBoundValue(control);
      expect(component.control.hasError('max')).toBeTruthy();
    });

    it('should not set min error to control if control value is lower than lower bound value', () => {
      control.setValue(103);
      component.checkBoundValue(control);
      expect(component.control.hasError('min')).toBeFalsy();
    });

    it('should not set any error to control if value', () => {
      control.setValue(50);
      component.checkBoundValue(control);
      expect(component.control.valid).toBeTruthy();
    });
  });
});
