import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxFileFieldBoxComponent } from './file-field-box.component';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { MatIconModule } from '@angular/material/icon';
import { Subscription } from 'rxjs';
import { LegacyTxFile } from '../../../../services/structure/models/attribute';
import { LegacyFileUtils } from '../../../../utilities/legacy-file-utils';

describe('TxFileFieldBoxComponent', () => {
  let component: TxFileFieldBoxComponent;
  let fixture: ComponentFixture<TxFileFieldBoxComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxFileFieldBoxComponent],
      imports: [FontAwesomeTestingModule, MatIconModule],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxFileFieldBoxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('testing cancelUpload', () => {
    it('should call reset', () => {
      component.file = new LegacyTxFile(
        'testFile',
        0,
        true,
        15,
        0,
        new File([], 'testFile'),
        new Subscription(),
        15
      );
      const resetSpy = jest.spyOn(component, 'reset');
      component.cancelUpload();
      expect(resetSpy).toHaveBeenCalled();
    });
  });

  describe('testing getFileSize...', () => {
    it('should return null if no file', () => {
      expect(component.getFileSize()).toBeNull();
    });

    it('should return null if null file size', () => {
      component.file = new LegacyTxFile(
        'testFile',
        0,
        true,
        15,
        0,
        new File([], 'testFile'),
        new Subscription(),
        15
      );
      expect(component.getFileSize()).toBeNull();
    });

    it('should return file size', () => {
      LegacyFileUtils.fileSizeToDisplay = jest.fn().mockReturnValue('200');
      component.file = new LegacyTxFile(
        'testFile',
        200,
        true,
        15,
        0,
        new File([], 'testFile'),
        new Subscription(),
        15
      );
      expect(component.getFileSize()).toBe('200');
    });
  });

  describe('testing changeView...', () => {
    beforeEach(() => {
      component.file = new LegacyTxFile(
        'testFile',
        0,
        true,
        15,
        0,
        new File([], 'testFile'),
        new Subscription(),
        15
      );
    });

    it('should change file view value', () => {
      component.changeView();
      expect(component.file.view).toBeFalsy();
    });

    it('should change file data base action if is none', () => {
      component.changeView();
      expect(component.file.action).toBe(2);
    });
  });
});
