import { Injectable } from '@angular/core';
import {
  BehaviorSubject,
  EMPTY,
  Observable,
  Subject,
  catchError,
  combineLatest,
  combineLatestWith,
  filter,
  finalize,
  of,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { CoreModelConceptService, HIDDEN_CONCEPT_TYPES } from '../../core-model-common';
import { TranslateService } from '@ngx-translate/core';
import { ErrorMessagesService } from 'src/app/core/error-messages/error-messages.service';
import { ArchiveData, ArchiveInfo, ArchiveStatus } from '../models/archive-info.model';
import { append, equals, isEmpty, isNil, sort, without } from 'ramda';
import { formatDate } from '@angular/common';
import { SessionService } from 'src/app/core/services/session/session.service';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { ErrorService } from 'src/app/core/services/errors/error.service';
import {
  FailedImportHistoryDTO,
  CompleteImportHistoryDTO,
  CoreModelImportHistoryDTO,
  CoreModelsArchiveSummaryDTO,
  ImportedCoreModelConceptsDTO,
  TestedCoreModelConceptDTO,
  TestedImportDTO,
} from '../models/core-models-import.dto';
import { CoreModelImportHistory } from '../models/import-history.model';
import { TestedCoreModelConcept } from '../models/tested-core-model-concept.model';
import {
  CoreModelImportConcept,
  ImportedCoreModelConcept,
} from '../models/imported-core-model-concept.model';
import { EnumUtils } from 'src/app/core/utils/enum';
import { ModificationTypeEnum } from '../enums/modification.enum';
import { ImportCoreModelConcept } from '../models/import-core-model-concept.model';
import { CoreModelImportGatewayService } from './core-model-import-gateway.service';
import { HttpErrorResponse } from '@angular/common/http';
import { ErrorUtils } from 'src/app/core/utils/error.utils';
import {
  isSupVersion,
  sortVersionInDescendingOrder,
  versionInNumericFormat,
} from 'src/app/core/utils/version.utils';
import { FileHistory } from 'src/app/shared/models/files/file-history.model';
import { ErrorKeyContextualized } from 'src/app/core/error-messages/models/error-key-contextualized';
import { ValidateArchiveErrorKey } from '../models/validate-archive-error-key.enum';
import { ConceptType, TxCommonService, TxObjectsTypeService } from '@bassetti-group/tx-web-core';

const DATE_FORMAT = 'short';
const DEFAULT_LANGUAGE_USED_CODE = 'en';
const META_DATA_HEADER = {
  add: _('generic.Add'),
  missing: _('generic.Del'),
  modification: _('generic.Modif'),
};
@Injectable()
export class CoreModelsImportService extends CoreModelConceptService<ImportCoreModelConcept> {
  archiveData$: Observable<ArchiveData | null>;
  archiveValidationError$: Observable<void | null>;
  historyList$: Observable<CoreModelImportHistory[]>;
  showOnlyLatestImports$: Observable<boolean>;
  showOnlyNotImportedVersions$: Observable<boolean>;
  statusNotification$: Observable<ArchiveStatus | undefined>;
  private readonly _archiveDataSub = new BehaviorSubject<ArchiveData | null>(null);
  private readonly _archiveValidationErrorSub = new Subject<void | null>(); // because it is an error, subject is more appropriate
  private readonly _historyListSub = new BehaviorSubject<CoreModelImportHistory[]>([]);
  private readonly _showOnlyLatestImportsSub = new BehaviorSubject<boolean>(false);
  private _historyList: CoreModelImportHistory[] = [];
  private _latestImportsHistoryList: CoreModelImportHistory[] = [];
  private _archiveData: ArchiveData | null = null;
  private _archiveDataWithNotImportedVersion: ArchiveData | null = null;
  private readonly _showOnlyNotImportedVersionsSub = new BehaviorSubject<boolean>(false);
  private readonly _statusNotification = new Subject<ArchiveStatus>();
  private readonly _cancelArchiveImportWorkflow = new Subject<void>();
  constructor(
    translateService: TranslateService,
    objectTypeService: TxObjectsTypeService,
    errorService: ErrorService,
    commonService: TxCommonService,
    errorMessagesService: ErrorMessagesService,
    private readonly _coreModelsImportGateway: CoreModelImportGatewayService,
    private readonly _session: SessionService
  ) {
    super(translateService, objectTypeService, errorService, errorMessagesService, commonService);
    this.concepts$ = this._conceptsSub.asObservable();
    this.archiveData$ = this._archiveDataSub.asObservable();
    this.historyList$ = this._historyListSub.asObservable();
    this.showOnlyLatestImports$ = this._showOnlyLatestImportsSub.asObservable();
    this.archiveValidationError$ = this._archiveValidationErrorSub
      .asObservable()
      .pipe(filter((error: void | null): error is void => error !== null));
    this.showOnlyNotImportedVersions$ = this._showOnlyNotImportedVersionsSub.asObservable();

    this.statusNotification$ = this._statusNotification.asObservable();
  }

  loadHistory(): void {
    this._coreModelsImportGateway
      .loadHistory()
      .pipe(
        take(1),
        combineLatestWith(this._objectsTypeService.isReady()),
        tap(([history]) => {
          this.historyProcess(this.toHistoryList(history));
        }),
        catchError((err) => ErrorUtils.defaultHandlerObservableError(err, this._errorService))
      )
      .subscribe();
  }

  validateArchive(file: File): void {
    this._isLoadingSub.next(true);
    this.resetArchiveState();
    this._coreModelsImportGateway
      .validateArchive(file)
      .pipe(
        takeUntil(this._cancelArchiveImportWorkflow),
        take(1),
        catchError((err) => this.handlerValidateArchiveError(err)),
        tap((summary) => {
          this._archiveData = this.sortVersionsHistory(this.toArchiveData(summary, file));
          this._archiveDataWithNotImportedVersion = this.filterOnNotImportedVersion(
            this._archiveData
          );
          this._archiveDataSub.next(this._archiveData);
          this._showOnlyNotImportedVersionsSub.next(
            (this._archiveDataWithNotImportedVersion?.history?.length ?? 0) > 0
          );
        }),
        catchError((err) => ErrorUtils.defaultHandlerObservableError(err, this._errorService)),
        switchMap(() => this.testImport()),
        finalize(() => {
          this._isLoadingSub.next(false);
        })
      )
      .subscribe();
  }

  /**
   *
   * @returns throws error
   */
  testImport(): Observable<TestedImportDTO | undefined> {
    const archive = this._archiveDataSub.value;
    if (!this.canTest(archive)) {
      return of(undefined);
    }
    this._isLoadingSub.next(true);
    return this._coreModelsImportGateway.testImport(archive.file).pipe(
      takeUntil(this._cancelArchiveImportWorkflow),
      take(1),
      tap((testedImportDTO) => {
        const concepts = this.toTestedConcepts(testedImportDTO.importedConcepts);
        this.updateConceptsAndFilterOnErrors(concepts, 'conflicts');
        if (this.isInvalidImport()) {
          this._statusNotification.next(ArchiveStatus.TestImportInvalid);
          this._archiveDataSub.next(this.updateArchiveStatus(ArchiveStatus.TestImportInvalid));
        } else {
          this._statusNotification.next(ArchiveStatus.TestImportValid);
          this._archiveDataSub.next(this.updateArchiveStatus(ArchiveStatus.TestImportValid));
        }
      }),
      catchError((err) => ErrorUtils.defaultHandlerObservableError(err, this._errorService)),
      finalize(() => {
        this._isLoadingSub.next(false);
      })
    );
  }
  import(): void {
    const archive = this._archiveDataSub.value;
    if (!this.canImport(archive)) {
      return undefined;
    }
    this._isLoadingSub.next(true);
    this._coreModelsImportGateway
      .import(archive.file)
      .pipe(
        takeUntil(this._cancelArchiveImportWorkflow),
        switchMap((importedConceptsDTO) =>
          combineLatest([of(importedConceptsDTO), this._objectsTypeService.listAll(true)])
        ),
        take(1),
        tap(([importedConceptsDTO]) => {
          const importedConcepts = this.toImportedConcepts(importedConceptsDTO.importedConcepts);
          this.updateConcepts(importedConcepts);
          this._archiveDataSub.next(this.updateArchiveStatus(ArchiveStatus.Imported));
          this._statusNotification.next(ArchiveStatus.Imported);
        }),
        catchError((err) => this.handlerImportError(err)),
        switchMap(() => this._coreModelsImportGateway.loadHistory()),
        tap((history) => {
          this.historyProcess(this.toHistoryList(history));
        }),
        catchError((err) => ErrorUtils.defaultHandlerObservableError(err, this._errorService)),
        finalize(() => {
          this._isLoadingSub.next(false);
        })
      )
      .subscribe();
  }

  cancelArchive(): void {
    this.resetArchiveState();
  }

  filterOnConceptsInConflicts(): void {
    this.filterOnErrors();
  }

  removeFilterOnConceptsInConflicts(): void {
    this.removeFilterOnConceptsInErrors();
  }

  filterOnLatestImportVersions(): void {
    this._historyListSub.next(this._latestImportsHistoryList);
    this._showOnlyLatestImportsSub.next(true);
  }

  removeFilterOnLatestImportVersions(): void {
    this._historyListSub.next(this._historyList);
    this._showOnlyLatestImportsSub.next(false);
  }
  filterOnNotImportedVersions() {
    this._archiveDataSub.next(this._archiveDataWithNotImportedVersion);
    this._showOnlyNotImportedVersionsSub.next(true);
  }
  removeFilterOnNotImportedVersion() {
    this._archiveDataSub.next(this._archiveData);
    this._showOnlyNotImportedVersionsSub.next(false);
  }
  private removeSomeConceptTypesFromList<T extends CoreModelImportConcept>(
    concepts: T[],
    ...conceptTypes: ConceptType[]
  ): T[] {
    return concepts.filter((concept) => {
      return !conceptTypes.some((type) => concept.type === type);
    });
  }
  private sortVersionsHistory(archiveData: ArchiveData): ArchiveData {
    const sortedArchiveData = {
      ...archiveData,
      history: sort(
        (histA, histB) =>
          sortVersionInDescendingOrder(
            versionInNumericFormat(histA.version),
            versionInNumericFormat(histB.version)
          ),
        archiveData.history
      ),
    };
    return sortedArchiveData;
  }
  private filterOnNotImportedVersion(archiveData: ArchiveData): ArchiveData {
    const filteredArchiveData = {
      ...archiveData,
      history: archiveData.history.filter((hist) =>
        this.historyVersionIsNotImported(hist, archiveData.id)
      ),
    };
    return filteredArchiveData;
  }
  private historyVersionIsNotImported(history: FileHistory, coreModelId: string): boolean {
    const latestImportedForId = this._latestImportsHistoryList.find(
      (latest) => latest.id === coreModelId
    );
    return latestImportedForId
      ? isSupVersion(
          versionInNumericFormat(history.version),
          versionInNumericFormat(latestImportedForId.version)
        )
      : true;
  }

  /**
   *
   * @param historyList
   * @returns throw error
   */
  private toHistoryList(
    historyList: CoreModelImportHistoryDTO['importHistory']
  ): CoreModelImportHistory[] {
    return historyList.map((item) => {
      const { importedConcepts, success, date, username, ...itemSubtract } = item;
      return {
        ...itemSubtract,
        username: username,
        date: new Date(date),
        concepts: this.correspondingImportConcepts(item),
        status: success ? 'Success' : 'Failed',
      };
    });
  }

  private correspondingImportConcepts(
    historyDTO: FailedImportHistoryDTO | CompleteImportHistoryDTO
  ) {
    if (this.isTestedCoreModelConceptsDTO(historyDTO)) {
      return this.toTestedConcepts(historyDTO.importedConcepts);
    }
    return this.toImportedConcepts(historyDTO.importedConcepts);
  }
  private isTestedCoreModelConceptsDTO(
    historyDTO: FailedImportHistoryDTO | CompleteImportHistoryDTO
  ): historyDTO is FailedImportHistoryDTO {
    return !historyDTO.success;
  }

  private historyProcess(historyList: CoreModelImportHistory[]): void {
    this._historyList = historyList;
    this._latestImportsHistoryList = this.filterOnLatestHistoryVersions();
    this._showOnlyLatestImportsSub.next(this._latestImportsHistoryList.length > 0);
    this._historyListSub.next(this._historyList);
  }

  private filterOnLatestHistoryVersions(): CoreModelImportHistory[] {
    const historyListFiltered = this._historyList.reduce((listFiltered, history) => {
      if (history.status === 'Failed') {
        return listFiltered;
      }
      const updatedList = this.updateLastHistoryVersionFrom(listFiltered, history);
      return updatedList;
    }, [] as CoreModelImportHistory[]);
    return historyListFiltered;
  }
  private updateLastHistoryVersionFrom(
    historyList: CoreModelImportHistory[],
    history: CoreModelImportHistory
  ) {
    const currentLastVersion = historyList.find(
      (currentHistory) => currentHistory.id === history.id
    );
    const updatedLastVersion = this.updateLastVersion(currentLastVersion, history, historyList);
    return updatedLastVersion;
  }

  private updateLastVersion(
    currentLastVersion: CoreModelImportHistory | undefined,
    history: CoreModelImportHistory,
    historyList: CoreModelImportHistory[]
  ): CoreModelImportHistory[] {
    return currentLastVersion
      ? this.updateHistoryList(currentLastVersion, history, historyList)
      : append(history, historyList);
  }

  private updateHistoryList(
    currentLastVersion: CoreModelImportHistory,
    history: CoreModelImportHistory,
    historyList: CoreModelImportHistory[]
  ) {
    return equals(
      this.lastHistoryVersionFrom(history, currentLastVersion).version,
      currentLastVersion.version
    )
      ? historyList
      : append(history, without([currentLastVersion], historyList));
  }
  /**
   *
   * @param firstHistory
   * @param secondHistory
   * @returns throw error
   */
  private lastHistoryVersionFrom(
    firstHistory: CoreModelImportHistory,
    secondHistory: CoreModelImportHistory
  ): CoreModelImportHistory {
    const firstHistoryVersion = versionInNumericFormat(firstHistory.version);
    const secondHistoryVersion = versionInNumericFormat(secondHistory.version);
    return this.lastHistoryBasesOnVersions(
      firstHistoryVersion,
      secondHistoryVersion,
      firstHistory,
      secondHistory
    );
  }

  private lastHistoryBasesOnVersions(
    firstHistoryVersion: number[],
    secondHistoryVersion: number[],
    firstHistory: CoreModelImportHistory,
    secondHistory: CoreModelImportHistory
  ): CoreModelImportHistory {
    if (isEmpty(firstHistoryVersion)) {
      return firstHistory;
    }
    const [firstCurrentNumber, ...firstRest] = firstHistoryVersion;
    const [secondCurrentNumber, ...secondRest] = secondHistoryVersion;

    if (firstCurrentNumber > secondCurrentNumber) {
      return firstHistory;
    } else if (secondCurrentNumber > firstCurrentNumber) {
      return secondHistory;
    }
    return this.lastHistoryBasesOnVersions(
      firstRest ?? [],
      secondRest ?? [],
      firstHistory,
      secondHistory
    );
  }
  private toImportedConcepts(
    importedConceptsDTO: ImportedCoreModelConceptsDTO[]
  ): ImportedCoreModelConcept[] {
    const concepts = importedConceptsDTO.map(this.toImportConcept.bind(this));
    return this.removeSomeConceptTypesFromList(concepts, ...HIDDEN_CONCEPT_TYPES);
  }

  private toImportConcept(
    concept: ImportedCoreModelConceptsDTO,
    index?: number,
    concepts?: ImportedCoreModelConceptsDTO[]
  ): ImportedCoreModelConcept {
    const conceptsMapped = this.toConcept(concept);
    const objectType = concept.idObjectType ? this.getObjectType(concept.idObjectType) : undefined;
    const icon = this.conceptIcon(conceptsMapped.type, objectType, conceptsMapped.id);
    return {
      ...conceptsMapped,
      objectType,
      icon,
    };
  }
  private toConcept(concept: ImportedCoreModelConceptsDTO): CoreModelImportConcept {
    const type = EnumUtils.getCorrespondingEnum(
      concept.type,
      Object.values(ConceptType)
    ) as ConceptType;
    const id = concept.id;
    const name = concept.name;
    const tags = [concept.tag];
    const modificationType = EnumUtils.getCorrespondingEnum(
      concept.modificationType,
      Object.values(ModificationTypeEnum)
    ) as ModificationTypeEnum;
    const translatedModificationType = this._translateService.instant(
      'generic.' + modificationType
    );
    return {
      type,
      id,
      tags,
      name,
      modificationType,
      translatedModificationType,
    };
  }

  private testedConcept(
    concept: TestedCoreModelConceptDTO,
    index?: number,
    concepts?: TestedCoreModelConceptDTO[]
  ): TestedCoreModelConcept {
    const conceptsMapped = this.toConcept(concept);
    const objectType = concept.idObjectType
      ? this.getCorrespondingObjectType(concept.idObjectType, concepts ?? [])
      : undefined;
    const icon = this._commonService.getConceptTypeIcon(
      conceptsMapped.type,
      concept.icon ?? objectType?.icon
    );
    const conflicts = concept.conflicts;
    const translatedConflicts = this.parseToTestedConceptConflicts(concept.conflicts);
    return {
      ...conceptsMapped,
      objectType,
      icon,
      conflicts,
      translatedConflicts,
    };
  }
  private parseToTestedConceptConflicts(conflictsKeys: string[]) {
    return conflictsKeys.map((key) =>
      this._translateService.instant(this._errorMessagesService.getMessageByKey(key)?.content ?? '')
    );
  }
  private getCorrespondingObjectType(
    idObjectType: number,
    concepts: TestedCoreModelConceptDTO[]
  ): TestedCoreModelConcept | undefined {
    const objectType = concepts
      .filter((concept) => concept.type === ConceptType.ObjectType)
      .find((objectTypeConcept) => objectTypeConcept.id === idObjectType);
    return objectType ? this.testedConcept(objectType) : undefined;
  }
  /**
   *
   * @param testedConcepts
   * @returns throw error
   */
  private toTestedConcepts(testedConcepts: TestedCoreModelConceptDTO[]): TestedCoreModelConcept[] {
    const concepts = testedConcepts.map(this.testedConcept.bind(this));
    return this.removeSomeConceptTypesFromList(concepts, ...HIDDEN_CONCEPT_TYPES);
  }
  private updateArchiveStatus(status: ArchiveStatus): ArchiveData {
    if (this._archiveDataWithNotImportedVersion) {
      this._archiveDataWithNotImportedVersion = {
        ...this._archiveDataWithNotImportedVersion,
        status,
      };
    }
    if (this._archiveData) {
      this._archiveData = { ...this._archiveData, status };
    }
    return {
      ...(this._archiveDataSub.value as ArchiveData),
      status,
    };
  }
  private handlerImportError(err: unknown): Observable<never> {
    this._archiveDataSub.next(this.updateArchiveStatus(ArchiveStatus.ImportFailed));
    if (err instanceof HttpErrorResponse) {
      const importError = {
        ...err,
        error: err?.error?.importConflictKeys
          ? { errorKeys: this.mapToErrorsKeys(err.error) }
          : err?.error,
      };
      this._errorService.addError(importError);
    }
    this.loadHistory();
    return EMPTY;
  }
  private mapToErrorsKeys(error: {
    importConflictKeys: string[];
    conceptName: string;
  }): ErrorKeyContextualized[] {
    return (
      error.importConflictKeys?.map((key) => ({
        key,
        contexts: [error.conceptName],
      })) ?? error
    );
  }

  private isInvalidImport(): boolean {
    return this._conceptsNbErrorsSub.value.errors > 0;
  }

  private canImport(archive: null | ArchiveData): archive is ArchiveData {
    return (
      !isNil(archive) &&
      (archive.status === ArchiveStatus.TestImportValid ||
        archive.status === ArchiveStatus.ImportFailed)
    );
  }
  private canTest(archive: null | ArchiveData): archive is ArchiveData {
    return (
      !isNil(archive) &&
      (archive.status === ArchiveStatus.Valid || archive.status === ArchiveStatus.TestImportInvalid)
    );
  }

  private toArchiveData(summary: CoreModelsArchiveSummaryDTO, file: File): ArchiveData {
    const version = _('admins.coreModelsImport.archiveVersion');
    const name = _('generic.name');
    const date = _('generic.creationDate');
    const user = _('generic.createdBy');
    const explanation = _('generic.explanation');
    const status = ArchiveStatus.Valid;
    const archiveInfo: ArchiveInfo = [
      {
        label: version,
        info: summary.version,
        icon: 'code-commit' as IconName,
      },
      {
        label: name,
        info: summary.name,
        icon: 'info' as IconName,
      },
      {
        label: date,
        info: formatDate(
          summary.date,
          DATE_FORMAT,
          this._session.currentLang?.languageUsedCode ?? DEFAULT_LANGUAGE_USED_CODE
        ),
        icon: 'calendar-day' as IconName,
      },
      { label: user, info: summary.user, icon: 'user' as IconName },
      {
        label: explanation,
        info: summary.explanation,
        icon: 'text' as IconName,
      },
    ];
    const id = summary.id;
    const cacheId = summary.cacheId;
    const history = summary.history;
    return { id, cacheId, history, file, archiveInfo, status };
  }
  private resetArchiveState(): void {
    this._archiveDataSub.next(null);
    this._archiveData = null;
    this._archiveDataWithNotImportedVersion = null;
    this.updateConcepts([]);
    this._cancelArchiveImportWorkflow.next();
  }
  private handlerValidateArchiveError(err: unknown): Observable<never> {
    this._archiveValidationErrorSub.next();
    this._archiveDataSub.next(this.updateArchiveStatus(ArchiveStatus.Invalid));
    if (err instanceof HttpErrorResponse) {
      const validateArchiveError: { error: { errorKey: ErrorKeyContextualized } } | { error: any } =
        {
          error: err?.error.context ? this.errorKeyContextualized(err?.error) : err.error,
        };
      this._errorService.addError(validateArchiveError);
    }
    return EMPTY;
  }

  private errorKeyContextualized(error: {
    errorKey: ValidateArchiveErrorKey;
    context: { [key: string]: string };
  }): { errorKey: ErrorKeyContextualized } {
    switch (error.errorKey) {
      case ValidateArchiveErrorKey.CoreModelInvalidArchiveVersion: {
        const contexts = [
          error.context['ArchiveVersion'] ?? [],
          error.context['LastImportedVersion'] ?? [],
        ].flat();
        return {
          errorKey: { key: error.errorKey, contexts },
        };
      }
      case ValidateArchiveErrorKey.CoreModelArchiveMissingFiles: {
        const contexts = [error.context['missingFiles'] ?? []].flat();
        return {
          errorKey: { key: error.errorKey, contexts },
        };
      }
      case ValidateArchiveErrorKey.CoreModelArchiveUnwantedFiles: {
        const contexts = [error.context['addedFiles'] ?? []].flat();
        return {
          errorKey: { key: error.errorKey, contexts },
        };
      }
      case ValidateArchiveErrorKey.CoreModelArchiveHasErrors:
      default:
        return {
          errorKey: { key: error.errorKey, contexts: [] },
        };
    }
  }
}
