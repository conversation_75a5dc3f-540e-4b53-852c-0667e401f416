import { TxClickableElementDirective } from './clickable-element.directive';
import { Component, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';

@Component({
  template: `<fa-icon txClickableElement></fa-icon>`,
})
class TestClickableComponent {}

describe('TxClickableElementDirective', () => {
  let fixture: ComponentFixture<TestClickableComponent>;
  let _el: DebugElement;
  let directive: TxClickableElementDirective;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [TestClickableComponent],
      imports: [TxClickableElementDirective],
    });
    directive = new TxClickableElementDirective(_el);
    fixture = TestBed.createComponent(TestClickableComponent);
    _el = fixture.debugElement.query(By.css('fa-icon'));
    fixture.detectChanges();
  });

  it('should create an instance', () => {
    expect(directive).toBeTruthy();
  });

  it('should add tabindex & data-id attributes', () => {
    expect(_el.nativeElement.getAttribute('tabindex')).toBe('0');
    expect(_el.nativeElement.getAttribute('data-id')).toBe('clickableElement');
  });
});
