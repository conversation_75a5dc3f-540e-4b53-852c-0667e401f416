<ng-container *ngIf="control">
  <mat-form-field
    (mouseenter)="onMouseEnter()"
    (mouseleave)="onMouseLeave()"
    #formField
    [appearance]="appearance"
    color="accent"
    class="input-text-field"
    [ngClass]="{ 'form-field-error': !control.valid && !control.disabled }"
    [hideRequiredMarker]="!required">
    <mat-hint> {{ hintLabel | translate }}</mat-hint>
    <mat-label
      class="input_text_field__label"
      [matTooltip]="labelTooltip | translate"
      matTooltipClass="mat-label-tooltip mat-tooltip-multiline"
      matTooltipShowDelay="500"
      matTooltipPosition="above">
      {{ label | translate }}
    </mat-label>

    <div *ngIf="!isTextArea">
      <input
        matInput
        color="accent"
        class="input-text-field__input-text"
        [formControl]="control"
        (focus)="onFocus()"
        (blur)="onBlur()"
        (keyup)="registerOnChange($event)"
        (keydown)="onKeyDown($event)"
        autocomplete="off"
        required />
    </div>
    <div *ngIf="isTextArea">
      <textarea
        class="input-text-field__text-area"
        matInput
        color="accent"
        [formControl]="control"
        (keyup)="registerOnChange($event)"
        (keydown)="onKeyDown($event)"
        required
        >{{ control.value }}</textarea
      >
    </div>

    <fa-icon *ngIf="icon" [icon]="['fas', icon]"></fa-icon>
    <button
      class="input-text-field__clear-btn"
      mat-button
      *ngIf="mouseInFormField && control.value && !control.disabled && !isTextArea"
      matSuffix
      aria-label="Clear"
      (click)="onClearButtonClicked()">
      <fa-icon [icon]="['fas', 'times']"></fa-icon>
    </button>
    <mat-hint
      class="input-text-field__hint-length"
      *ngIf="
        control.errors?.maxlength !== null &&
        control.errors?.maxlength !== undefined &&
        control.value !== null
      "
      align="end">
      {{ control.value.length }}/{{ maxLength }}
    </mat-hint>
    <mat-error *ngIf="control.errors?.required">{{
      'txWebCore.errorMessage.required' | translate
    }}</mat-error>
  </mat-form-field>
</ng-container>
