import type { Args, <PERSON>a, StoryObj } from '@storybook/angular';
import { TxSlideToggleComponent } from './tx-slide-toggle.component';
import { FormControl } from '@angular/forms';

const control = new FormControl('');
const meta: Meta<TxSlideToggleComponent> = {
  component: TxSlideToggleComponent,
  title: 'Generic fields/TxSlideToggleComponent',
  tags: ['autodocs'],
};
export default meta;

export const Primary: StoryObj = {
  render: (args: Partial<Args>) => ({
    props: { formControl: control },
    template: `
      <tx-slide-toggle
          [formControl]="formControl"
        ></tx-slide-toggle>
     `,
  }),
};
