import { TestBed, waitForAsync } from '@angular/core/testing';
import localeFrench from '@angular/common/locales/fr';
import { registerLocaleData } from '@angular/common';
import { LocalizedNumberPipe } from './localized-number.pipe';
import { AbstractSessionService, MockSessionService } from '../../data-access/session';

describe('LocalNumberPipe', () => {
  let pipe: LocalizedNumberPipe;
  let sessionService: AbstractSessionService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      providers: [{ provide: AbstractSessionService, useClass: MockSessionService }],
    }).compileComponents();

    sessionService = TestBed.inject(AbstractSessionService);
    pipe = new LocalizedNumberPipe(sessionService);
  }));

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it(`transforms '15203.3296' to '15,203.33'`, () => {
    const actual = pipe.transform(15203.3296);
    const expected = '15,203.33';

    expect(actual).toBe(expected);
  });

  it(`transforms '15203.3296' to '15 203,33'`, () => {
    if (sessionService.currentLang) {
      sessionService.currentLang.languageUsedCode = 'fr-FR';
    }
    registerLocaleData(localeFrench);
    const actual = pipe.transform(15203.3296);
    const expected = '15 203,33';

    expect(actual).toBe(expected);
  });

  it(`transforms '15203.3296' to '15,203.3296'`, () => {
    const actual = pipe.transform(15203.3296, '1.0-5');
    const expected = '15,203.3296';

    expect(actual).toBe(expected);
  });

  it(`transforms '2' to '02.00'`, () => {
    const actual = pipe.transform(2, '2.2-2');
    const expected = '02.00';

    expect(actual).toBe(expected);
  });
});
