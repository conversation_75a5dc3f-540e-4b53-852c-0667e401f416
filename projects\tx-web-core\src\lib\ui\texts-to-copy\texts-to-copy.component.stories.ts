import type { Meta, StoryObj } from '@storybook/angular';
import { TxTextsToCopyComponent } from './texts-to-copy.component';

const meta: Meta<TxTextsToCopyComponent> = {
  component: TxTextsToCopyComponent,
  title: 'TxTagAndCopyComponent',
  tags: ['autodocs'],
};
export default meta;
type Story = StoryObj<TxTextsToCopyComponent>;

export const Primary: Story = {
  args: {
    texts: ['tag1', 'tag2', 'tag3'],
  },
};
