import { runInInjectionContext } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { TxObjectsTypeService } from '@bassetti-group/tx-web-core/src/lib/data-access/structure';
import { Page } from '@bassetti-group/tx-web-core/src/lib/features/grid';
import { of } from 'rxjs';
import {
  AsyncTreeGridTXDataSource,
  AsyncTxTreeGrid,
  CreateTreeGridElementsFn,
  TxLoadChildrenDataFn,
} from '../async-tree-tx-data-source';

jest.mock('@bassetti-group/tx-web-core/src/lib/data-access/structure');

describe('AsyncTreeGridTXDataSource', () => {
  let dataSource: AsyncTreeGridTXDataSource<any>;
  let mockTxObjectsTypeService: jest.Mocked<TxObjectsTypeService>;

  // Mock data
  const mockTreeData: AsyncTxTreeGrid<any>[] = [
    {
      id: 1,
      name: 'Parent 1',
      txObject: {},
      children: [{ id: 1.1, name: 'Child 1.1', txObject: {} }],
    },
    {
      id: 2,
      name: 'Parent 2',
      txObject: {},
      children: [],
    },
  ];

  const mockChildrenData = [
    { id: 2.1, name: 'Child 2.1' },
    { id: 2.2, name: 'Child 2.2' },
  ];

  // Mock functions
  const mockLoadChildrenFn: TxLoadChildrenDataFn<any> = jest.fn(
    (idObjectType: number, id: number | string) => {
      return of(mockChildrenData);
    }
  );

  const mockCreateTreeGridElements: CreateTreeGridElementsFn<any> = jest.fn((objects: any[]) => {
    return objects.map((obj) => ({
      ...obj,
      children: [],
    }));
  });

  beforeEach(() => {
    mockTxObjectsTypeService = {
      someMethod: jest.fn(),
    } as any;

    TestBed.configureTestingModule({
      providers: [{ provide: TxObjectsTypeService, useValue: mockTxObjectsTypeService }],
    });
  });

  beforeEach(() => {
    // Create the data source within the injection context
    runInInjectionContext(TestBed.inject(TestBed), () => {
      dataSource = new AsyncTreeGridTXDataSource(
        of(mockTreeData),
        'id',
        mockLoadChildrenFn,
        mockCreateTreeGridElements,
        1
      );
    });
  });

  it('should create an instance', () => {
    expect(dataSource).toBeDefined();
  });

  it('should initialize with loading state as false', (done) => {
    dataSource.isLoading$.subscribe((isLoading) => {
      expect(isLoading).toBe(false);
      done();
    });
  });

  describe('toggleNode', () => {
    it('should throw error if primaryKey is undefined', () => {
      runInInjectionContext(TestBed.inject(TestBed), () => {
        dataSource = new AsyncTreeGridTXDataSource(of(mockTreeData), undefined);
        expect(() => dataSource.toggleNode(1)).toThrow('primaryKey not defined');
      });
    });

    it('should load children when node is expanded for the first time', (done) => {
      const nodeId = 2;
      jest
        .spyOn({ loadChildren: mockLoadChildrenFn }, 'loadChildren')
        .mockReturnValue(of(mockChildrenData));

      const initialPage: Page<AsyncTxTreeGrid<any>> = {
        data: mockTreeData,
        length: mockTreeData.length,
        pageSize: 10,
        pageIndex: 0,
      };

      dataSource.triggerUpdatePageRequest(initialPage);
      dataSource.toggleNode(nodeId);

      dataSource.isLoading$.subscribe((isLoading) => {
        if (!isLoading) {
          expect(dataSource['expandedNodes'].has(nodeId)).toBe(true);
          done();
        }
      });
    });

    it('should collapse node when already expanded', () => {
      const nodeId = 1;
      dataSource['expandedNodes'].add(nodeId);

      dataSource.toggleNode(nodeId);

      expect(dataSource['expandedNodes'].has(nodeId)).toBe(false);
    });
  });

  describe('processNodeIdsSequentially', () => {
    it('should return immediately if no nodes to process', (done) => {
      const selectedNodeId = 1;

      dataSource.processNodeIdsSequentially([selectedNodeId]).subscribe({
        next: (result) => {
          expect(result).toBe(selectedNodeId);
          expect(mockLoadChildrenFn).not.toHaveBeenCalled();
          done();
        },
        error: done,
      });
    });

    it('should throw error if required parameters are missing', () => {
      runInInjectionContext(TestBed.inject(TestBed), () => {
        dataSource = new AsyncTreeGridTXDataSource(of(mockTreeData), 'id');
        expect(() => dataSource.processNodeIdsSequentially([1])).toThrow(
          "Can't load Teexma Tree node children.\n Please provide required params: loadChildrenFn and idObjectType "
        );
      });
    });
  });

  describe('clearExpandedNodes', () => {
    it('should clear all expanded nodes', () => {
      dataSource['expandedNodes'].add(1);
      dataSource['expandedNodes'].add(2);

      dataSource.clearExpandedNodes();

      expect(dataSource['expandedNodes'].size).toBe(0);
    });
  });

  describe('changeLoadingState', () => {
    it('should update loading state', (done) => {
      dataSource.changeLoadingState(true);

      dataSource.isLoading$.subscribe((isLoading) => {
        expect(isLoading).toBe(true);
        done();
      });
    });
  });

  // Clean up subscriptions
  afterEach(() => {
    jest.clearAllMocks();
  });
});
