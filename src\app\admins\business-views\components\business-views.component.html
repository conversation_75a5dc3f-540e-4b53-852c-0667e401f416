<div class="admin-container" style="position: relative">
  <mat-progress-bar
    *ngIf="isLoadingBar"
    mode="indeterminate"
    color="accent"
    style="position: absolute; top: 0; left: 0"></mat-progress-bar>
  <app-breadcrumd *ngIf="!isInsideRightPane"></app-breadcrumd>
  <div class="admin-content">
    <div class="admin-bar-title">
      <div class="h1-title">{{ 'admins.businessViews.title' | translate }}</div>
      <fa-icon
        [matTooltip]="'tooltip.showExplanation' | translate"
        [icon]="['fal', 'question-circle']"
        size="lg"
        class="icon-explanation"
        (click)="
          isExplanationDisplayed
            ? closeHelpbox()
            : getExplanation('businessViews', 'expBusinessViews', false)
        "></fa-icon>
      <app-last-save-chip [date]="lastSaveDate" [isSaving]="saveInProgress"></app-last-save-chip>
    </div>
    <mat-divider class="divider-title"></mat-divider>
    <div class="ttypes-container">
      <div class="ttypes-container-left">
        <div class="header">
          <div class="h2-section-title">{{ 'admins.wording.businessViews' | translate }}</div>
          <div class="button-section">
            <mat-slide-toggle
              #toggle
              style="margin-top: 10px"
              [checked]="showDeletionMode"
              [matTooltip]="'admins.businessViews.deletionModeTooltip' | translate"
              (change)="showDeletionMode = !showDeletionMode">
              {{ 'admins.businessViews.deletionMode' | translate }}
            </mat-slide-toggle>
          </div>
        </div>

        <app-concepts-list
          #appConcepts
          [concepts]="businessViewsFiltered"
          [showMasterCheckbox]="showDeletionMode"
          [showCheckboxes]="showDeletionMode"
          [buttons]="businessViewsButtons"
          [toolbarButtons]="businessViewsToolbarButtons"
          [enableDragAndDrop]="enableDragAndDrop"
          [displayToolbar]="true"
          [displaySearch]="true"
          (add)="displayBusinessViewForm(true)"
          (selectConcept)="selectItem($event)"
          (dropConcept)="onConceptDropped($event)">
          <div toolbar-buttons class="dm-buttons">
            <div class="object-type-dropdown-filter" *ngIf="objectTypes">
              <tx-objects-type-dropdown
                #dropdownObjectTypeFilter
                floatLabelType="Auto"
                [label]="'admins.businessViews.filterByObjectTypes' | translate"
                [objectTypes]="objectTypes"
                [filtered]="true"
                [showCheckBox]="true"
                [onlyVisible]="false"
                [checkedIds]="objectTypesFilteredIds"
                [disabledIds]="objectTypesDisabledIds"
                [displayHint]="false"
                (valueChange)="onObjectTypesDropdownChange($event)"
                (blurCombo)="onObjectTypesDropdownBlur($event)"></tx-objects-type-dropdown>
            </div>
          </div>
        </app-concepts-list>
      </div>
      <div class="ttypes-container-right">
        <div class="h2-section-title">{{ 'admins.businessViews.attributes' | translate }}</div>
        <tx-no-record
          *ngIf="showDeletionMode || !businessViewSelected"
          [noRecordText]="
            (businessViewSelected
              ? 'admins.businessViews.deletionModeActive'
              : 'admins.businessViews.selectBusinessView'
            ) | translate
          "
          class="no-record-grid"></tx-no-record>

        <div
          *ngIf="!isLangLoading && !showDeletionMode && businessViewSelected"
          class="ttypes-grid border-grey">
          <!-- attribute grid -->
          <tx-attributes-tree-grid
            *ngIf="businessViewSelected"
            #attributeTreeGrid
            [idObjectType]="businessViewSelected.idObjectType"
            [reloadAttributesOnSameOT]="reloadAttributeOnSameOT"
            [attributeSetLevels]="businessViewSelected.attributeSetLevels"
            [attributeNameColumnWidth]="'50%'"
            [enableCheckbox]="true"
            [multipleSelection]="true"
            [visibleDataType]="rootAttributeTypesDisplayed"
            [disableLinkLoading]="false"
            (checkChange)="onCheckAttribute($event)">
          </tx-attributes-tree-grid>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- user form -->
<ng-template #templateBusinessViewForm>
  <app-concept-form-pane
    [iconForm]="mainIcon"
    [rightPaneRef]="rightPaneRef"
    [settings]="formSettings"
    [concepts]="businessViewsFilteredSameOT"
    [unicityOnName]="false"
    [newConceptName]="'admins.businessViews.newBusinessView' | translate"
    [title]="'admins.businessViews.addBusinessView' | translate"
    [idObjectType]="objectTypeControl?.value[0]"
    [tagConcept]="tagConcept"
    (ready)="onFormPaneReady($event)"
    (onAdd)="onAddingBusinessView($event)"
    (onSave)="onEditingBusinessView($event)">
    <div class="object-type-dropdown">
      <tx-objects-type-dropdown
        *ngIf="formGroup"
        #dropDownOT
        [label]="'concepts.objectType' | translate"
        [checkedIds]="checkedIds"
        [readonly]="formSettings.isEditMode"
        [required]="true"
        [displayHint]="true"
        [onlyVisible]="false"
        [multipleSelection]="false"
        (valueChange)="onObjectTypesDropdownFormChange($event)"></tx-objects-type-dropdown>
    </div>
  </app-concept-form-pane>
</ng-template>
