import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SeriesType, SeriesTypesType, TableType } from '../../../business-models';
import { TxTableTypesService } from '../table-types.service';
import { MOCK_TX_CONFIG_SERVICE, TxConfigService } from '../../config';
import { MockProvider } from 'ng-mocks';
import { MOCK_OBJECT_TYPE_ICON_SERVICE, TxObjectTypeIconService } from '../../session';

describe('TableTypesService', () => {
  let apiUrl: string;
  let service: TxTableTypesService;
  let http: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        MockProvider(TxConfigService, MOCK_TX_CONFIG_SERVICE, 'useValue'),
        MockProvider(TxObjectTypeIconService, MOCK_OBJECT_TYPE_ICON_SERVICE, 'useValue'),
      ],
    });
    service = TestBed.inject(TxTableTypesService);
    http = TestBed.inject(HttpTestingController);
  });

  beforeEach(() => {
    apiUrl = 'https://localhost:44336';
  });

  afterEach(() => {
    http.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Loading state', () => {
    it('should get initial loading state', () => {
      let load: boolean;
      service.isLoading$.subscribe((res) => {
        expect(res).toBeTruthy();
      });
    });
  });

  describe('Get data', () => {
    let data: TableType[];
    beforeEach(() => {
      data = [
        {
          id: 1,
          name: 'tableType1',
          explanation: '',
          tags: [],
          isUsed: true,
          series: [
            {
              id: 1,
              name: 'seriesType1',
              order: 1,
              type: SeriesTypesType.sttUndefined,
              isMultiple: true,
              tags: [],
              idTableType: 1,
            },
            {
              id: 2,
              name: 'seriesType2',
              order: 2,
              type: SeriesTypesType.sttUndefined,
              isMultiple: true,
              tags: [],
              idTableType: 1,
            },
            {
              id: 3,
              name: 'seriesType3',
              order: 0,
              type: SeriesTypesType.sttText,
              isMultiple: true,
              tags: [],
              idTableType: 1,
            },
          ],
        },
        {
          id: 2,
          name: 'abc',
          explanation: '',
          tags: [],
          isUsed: true,
          series: [],
        },
        {
          id: 3,
          name: 'TT2',
          explanation: '',
          tags: [],
          isUsed: true,
          series: [],
        },
      ];
    });

    it('should call "api/Structure/tabletype"', () => {
      service.getTableTypes().subscribe();

      http.expectOne(`${apiUrl}/api/Structure/tabletype`);
    });

    it('should get Table types & Series', () => {
      let table: TableType[] | undefined;
      service.getTableTypes().subscribe((res) => (table = res));
      http.expectOne(`${apiUrl}/api/Structure/tabletype`).flush(data);
      expect(table?.length).toBe(3);
      expect(table?.reduce((prev: any, cur) => prev.concat(cur.series), []).length).toBe(3);
    });

    it('should sort Table types by name', () => {
      let table: TableType[] | undefined;
      service.getTableTypes().subscribe((res) => (table = res));
      http.expectOne(`${apiUrl}/api/Structure/tabletype`).flush(data);

      if (table !== undefined) {
        expect(table.map((t) => t.name).join(',')).toBe('abc,tableType1,TT2');
      }
    });

    it('should sort Series types by order', () => {
      let table: TableType[] | undefined;
      service.getTableTypes().subscribe((res) => (table = res));
      http.expectOne(`${apiUrl}/api/Structure/tabletype`).flush(data);

      const series = table?.reduce((prev: any, cur) => prev.concat(cur.series), []);
      expect(series.map((s: SeriesType) => s.name).join(',')).toBe(
        'seriesType3,seriesType1,seriesType2'
      );
    });

    it('should update loading state to "false"', () => {
      let load: boolean | undefined;
      service.isLoading$.subscribe((res) => (load = res));

      service.getTableTypes().subscribe();
      http.expectOne(`${apiUrl}/api/Structure/tabletype`).flush(data);

      expect(load).toBeFalsy();
    });
  });

  describe('Get specific Series Type', () => {
    it('should call "api/Structure/tabletype"', () => {
      service.getSeriesType(10).subscribe();
      http.expectOne(`${apiUrl}/api/Structure/seriesType/10`);
    });
  });

  describe('Add Table Type', () => {
    let data: TableType;
    beforeEach(() => {
      data = { id: 0, name: 'New Table Type', explanation: '', tags: [] };
    });

    it('should call POST "api/Structure/tabletype" with param', () => {
      service.addTableType(data).subscribe();

      const req = http.expectOne({
        url: `${apiUrl}/api/Structure/tabletype`,
        method: 'POST',
      });
      req.flush('');
      expect(req.request.body).toEqual(data);
    });

    it('should add new table to TableTypes', () => {
      service.concepts = [];
      service.addTableType(data).subscribe();

      http.expectOne({ url: `${apiUrl}/api/Structure/tabletype`, method: 'POST' }).flush(data);

      expect(service.concepts.length).toBe(1);
    });

    it('should sort table types with new table', () => {
      service.concepts = [{ id: 1, name: 'Test', explanation: '', tags: [] }];
      service.addTableType(data).subscribe();

      http.expectOne({ url: `${apiUrl}/api/Structure/tabletype`, method: 'POST' }).flush(data);

      expect(service.concepts.map((t) => t.name).join(',')).toBe('New Table Type,Test');
    });
  });

  describe('Edit Table Type', () => {
    let originalData: TableType;
    let editedData: TableType;
    beforeEach(() => {
      originalData = {
        id: 1,
        name: 'New Table Type',
        explanation: '',
        tags: [],
        isUsed: true,
        series: [
          {
            id: 1,
            name: 'seriesType1',
            order: 1,
            type: SeriesTypesType.sttUndefined,
            isMultiple: true,
            tags: [],
            idTableType: 1,
          },
        ],
      };
      editedData = {
        id: 1,
        name: 'New Table Type_bis',
        explanation: 'Test exp',
        tags: ['tblTypNewTableTypeBis'],
      };
    });

    it('should call PUT "api/Structure/tabletype" with param', () => {
      service.editTableType(editedData).subscribe();

      const req = http.expectOne({
        url: `${apiUrl}/api/Structure/tabletype/${editedData.id}`,
        method: 'PUT',
      });
      req.flush('');
      expect(req.request.body).toEqual(editedData);
    });

    it('should update original Table Type', () => {
      service.concepts = [originalData];
      service.editTableType(editedData).subscribe();

      http
        .expectOne({
          url: `${apiUrl}/api/Structure/tabletype/${editedData.id}`,
          method: 'PUT',
        })
        .flush('');

      expect(service.concepts[0]).toStrictEqual(Object.assign(originalData, editedData));
    });
  });

  describe('Delete Table Type', () => {
    let originalData: TableType;
    beforeEach(() => {
      originalData = {
        id: 1,
        name: 'New Table Type',
        explanation: '',
        tags: [],
        isUsed: true,
        series: [
          {
            id: 1,
            name: 'seriesType1',
            order: 1,
            type: SeriesTypesType.sttUndefined,
            isMultiple: true,
            tags: [],
            idTableType: 1,
          },
        ],
      };
    });

    it('should call DELETE "api/Structure/tabletype" with param', () => {
      service.deleteTableType(originalData).subscribe();

      http
        .expectOne({
          url: `${apiUrl}/api/Structure/tabletype/${originalData.id}`,
          method: 'DELETE',
        })
        .flush('');
    });

    it('should remove table type from array', () => {
      service.concepts = [originalData];
      service.deleteTableType(originalData).subscribe();

      http
        .expectOne({
          url: `${apiUrl}/api/Structure/tabletype/${originalData.id}`,
          method: 'DELETE',
        })
        .flush('');

      expect(service.concepts.length).toBe(0);
    });
  });

  describe('Add Series Type', () => {
    let data: SeriesType;
    let tableType: TableType;
    beforeEach(() => {
      tableType = {
        id: 1,
        name: 'New Table Type',
        explanation: '',
        tags: [],
        isUsed: true,
        series: [],
      };
      data = {
        id: 1,
        name: 'seriesType1',
        order: 1,
        type: SeriesTypesType.sttUndefined,
        isMultiple: true,
        tags: [],
        idTableType: 1,
      };
    });

    it('should call POST "api/Structure/seriestype" with param', () => {
      service.addSeriesType(data).subscribe();

      const req = http.expectOne({
        url: `${apiUrl}/api/Structure/seriestype`,
        method: 'POST',
      });
      req.flush('');
      expect(req.request.body).toEqual(data);
    });

    it('should add new series to TableTypes', () => {
      service.concepts = [tableType];
      service.addSeriesType(data).subscribe();

      http
        .expectOne({
          url: `${apiUrl}/api/Structure/seriestype`,
          method: 'POST',
        })
        .flush(data);

      expect(service.concepts[0].series?.length).toBe(1);
    });
  });

  describe('Edit Series Type', () => {
    let originalData: SeriesType;
    let editedData: SeriesType;
    beforeEach(() => {
      originalData = {
        id: 1,
        name: 'seriesType1',
        order: 1,
        type: SeriesTypesType.sttUndefined,
        isMultiple: true,
        tags: [],
        idTableType: 1,
      };
      editedData = {
        id: 1,
        name: 'seriesType1_bis',
        order: 1,
        type: SeriesTypesType.sttNumerical,
        idUnit: 5,
        isMultiple: false,
        tags: ['serTypBis'],
        idTableType: 1,
      };
    });

    it('should call PUT "api/Structure/seriesType" with param', () => {
      service.editSeriesType(editedData).subscribe();

      const req = http.expectOne({
        url: `${apiUrl}/api/Structure/seriesType/${editedData.id}`,
        method: 'PUT',
      });
      req.flush('');

      expect(req.request.body).toEqual(editedData);
    });

    it('should update original Series Type', () => {
      service.concepts = [
        {
          id: 1,
          name: 'New Table Type',
          explanation: '',
          tags: [],
          isUsed: true,
          series: [originalData],
        },
      ];
      service.editSeriesType(editedData).subscribe();

      http
        .expectOne({
          url: `${apiUrl}/api/Structure/seriesType/${editedData.id}`,
          method: 'PUT',
        })
        .flush('');

      expect(service.concepts[0].series?.[0]).toStrictEqual(
        Object.assign(originalData, editedData)
      );
    });
  });

  describe('Update series type order', () => {
    let data: SeriesType;
    beforeEach(() => {
      data = {
        id: 1,
        name: 'seriesType1',
        order: 2,
        type: SeriesTypesType.sttUndefined,
        isMultiple: true,
        tags: [],
        idTableType: 1,
      };
    });

    it('should call PUT "api/Structure/tabletype/order"', () => {
      service.updateSeriesTypeOrder(data).subscribe();

      const req = http.expectOne({
        url: `${apiUrl}/api/Structure/tabletype/order`,
        method: 'PUT',
      });
      req.flush('');

      expect(req.request.body).toEqual({ id: 1, order: 2 });
    });

    it('should update order of all Series Type', () => {
      service.concepts = [
        {
          id: 1,
          name: 'New Table Type',
          explanation: '',
          tags: [],
          isUsed: true,
          series: [
            {
              id: 1,
              name: 'seriesType1',
              order: 0,
              type: SeriesTypesType.sttUndefined,
              isMultiple: true,
              tags: [],
              idTableType: 1,
            },
            {
              id: 2,
              name: 'seriesType2',
              order: 1,
              type: SeriesTypesType.sttUndefined,
              isMultiple: true,
              tags: [],
              idTableType: 1,
            },
            {
              id: 3,
              name: 'seriesType3',
              order: 2,
              type: SeriesTypesType.sttUndefined,
              isMultiple: true,
              tags: [],
              idTableType: 1,
            },
          ],
        },
      ];
      service.updateSeriesTypeOrder(data).subscribe();

      http
        .expectOne({
          url: `${apiUrl}/api/Structure/tabletype/order`,
          method: 'PUT',
        })
        .flush([
          { id: 1, order: 2 },
          { id: 2, order: 0 },
          { id: 3, order: 1 },
        ]);

      expect(service.concepts[0].series?.map((s) => s.order)).toStrictEqual([2, 0, 1]);
    });
  });

  describe('Delete Series Type', () => {
    let originalData: TableType;
    beforeEach(() => {
      originalData = {
        id: 1,
        name: 'New Table Type',
        explanation: '',
        tags: [],
        isUsed: true,
        series: [
          {
            id: 10,
            name: 'seriesType1',
            order: 1,
            type: SeriesTypesType.sttUndefined,
            isMultiple: true,
            tags: [],
            idTableType: 1,
          },
        ],
      };
    });

    it('should call DELETE "api/Structure/seriestype" with param', () => {
      if (originalData.series) {
        service.deleteSeriesType(originalData.series[0]).subscribe();
      }

      http
        .expectOne({
          url: `${apiUrl}/api/Structure/seriestype/${originalData.series?.[0].id}`,
          method: 'DELETE',
        })
        .flush('');
    });

    it('should remove table type from array', () => {
      service.concepts = [originalData];
      if (originalData.series) {
        service.deleteSeriesType(originalData.series[0]).subscribe();
      }
      http
        .expectOne({
          url: `${apiUrl}/api/Structure/seriestype/${originalData.series?.[0].id}`,
          method: 'DELETE',
        })
        .flush('');

      expect(service.concepts[0].series?.length).toBe(0);
    });
  });
});
