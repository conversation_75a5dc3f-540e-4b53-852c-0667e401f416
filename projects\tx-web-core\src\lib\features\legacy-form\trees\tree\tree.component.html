<ejs-treeview
  #treeview
  [id]="uniqueId"
  [allowDragAndDrop]="allowDragAndDrop"
  [allowEditing]="allowEditing"
  [allowMultiSelection]="allowMultiSelection"
  [animation]="animation"
  [autoCheck]="autoCheck"
  [checkedNodes]="checkedNodes"
  [cssClass]="cssClass"
  [disabled]="disabled"
  [dragArea]="dragArea"
  [enableHtmlSanitizer]="enableHtmlSanitizer"
  [enablePersistence]="enablePersistence"
  [enableRtl]="enableRtl"
  [expandOn]="expandOn"
  [expandedNodes]="expandedNodes"
  [fields]="fields"
  [fullRowNavigable]="fullRowNavigable"
  [fullRowSelect]="fullRowSelect"
  [loadOnDemand]="loadOnDemand"
  [nodeTemplate]="nodeTemplate"
  [selectedNodes]="selectedNodes"
  [showCheckBox]="showCheckBox"
  [sortOrder]="sortOrder"
  [style]="cssRules"
  (actionFailure)="onActionFailure($event)"
  (created)="onCreated($event)"
  (dataBound)="onDataBounded($event)"
  (dataSourceChanged)="onDataSourceChanged($event)"
  (destroyed)="onDestroyed($event)"
  (drawNode)="onDrawNode($event)"
  (keyPress)="onKeyPressed($event)"
  (nodeChecked)="onNodeChecked($event)"
  (nodeChecking)="onNodeChecking($event)"
  (nodeClicked)="onNodeClicked($event)"
  (nodeCollapsed)="onNodeCollapsed($event)"
  (nodeCollapsing)="onNodeCollapsing($event)"
  (nodeDragStart)="onNodeDragStart($event)"
  (nodeDragStop)="onNodeDragStop($event)"
  (nodeDragging)="onNodeDragging($event)"
  (nodeDropped)="onNodeDropped($event)"
  (nodeEdited)="onNodeEdited($event)"
  (nodeEditing)="onNodeEditing($event)"
  (nodeExpanded)="onNodeExpanded($event)"
  (nodeExpanding)="onNodeExpanding($event)"
  (nodeSelected)="onNodeSelected($event)"
  (nodeSelecting)="onNodeSelecting($event)"></ejs-treeview>
<div *ngIf="displayContextMenu">
  <ejs-contextmenu
    #contextMenuTree
    target="#idDivTree"
    [items]="menuItems"
    (beforeOpen)="onBeforeContextMenuOpen($event)"
    (select)="onContextMenuClick($event)"></ejs-contextmenu>
</div>
