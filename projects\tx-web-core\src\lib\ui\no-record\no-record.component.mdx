import { Meta, Story, Canvas, ArgTypes, Controls, Description, Stories } from '@storybook/addon-docs';
import * as NoRecordComponentStories from './no-record.component.stories';

<Meta of={NoRecordComponentStories} />
# Component NoRecord
<Description> This component is used when there is no data to display in another component </Description>
### Primary story and code snippet
<Canvas>
    <Story of={NoRecordComponentStories.Primary} />
</Canvas>

### Component input and output
<ArgTypes>

</ArgTypes>

### Description and control of the input and output
<Controls />

### Display all of the stories of the component
<Stories />
