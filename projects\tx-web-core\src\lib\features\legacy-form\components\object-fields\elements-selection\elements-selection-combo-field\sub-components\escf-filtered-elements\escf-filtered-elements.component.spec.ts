import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxEscfFilteredElementsComponent } from './escf-filtered-elements.component';
import { MockComponent } from 'ng-mocks';
import { ListBoxComponent } from '@syncfusion/ej2-angular-dropdowns';

describe('TxEscfFilteredElementsComponent', () => {
  let component: TxEscfFilteredElementsComponent;
  let fixture: ComponentFixture<TxEscfFilteredElementsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxEscfFilteredElementsComponent, MockComponent(ListBoxComponent)],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxEscfFilteredElementsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
