import {
  getLocaleDateFormat,
  getLocaleDateTimeFormat,
  FormatWidth,
  getLocaleTimeFormat,
} from '@angular/common';
import { AbstractLocaleService } from '../../../data-access/session';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class LegacyDateUtilsHelper {
  constructor(private locale: AbstractLocaleService) {}
  /**
   * transform a date object into a float value
   */
  dateToFloat(date: Date): number | undefined {
    if (!date) {
      return undefined;
    }

    return (
      Math.ceil(
        (25569.0 +
          (date.getTime() - date.getTimezoneOffset() * 60 * 1000) / (1000 * 60 * 60 * 24)) *
          100000
      ) / 100000
    );
  }

  /**
   * transform a float value into a date object
   */
  floatToDate(float: number): Date {
    return new Date(0, 0, 0, 0, 0, float * 24 * 60 * 60 - 24 * 60 * 60);
  }

  /**
   * Get the format date string use to format date
   * exemple : dd/mm/yyyy
   * @param type : enumeration of FormatWidth.short / medium / long / full
   */
  public getDateFormat(type: FormatWidth) {
    return getLocaleDateFormat(this.locale.locale, type);
  }

  /**
   * Get the format date time string use to format dateTime
   * exemple : dd/mm/yyyy
   * @param type : enumeration of FormatWidth.short / medium / long / full
   */
  public getDateTimeFormat(type: FormatWidth) {
    const dateFormat = this.getDateFormat(type);
    const timeFormat = getLocaleTimeFormat(this.locale.locale, type);
    const dateTimeFormat = getLocaleDateTimeFormat(this.locale.locale, type);
    return dateTimeFormat.replace('{1}', dateFormat).replace('{0}', timeFormat);
  }
}
