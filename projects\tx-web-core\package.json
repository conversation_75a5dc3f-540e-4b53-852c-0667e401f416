{"name": "@bassetti-group/tx-web-core", "version": "18.0.8", "peerDependencies": {"@angular/animations": "^18.2.13", "@angular/cdk": "^18.2.14", "@angular/common": "^18.2.13", "@angular/core": "^18.2.13", "@angular/material": "^18.2.14", "@angular/router": "^18.2.13", "@angular/forms": "^18.2.13", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@fortawesome/angular-fontawesome": "^0.15.0", "@biesbjerg/ngx-translate-extract-marker": "^1.0.0", "@fortawesome/fontawesome-pro": "^6.7.1", "@fortawesome/fontawesome-svg-core": "^6.7.1", "@fortawesome/pro-light-svg-icons": "^6.7.1", "@fortawesome/pro-solid-svg-icons": "^6.7.1", "@syncfusion/ej2-angular-buttons": "~20.4.38", "@syncfusion/ej2-angular-calendars": "~20.4.38", "@syncfusion/ej2-angular-dropdowns": "~20.4.38", "@syncfusion/ej2-angular-grids": "~20.4.38", "@syncfusion/ej2-angular-inputs": "^20.4.38", "@syncfusion/ej2-angular-navigations": "~20.4.38", "@syncfusion/ej2-angular-treegrid": "~20.4.38", "rxjs": "^7.5.6", "ng-mocks": "^14.1.1", "unzipit": "^1.4.3", "ngx-colors": "^3.6.0", "ramda": "^0.29.0"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}, "repository": {"type": "git", "url": "https://github.com/BASSETTI-GROUP/tx-administration.git"}, "dependencies": {"tslib": "^2.3.0"}, "exports": {"./styles/*.scss": "./styles/*.scss", "./i18n/*.json": "./i18n/*.json"}, "sideEffects": false}