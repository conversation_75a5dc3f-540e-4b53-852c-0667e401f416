import { Component, Input, OnInit, Renderer2 } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { NgxColorsModule } from 'ngx-colors';
import { ColorFormats } from './enums/formats';
import { ColorVariants } from './interfaces/color-variants';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DEFAULT_PALETTE, LIGHT_PALETTE } from './palettes';
import { ArrayUtils } from '@bassetti-group/tx-web-core/src/lib/utilities';

@Component({
  standalone: true,
  selector: 'tx-color-picker',
  templateUrl: './tx-color-picker.component.html',
  styleUrls: ['./tx-color-picker.component.scss'],
  imports: [NgxColorsModule, ReactiveFormsModule, TranslateModule],
})
export class TxColorPickerComponent implements OnInit {
  @Input() colorFormControl: FormControl<string | null> = new FormControl<string>('#fff');

  @Input() formats = [ColorFormats.HEX, ColorFormats.RGBA];

  @Input() colorPalette: Array<string | ColorVariants> = DEFAULT_PALETTE;

  private unlistenClick: (() => void) | null = null;

  public specificClassName: string | undefined;

  private readonly recommendedText = _('txWebCore.components.colorPicker.recommendedColors');
  private readonly advancedText = _('txWebCore.components.colorPicker.advancedColors');

  constructor(private readonly translate: TranslateService, private readonly renderer: Renderer2) {}

  ngOnInit() {
    if (ArrayUtils.equalsCheck(this.colorPalette, LIGHT_PALETTE)) {
      this.specificClassName = 'tx-color-light-mode';
    }
  }

  paletteOpened() {
    setTimeout(() => {
      // waiting for palette to be inserted into the DOM
      if (this.specificClassName === 'tx-color-light-mode') {
        this.addTextBlocks();
      }
      this.replacePalette();
      this.addListenerOnBackButton();
    }, 100);
  }

  paletteClosed() {
    if (this.unlistenClick) {
      this.unlistenClick();
      this.unlistenClick = null;
    }
  }

  private addTextBlocks() {
    const colorsDiv = document.querySelector('.tx-color-light-mode .colors');
    if (colorsDiv) {
      let textRecommendedDiv = colorsDiv.querySelector('.color-text-recommended');
      if (!textRecommendedDiv) {
        textRecommendedDiv = this.renderer.createElement('div');
        this.renderer.addClass(textRecommendedDiv, 'color-text-recommended');
        this.renderer.insertBefore(colorsDiv, textRecommendedDiv, colorsDiv.firstChild);
      }
      this.renderer.setProperty(
        textRecommendedDiv,
        'textContent',
        this.translate.instant(this.recommendedText)
      );

      let textAdvancedDiv = colorsDiv.querySelector('.color-text-advanced');
      if (!textAdvancedDiv) {
        textAdvancedDiv = this.renderer.createElement('div');
        this.renderer.addClass(textAdvancedDiv, 'color-text-advanced');
        this.renderer.insertBefore(colorsDiv, textAdvancedDiv, colorsDiv.firstChild);
      }
      this.renderer.setProperty(
        textAdvancedDiv,
        'textContent',
        this.translate.instant(this.advancedText)
      );
    }
  }

  private replacePalette() {
    const paletteContainer = document.querySelector('.ngx-colors-overlay ngx-colors-panel');
    const palette = document.querySelector('.ngx-colors-overlay ngx-colors-panel .opened');
    if (paletteContainer instanceof HTMLElement && palette instanceof HTMLElement) {
      const elementWidth = palette.offsetWidth;

      const windowWidth =
        window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;

      // Check if palette is not outside of window
      if (paletteContainer.offsetLeft + elementWidth > windowWidth) {
        this.renderer.setStyle(paletteContainer, 'left', windowWidth - elementWidth + 'px');
      }
    }
  }

  private addListenerOnBackButton() {
    const palette = document.querySelector('.ngx-colors-overlay ngx-colors-panel .opened');
    if (palette) {
      this.unlistenClick = this.renderer.listen(palette, 'click', (event: MouseEvent) => {
        let targetElement = event.target as HTMLElement;
        // Go up the DOM tree until the palette element is reached
        while (targetElement && targetElement !== palette) {
          if (targetElement.matches('.round-button')) {
            // match the click on "back" button
            this.addTextBlocks();
            break;
          }
          targetElement = targetElement.parentNode as HTMLElement;
        }
      });
    }
  }
}
