export class _BooleanUtils {
  /**
   * @param b a boolean value
   * @return boolean : if param is null or undefined
   */
  static isEmpty(b: boolean): boolean {
    return b === null || typeof b === undefined;
  }

  /**
   * @param b a boolean value
   * @param defaultVal the boolean value to return if n is empty
   * @return n or defaultVal
   */
  static getValue(b: boolean, defaultVal: boolean): boolean {
    return this.isEmpty(b) ? defaultVal : b;
  }
}
