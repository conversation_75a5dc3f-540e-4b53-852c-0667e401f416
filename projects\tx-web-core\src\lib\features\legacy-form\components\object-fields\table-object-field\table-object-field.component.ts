import { TxObjectFieldComponent } from './../_system/object-field/object-field.component';
import { Component, Input } from '@angular/core';
import { TxAttributeField } from '../../../models/formConfiguration/businessClass/attribute-field';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { LegacyTxDataTable } from '../../../services/structure/models/data';

@Component({
  selector: 'tx-table-object-field',
  templateUrl: './table-object-field.component.html',
  styleUrls: ['./table-object-field.component.scss'],
})
export class TxTableObjectFieldComponent extends TxObjectFieldComponent {
  @Input() declare field: TxAttributeField;
  @Input() declare data: LegacyTxDataTable;

  constructor(public attributeService: LegacyTxAttributesService) {
    super(attributeService);
  }

  initValue() {
    if (this.data) {
      this.value = this.data.series;
    }
  }

  // getData(): TxDataTable {
  //   const series = [];
  //   return new TxDataTable(this.idObject, this.idAttribute, series);
  // }
}
