import type { Meta, StoryObj } from '@storybook/angular';
import { TxDropdownTreeComponent } from './dropdown-tree.component';

const meta: Meta<TxDropdownTreeComponent<{ name: '' }>> = {
  component: TxDropdownTreeComponent,
  title: 'Dropdown/TxDropdownTreeComponent',
  tags: ['autodocs'],
  render: (args) => ({
    props:args,
    template:`
    <tx-dropdown-tree
      name="myDropdownTree"
      [label]="label"
      [width]="width"
      [dataSource]="dataSource"
      [dataOptions]="dataOptions"
      [treeNodeTemplate]="treeNodeTemplate"
      [disabled]="disabled"
      [readonly]="readonly"
      [required]="required"
      [returnIds]="returnIds"
      [showClearButton]="showClearButton"
      [multipleSelection]="multipleSelection"
      [enableRemoveSelection]="enableRemoveSelection"
      [showCheckBox]="showCheckBox"
      [readyToLoad]="readyToLoad"
      [displayHint]="displayHint"
      [checkedIds] ="checkedIds"
      >
    </tx-dropdown-tree>
    <ng-template #treeNodeTemplate let-node="node">
      <div class="node-row">
        <img
          [alt]="'concept-icon-' + node.objectData.icon"
          [src]="node.objectData.iconPath" />
        <span> {{ node.name }}</span>
      </div>
    </ng-template>`,
  }),
};
export default meta;
type Story = StoryObj<TxDropdownTreeComponent<{ name: '' }>>;

export const Primary: Story = {
  args: {
    dataSource: [{
      idObjectTypeParent: 4213,
      order: 0,
      icon: 232,
      isFolder: false,
      type: "ottStandard",
      hasDistinctNames: false,
      isVisible: true,
      lockingType: "otltNone",
      lockingDuration: 0.0013888888888888887,
      displayResultInTextSearch: true,
      right: "dbrStructure",
      associatedObjectTypesIds: [],
      description: "On regroupe ici tous les services.",
      explanation: "",
      name: "Services",
      label: "Services",
      tags: [
        "OTLaboratories"
      ],
      id: 24,
      iconPath: "./img/icons/svg/232.svg"
    },
    {
      order: 1,
      icon: 262,
      isFolder: false,
      type: "ottStandard",
      hasDistinctNames: false,
      isVisible: true,
      lockingType: "otltNone",
      lockingDuration: 0.0006944444444444445,
      displayResultInTextSearch: true,
      right: "dbrStructure",
      associatedObjectTypesIds: [],
      description: "On regroupe ici tous les départements.",
      explanation: "",
      name: "Départements",
      label:"Départements",
      tags: [
        "oTDepartements"
      ],
      id: 4213,
      iconPath: "./img/icons/svg/262.svg"
    },{
      order: 2,
      icon: 222,
      isFolder: false,
      type: "ottStandard",
      hasDistinctNames: false,
      isVisible: true,
      lockingType: "otltNone",
      lockingDuration: 0.0006944444444444445,
      displayResultInTextSearch: true,
      right: "dbrStructure",
      associatedObjectTypesIds: [],
      description: "On regroupe ici tous Sociétés.",
      explanation: "",
      name: "Sociétés",
      label:"Sociétés",
      tags: [
        "oTSocietes"
      ],
      id: 1234,
      iconPath: "./img/icons/svg/222.svg"
    },],
    dataOptions: {idProperty: 'id', idParentProperty: 'idObjectTypeParent'},
    label: 'Dropdown Tree',
    width: '100%',
    readyToLoad: true,
    allowFiltering: true,
    showClearButton: true,
    returnIds: false,
    disabled: false,
    readonly: false,
    displayHint: true,
    enableRemoveSelection: true,
    showCheckBox: false,
    multipleSelection: true,
    hierarchicalSelection: false,
    useChipsForLabels : true,
    checkedIds : [],
    // The component is working without the controle, but not too well. it causes an error on the console 
    // Adding the controle would cause a JSON error, preventing the component from displaying when modifying an option.
    // control: new FormControl(''),
  }
};
