import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxReadTextFieldComponent } from './read-text-field.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTooltipModule } from '@angular/material/tooltip';

describe('ReadTextFieldComponent', () => {
  let component: TxReadTextFieldComponent;
  let fixture: ComponentFixture<TxReadTextFieldComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TxReadTextFieldComponent],
      imports: [MatFormFieldModule, MatTooltipModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxReadTextFieldComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
