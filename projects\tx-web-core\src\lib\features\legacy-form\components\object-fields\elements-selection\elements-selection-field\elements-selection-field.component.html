<mat-label>{{ label }}</mat-label>
<div class="control-section">
  <div class="checkbox-control">
    <ng-container *ngFor="let element of elements; let i = index">
      <div
        [class]="i % 2 === 0 ? 'row' : 'col'"
        [style.width]="i % 2 === 0 ? elementSize + 'px' : 'calc( 100% - ' + elementSize + 'x)'">
        <ng-container *ngIf="selection === 'Multiple'; else elseTemplate">
          <ejs-checkbox
            #checkbox
            [label]="element.text"
            [checked]="isSelected(element)"
            (change)="onSelectionChange(element, $event)"
            [title]="element.hintValue"></ejs-checkbox>
        </ng-container>
        <ng-template #elseTemplate>
          <ejs-radiobutton
            name="radio"
            [label]="element.text"
            [checked]="isSelected(element)"
            (change)="onSelectionChange(element, $event)"
            [title]="element.hintValue"></ejs-radiobutton>
        </ng-template>
      </div>
    </ng-container>
  </div>
</div>
