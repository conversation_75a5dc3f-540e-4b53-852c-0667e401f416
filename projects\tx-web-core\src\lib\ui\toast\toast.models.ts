import { TemplateRef } from '@angular/core';

export interface ToastData {
  isUnread?: boolean;
  isPersistent?: boolean;
  type?: ToastType;
  interval?: number;
  title?: string;
  description?: string;
  date?: Date;
  template?: TemplateRef<any>;
  templateContext?: any;
  clickEvent?: ToastEvent;
  progress?: number;
  displayPercent?: boolean;
}

export interface ToastEvent {
  route?: string;
  data: any;
}

export type ToastType = 'success' | 'error' | 'information' | 'warning' | 'loading';
