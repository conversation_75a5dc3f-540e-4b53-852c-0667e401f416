import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterTestingModule } from '@angular/router/testing';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { MonacoEditorModule, NgxMonacoEditorConfig } from 'ngx-monaco-editor-v2';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { ConfigServiceMock, LocalizedDateMockPipe } from 'src/app/app.testing.mock';
import { DialogConfirmServiceMock } from 'src/app/shared/tests/shared-testing-mock';
import { FileManagerService } from '../../services/file-manager.service';
import { ResourcesService } from '../../services/resources.service';
import { FileManagerServiceMock, ResourcesServiceMock } from '../../tests/file-manager.mock';
import { FileEditorComponent } from './file-editor.component';
import { MockComponent, MockProvider } from 'ng-mocks';
import { LoaderComponent } from 'src/app/shared/components/loader/loader.component';
import { LastSaveChipComponent } from 'src/app/shared/components/last-save-chip/last-save-chip.component';
import { By } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';
import { TxConfigService, TxDialogService } from '@bassetti-group/tx-web-core';
import { CanSaveEditorPipe } from '../../pipes/can-save-editor.pipe';

const monacoConfig: NgxMonacoEditorConfig = {
  defaultOptions: { scrollBeyondLastLine: false }, // pass default options to be used
  onMonacoLoad: () => {},
};

const fileToEdit = {
  name: 'test.xml',
  type: 'text/xml',
  lastWriteTime: '2020-01-01T00:00:00.000Z',
  extension: 'xml',
  length: 89,
};

describe('FileEditorComponent', () => {
  let component: FileEditorComponent;
  let fixture: ComponentFixture<FileEditorComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        FileEditorComponent,
        LocalizedDateMockPipe,
        CanSaveEditorPipe,
        MockComponent(LoaderComponent),
        MockComponent(LastSaveChipComponent),
      ],
      imports: [
        MatButtonModule,
        MatTooltipModule,
        FontAwesomeTestingModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
        RouterTestingModule.withRoutes([]),
        MonacoEditorModule.forRoot(monacoConfig),
        FormsModule,
      ],
      providers: [
        { provide: FileManagerService, useClass: FileManagerServiceMock },
        { provide: ResourcesService, useClass: ResourcesServiceMock },
        { provide: TxDialogService, useClass: DialogConfirmServiceMock },
        MockProvider(TxConfigService, ConfigServiceMock, 'useClass'),
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FileEditorComponent);
    component = fixture.componentInstance;
    component.fileToEdit = fileToEdit;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should init with dummy xml file', () => {
    expect(component.file).toStrictEqual({
      name: 'test.xml',
      content:
        '<?xml version="1.0" encoding="UTF-8"?><note><to></to><from></from><heading></heading><body></body></note>',
    });
  });

  it('should detect modification of file', () => {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    component.file!.content =
      '<?xml version="1.0" encoding="UTF-8"?><note><to></to><from></from><body></body></note>';
    expect(component.isFileModified()).toBe(true);
  });

  it('should display errors after save', () => {
    const spyDisplayErrorInEditor = jest.spyOn(component, 'displayErrorInEditor');
    component.fileHasModifications = true;
    component.isSaving = false;
    component.saveFile();
    expect(spyDisplayErrorInEditor).toHaveBeenCalled();
  });

  it('should set editor options on readonly when no write permissions on file', () => {
    component.isFileReadOnly = false;
    component.ngOnInit();
    expect(component.editorOptions.readOnly).toBe(false);
  });

  it('should disable the save button when isSaving is true', () => {
    component.isSaving = true;
    fixture.detectChanges();

    const btnSave = fixture.debugElement.query(By.css('#btnSave')).nativeElement;
    expect(btnSave.disabled).toBeTruthy();
  });

  it('should not be possible to save when isSaving is true', () => {
    component.isSaving = true;
    fixture.detectChanges();

    expect(component.canSaveFile()).toBeFalsy();
  });

  it('should not be possible to save when there is no modification', () => {
    component.fileHasModifications = false;
    fixture.detectChanges();

    expect(component.canSaveFile()).toBeFalsy();
  });
});
