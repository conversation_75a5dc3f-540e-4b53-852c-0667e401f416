import {
  Component,
  Input,
  ViewChild,
  AfterViewInit,
  ElementRef,
  Output,
  EventEmitter,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatChipInputEvent } from '@angular/material/chips';
import { TxChip } from '../../../models/formFields/setting-model';
import { TxFileService } from '../../../services/structure/services/file.service';
import { DomSanitizer } from '@angular/platform-browser';
import { TxAttributeField } from '../../../models/formConfiguration/businessClass/attribute-field';
import {
  LegacyTxData,
  LegacyTxDataFile,
  LegacyTxDataLink,
  LegacyTxDataTab,
  LegacyTxDataType,
} from '../../../services/structure/models/data';
import { LegacyTxFile } from '../../../services/structure/models/attribute';
import { LegacyTxObject } from '../../../services/structure/models/object';
import { TxBaseFieldComponent } from '../base-field/base-field.component';
import { LegacyFileUtils } from '../../../utilities/legacy-file-utils';

@Component({
  selector: 'tx-chips-field',
  templateUrl: './chips-field.component.html',
  styleUrls: ['./chips-field.component.scss'],
})
export class TxChipsFieldComponent extends TxBaseFieldComponent implements OnInit, AfterViewInit {
  @Input() field!: TxAttributeField;
  @Input() placeHolder!: string;
  @Input() visible!: boolean;
  @Input() selectable: boolean = true;
  @Input() removable!: boolean;
  @Input() addOnBlur = true;
  @Input() multiple!: boolean;
  @Input() multipleSelection!: boolean;
  @Input() chips: TxChip[] = [];
  @Input() maxChipsMessage = '';
  @Input() pattern: RegExp | null = null;
  @Input() data!: LegacyTxData;
  @Input() fieldType!: LegacyTxDataType;
  @Input() maxChipDisplay: number = 100;
  @Input() fullWidthChip: boolean = false;
  @Input() hideTitleImageThumbnail = false;
  @Input() withInputText = false;
  @Input() checkedChips: string[] = [];
  @Input() checkedChip!: string;
  @Input() options: string[] = [];

  @Output() chipClick = new EventEmitter<TxChip>();
  @Output() fieldChange = new EventEmitter<TxChip[]>();
  @Output() displayPaneEvent = new EventEmitter<any>();
  @Output() selectionChange = new EventEmitter<string[]>();

  @ViewChild('inputChip') inputChip!: ElementRef;
  @ViewChild('ChipList') chipList: any;

  public locked = false;
  attributeType = LegacyTxDataType;
  withViewableFile = false;
  setToDoubleWidth = false;
  displayExtenderChip = false;

  readonly separatorKeysCodes: number[] = [ENTER, COMMA];

  constructor(public fileService?: TxFileService, private sanitizer?: DomSanitizer) {
    super();
  }

  ngOnInit(): void {
    super.ngOnInit();
    if (this.options && (!this.chips || this.chips.length < 1)) {
      this.chips = [];
      this.options.forEach((option) =>
        this.chips.push({
          name: option,
          removable: this.removable,
          selectable: this.selectable,
          selected: this.isChipSelected(option),
        })
      );
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    super.ngOnChanges(changes);
    if (changes.options) {
      this.chips = [];
      this.options.forEach((option) =>
        this.chips.push({
          name: option,
          removable: this.removable,
          selectable: this.selectable,
          selected: this.isChipSelected(option),
        })
      );
    }
  }

  OnSelectionChange() {
    const chipsSelected = this.getSelectedChips();
    this.control.setValue(chipsSelected);
    this.selectionChange.emit(chipsSelected);
  }

  getSelectedChips(): string[] {
    let chipsSelected = [];
    for (let chip of this.chips) {
      if (chip.selected) {
        chipsSelected.push(chip.name);
      }
    }
    return chipsSelected;
  }

  initValue(): void {
    this.clearControl();
    if (
      this.checkedChip &&
      (!this.checkedChips || (this.checkedChips && this.checkedChips.length < 1))
    ) {
      this.checkedChips = [this.checkedChip];
    }
    if (this.checkedChips && this.checkedChips.length) {
      this.checkedChips = this.checkedChips.map((o) => '' + o);
      this.control.setValue(this.checkedChips);
    }
  }

  ngAfterViewInit() {
    // if (!this.control.value) {
    //   this.clearControl();
    // }
    // else if (this.data){
    //   this.setData();
    // }
    setTimeout(() => {
      this.checkValidForm();
    }, 200);
  }

  valueIsEmpty(): boolean {
    if (!this.control.value) {
      return true;
    }
    return (
      this.control.value === '' ||
      this.control.value === undefined ||
      this.control.value === null ||
      (Array.isArray(this.control.value) &&
        this.control.value.length <= 1 &&
        this.control.value[0] !== 0 &&
        !this.control.value[0])
    );
  }

  clearControl() {
    this.control.setValue([]);
  }

  checkValidForm() {
    if (!this.multiple && this.chips.length) {
      this.locked = true;
      // this.inputChip.nativeElement.setAttribute('disabled', 'disabled');
      this.information = this.maxChipsMessage;
    } else {
      this.information = '';
      this.locked = false;
      // if (!this.disabled && this.inputChip) {
      //   this.inputChip.nativeElement.removeAttribute('disabled');
      // }
    }

    if (this.maxChipDisplay && this.chips.length > this.maxChipDisplay) {
      this.displayExtenderChip = true;
    } else {
      this.displayExtenderChip = false;
    }

    setTimeout(() => {
      if (this.chips.length < 1 && this.required) {
        this.control.setErrors({ required: this.required });
      } else {
        // this.formControl.setErrors(null);
      }
    }, 100);

    return this.locked;
  }

  onAdd(event: MatChipInputEvent) {
    const input = event.input;
    const value = event.value;

    if (this.checkValidForm()) {
      return;
    }

    const valid = this.add(value);

    if (!valid) {
      return;
    }

    // Reset the input value
    if (input) {
      input.value = '';
    }
  }

  addList(values: string | string[]) {
    if (Array.isArray(values)) {
      values.forEach((value) => {
        this.add(value);
      });
    } else {
      this.add(values);
    }
  }

  addFileList(files: LegacyTxFile[]) {
    if (files) {
      files.forEach((file) => {
        this.addFile(file);
      });
    }
  }

  addObjectList(objects: LegacyTxObject[]) {
    if (objects) {
      objects.forEach((object) => {
        this.addObject(object);
      });
    }
  }

  add(value: string): boolean {
    let valid = true;
    if ((value || '').trim()) {
      valid = this.pattern ? this.pattern.test(value) : true;
      if (valid) {
        this.chips.push({
          name: value.trim(),
          removable: this.removable,
          selectable: this.selectable,
        });
        if (!this.control.value) {
          this.control.setValue([]);
        }
        (this.control.value as any[]).push(value);
        this.fieldChange.emit(this.chips);
        this.control.updateValueAndValidity();
      }
    }
    this.checkValidForm();
    return valid;
  }

  addFile(file: LegacyTxFile) {
    if (file) {
      if (this.withViewableFile && !this.setToDoubleWidth) {
        this.field.classes += ' double-field';
        this.setToDoubleWidth = true;
      }
      const chip: TxChip = {
        name: file.name,
        removable: this.removable,
        selectable: this.selectable,
        icon: LegacyFileUtils.getFileIcon(file.name),
        viewableFile: file.view && LegacyFileUtils.isAFilePicture(file.name),
        idFile: file.idArchivedFile,
      };
      if (!this.control.value) {
        this.control.setValue([]);
      }
      if (chip.viewableFile) {
        this.withViewableFile = true;
        this.getImage(chip);
        const newChipsValue = [chip].concat(this.chips);
        this.chips = newChipsValue;
        const newControlValue = [file].concat(this.control.value);
        this.control.setValue(newControlValue);
      } else {
        this.chips.push(chip);
        (this.control.value as any[]).push(file);
      }

      this.fieldChange.emit(this.chips);
      this.control.updateValueAndValidity();
    }
    this.checkValidForm();
  }

  addObject(object: LegacyTxObject) {
    if (object) {
      this.chips.push({
        name: object.name,
        removable: this.removable,
        selectable: this.selectable,
      });
      if (!this.control.value) {
        this.control.setValue([]);
      }
      (this.control.value as any[]).push(object);
      this.fieldChange.emit(this.chips);
      this.control.updateValueAndValidity();
    }
    this.checkValidForm();
  }

  remove(chip: TxChip): void {
    const index = this.chips.indexOf(chip);

    if (index >= 0) {
      this.chips.splice(index, 1);
      (this.control.value as any[]).splice(index, 1);
      this.control.updateValueAndValidity();
      this.value = this.control.value;
      this.fieldChange.emit(this.chips);
      this.checkValidForm();
    }
  }

  clearField() {
    this.control.setValue([]);
    this.chips = [];
    this.control.updateValueAndValidity();
    this.fieldChange.emit(this.chips);
    this.checkValidForm();
  }

  onChipClick(chip: TxChip) {
    this.changeSelection(chip);
    this.chipClick.emit(chip);
  }

  changeSelection(chip: TxChip) {
    if (chip.selectable && !this.disabled) {
      if (chip.selected) {
        let index = this.checkedChips.indexOf(chip.name);
        this.checkedChips.splice(index, 1);
      } else {
        if (!this.multipleSelection) {
          this.unselectAllChip();
        }
        this.checkedChips.push(chip.name);
      }
      chip.selected = !chip.selected;
      this.OnSelectionChange();
    }
  }

  unselectAllChip() {
    this.chips.forEach((chip) => {
      chip.selected = false;
    });
    this.checkedChips = [];
  }

  isChipSelected(name: string): boolean {
    return this.checkedChips.includes(name) || this.checkedChip === name;
  }

  onActionIconClick() {
    this.actionIconClick.emit(this.chips);
  }

  setData() {
    this.clearField();
    if (this.data) {
      if (this.data instanceof LegacyTxDataTab) {
        this.addList(this.data.value as string | string[]);
      } else if (this.data instanceof LegacyTxDataFile) {
        this.addFileList(this.data.files as LegacyTxFile[]);
      } else if (this.data instanceof LegacyTxDataLink) {
        this.addObjectList(this.data.linkedObjects as LegacyTxObject[]);
      }
    }
  }

  displayRightPane() {
    setTimeout(() => {
      this.displayPaneEvent.emit({ name: this.label, values: this.chips });
    });
  }

  getImage(chip: TxChip) {
    this.fileService?.download(chip.idFile as number).subscribe((blob) => {
      let objectURL = URL.createObjectURL(blob);
      chip.fileUrl = this.sanitizer?.bypassSecurityTrustUrl(objectURL);
      localStorage.setItem(chip.name, chip.fileUrl?.toString() as string);
    });
  }
}
