import { FormControl } from '@angular/forms';
import { Component, Input, ViewChild, ElementRef, OnInit } from '@angular/core';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { TxInputObjectFieldComponent } from '../_system/input-object-field/input-object-field.component';
import { MatFormField } from '@angular/material/form-field';
@Component({
  selector: 'tx-text-input-field',
  templateUrl: './text-input-object-field.component.html',
  styleUrls: [
    './text-input-object-field.component.scss',
    '../../generic-fields/base-field/base-field.component.scss',
  ],
})
export class TxTextInputObjectFieldComponent extends TxInputObjectFieldComponent implements OnInit {
  @Input() numberOfLine!: number;
  @Input() inTextArea = false;

  textAreaHeight!: number;
  focusState!: boolean;
  appearance = 'legacy';
  mouseInFormField = false;
  @ViewChild('input') public input!: ElementRef;
  @ViewChild('formField') formField!: MatFormField;

  constructor(public attributeService: LegacyTxAttributesService) {
    super(attributeService);
  }

  ngOnInit() {
    super.ngOnInit();
    if (!this.control) {
      this.control = new FormControl();
      this.control.setValue(this.value);
    }
  }

  getValue() {
    return this.input.nativeElement.value;
  }

  onMouseEnter() {
    this.mouseInFormField = true;
  }

  onMouseLeave() {
    this.mouseInFormField = false;
  }

  onFocus(): void {}

  onBlur(): void {}

  checkValue(): void {}
}
