import { SortDirection } from '@angular/material/sort';
import { Sort, sortData } from '../data-source/sort.util';

describe('sortData', () => {
  interface TestData {
    name: string;
    age: number;
  }

  const testData: TestData[] = [
    { name: '<PERSON>', age: 30 },
    { name: '<PERSON>', age: 25 },
    { name: '<PERSON>', age: 35 },
  ];

  it('should sort by string (name) in ascending order', () => {
    const sort: Sort<TestData> = {
      active: 'name',
      direction: 'asc' as SortDirection,
    };

    const result = sortData(sort, testData);

    expect(result).toEqual([
      { name: '<PERSON>', age: 25 },
      { name: '<PERSON>', age: 35 },
      { name: '<PERSON>', age: 30 },
    ]);
  });

  it('should sort by string (name) in descending order', () => {
    const sort: Sort<TestData> = {
      active: 'name',
      direction: 'desc' as SortDirection,
    };

    const result = sortData(sort, testData);

    expect(result).toEqual([
      { name: '<PERSON>', age: 30 },
      { name: '<PERSON>', age: 35 },
      { name: '<PERSON>', age: 25 },
    ]);
  });

  it('should sort by number (age) in ascending order', () => {
    const sort: Sort<TestData> = {
      active: 'age',
      direction: 'asc' as SortDirection,
    };

    const result = sortData(sort, testData);

    expect(result).toEqual([
      { name: 'Alice', age: 25 },
      { name: 'John', age: 30 },
      { name: 'Bob', age: 35 },
    ]);
  });

  it('should sort by number (age) in descending order', () => {
    const sort: Sort<TestData> = {
      active: 'age',
      direction: 'desc' as SortDirection,
    };

    const result = sortData(sort, testData);

    expect(result).toEqual([
      { name: 'Bob', age: 35 },
      { name: 'John', age: 30 },
      { name: 'Alice', age: 25 },
    ]);
  });

  it('should return data unchanged when sort is undefined or null', () => {
    let result = sortData({} as Sort<TestData>, testData);
    expect(result).toEqual(testData);

    result = sortData({} as Sort<TestData>, testData);
    expect(result).toEqual(testData);
  });
});
