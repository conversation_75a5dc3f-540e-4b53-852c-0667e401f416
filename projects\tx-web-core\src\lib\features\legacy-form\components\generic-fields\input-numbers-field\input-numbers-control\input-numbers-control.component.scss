input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.numbers-input-spacer {
  margin: 0px 5px;
  opacity: 0;
  transition: opacity 200ms;
  font-size: 13px;
}

:host.example-floating .numbers-input-spacer {
  opacity: 1;
}

.numbers-input-container {
  width: fit-content;
  display: flex;
}

.numbers-input-element {
  padding: 0;
  // outline: none;
  font-size: 13px;
  width: 54px;
}
