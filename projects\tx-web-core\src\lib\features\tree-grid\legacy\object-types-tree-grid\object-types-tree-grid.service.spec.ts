import { TestBed } from '@angular/core/testing';
import {
  DataBaseRights,
  MOCK_OBJECTS_TYPE_SERVICE,
  TxLockingType,
  TxObjectsTypeService,
  TxObjectType,
  TxObjectTypeType,
  TxTreeGrid,
} from '@bassetti-group/tx-web-core';
import { MockService } from 'ng-mocks';
import { TxObjectTypesTreeGridService } from './object-types-tree-grid.service';

describe('Service: ObjectTypesTreeGrid', () => {
  let service: TxObjectTypesTreeGridService;
  let originalData: TxObjectType[];
  let expectedData: TxTreeGrid<any>[];
  let initialisedData: TxTreeGrid<any>[];
  let otService: TxObjectsTypeService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        TxObjectTypesTreeGridService,
        { provide: TxObjectsTypeService, useValue: MockService(MOCK_OBJECTS_TYPE_SERVICE) },
      ],
    });
    service = TestBed.inject(TxObjectTypesTreeGridService);
    otService = TestBed.inject(TxObjectsTypeService);

    originalData = [
      {
        id: 1,
        name: 'Contact',
        order: 1,
        type: TxObjectTypeType.Standard,
        isVisible: true,
        displayResultInTextSearch: true,
        hasDistinctName: true,
        icon: 1,
        isFolder: false,
        lockingDuration: 0,
        lockingType: TxLockingType.None,
        right: DataBaseRights.DbrStructure,
        tags: [],
      },
      {
        id: 11,
        idObjectTypeParent: 1,
        name: 'Contact 1',
        order: 1,
        type: TxObjectTypeType.Standard,
        isVisible: true,
        displayResultInTextSearch: true,
        hasDistinctName: true,
        icon: 1,
        isFolder: false,
        lockingDuration: 0,
        lockingType: TxLockingType.None,
        right: DataBaseRights.DbrStructure,
        tags: [],
      },
      {
        id: 12,
        idObjectTypeParent: 1,
        name: 'Contact 2',
        order: 1,
        type: TxObjectTypeType.Standard,
        isVisible: true,
        displayResultInTextSearch: true,
        hasDistinctName: true,
        icon: 1,
        isFolder: false,
        lockingDuration: 0,
        lockingType: TxLockingType.None,
        right: DataBaseRights.DbrStructure,
        tags: [],
      },
      {
        id: 2,
        name: 'User',
        order: 2,
        type: TxObjectTypeType.User,
        isVisible: true,
        displayResultInTextSearch: true,
        hasDistinctName: true,
        icon: 1,
        isFolder: false,
        lockingDuration: 0,
        lockingType: TxLockingType.None,
        right: DataBaseRights.DbrStructure,
        tags: [],
      },
      {
        id: 3,
        name: 'Portal',
        order: 2,
        type: TxObjectTypeType.Portal,
        isVisible: true,
        displayResultInTextSearch: true,
        hasDistinctName: true,
        icon: 1,
        isFolder: false,
        lockingDuration: 0,
        lockingType: TxLockingType.None,
        right: DataBaseRights.DbrStructure,
        tags: [],
      },
      {
        id: 4,
        name: 'Listing',
        order: 2,
        type: TxObjectTypeType.Listing,
        isVisible: true,
        displayResultInTextSearch: true,
        hasDistinctName: true,
        icon: 1,
        isFolder: false,
        lockingDuration: 0,
        lockingType: TxLockingType.None,
        right: DataBaseRights.DbrStructure,
        tags: [],
      },
      {
        id: 41,
        idObjectTypeParent: 4,
        name: 'Listing 1',
        order: 2,
        type: TxObjectTypeType.Listing,
        isVisible: true,
        displayResultInTextSearch: true,
        hasDistinctName: true,
        icon: 1,
        isFolder: false,
        lockingDuration: 0,
        lockingType: TxLockingType.None,
        right: DataBaseRights.DbrStructure,
        tags: [],
      },
      {
        id: 5,
        name: 'Assoc',
        order: 2,
        type: TxObjectTypeType.Associativity,
        isVisible: true,
        displayResultInTextSearch: true,
        hasDistinctName: true,
        icon: 1,
        isFolder: false,
        lockingDuration: 0,
        lockingType: TxLockingType.None,
        right: DataBaseRights.DbrStructure,
        tags: [],
      },
      {
        id: 6,
        name: 'Source',
        order: 2,
        type: TxObjectTypeType.Source,
        isVisible: true,
        displayResultInTextSearch: true,
        hasDistinctName: true,
        icon: 1,
        isFolder: false,
        lockingDuration: 0,
        lockingType: TxLockingType.None,
        right: DataBaseRights.DbrStructure,
        tags: [],
      },
      {
        id: 7,
        name: 'Information',
        order: 2,
        type: TxObjectTypeType.Information,
        isVisible: true,
        displayResultInTextSearch: true,
        hasDistinctName: true,
        icon: 1,
        isFolder: false,
        lockingDuration: 0,
        lockingType: TxLockingType.None,
        right: DataBaseRights.DbrStructure,
        tags: [],
      },
    ];
  });

  it('should create service', () => {
    expect(service).toBeTruthy();
  });

  describe('initTreeGridData', () => {
    beforeEach(() => {
      otService.getIconPath = jest.fn().mockReturnValue('src');
      // eslint-disable-next-line @typescript-eslint/dot-notation
      service['getExpandedState'] = jest.fn().mockReturnValue(true);

      expectedData = [
        {
          id: -1,
          icon: './assets/tx-web-core/img/icons/124.svg',
          name: 'txWebCore.generic.standards',
          expanded: true,
          tags: [],
          txObject: {},
        },
        {
          id: -2,
          icon: './assets/tx-web-core/img/icons/124.svg',
          name: 'txWebCore.generic.listings',
          expanded: false,
          tags: [],
          txObject: {},
        },
        {
          id: -3,
          icon: './assets/tx-web-core/img/icons/124.svg',
          name: 'txWebCore.generic.associatives',
          expanded: false,
          tags: [],
          txObject: {},
        },
        {
          id: -4,
          icon: './assets/tx-web-core/img/icons/124.svg',
          name: 'txWebCore.generic.systems',
          expanded: false,
          tags: [],
          txObject: {},
        },
        {
          id: 1,
          idParent: -1,
          icon: 'src',
          name: 'Contact',
          tags: [],
          expanded: true,
          txObject: originalData[0],
        },
        {
          id: 11,
          idParent: 1,
          icon: 'src',
          name: 'Contact 1',
          tags: [],
          expanded: true,
          txObject: originalData[1],
        },
        {
          id: 12,
          idParent: 1,
          icon: 'src',
          name: 'Contact 2',
          tags: [],
          expanded: true,
          txObject: originalData[2],
        },
        {
          id: 2,
          idParent: -1,
          icon: 'src',
          name: 'User',
          tags: [],
          expanded: true,
          txObject: originalData[3],
        },
        {
          id: 3,
          idParent: -1,
          icon: 'src',
          name: 'Portal',
          tags: [],
          expanded: true,
          txObject: originalData[4],
        },
        {
          id: 4,
          idParent: -2,
          icon: 'src',
          name: 'Listing',
          tags: [],
          expanded: true,
          txObject: originalData[5],
        },
        {
          id: 41,
          idParent: 4,
          icon: 'src',
          name: 'Listing 1',
          tags: [],
          expanded: true,
          txObject: originalData[6],
        },
        {
          id: 5,
          idParent: -3,
          icon: 'src',
          name: 'Assoc',
          tags: [],
          expanded: true,
          txObject: originalData[7],
        },
        {
          id: 6,
          idParent: -4,
          icon: 'src',
          name: 'Source',
          tags: [],
          expanded: true,
          txObject: originalData[8],
        },
        {
          id: 7,
          idParent: -4,
          icon: 'src',
          name: 'Information',
          tags: [],
          expanded: true,
          txObject: originalData[9],
        },
      ];
    });

    it('should initialise data correctly', () => {
      initialisedData = service.initTreeGridData(originalData);
      expect(initialisedData).toStrictEqual(expectedData); //object parents created and all other objects
    });
  });

  describe('getDataLengthAccordingType', () => {
    beforeEach(() => {
      initialisedData = [
        { id: 1, name: 'Object Standard 1', txObject: { type: TxObjectTypeType.Standard } },
        { id: 2, name: 'Object Standard 2', txObject: { type: TxObjectTypeType.Standard } },
        { id: 3, name: 'Object Standard 3', txObject: { type: TxObjectTypeType.User } },
        { id: 4, name: 'Object Standard 4', txObject: { type: TxObjectTypeType.Portal } },
        { id: 5, name: 'Object Standard 5', txObject: { type: TxObjectTypeType.Portal } },
        { id: 6, name: 'Object Listing 1', txObject: { type: TxObjectTypeType.Listing } },
        { id: 7, name: 'Object System 1', txObject: { type: TxObjectTypeType.Source } },
        { id: 7, name: 'Object System 2', txObject: { type: TxObjectTypeType.Information } },
        { id: 7, name: 'Object System 3', txObject: { type: TxObjectTypeType.Information } },
      ];
    });

    it('should return correct length of standard objects', () => {
      expect(service.getDataLengthAccordingType(-1, initialisedData)).toBe(5);
    });

    it('should return correct length of listing objects', () => {
      expect(service.getDataLengthAccordingType(-2, initialisedData)).toBe(1);
    });

    it('should return correct length of associativity objects', () => {
      expect(service.getDataLengthAccordingType(-3, initialisedData)).toBe(0);
    });

    it('should return correct length of system objects', () => {
      expect(service.getDataLengthAccordingType(-4, initialisedData)).toBe(3);
    });

    it('should return undefined if invalid id is passed as argument', () => {
      expect(service.getDataLengthAccordingType(99, initialisedData)).toBeUndefined();
    });
  });

  describe('createObjectParentArray', () => {
    let title: string[];
    let objectParentArray: TxTreeGrid<any>[];
    let expectedParentArray: TxTreeGrid<any>[];
    beforeEach(() => {
      title = ['Title1', 'Title2', 'Title3', 'Title4', 'Title5'];
      expectedParentArray = [
        {
          id: -1,
          icon: './assets/tx-web-core/img/icons/124.svg',
          name: 'Title1',
          expanded: true,
          tags: [],
          txObject: {},
        },
        {
          id: -2,
          icon: './assets/tx-web-core/img/icons/124.svg',
          name: 'Title2',
          expanded: false,
          tags: [],
          txObject: {},
        },
        {
          id: -3,
          icon: './assets/tx-web-core/img/icons/124.svg',
          name: 'Title3',
          expanded: false,
          tags: [],
          txObject: {},
        },
        {
          id: -4,
          icon: './assets/tx-web-core/img/icons/124.svg',
          name: 'Title4',
          expanded: false,
          tags: [],
          txObject: {},
        },
        {
          id: -5,
          icon: './assets/tx-web-core/img/icons/124.svg',
          name: 'Title5',
          expanded: false,
          tags: [],
          txObject: {},
        },
      ];
    });

    it('should create a correct parent array', () => {
      objectParentArray = service.createObjectParentArray(title);
      expect(objectParentArray).toStrictEqual(expectedParentArray);
    });
  });
});
