import { GlobalPositionStrategy, Overlay, OverlayModule } from '@angular/cdk/overlay';
import { TestBed } from '@angular/core/testing';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { ToastComponent } from './toast.component';
import { ToastData } from './toast.models';
import { ToastRef } from './toast.reference';
import { ToastService } from './toast.service';
import { TranslateTestingModule } from 'ngx-translate-testing';
const TRANSLATIONS = {
  en: {},
  fr: {},
};
describe('ToastService', () => {
  let service: ToastService;
  let overlayService: Overlay;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        OverlayModule,
        MatProgressBarModule,
        FontAwesomeTestingModule,
        ToastComponent,
        TranslateTestingModule.withTranslations(TRANSLATIONS),
      ],
    });
    service = TestBed.inject(ToastService);
    overlayService = TestBed.inject(Overlay);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Show', () => {
    let data: ToastData;
    beforeEach(() => {
      data = {
        type: 'success',
        title: 'Test Title',
        description: `test description`,
        date: new Date(),
        isUnread: true,
        isPersistent: true,
        interval: 8000,
      };
    });

    it('should get position of new toast', () => {
      const spyPosition = jest.spyOn(service, 'getPositionStrategy');

      service.show(data);

      expect(spyPosition).toBeCalledWith(-1);
    });

    it('should add a toast', () => {
      service.show(data);
      expect(service['toasts'].length).toBe(1);
    });

    it('should add a notification', () => {
      service.show(data);
      expect(service['notifications'].length).toBe(1);
    });

    it('should create a ToastComponent with toast data', () => {
      const component = service.show(data);

      expect(component.data).toEqual(data);
    });

    it('should not store notif if no persistance', () => {
      const dataNoPers: ToastData = {
        type: 'success',
        title: 'Test Title',
        description: `test description`,
        date: new Date(),
        isUnread: true,
        isPersistent: false,
      };

      service.show(dataNoPers);
      expect(service['notifications'].length).toBe(0);
    });
  });

  describe('After display', () => {
    let ref: ToastRef;
    let ref2: ToastRef;
    beforeEach(() => {
      ref = new ToastRef(overlayService.create(), service);
      ref2 = new ToastRef(overlayService.create(), service);
    });
    it('should not update toasts positions after display', () => {
      const spyPosition = jest.spyOn(service, 'getPositionStrategy');
      service['toasts'] = [ref];
      service.afterDisplay(ref);

      expect(spyPosition).toBeCalledTimes(0);
    });

    it('should update toasts positions after display', () => {
      const spyPosition = jest.spyOn(service, 'getPositionStrategy');
      service['toasts'] = [ref, ref2];
      service.afterDisplay(ref);

      expect(spyPosition).toBeCalledWith(0);
    });
  });

  describe('Close', () => {
    let ref: ToastRef;
    let ref2: ToastRef;
    beforeEach(() => {
      ref = new ToastRef(overlayService.create(), service);
      ref2 = new ToastRef(overlayService.create(), service);
    });

    it('should not update toasts positions after close ', () => {
      const spyPosition = jest.spyOn(service, 'getPositionStrategy');
      service['toasts'] = [ref, ref2];
      service.close(ref2);
      expect(spyPosition).toBeCalledTimes(0);
    });

    it('should update toasts positions after close', () => {
      const spyPosition = jest.spyOn(service, 'getPositionStrategy');
      service['toasts'] = [ref, ref2];
      service.close(ref);
      expect(spyPosition).toBeCalledWith(-1);
    });
  });

  describe('Get Position Strategy', () => {
    it('should use getPosition with index', () => {
      const spyPosition = jest.spyOn(service, 'getPosition');

      service.getPositionStrategy(-1);

      expect(spyPosition).toBeCalledWith(-1);
    });

    it('should return position with top = 63px', () => {
      const gp: GlobalPositionStrategy = service.getPositionStrategy(-1);
      expect(gp['_topOffset']).toBe('63px');
    });

    it('should return position with right = 16px', () => {
      const gp: GlobalPositionStrategy = service.getPositionStrategy(-1);
      expect(gp['_xOffset']).toBe('16px');
    });
  });

  describe('Get position', () => {
    it('should get position of "63px" for first toast', () => {
      expect(service.getPosition(-1)).toBe('63px');
    });

    it('should get position of "92px" for last toast visible', () => {
      const ref = new ToastRef(overlayService.create(), service);
      ref.isVisible = jest.fn(() => true);
      ref.getPosition = jest.fn(() => ({ bottom: 100 } as DOMRect));
      service['toasts'] = [ref];
      expect(service.getPosition(0)).toBe('92px');
    });
  });

  describe('Get Notifications', () => {
    it('should get Notifications', () => {
      const data: ToastData = {
        type: 'success',
        title: 'Test Title',
        description: `test description`,
        date: new Date(),
        isUnread: true,
        isPersistent: false,
      };
      service['notifications'] = [data];
      expect(service.getNotifications()).toEqual([data]);
    });
  });

  describe('Has unread notifications', () => {
    let data: ToastData;
    beforeEach(() => {
      data = {
        type: 'success',
        title: 'Test Title',
        description: `test description`,
        date: new Date(),
        isUnread: true,
        isPersistent: false,
      };
    });

    it('should has unread notifs', () => {
      service['notifications'] = [data];
      expect(service.hasUnreadNotifications()).toBeTruthy();
    });

    it('should not has unread notifs', () => {
      data.isUnread = false;
      // eslint-disable-next-line @typescript-eslint/dot-notation
      service['notifications'] = [data];
      expect(service.hasUnreadNotifications()).toBeFalsy();
    });
  });
});
