:host {
  display: block;
}
* {
  box-sizing: border-box;
}
.core-model-export-form {
  height: 100vh;
  display: flex;
  flex-direction: column;
  &__header {
    min-height: 2.875rem;
    display: flex;
    justify-content: flex-start;
  }
  &__content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(8, 1fr);
    @extend %form-contents;
  }
  &__version {
    grid-column: 1;
    grid-row: 1;
    display: flex;
    flex-direction: column;
  }

  &__name {
    grid-row: 2;
  }
  &__description {
    grid-column: 1/4;
    grid-row: 3;
    display: flex;
    flex-direction: column;
  }
  &__comment {
    grid-column: 1/4;
    grid-row: 4;
    display: flex;
    flex-direction: column;
  }
  &__footer {
    @extend %form-contents;
    display: flex;
    justify-content: flex-end;
  }
  &__version-option {
    display: flex;
    justify-content: space-between;
  }
}
mat-form-field {
  width: 100%;
}
%form-contents {
  min-height: 3.125rem;
  padding: 0 2rem;
}
