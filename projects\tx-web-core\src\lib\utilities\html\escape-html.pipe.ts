/* eslint-disable @typescript-eslint/naming-convention */
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'escapeHtml',
  standalone: true,
})
export class TxEscapeHtmlPipe implements PipeTransform {
  entityMap: { [key: string]: string } = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
    '/': '&#x2F;',
  };

  transform(value: string | undefined, replaceLB?: boolean): string {
    if (value === undefined) {
      return '';
    }
    const encodeValue = value.toString().replace(/[&<>'"/]/g, (s: string) => this.entityMap[s]);
    return replaceLB ? encodeValue.replace(/\n/g, (s: string) => '<br>') : encodeValue;
  }
}
