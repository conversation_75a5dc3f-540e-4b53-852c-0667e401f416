import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { LegacyTxObject } from '../../services/structure/models/object';
import { LegacyTxObjectTypeService } from '../../services/structure/services/object-type.service';
import { LegacyTxObjectsService } from '../../services/structure/services/objects.service';
import { TxComboboxComponent } from '../combobox/combobox.component';

@Component({
  selector: 'tx-combobox-object',
  templateUrl: '../combobox/combobox.component.html',
})
export class TxComboboxObjectComponent extends TxComboboxComponent implements OnInit, OnChanges {
  @Input() txObjects: LegacyTxObject[] = [];
  @Input() idObjectType!: number;
  @Input() idParentObjectFiltering = 0;
  @Input() recursive = true;
  @Input() includeFolder = false;
  @Input() displayObjectTypeIcons = false;

  constructor(
    public txObjectService: LegacyTxObjectsService,
    public txObjectTypeService: LegacyTxObjectTypeService
  ) {
    super();
  }

  ngOnInit() {
    super.ngOnInit();
    if (!this.txObjects || !this.txObjects.length) {
      this.txObjectService
        .listObjects(
          this.idObjectType,
          this.idParentObjectFiltering,
          this.recursive,
          this.includeFolder
        )
        .subscribe((txObject: LegacyTxObject[]) => {
          this.txObjects = txObject;
          if (this.displayObjectTypeIcons) {
            this.txObjects = this.txObjects.map((o) => {
              o.image = 'tx-icon-objectType-' + (o.isFolder ? 'folder' : o.image);
              return o;
            });
          }
          this.dataSource = this.txObjects;
        });
    }

    this.reloadOptions(this.txObjects);
  }

  ngOnChanges(changes: SimpleChanges) {
    super.ngOnChanges(changes);

    if (changes.txObjects && changes.txObjects.previousValue !== undefined) {
      this.reloadOptions(changes.txObjects.currentValue);
    }
  }

  reloadOptions(txObjects: LegacyTxObject[]) {
    this.txObjects = txObjects;
    this.txObjectTypeService.listFromIds([this.idObjectType]).subscribe((o) => {
      this.dataSource = this.txObjects.map((txObject) => {
        txObject.image = 'tx-icon-objectType-' + (txObject.isFolder ? 'folder' : o[0].icon);
        return txObject;
      });
    });
  }
}
