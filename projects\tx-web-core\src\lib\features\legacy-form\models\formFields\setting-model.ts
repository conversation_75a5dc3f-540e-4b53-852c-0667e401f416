import { SafeUrl } from '@angular/platform-browser';
import { LegacyTxAttribute } from '../../services/structure/models/attribute';
import { LegacyTxData } from '../../services/structure/models/data';

export enum TxFieldType {
  Boolean,
  Chips,
  Datepicker,
  Dropdown,
  File,
  InputNumberBounds,
  InputNumber,
  Mail,
  TextArea,
  TextInput,
}

export enum TxDropEffect {
  None = 'None',
  Default = 'Default',
  Copy = 'Copy',
  Move = 'Move',
  Link = 'Link ',
}

export enum TxObjectFieldType {
  Boolean,
  Date,
  File,
  Link,
  LongText,
  Mail,
  Point,
  ShortString,
  Table,
  Url,
}

export interface TxBaseFieldSettingModel {
  id?: string;

  label?: string;

  pattern?: RegExp;

  value?: any;

  required?: boolean;

  order?: number;

  placeHolder?: string;

  // information to fill a field
  information?: string;

  // fontawesome icon name
  icon?: string;

  // css classes names
  classes?: string;

  // define the min size of the field value
  minLength?: number;

  // define the max size of the field value
  maxLength?: number;

  // is the field editable
  disabled?: boolean;

  // numeric fields
  // define the lower bound of the field value
  minValue?: any;

  // define the upper bound of the field value
  maxValue?: any;

  // date format
  dateFormat?: string;

  // combo params
  options?: any[];

  multiple?: boolean;

  allowFiltering?: boolean;

  hideSelectedItem?: boolean;

  maximumSelectionLength?: number;

  popupHeight?: number;

  popupWidth?: number;

  selectAllText?: string;

  // booleans options
  trueCaption?: string;
  falseCaption?: string;

  // chips options
  visible?: boolean;
  selectable?: boolean;
  removable?: boolean;
  addOnBlur?: boolean;
  single?: boolean;
  chips?: TxChip[];

  // file setting
  allowedExtensions?: string;
  maxFileSize?: number;
  minFileSize?: number;
}

export interface TxFieldSettingModel extends TxBaseFieldSettingModel {
  type?: TxFieldType;
}

export interface TxInputSettingModel {
  pattern?: RegExp;
  placeHolder?: string;
  minLength?: number;
  maxLength?: number;
  minValue?: any;
  maxValue?: any;
  withBounds?: boolean;
  withMaxLength?: boolean;
  inputType?: string;
}

export interface TxDropDownSettingModel {
  options: any[];
  multiple?: boolean;
  allowFiltering?: boolean;
  hideSelectedItem?: boolean;
  maximumSelectionLength?: number;
  popupHeight?: number;
  popupWidth?: number;
  selectAllText?: string;
}

export interface TxFileSettingModel {
  allowedExtensions?: string;
  maxFileSize?: number;
  minFileSize?: number;
  multiple?: boolean;
}

export interface TxBooleanSettingModel {
  trueCaption?: string;
  falseCaption?: string;
}

export interface TxObjectFieldSettingModel extends TxBaseFieldSettingModel {
  attribute: LegacyTxAttribute;
  type?: TxObjectFieldType;
  data?: LegacyTxData;
}

export interface TxBaseFormSettingsModel {
  name?: string;
}

export interface TxFormSettingsModel extends TxBaseFormSettingsModel {
  fields: TxFieldSettingModel[];
}

export interface TxObjectFormSettingsModel extends TxBaseFormSettingsModel {
  fields: TxObjectFieldSettingModel[];
}

export interface TxFormStepperSettingModel {
  steps: TxFormSettingsModel[];
}

export interface TxChip {
  name: string;
  selectable?: boolean;
  selected?: boolean;
  removable?: boolean;
  icon?: string[];
  viewableFile?: boolean;
  fileUrl?: SafeUrl;
  idFile?: number;
}
