import { TestBed, fakeAsync, flush, tick } from '@angular/core/testing';
import { TxGridService } from '../grid.service';
import { TreeGridComponent } from '@syncfusion/ej2-angular-treegrid';
import { GridComponent, RowInfo } from '@syncfusion/ej2-angular-grids';
import { ElementRef } from '@angular/core';

describe('GridService', () => {
  let service: TxGridService;
  let inputSearch: ElementRef;
  let grid: GridComponent | TreeGridComponent;
  let contentElement: ElementRef;
  let event: KeyboardEvent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [TxGridService],
    });
    service = TestBed.inject(TxGridService);
    inputSearch = new ElementRef(document.createElement('input'));
    contentElement = new ElementRef(document.createElement('div'));
    event = new KeyboardEvent('keyup', { code: 'Enter' });
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Reset Search Test', () => {
    it('should return undefined', () => {
      expect(service.resetSearch()).toBeUndefined(); // Check if the result is undefined.
    });

    it('Check if the searchId property is undefined after calling the method', () => {
      service.searchId = 42;
      service.resetSearch();
      expect(service.searchId).toBeUndefined();
    });
  });

  describe('Search', () => {
    let searchByIDSpy: any;
    let searchByTextSpy: any;

    beforeEach(() => {
      grid = TestBed.createComponent(GridComponent).componentInstance;
      searchByIDSpy = jest.spyOn(service, 'searchByID');
      searchByTextSpy = jest.spyOn(service, 'searchByText');
    });

    describe('Input value is a number', () => {
      beforeEach(() => {
        inputSearch.nativeElement.value = '123';
        grid.getRowByIndex = jest.fn().mockReturnValue(null);
      });

      it('searchByID method should be called', () => {
        (grid as GridComponent).getRowIndexByPrimaryKey = jest.fn().mockReturnValue(0);
        service.search(inputSearch, grid, contentElement, event, false);
        expect(searchByIDSpy).toHaveBeenCalledWith(123, inputSearch, grid, contentElement, false);
      });

      describe('Id found', () => {
        beforeEach(() => {
          (grid as GridComponent).getRowIndexByPrimaryKey = jest.fn().mockReturnValue(0);
        });

        it('should return the the input value', () => {
          const result = service.search(inputSearch, grid, contentElement, event, false);
          expect(result).toBe(123);
        });

        it('should not call searchByText', () => {
          service.search(inputSearch, grid, contentElement, event, false);
          expect(searchByTextSpy).not.toHaveBeenCalled();
        });
      });

      describe('Id not found', () => {
        beforeEach(() => {
          (grid as GridComponent).getRowIndexByPrimaryKey = jest.fn().mockReturnValue(-1);
        });

        it('should return undefined', () => {
          const result = service.search(inputSearch, grid, contentElement, event, false);
          expect(result).toBeUndefined();
        });

        it('should call searchByText', () => {
          service.search(inputSearch, grid, contentElement, event, false);
          expect(searchByTextSpy).toHaveBeenCalled();
        });
      });
    });

    describe('Input value is a text', () => {
      beforeEach(() => {
        inputSearch.nativeElement.value = 'text';
      });

      it('should not call searchByID', () => {
        service.search(inputSearch, grid, contentElement, event, false);
        expect(searchByIDSpy).not.toHaveBeenCalled();
      });

      it('should call searchByText', () => {
        service.search(inputSearch, grid, contentElement, event, false);
        expect(searchByTextSpy).toHaveBeenCalled();
      });

      it('should return undefined', () => {
        const result = service.search(inputSearch, grid, contentElement, event, false);
        expect(result).toBeUndefined();
      });
    });
  });

  describe('Search by Id', () => {
    let filterValue: number;
    let divMarks: HTMLDivElement;
    beforeEach(() => {
      filterValue = 24;
      grid = TestBed.createComponent(GridComponent).componentInstance;
      divMarks = document.createElement('div');
      const rowMock = document.createElement('rowMock');
      divMarks.appendChild(rowMock);
    });

    describe('id found', () => {
      beforeEach(() => {
        (grid as GridComponent).getRowIndexByPrimaryKey = jest.fn().mockReturnValue(0);
        grid.getRowByIndex = jest.fn().mockReturnValue(divMarks.childNodes[0]);
        grid.getRowInfo = jest
          .fn()
          .mockReturnValueOnce({
            rowIndex: 1,
            row: divMarks.childNodes[0],
            column: {},
            rowData: {},
          });
      });

      it('should set the search id to the filtervalue', () => {
        service.searchId = -1;
        service.searchByID(filterValue, inputSearch, grid, contentElement, false);
        expect(service.searchId).toBe(24);
      });

      it('should select row if isRowSelected set to true', () => {
        const spySelectRow = jest.spyOn(grid, 'selectRow');
        service.searchByID(filterValue, inputSearch, grid, contentElement, true);
        expect(spySelectRow).toBeCalled();
      });

      it('should mark row as selected if isRowSelected set to false', () => {
        service.searchByID(filterValue, inputSearch, grid, contentElement, false);
        const isSelected =
          divMarks.childNodes[0].parentElement?.firstElementChild?.classList.contains('selected');
        expect(isSelected).toBe(true);
      });

      it('should return the search id', () => {
        const result = service.searchByID(filterValue, inputSearch, grid, contentElement, false);
        expect(result).toBe(24);
      });
    });

    describe('id not found', () => {
      beforeEach(() => {
        (grid as GridComponent).getRowIndexByPrimaryKey = jest.fn().mockReturnValue(-1);
        grid.getRowByIndex = jest.fn().mockReturnValue(null);
      });

      it('should set the search id to undefined', () => {
        service.searchId = -1;
        service.searchByID(filterValue, inputSearch, grid, contentElement, false);
        expect(service.searchId).toBeUndefined();
      });

      it('should return the search id', () => {
        const result = service.searchByID(filterValue, inputSearch, grid, contentElement, false);
        expect(result).toBeUndefined();
      });
    });
  });

  describe('Search By Text', () => {
    let divMarks: HTMLDivElement;

    beforeEach(() => {
      grid = TestBed.createComponent(TreeGridComponent).componentInstance;
      divMarks = document.createElement('div');
      const div1 = document.createElement('div');
      const mark1 = document.createElement('mark');
      mark1.classList.add('first');
      div1.appendChild(mark1);
      divMarks.appendChild(div1);
      const div2 = document.createElement('div');
      const mark2 = document.createElement('mark');
      mark2.classList.add('second');
      div2.appendChild(mark2);
      divMarks.appendChild(div2);
      const div3 = document.createElement('div');
      const mark3 = document.createElement('mark');
      mark3.classList.add('third');
      div3.appendChild(mark3);
      divMarks.appendChild(div3);
      contentElement = new ElementRef(divMarks);
      service.expandParentRows = jest.fn();
      grid.clearSelection = jest.fn();
      grid.selectRow = jest.fn();
      divMarks.scrollIntoView = jest.fn();
      grid.getRowInfo = jest.fn().mockReturnValue({ rowIndex: 1, rowData: { id: 24 }, column: {} });
    });

    describe('isRowSelected is set to true', () => {
      it('should select first line if no marks selected', () => {
        const spySelect = jest.spyOn(service, 'selectRow').mockImplementation(jest.fn());
        grid.getSelectedRowIndexes = jest.fn().mockReturnValue([]);

        service.searchByText(inputSearch, grid, contentElement, true, true);

        expect(spySelect).toBeCalledWith(
          divMarks.childNodes[0].firstChild,
          inputSearch,
          grid,
          true
        );
      });

      it('should select next line', () => {
        const spySelect = jest.spyOn(service, 'selectRow').mockImplementation(jest.fn());
        grid.getSelectedRowIndexes = jest.fn().mockReturnValue([1]);
        grid.getRowInfo = jest
          .fn()
          .mockReturnValueOnce({ column: {}, rowIndex: 2 })
          .mockReturnValueOnce({ column: {}, rowIndex: 2 })
          .mockReturnValueOnce({ column: {}, rowIndex: 1 })
          .mockReturnValueOnce({ column: {}, rowIndex: 1 });

        service.searchByText(inputSearch, grid, contentElement, true, true);

        expect(spySelect).toBeCalledWith(
          divMarks.childNodes[2].firstChild,
          inputSearch,
          grid,
          true
        );
      });

      it('should select first line after last one', () => {
        const spySelect = jest.spyOn(service, 'selectRow').mockImplementation(jest.fn());
        grid.getSelectedRowIndexes = jest.fn().mockReturnValue([2]);
        grid.getRowInfo = jest.fn().mockReturnValue({ column: {}, rowIndex: 2 });

        service.searchByText(inputSearch, grid, contentElement, true, true);

        expect(spySelect).toBeCalledWith(
          divMarks.childNodes[0].firstChild,
          inputSearch,
          grid,
          true
        );
      });
    });

    describe('isRowSelected is set to false', () => {
      it('should select first line if no marks selected', () => {
        const spySelect = jest.spyOn(service, 'selectRow').mockImplementation(jest.fn());
        service.searchByText(inputSearch, grid, contentElement, false, true);

        expect(spySelect).toBeCalledWith(
          divMarks.childNodes[0].firstChild,
          inputSearch,
          grid,
          false
        );
      });

      it('should select next line', () => {
        const spySelect = jest.spyOn(service, 'selectRow').mockImplementation(jest.fn());
        divMarks.children[1]?.children[0]?.classList.add('selected');
        service.searchByText(inputSearch, grid, contentElement, false, true);

        expect(spySelect).toBeCalledWith(
          divMarks.childNodes[2].firstChild,
          inputSearch,
          grid,
          false
        );
      });

      it('should select first line after last one', () => {
        const spySelect = jest.spyOn(service, 'selectRow').mockImplementation(jest.fn());
        divMarks.children[2]?.children[0]?.classList.add('selected');
        service.searchByText(inputSearch, grid, contentElement, false, true);

        expect(spySelect).toBeCalledWith(
          divMarks.childNodes[0].firstChild,
          inputSearch,
          grid,
          false
        );
      });
    });
  });

  describe('Select Row', () => {
    let divMock: HTMLDivElement;
    let divMarks: HTMLDivElement;
    let spyClear: jest.SpyInstance<void, []>;
    let spySelectRow: jest.SpyInstance<void, [index: number, isToggle?: boolean | undefined]>;
    let spyExpandParentRows: jest.SpyInstance<void, [rowInfo: RowInfo, grid: TreeGridComponent]>;

    beforeEach(() => {
      grid = TestBed.createComponent(TreeGridComponent).componentInstance;
      divMarks = document.createElement('div');
      const mark = document.createElement('mark');
      mark.classList.add('first');
      divMarks.appendChild(mark);
      divMock = document.createElement('div');
      service.expandParentRows = jest.fn();
      grid.clearSelection = jest.fn();
      grid.selectRow = jest.fn();
      divMock.scrollIntoView = jest.fn();
      spyClear = jest.spyOn(grid, 'clearSelection');
      spySelectRow = jest.spyOn(grid, 'selectRow');
      spyExpandParentRows = jest.spyOn(service, 'expandParentRows');
    });

    describe('Expand parents row', () => {
      beforeEach(() => {
        grid.getRowInfo = jest
          .fn()
          .mockReturnValue({ rowIndex: 1, row: divMock, rowData: {}, column: {} });
      });

      it('should call expandParentsRow if grid is TreeGridComponent', () => {
        service.selectRow(divMarks.childNodes[0], inputSearch, grid, false);
        expect(spyExpandParentRows).toBeCalled();
      });

      it('should not call expandParentsRow if grid is not TreeGridComponent', () => {
        service.selectRow(
          divMarks.childNodes[0],
          inputSearch,
          TestBed.createComponent(GridComponent).componentInstance,
          false
        );
        expect(spyExpandParentRows).not.toBeCalled();
      });
    });

    describe('isRowSelected parameter is set to true', () => {
      it('should clear selection of grids', () => {
        grid.getRowInfo = jest
          .fn()
          .mockReturnValue({ rowIndex: 1, row: divMock, rowData: {}, column: {} });
        service.selectRow(divMarks.childNodes[0], inputSearch, grid, true);
        expect(spyClear).toBeCalled();
      });

      it('should select row when rowIndex is not undefined', () => {
        grid.getRowInfo = jest
          .fn()
          .mockReturnValue({ rowIndex: 1, row: divMock, column: {}, rowData: {} });
        service.selectRow(divMarks.childNodes[0], inputSearch, grid, true);
        expect(spySelectRow).toBeCalled();
      });

      it('should not select row when rowIndex is undefined', () => {
        grid.getRowInfo = jest
          .fn()
          .mockReturnValue({ rowIndex: undefined, row: divMock, column: {}, rowData: {} });
        service.selectRow(divMarks.childNodes[0], inputSearch, grid, true);
        expect(spySelectRow).not.toBeCalled();
      });

      it('the first element should not have the "selected" class', () => {
        grid.getRowInfo = jest
          .fn()
          .mockReturnValue({ rowIndex: 1, row: divMock, column: {}, rowData: {} });
        service.selectRow(divMarks.childNodes[0], inputSearch, grid, true);
        const isSelected =
          divMarks.childNodes[0].parentElement?.firstElementChild?.classList.contains('selected');
        expect(isSelected).toBe(false);
      });
    });

    describe('isRowSelected parameter is set to false', () => {
      beforeEach(() => {
        grid.getRowInfo = jest
          .fn()
          .mockReturnValue({ rowIndex: 1, row: divMock, rowData: {}, column: {} });
      });

      it('should not clear selection of grids', () => {
        service.selectRow(divMarks.childNodes[0], inputSearch, grid, false);
        expect(spyClear).not.toBeCalled();
      });

      it('should not select row ', () => {
        service.selectRow(divMarks.childNodes[0], inputSearch, grid, false);
        expect(spySelectRow).not.toBeCalled();
      });

      it('should add "selected" class', () => {
        service.selectRow(divMarks.childNodes[0], inputSearch, grid, false);
        const isSelected =
          divMarks.childNodes[0].parentElement?.firstElementChild?.classList.contains('selected');
        expect(isSelected).toBe(true);
      });
    });

    describe('Should focus input', () => {
      beforeEach(() => {
        grid.getRowInfo = jest
          .fn()
          .mockReturnValue({ rowIndex: 1, row: divMock, rowData: {}, column: {} });
      });

      it('should focus the input after 100ms', fakeAsync(() => {
        const spyFocus = jest.spyOn(inputSearch.nativeElement, 'focus');
        service.selectRow(divMarks.childNodes[0], inputSearch, grid, true);
        tick(100);
        expect(spyFocus).toBeCalled();

        flush();
      }));

      it('should scroll the row into the view after 300ms', fakeAsync(() => {
        const spyScroll = jest.spyOn(divMock, 'scrollIntoView');
        service.selectRow(divMarks.childNodes[0], inputSearch, grid, false);
        tick(300);
        expect(spyScroll).toBeCalledWith({ behavior: 'smooth', block: 'center' });

        flush();
      }));
    });
  });
});
