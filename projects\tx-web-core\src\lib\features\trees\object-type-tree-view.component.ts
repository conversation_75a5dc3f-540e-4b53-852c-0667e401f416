import { SelectionModel } from '@angular/cdk/collections';
import { FlatTreeControl } from '@angular/cdk/tree';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
} from '@angular/core';
import { MatTreeFlattener, MatTreeFlatDataSource, MatTreeModule } from '@angular/material/tree';
import { TxTreeViewService } from './object-type-tree-view.service';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { FlatTreeNode, TreeData, TreeDataOptions, TreeNode } from './object-type-tree-view.model';
import { TxIsNodeDisabledPipe } from './is-node-disabled.pipe';

@Component({
  standalone: true,
  imports: [
    MatTreeModule,
    MatCheckboxModule,
    FontAwesomeModule,
    TranslateModule,
    CommonModule,
    TxIsNodeDisabledPipe,
  ],
  selector: 'tx-object-type-tree-view',
  templateUrl: './object-type-tree-view.component.html',
  styleUrls: ['./object-type-tree-view.component.scss'],
  providers: [TxTreeViewService, TxIsNodeDisabledPipe],
})
export class TxObjectTypeTreeViewComponent<T extends TreeData & { id?: number }>
  implements OnInit, OnChanges
{
  /**
   * Generate a tree view from a list of any object.
   *
   * @Input treeData The data source of the tree. It can be an array of any object, the only restriction is that it must have a "name" attribute. Mandatory
   * @Input treeDataOptions To specify how id and idParent are named in the source list (treeData). Can be {idProperty: 'id', idParentProperty: 'idObjectTypeParent'} for example. Mandatory to build the tree correctly.
   * @Input nodeTemplate Template Ref to have a custom node template. The node data is accessible in this template using node.objectData.
   * @Input showCheckBox Displays checkboxes in front of nodes.
   * @Input multipleSelection Allow multiple selection.
   * @Input hierarchicalSelection If the option is enabled, the parent will be automatically selected if all its children are selected. Otherwise, selections are independent of the hierarchy.
   */

  @Input() treeData: T[] = [];
  @Input() treeDataOptions: TreeDataOptions | undefined;
  @Input() filteredTreeData: T[] = [];
  @Input() nodeTemplate?: TemplateRef<any>;
  @Input() checkedIds: (number | string)[] = []; // It corresponds to the "idProperty" value defined in "treeDataOptions". By default it's the id
  @Input() disabledIds?: (number | string)[] = [];
  @Input() showCheckBox?: boolean;
  @Input() multipleSelection?: boolean;
  @Input() hierarchicalSelection?: boolean;

  @Output() selectionChange = new EventEmitter<FlatTreeNode<T>[]>();

  flatNodeMap = new Map<FlatTreeNode<T>, TreeNode<T>>();
  nestedNodeMap = new Map<TreeNode<T>, FlatTreeNode<T>>();
  selectedParent: FlatTreeNode<T> | null = null;
  newItemName = '';

  // sources
  treeControl!: FlatTreeControl<FlatTreeNode<T>>;
  treeFlattener!: MatTreeFlattener<TreeNode<T>, FlatTreeNode<T>>;
  dataSource!: MatTreeFlatDataSource<TreeNode<T>, FlatTreeNode<T>>;
  checklistSelection!: SelectionModel<FlatTreeNode<T>>;
  checkListDisabled: (string | number)[] = [];
  currentTreeData: T[] = [];

  selectedNode: FlatTreeNode<T> | undefined = undefined;

  constructor(
    private readonly treeService: TxTreeViewService<T>,
    private readonly TxIsNodeDisabledPipe: TxIsNodeDisabledPipe
  ) {}

  ngOnInit() {
    /* This is because if we have a parent component that itself has input properties but which are not defined,
       it passes undefined and the initialization at @Input level is ignored.  */
    this.multipleSelection ??= true;
    this.showCheckBox ??= true;
    this.hierarchicalSelection ??= false;
    /* if no option and no ids, give some ids to display tree as list */
    if (this.treeDataOptions === undefined) {
      this.treeDataOptions = { idProperty: 'id', idParentProperty: 'idParent' };
      if (this.treeData?.length > 0 && !this.treeData[0].id) {
        let givenID = 0;
        for (const data of this.treeData) {
          data.id = givenID;
          givenID++;
        }
      }
    }

    this.checklistSelection = new SelectionModel<FlatTreeNode<T>>(
      this.multipleSelection /* multiple */
    );
    this.treeFlattener = new MatTreeFlattener(
      this.transformer,
      this.getLevel,
      this.isExpandable,
      this.getChildren
    );
    this.treeControl = new FlatTreeControl<FlatTreeNode<T>>(this.getLevel, this.isExpandable);
    this.dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);
    this.dataSource.data = this.treeService.buildTree(this.treeData, this.treeDataOptions);

    this.currentTreeData = this.treeData;

    if (this.checkedIds && this.checkedIds.length > 0) {
      this.updateCheckedIds(false, [...this.checkedIds]);
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (this.dataSource) {
      if (changes.treeData) {
        this.updateTreeAndSelection(changes.treeData.currentValue, false);
      }
      if (changes.filteredTreeData) {
        this.updateTreeAndSelection(changes.filteredTreeData.currentValue, true);
      }
      if (changes.checkedIds) {
        this.updateCheckedIds(true, changes.checkedIds.currentValue);
      }
    }
  }

  onSelectionChange(node: FlatTreeNode<T>) {
    const nodeID: number | string = node.objectData[this.treeDataOptions?.idProperty ?? 'id'] as
      | string
      | number;
    if (this.multipleSelection) {
      const existingNodeIndex = this.checkedIds.findIndex((checkedId) => checkedId === nodeID);
      if (existingNodeIndex === -1) {
        this.checkedIds = [...this.checkedIds, nodeID];
      } else {
        this.checkedIds.splice(existingNodeIndex, 1);
      }
    } else {
      this.checkedIds = [nodeID];
    }
    const checkedNodes = this.treeControl.dataNodes.filter((node) =>
      this.checkedIds.includes(
        node.objectData[this.treeDataOptions?.idProperty ?? 'id'] as string | number
      )
    );
    this.selectionChange.emit(checkedNodes);
  }

  /** Misc Tree functions **/

  getLevel = (node: FlatTreeNode<T>) => node.level;

  isExpandable = (node: FlatTreeNode<T>) => node.expandable;

  getChildren = (node: TreeNode<T>): TreeNode<T>[] => node.children;

  hasChild = (_: number, nodeData: FlatTreeNode<T>) => nodeData.expandable;

  hasNoContent = (_: number, nodeData: FlatTreeNode<T>) => nodeData.name === '';

  transformer = (node: TreeNode<T>, level: number) => {
    const existingNode = this.nestedNodeMap.get(node);
    const flatNode: FlatTreeNode<T> =
      existingNode && existingNode.name === node.name
        ? existingNode
        : {
            name: node.name,
            level,
            expandable: node.children.length > 0,
            objectData: node.objectData,
          };
    this.flatNodeMap.set(flatNode, node);
    this.nestedNodeMap.set(node, flatNode);
    return flatNode;
  };

  checkNodesByIDs(nodeIDs: (string | number)[]) {
    const checkedNodes = this.treeControl.dataNodes.filter((node) =>
      nodeIDs.includes(node.objectData[this.treeDataOptions?.idProperty ?? 'id'] as number)
    );
    this.checklistSelection.select(...checkedNodes);
  }

  disableNodesByIDs(nodeIDs: (number | string)[]) {
    const disabledNodes = this.treeControl.dataNodes.filter((node) =>
      nodeIDs.includes(node.objectData[this.treeDataOptions?.idProperty ?? 'id'] as string | number)
    );
    this.checkListDisabled = disabledNodes.map(
      (node) => node.objectData[this.treeDataOptions?.idProperty ?? 'id'] as string | number
    );
    this.checkNodesByIDs(nodeIDs);
  }

  checkAll() {
    for (const node of this.treeControl.dataNodes) {
      if (!this.checklistSelection.isSelected(node)) {
        this.checklistSelection.toggle(node);
      }
      this.treeControl.expand(node);
    }
  }

  uncheckAll(isClear?: boolean) {
    for (const node of this.treeControl.dataNodes) {
      if (
        this.checklistSelection.isSelected(node) &&
        !this.TxIsNodeDisabledPipe.transform(node, this.checkedIds, this.treeDataOptions)
      ) {
        this.checklistSelection.toggle(node);
      }
    }
    this.checkedIds = this.checkedIds.filter((id) => this.disabledIds?.includes(id));
    if (isClear) {
      const checkedNodes = this.treeControl.dataNodes.filter((node) =>
        this.checkedIds.includes(
          node.objectData[this.treeDataOptions?.idProperty ?? 'id'] as string | number
        )
      );
      this.selectionChange.emit(checkedNodes);
    }
  }

  /** Whether all the descendants of the node are selected. */
  descendantsAllSelected(node: FlatTreeNode<T>): boolean {
    const descendants = this.treeControl.getDescendants(node);
    const descAllSelected = descendants.every((child) => this.checklistSelection.isSelected(child));
    return descAllSelected;
  }

  /** Whether part of the descendants are selected */
  descendantsPartiallySelected(node: FlatTreeNode<T>): boolean {
    const descendants = this.treeControl.getDescendants(node);
    const result = descendants.some((child) => this.checklistSelection.isSelected(child));
    return result && !this.descendantsAllSelected(node);
  }

  /** Toggle the to-do item selection. Select/deselect all the descendants node */
  itemSelectionToggle(node: FlatTreeNode<T>): void {
    this.checklistSelection.toggle(node);
    const descendants = this.treeControl.getDescendants(node);
    if (this.checklistSelection.isSelected(node)) {
      this.checklistSelection.select(...descendants);
    } else {
      this.checklistSelection.deselect(...descendants);
    }
    this.checkAllParentsSelection(node);
    this.onSelectionChange(node);
  }

  updateTreeAndSelection(newData: T[], isFilteredData: boolean) {
    this.currentTreeData = newData;

    if (newData.length === 0) {
      if (isFilteredData) {
        newData = [...this.treeData];
      } else {
        this.currentTreeData = [];
        return;
      }
    } else {
      let newBuildedTree: TreeNode<T>[];

      if (isFilteredData) {
        this.filteredTreeData = [...newData];
        newBuildedTree = this.treeService.buildFilteredTree(
          this.treeData,
          this.filteredTreeData,
          this.treeDataOptions
        );
      } else {
        this.treeData = [...newData];
        newBuildedTree = this.treeService.buildTree(this.treeData, this.treeDataOptions);
      }

      this.checklistSelection = new SelectionModel<FlatTreeNode<T>>(
        this.multipleSelection /* multiple */
      );
      this.dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);
      this.dataSource.data = newBuildedTree;

      this.checkNodesByIDs(this.checkedIds);
      this.treeControl.expandAll();
    }
  }

  /** Toggle a leaf to-do item selection. Check all the parents to see if they changed */
  leafItemSelectionToggle(node: FlatTreeNode<T>): void {
    this.checklistSelection.toggle(node);
    if (this.hierarchicalSelection) {
      this.checkAllParentsSelection(node);
    }
    this.onSelectionChange(node);
  }

  /* Checks all the parents when a leaf node is selected/unselected */
  checkAllParentsSelection(node: FlatTreeNode<T>): void {
    let parent: FlatTreeNode<T> | null = this.getParentNode(node);
    while (parent) {
      this.checkRootNodeSelection(parent);
      parent = this.getParentNode(parent);
    }
  }

  /** Check root node checked state and change it accordingly */
  checkRootNodeSelection(node: FlatTreeNode<T>): void {
    const nodeSelected = this.checklistSelection.isSelected(node);
    const descendants = this.treeControl.getDescendants(node);
    const descAllSelected = descendants.every((child) => this.checklistSelection.isSelected(child));
    if (nodeSelected && !descAllSelected) {
      this.checklistSelection.deselect(node);
    } else if (!nodeSelected && descAllSelected) {
      this.checklistSelection.select(node);
    }
  }

  /* Get the parent node of a node */
  getParentNode(node: FlatTreeNode<T>): FlatTreeNode<T> | null {
    const currentLevel = this.getLevel(node);
    if (currentLevel < 1) {
      return null;
    }

    const startIndex = this.treeControl.dataNodes.indexOf(node) - 1;

    for (let i = startIndex; i >= 0; i--) {
      const currentNode = this.treeControl.dataNodes[i];

      if (this.getLevel(currentNode) < currentLevel) {
        return currentNode;
      }
    }
    return null;
  }

  updateCheckedIds(reset: boolean, checkedIds: (string | number)[]) {
    if (reset) {
      this.uncheckAll();
    }
    this.checkedIds = [...checkedIds];
    if (this.checkedIds) {
      this.checkNodesByIDs(checkedIds);
    }
    if (this.disabledIds) {
      this.disableNodesByIDs(this.disabledIds);
    }
  }
  
  onNodeClicked(node: FlatTreeNode<T>) {
    if (!this.showCheckBox) {
      this.leafItemSelectionToggle(node);
    }
  }

  onSingleSelection(node: FlatTreeNode<T>) {
    if (!this.multipleSelection && this.showCheckBox) {
      this.leafItemSelectionToggle(node);
    }
  }
}
