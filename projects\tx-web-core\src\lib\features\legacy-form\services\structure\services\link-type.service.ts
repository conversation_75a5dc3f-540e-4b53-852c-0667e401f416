import { LegacyTxLinkType } from './../models/link-type';
import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { TxApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class TxLinkTypeService {
  linkTypes: LegacyTxLinkType[] = [];

  linkTypesLoaded: Subject<boolean> = new Subject<boolean>();

  initialized = false;

  private fill(linkType: any): LegacyTxLinkType {
    const newLinkType = new LegacyTxLinkType(linkType);

    this.linkTypes.push(newLinkType);

    return newLinkType;
  }

  private add(linkTypes: any[]): LegacyTxLinkType[] {
    return linkTypes.map((l) => this.fill(l));
  }

  constructor(public apiService: TxApiService) {
    this.linkTypesLoaded.subscribe((value) => {
      this.initialized = true;
    });
  }

  start() {
    this.apiService.listLinkTypes().subscribe((linkTypes: LegacyTxLinkType[]) => {
      // setTimeout(() => {
      this.add(linkTypes);

      this.linkTypesLoaded.next(true);
      this.linkTypesLoaded.complete();
      // }, 2000);
    });
  }

  get(idLinkType: number): LegacyTxLinkType {
    return this.linkTypes.find((l) => l.id === idLinkType) as LegacyTxLinkType;
  }
}
