import { TemplateRef } from '@angular/core';
import {
  PageEventArgs,
  FilterEventArgs,
  SortEventArgs,
  SearchEventArgs,
  AddEventArgs,
  SaveEventArgs,
  EditEventArgs,
  DeleteEventArgs,
} from '@syncfusion/ej2-angular-grids';
import { TxGridObject } from '@bassetti-group/tx-web-core/src/lib/features/grid';
import {
  CTxAttributeSetLevel,
  TxObjectType,
} from '@bassetti-group/tx-web-core/src/lib/business-models';

export interface TxEditInfo {
  rowIndex: number;
  column: string;
}

export interface TxTreeGrid<T> extends TxGridObject<T> {
  idParent?: number;
  isParent?: boolean;
  expanded?: boolean;
  uniqueIdParent?: string;
  objectType?: TxObjectType;
}

export interface TxTreeGridColumn {
  field: string;
  headerText?: string;
  displayAsCheckBox?: boolean;
  type?: string;
  allowEditing?: boolean;
  width?: string; // in px
  template?: TemplateRef<Element>; // in px
}
export interface TxAttributeSelectionGrid {
  uniqueId: string;
  idAttribute: number;
  uniqueIdParent?: string;
}
export interface TxAttributeCheckChangeEvent<T = any> {
  attributeSetLevels: CTxAttributeSetLevel[];
  rowChecked?: TxTreeGrid<T>;
  isChecked?: boolean;
}

export interface TxAttributeSelectionEvent<T = any> {
  data: TxTreeGrid<T>;
  isChecked: boolean;
}
export enum TxTreeGridActionsType {
  Refresh = 'refresh',
  Save = 'save',
  Edit = 'edit',
}

export enum TxCheckBoxState {
  Check = 'check',
  UnCheck = 'uncheck',
}

export type TxActionEvents =
  | PageEventArgs
  | FilterEventArgs
  | SortEventArgs
  | SearchEventArgs
  | AddEventArgs
  | SaveEventArgs
  | EditEventArgs
  | DeleteEventArgs;
