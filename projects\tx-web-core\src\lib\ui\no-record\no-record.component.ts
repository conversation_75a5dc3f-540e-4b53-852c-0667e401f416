import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'tx-no-record',
  standalone: true,
  imports: [FontAwesomeModule, CommonModule, TranslateModule, MatButtonModule],
  templateUrl: './no-record.component.html',
  styleUrls: ['./no-record.component.scss'],
})
export class NoRecordComponent {
  @Input() noRecordText = _('txWebCore.generic.noRecordToDisplay');
  @Input() additionalText = '';
  @Input() icon = 'empty-set';
  @Input() displayAction = false;

  @Output() clickAction = new EventEmitter<any>();
}
