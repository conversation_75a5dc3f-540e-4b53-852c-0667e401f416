import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AccessRightsGuard } from 'src/app/core/guards/access-rights.guard';
import { AuthenticationGuard } from 'src/app/core/guards/authentication.guard';
import { AuditsComponent } from './components/audits.component';
import { AdminRights } from '@bassetti-group/tx-web-core';

const routes: Routes = [
  {
    path: '',
    component: AuditsComponent,
    data: { breadcrumb: 'admins.wording.audit', adminRights: AdminRights.IsAdmin },
    canActivate: [AuthenticationGuard, AccessRightsGuard],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AuditsRoutingModule {}
