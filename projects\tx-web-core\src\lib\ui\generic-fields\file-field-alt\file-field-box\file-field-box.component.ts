import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TxDataBaseAction, TxFile } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FilesUtils } from 'projects/tx-web-core/src/lib/utilities/helpers/files-utils';

@Component({
  selector: 'tx-file-field-box',
  templateUrl: './file-field-box.component.html',
  styleUrls: ['./file-field-box.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FontAwesomeModule,
    MatSlideToggleModule,
    MatProgressBarModule,
    MatTooltipModule,
    MatChipsModule,
  ],
})
export class TxFileFieldBoxComponent implements OnChanges {
  @Input({ required: true }) file!: TxFile;
  @Input() icon!: string[];
  @Input() hideVisualisationToggle = true;
  @Output() removed = new EventEmitter<TxFile>();

  constructor() {}

  get hideToggle() {
    return this.hideVisualisationToggle && !this.fileUploading;
  }

  get fileUploading() {
    return this.file?.uploadProgress;
  }

  ngOnChanges() {
    if (this.file) {
      this.icon = FilesUtils.getFileIcon(this.file.name);
    }
  }

  removeFile() {
    this.removed.emit(this.file);
  }

  getFileSize() {
    if (this.file && this.file.size > 0) {
      return FilesUtils.fileSizeToDisplay(this.file.size);
    }
    return null;
  }

  changeView() {
    this.file.view = !this.file.view;
    if (this.file.action === TxDataBaseAction.None) {
      this.file.action = TxDataBaseAction.Modify;
    }
  }
}
