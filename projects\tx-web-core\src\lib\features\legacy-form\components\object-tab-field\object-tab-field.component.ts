import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  OnChanges,
  SimpleChanges,
  QueryList,
  ViewChildren,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { LegacyTxData, LegacyTxDataType } from '../../services/structure/models/data';
import { TxGroupAttributeField } from '../../models/formConfiguration/businessClass/group-attribute-field';
import { TxTabAttributeField } from '../../models/formConfiguration/businessClass/tab-attribute-field';
import { TxVirtualAttributeField } from '../../models/formConfiguration/businessClass/virtual-attribute-field';
import { LegacyTxFormsService } from '../../services/forms.service';
import { TxObjectFieldComponent } from '../object-fields/_system/object-field/object-field.component';
import { LegacyTxObjectGroupFieldComponent } from '../object-group-field/object-group-field.component';

@Component({
  selector: 'tx-object-tab-field',
  templateUrl: './object-tab-field.component.html',
  styleUrls: [
    './object-tab-field.component.scss',
    '../container-field/container-field.component.scss',
  ],
})
export class LegacyTxObjectTabFieldComponent implements OnInit, OnChanges {
  @Input() form!: FormGroup;
  @Input() tab!: TxTabAttributeField;
  @Input() idObject!: number;
  @Input() index!: number;
  @Input() formConfigTag!: string;
  @Input() readMode!: boolean;
  @Output() loadEvent = new EventEmitter();
  @Output() onTabChange = new EventEmitter();

  @ViewChildren('fieldComponent') fieldComponents!: QueryList<TxObjectFieldComponent>;
  @ViewChildren('groupComponent') groupComponents!: QueryList<LegacyTxObjectGroupFieldComponent>;

  // @ViewChild('container') containerField: TxContainerFieldComponent;
  @Output() displayPaneEvent = new EventEmitter<any>();

  listAttributesIds: number[] = [];

  fieldType = LegacyTxDataType;

  fields!: TxVirtualAttributeField[];
  fieldsToDisplay!: TxVirtualAttributeField[];
  tabName!: string;
  dataLoaded = false;
  isLoaderActive!: boolean;

  constructor(public formService: LegacyTxFormsService) {}

  ngOnInit(): void {
    this.fields = this.tab.children;
    // console.log('fields', this.fields)
    this.tabName = this.tab.attribute.name;
    this.getListAttributesIds(this.tab);
    if (!this.readMode) {
      this.fieldsToDisplay = this.fields;
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.idObject && !changes.idObject.firstChange) {
      if (changes.idObject.previousValue !== changes.idObject.currentValue) {
        this.dataLoaded = false;
        this.idObject = changes.idObject.currentValue;
        this.selectedTabChange();
      }
    }

    // if (changes.tab && !changes.tab.firstChange) {
    //   this.dataLoaded = false;
    //   this.tab = changes.tab.currentValue;
    //   this.fields = this.tab.children;
    //   this.selectedTabChange();
    // }
  }

  getNbError(): number {
    if (this.readMode) {
      return 0;
    }
    //return this.containerField.getNbError();
    let res = 0;
    if (this.form) {
      for (const field of this.fields) {
        if (
          this.form.contains(field.id.toString()) &&
          this.form.get(field.id.toString())?.invalid
        ) {
          res += 1;
        }
      }
      for (const group of this.groupComponents) {
        res += group.getNbError();
      }
    }
    return res;
  }

  selectedTabChange() {
    // this.containerField.selectedTabChange();
    for (const group of this.groupComponents) {
      group.selectedTabChange();
    }
    if (!this.dataLoaded) {
      this.loadData();
      this.dataLoaded = true;
    }
    this.onTabChange.emit((this.form, this.fields));
  }

  loadData() {
    // this.containerField.reset();
    for (const fieldComponent of this.fieldComponents) {
      fieldComponent.clear();
    }
    if (this.idObject > 0) {
      this.loadEvent.emit(true);
      this.formService
        .loadData(this.formConfigTag, this.idObject, this.index)
        .subscribe((dataList: LegacyTxData[]) => {
          console.log('data modified', dataList);
          for (const data of dataList) {
            for (const fieldComponent of this.fieldComponents) {
              if (data.idAttribute === fieldComponent.idAttribute) {
                fieldComponent.setData(data);
                // if (this.readMode){
                //   this.fieldsToDisplay.push(fieldComponent.field);
                // }
                const index = dataList.indexOf(data);
                dataList.slice(index, 1);
              }
            }
          }
          for (const group of this.groupComponents) {
            group.loadData(dataList);
            // if (this.readMode){
            //   this.fieldsToDisplay.push(group.field);
            // }
          }
          this.loadEvent.emit(false);
        });
    }
  }

  getListAttributesIds(group: TxGroupAttributeField) {
    for (const attributeField of group.children) {
      if (!attributeField.attribute) {
        // console.log('getListAttributesIds', attributeField)
      }
      if (attributeField.attribute.dataType === LegacyTxDataType.Group) {
        this.getListAttributesIds(attributeField as TxGroupAttributeField);
      } else {
        this.listAttributesIds.push(attributeField.attribute.id);
      }
    }
  }

  displayRightPane(event: any) {
    this.displayPaneEvent.emit(event);
  }

  onSubmit(): LegacyTxData[] {
    let data: LegacyTxData[] = [];
    for (const field of this.fieldComponents) {
      const dataToSave = field.getDataToSave();

      if (dataToSave) {
        data.push(dataToSave);
      }
    }
    for (const group of this.groupComponents) {
      data = data.concat(group.onSubmit());
    }

    return data;
  }

  getFieldCmpntIndexFromGlobalIndex(globalIndex: number) {
    let resIndex = globalIndex;
    for (let i = 0; i < globalIndex; i++) {
      if (this.fields[i].attribute.dataType === this.fieldType.Group) {
        resIndex--;
      }
    }
    return resIndex;
  }

  haveToBeHidden(index: number) {
    const i = this.getFieldCmpntIndexFromGlobalIndex(index);
    if (this.fieldComponents) {
      const fieldComponent = this.fieldComponents.get(i);
      if (fieldComponent) {
        return fieldComponent.haveToBeHidden();
      }
    } else {
      return false;
    }
  }
}
