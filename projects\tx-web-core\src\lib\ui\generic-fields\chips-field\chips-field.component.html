<ng-container *ngIf="control" class="chips-field">
  <div *ngIf="!readMode; else readField">
    <mat-form-field
      [hideRequiredMarker]="!required"
      color="accent"
      class="chips-field__form-field"
      [hintLabel]="hintLabel | translate">
      <mat-label
        class="chips-field__label"
        matTooltipClass="chips-field__tooltip"
        matTooltipShowDelay="500"
        matTooltipPosition="above"
        [matTooltip]="labelTooltip">
        {{ label | translate }}
      </mat-label>
      <div class="chips-field__container">
        <mat-chip-grid #chipGrid [formControl]="control" required class="chips-field__chips-grid">
          <mat-chip-row
            *ngFor="let chip of chips; index as i"
            class="chips-field__chips-row"
            [matTooltip]="chip.name | translate"
            [matTooltipShowDelay]="500"
            matTooltipPosition="above"
            [editable]="true"
            (edited)="edit($event, i)"
            (click)="onChipClick(chip)"
            (onKeyUp)="onChipClick(chip)"
            [highlighted]="chip.selected"
            [removable]="chip.removable"
            (removed)="remove(i)">
            {{ chip.name | translate }}
            <fa-icon
              matChipRemove
              class="chips-field__icon-remove"
              *ngIf="chip.removable"
              [icon]="['fal', 'times']">
            </fa-icon>
          </mat-chip-row>
        </mat-chip-grid>
        <input
          matInput
          #inputChip
          class="chips-field__input"
          [disabled]="!canAddChips"
          [placeholder]="placeHolder | translate"
          [matChipInputFor]="chipGrid"
          [matChipInputAddOnBlur]="addOnBlur"
          (matChipInputTokenEnd)="onAdd($event)"
          autocomplete="off" />
      </div>
    </mat-form-field>
    <mat-hint class="chips-field__hint-max" *ngIf="chips.length >= maxChipDisplay">
      {{ maxChipsMessage | translate }}</mat-hint
    >
    <mat-error *ngIf="control?.hasError('required')" class="chips-field__error">{{
      'txWebCore.errorMessage.required' | translate
    }}</mat-error>
  </div>
  <ng-template #readField>
    <div *ngIf="readMode">
      <fa-icon
        matSuffix
        mat-icon-button
        *ngIf="actionIcon"
        [matTooltip]="actionIconToolTip"
        [icon]="['fal', actionIcon]"
        class="chips-field__action-icon"
        (click)="onActionIconClick()"></fa-icon>
      <mat-label
        class="chips-field__label"
        matTooltipClass="chips-field__tooltip"
        matTooltipShowDelay="500"
        matTooltipPosition="above"
        [matTooltip]="labelTooltip">
        {{ label | translate }}
        <span *ngIf="required">*</span>
      </mat-label>
      <div class="chips-field__read-field">
        <mat-chip-grid #chipGrid [formControl]="control" required class="chips-field__chips-grid">
          <mat-chip-row
            *ngFor="let chip of chips.slice(0, maxChipDisplay); index as i"
            class="chips-field__chips-row"
            [matTooltip]="chip.name | translate"
            [matTooltipShowDelay]="500"
            matTooltipPosition="above"
            [editable]="editable"
            (edited)="edit($event, i)"
            (click)="onChipClick(chip)"
            (onKeyUp)="onChipClick(chip)"
            [highlighted]="chip.selected"
            [removable]="chip.removable"
            (removed)="remove(i)">
            <div *ngIf="chip.fileUrl" class="chips-field__img-thumbnail-container">
              <img
                [src]="chip.fileUrl"
                [alt]="chip.name | translate"
                class="chips-field__img-thumbnail" />
            </div>
            <div
              *ngIf="!chip.fileUrl || !hideTitleImageThumbnail"
              class="chips-field__chip-text-container">
              <fa-icon
                *ngIf="chip.icon"
                matSuffix
                mat-icon-button
                [icon]="chip.icon"
                class="chips-field__icon-left"></fa-icon>
              {{ chip.name | translate }}
              <fa-icon
                matChipRemove
                class="chips-field__icon-remove"
                *ngIf="chip.removable"
                [icon]="['fal', 'times']">
              </fa-icon>
            </div>
          </mat-chip-row>

          <div
            *ngIf="displayExtenderChip && maxChipDisplay < chips.length"
            class="chips-field__display-all">
            <mat-chip (click)="onAllChipsLink()" class="chips-field__right-pane">
              <div class="chips-field__chip-show-all">
                <span class="chips-field__show-all">
                  {{
                    'txWebCore.components.chipsField.showAllLinksChip'
                      | translate : { numberChips: chips.length }
                  }}</span
                >
              </div>
            </mat-chip>
          </div>
        </mat-chip-grid>
      </div>
      <mat-error *ngIf="control?.hasError('required')" class="chips-field__error">{{
        'txWebCore.errorMessage.required' | translate
      }}</mat-error>
    </div>
  </ng-template>
</ng-container>
