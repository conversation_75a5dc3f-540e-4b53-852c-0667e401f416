import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChange,
  SimpleChanges,
  TemplateRef,
} from '@angular/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { CommonModule } from '@angular/common';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TreeGridModule } from '@syncfusion/ej2-angular-treegrid';
import { RowDataBoundEventArgs, RowSelectEventArgs } from '@syncfusion/ej2-angular-grids';
import { TxTreeGrid } from '../../tree-grid.models';
import {
  ToastComponent,
  ToastService,
  ToastType,
} from '@bassetti-group/tx-web-core/src/lib/ui/toast';
import {
  EmitObjectType,
  EmitObjectTypeList,
  TxObjectType,
} from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TxObjectsTypeService } from '@bassetti-group/tx-web-core/src/lib/data-access/structure';
import {
  InputSearchComponent,
  TxColumnNameTemplateComponent,
} from '@bassetti-group/tx-web-core/src/lib/features/grid';
import {
  TxEscapeHtmlPipe,
  TxHighlightSearchPipe,
  TxCommonUtils,
} from '@bassetti-group/tx-web-core/src/lib/utilities';
import { TxDataTreeGridComponent } from '../data-tree-grid/data-tree-grid.component';
import { TxObjectTypesTreeGridService } from './object-types-tree-grid.service';
import { TxTreeGridService } from '../../tree-grid.service';

@Component({
  standalone: true,
  imports: [
    MatTooltipModule,
    TranslateModule,
    InputSearchComponent,
    TxColumnNameTemplateComponent,
    TreeGridModule,
    FontAwesomeModule,
    CommonModule,
    TxEscapeHtmlPipe,
    TxHighlightSearchPipe,
    ClipboardModule,
  ],
  selector: 'tx-object-types-tree-grid',
  templateUrl: './object-types-tree-grid.component.html',
  styleUrls: ['./object-types-tree-grid.component.scss'],
  providers: [TxTreeGridService, TxObjectTypesTreeGridService],
})
export class TxObjectTypesTreeGridComponent
  extends TxDataTreeGridComponent<TxObjectType>
  implements OnInit, OnChanges, OnDestroy
{
  @Input() showCheckbox = false;
  @Input() showTagsColumn = true;
  @Input() multipleSelection = true;
  @Input() folderCheckable = false;
  @Input() checkedIds: number[] = [];
  @Input() objectTypesFilteredIds: string[] = [];

  @Output() checkChange = new EventEmitter<EmitObjectType>();
  @Output() changeSelection = new EventEmitter<TxObjectType>();
  @Output() checkMultiple = new EventEmitter<EmitObjectTypeList>();

  public isDataLoaded = false;
  public isExplanationDisplayed = false;
  public allOT: TxObjectType[] = [];
  public dataModelOT?: TxObjectType;
  public isSearchActive = false;
  public activeTemplate?: TemplateRef<any>;

  protected hasDescription = true;

  constructor(
    public otService: TxObjectsTypeService,
    private readonly toastService: ToastService,
    public el: ElementRef,
    public translate: TranslateService,
    public otTreeGridService: TxObjectTypesTreeGridService,
    public treeGridService: TxTreeGridService
  ) {
    super(el, translate, treeGridService);
  }

  ngOnInit(): void {
    this.subscription = this.otService.listAll().subscribe((ot) => {
      this.allOT = ot;
      this.init();
      if (ot.length > 0) {
        this.isDataLoaded = true;
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  ngOnChanges(changes: SimpleChanges): void {
    const oTsChecked: SimpleChange = changes.checkedIds;
    if (oTsChecked) {
      this.checkedIds = oTsChecked.currentValue;
      this.applyChanges();
    }
    if (
      changes.objectTypesFilteredIds &&
      changes.objectTypesFilteredIds.currentValue !== changes.objectTypesFilteredIds.previousValue
    ) {
      this.init();
    }
  }

  applyChanges(): void {
    const cpData = this.data.map((elt) => {
      if (this.checkedIds.includes(elt.id) && elt.isChecked !== undefined) {
        elt.isChecked = true;
      } else {
        elt.isChecked = false;
      }
      return elt;
    });
    this.data = cpData;
  }

  selectOrUnselectAll(ischeck: boolean) {
    const listOT: TxObjectType[] = [];
    const cpData = this.data.map((elt) => {
      if (elt.isChecked !== undefined) {
        elt.isChecked = ischeck;
        const objectType = this.allOT.find((ot) => elt.txObject.id === ot.id);
        if (objectType) {
          listOT.push(objectType);
        }
      }
      return elt;
    });
    this.checkMultiple.emit({ ot: listOT, ischeked: ischeck });
    this.data = cpData;
  }

  updateSearch(value: string) {
    this.inputSearchValue = value;
  }

  onRowBound(args: RowDataBoundEventArgs) {
    // add custom css rule for child which are not parent
    const data = args.data as any;
    if (!data.isParent) {
      const widthOfBrothers = 10 * (data.level + 1);
      const widthTotal = 25 * (data.level + 1) + 2;
      const width = widthTotal - widthOfBrothers;
      const elements = args.row?.getElementsByClassName('e-none');
      if (elements?.length) {
        const lastElement = elements[elements.length - 1] as HTMLElement;
        lastElement.setAttribute('style', 'width:' + width + 'px');
      }
    }

    super.onRowBound(args);
  }

  public changeObjectType(event: RowSelectEventArgs): void {
    const objectType = this.allOT.find(
      (ot) => (event.data as TxTreeGrid<TxObjectType>).txObject.id === ot.id
    );
    if (objectType) {
      this.dataModelOT = objectType;
      this.changeSelection.emit(objectType);
    }
  }

  public showToast(
    state: string,
    message: string,
    isPersistent: boolean,
    title?: string,
    duration: number = 0
  ): ToastComponent {
    return this.toastService.show({
      templateContext: { test: { state, message, progress: 0 } },
      type: state === 'loading' ? 'information' : (state as ToastType),
      title,
      description: message,
      date: new Date(),
      isPersistent,
      isUnread: true,
      interval: duration,
    });
  }

  // get the number of Object Types according to their type (Standard, Listings...)
  public getNumberOTFormType(id: number): number | undefined {
    return this.otTreeGridService.getDataLengthAccordingType(id, this.data);
  }

  isCheckBoxDisplayed(data: any): boolean {
    if (!data.txObject) {
      return false;
    }

    if (!this.showCheckbox) {
      return false;
    }

    if (data.id < 0 && !this.folderCheckable) {
      return false;
    }

    return true;
  }

  onCheck(args: MatCheckboxChange, data: any): void {
    data.checkboxState = args.checked ? 'check' : 'uncheck'; // setting the changed value to checkbox field
    data.isChecked = args.checked;
    const objectType = this.allOT.find((ot) => data.txObject.id === ot.id);
    if (objectType) {
      this.checkChange.emit({ ot: objectType, ischeked: args.checked });
    }
  }

  protected init(): void {
    this.allOT.sort((a, b) => a.order - b.order);
    let filterList = this.allOT.filter((ot) => this.objectTypesFilteredIds.includes(String(ot.id)));
    if (filterList.length === 0) {
      this.data = this.otTreeGridService.initTreeGridData(this.allOT);
    } else {
      filterList = this.updateParents(filterList);
      this.data = this.otTreeGridService.initTreeGridData(filterList);
    }
  }

  private updateParents(filterList: TxObjectType[]): TxObjectType[] {
    let otFilterList = TxCommonUtils.deepCopy(filterList);
    otFilterList.forEach((otFromFilter) => {
      if (
        otFromFilter.idObjectTypeParent &&
        !otFilterList.some((ot) => ot.id === otFromFilter.idObjectTypeParent)
      ) {
        otFromFilter.idObjectTypeParent = undefined;
      }
    });
    return otFilterList;
  }
}
