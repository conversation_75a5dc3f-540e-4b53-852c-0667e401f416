import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { OpenRoute } from './open-route.model';
import { PathDetailed } from './path-detailed.model';
import {
  AdminRights,
  ConnectedUser,
  AbstractSessionService,
} from '@bassetti-group/tx-web-core/src/lib/data-access/session';

@Injectable({
  providedIn: 'root',
})
export class PathService {
  private pathDic: Map<string, PathDetailed> = new Map();
  constructor(private router: Router, private sessionService: AbstractSessionService) {
    this.sessionService.getConnectedUser().subscribe((connectedUser) => {
      this.setPathByConcept(connectedUser, router);
    });
  }
  setPathByConcept(connectedUser: ConnectedUser, router: Router): void {
    const pathByKey: [string, PathDetailed][] = router.config
      .filter((route) => route.path !== undefined && route.data?.openWith)
      .flatMap((route) =>
        (route.data?.openWith as OpenRoute[]).map(
          (openRoute) =>
            [
              openRoute.key,
              {
                path: route.path,
                params: openRoute.params,
                canNavigate: this.canNavigate(connectedUser, route.data?.adminRights),
              },
            ] as [string, PathDetailed]
        )
      );
    this.pathDic = new Map(pathByKey);
  }
  getPath(key: string): PathDetailed | undefined {
    return this.pathDic.get(key);
  }
  goto(key: string, id?: number): void {
    const pathDetailed = this.getPath(key);
    if (pathDetailed && id !== undefined) {
      this.gotoSpecificItem(pathDetailed, id);
    } else if (pathDetailed) {
      this.gotoView(pathDetailed);
    }
  }

  private gotoView(pathDetailed: PathDetailed): void {
    this.router.navigate([`/${pathDetailed.path}`], {
      queryParams: pathDetailed.params,
    });
  }

  private gotoSpecificItem(pathDetailed: PathDetailed, id: number): void {
    this.router.navigate([`/${pathDetailed.path}`, id], {
      queryParams: pathDetailed.params,
    });
  }

  private canNavigate(
    connectedUser: ConnectedUser,
    routeRights: AdminRights | AdminRights[]
  ): boolean {
    return this.sessionService.hasRightsOn(routeRights, connectedUser.adminRights);
  }
}
