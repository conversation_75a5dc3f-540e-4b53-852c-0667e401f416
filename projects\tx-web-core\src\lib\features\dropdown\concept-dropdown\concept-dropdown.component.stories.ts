import { type Meta, type StoryObj } from '@storybook/angular';
import { TxConceptDropdownComponent } from './concept-dropdown.component';


const meta: Meta<TxConceptDropdownComponent> = {
  component: TxConceptDropdownComponent,
  title: 'Dropdown/TxConceptDropdownComponent',
  tags: ['autodocs'],
};
export default meta;
type Story = StoryObj<TxConceptDropdownComponent>;

export const Primary: Story = {
  args: {
    showCheckBox: false,
    label: 'Concept Dropdown',
    dataSource: [{
      idObjectTypeParent: 4213,
      order: 0,
      icon: 232,
      isFolder: false,
      type: "ottStandard",
      hasDistinctNames: false,
      isVisible: true,
      lockingType: "otltNone",
      lockingDuration: 0.0013888888888888887,
      displayResultInTextSearch: true,
      right: "dbrStructure",
      associatedObjectTypesIds: [],
      description: "On regroupe ici tous les services.",
      explanation: "",
      name: "Services",
      label: "Services",
      tags: [
        "OTLaboratories"
      ],
      id: 24,
    },
    {
      order: 1,
      icon: 262,
      isFolder: false,
      type: "ottStandard",
      hasDistinctNames: false,
      isVisible: true,
      lockingType: "otltNone",
      lockingDuration: 0.0006944444444444445,
      displayResultInTextSearch: true,
      right: "dbrStructure",
      associatedObjectTypesIds: [],
      description: "On regroupe ici tous les départements.",
      explanation: "",
      name: "Départements",
      label:"Départements",
      tags: [
        "oTDepartements"
      ],
      id: 4213,
    },{
      order: 2,
      icon: 222,
      isFolder: false,
      type: "ottStandard",
      hasDistinctNames: false,
      isVisible: true,
      lockingType: "otltNone",
      lockingDuration: 0.0006944444444444445,
      displayResultInTextSearch: true,
      right: "dbrStructure",
      associatedObjectTypesIds: [],
      description: "On regroupe ici tous Sociétés.",
      explanation: "",
      name: "Sociétés",
      label:"Sociétés",
      tags: [
        "oTSocietes"
      ],
      id: 1234,
    },],
    dataOptions: {
      idProperty: 'id',
      idParentProperty: 'idObjectTypeParent',
    },
    displayIconOption : false,
  },
};
