import {
  NodeCheckEventArgs,
  NodeExpandEventArgs,
  TreeViewComponent,
} from '@syncfusion/ej2-angular-navigations';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
  OnChanges,
  SimpleChanges,
  OnInit,
} from '@angular/core';
import {
  DdtBeforeOpenEventArgs,
  DdtChangeEventArgs,
  DdtDataBoundEventArgs,
  DdtFilteringEventArgs,
  DdtPopupEventArgs,
  DdtSelectEventArgs,
  DropDownTreeComponent,
} from '@syncfusion/ej2-angular-dropdowns';
import { TxBaseFieldComponent } from '../base-field/base-field.component';
import { TxLinkObjectFieldService } from '../../object-fields/link-object-field/services/link-object-field.service';
import { LegacyTxObject } from '../../../services/structure/models/object';
import { TxTreeObjectsComponent } from '../../../trees/tree-objects/tree-objects.component';
import { _LegacyStringUtils } from '../../../utilities/legacy-string-utils';
import { _ArrayUtils } from '../../../utilities/legacy-array-utils';

@Component({
  selector: 'tx-dropdown-list-field',
  templateUrl: './dropdown-list-field.component.html',
  styleUrls: ['./dropdown-list-field.component.scss'],
})
export class TxDropdownListFieldComponent
  extends TxBaseFieldComponent
  implements OnInit, OnChanges
{
  @Input() showCheckBox = false;
  @Input() height = '250px';
  @Input() placeholder = 'Search';
  @Input() allowFiltering = true;
  @Input() popupHeight = 220;
  @Input() selectedObjects: LegacyTxObject[] = [];
  // @Input() checkedId: string;
  @Input() checkedIds!: string[] | string;
  @Input() idObjectType = 0;
  @Input() idFilteringObject = 0;
  @Input() displayIconOption = true;
  @Input() filteringWithRequest = false;
  @Input() txCheckedObjects: LegacyTxObject[] = [];
  @Input() mode = 'Delimiter';
  @Input() imagePath = '/assets/tx-web-core/imgs/objectTypesIcons/';
  @Input() floatLabelType = 'Auto'; // Auto, Always, Never
  @Input() sortedBy = 'order';
  @Input() treeMode = true;
  @Input() useNameaAsId = false;

  @Output() change = new EventEmitter();
  @Output() select = new EventEmitter();
  @Output() valueChange = new EventEmitter();
  @Output() beforeOpen = new EventEmitter();
  @Output() loadObjOnExpandingNodes = new EventEmitter();
  @Output() loadObjOnBeforeOpen = new EventEmitter();

  @ViewChild('dropdownTree') dropdownTree!: DropDownTreeComponent;
  public spanNoData!: HTMLElement;
  public searchTree!: TxTreeObjectsComponent;
  // @ViewChild("combobox", { static: false }) combobox: ComboBoxComponent;

  treeview!: TreeViewComponent;
  uniqueId: string = _LegacyStringUtils.getUniqueId();
  rootNodesLoaded: boolean = false;

  @Input() options: { [key: string]: any }[] = [];
  optionsInTab: { [key: string]: any }[] = [];

  @Input() field = {
    dataSource: this.options,
    value: 'id',
    text: 'name',
    child: 'child',
    hasChildren: 'isParent',
    iconCss: 'icon',
    imageUrl: 'image',
  };

  public treeSettings = {
    autoCheck: false,
    loadOnDemand: true,
  };

  txObjects: LegacyTxObject[] = this.txCheckedObjects;
  hasEnoughValueToSearch!: boolean;

  public filterType = 'Contains';

  // searchTree
  searchTreeDisplayed = false;
  public filteredOptions: { [key: string]: Object }[] = [];
  public searchTreeField = {
    dataSource: [{ id: 1, name: 'test' }],
    value: 'id',
    text: 'name',
    child: 'child',
    hasChildren: 'isParent',
    iconCss: 'icon',
    imageUrl: 'image',
  };

  // this array contains checked options not displayed, in a parent option not loaded yet for example.
  private checkOptionsNotLoaded: any[] = [];

  private _addCheckedNode(id: string) {
    if (!this.checkedIds.indexOf(id)) (this.checkedIds as string[]).push(id);
  }

  private _removeCheckedNode(id: string) {
    if (this.checkedIds.indexOf(id))
      this.checkedIds = (this.checkedIds as string[]).filter((checkedId) => checkedId !== id);
  }

  // this function fix the syncfusion issue according to the expanding of parent node (with lazyloading and checkboxes mode) after a filter search executed
  private _prepareFixingExpandingIssue() {
    var _fixChildExpandingIssue = (options: any) => {
      options.forEach((o: any) => {
        if (o.isParent) {
          o._fixExpandingIssue = true;
        }

        if (o.child) _fixChildExpandingIssue(o.child);
      });
    };

    if (!this.showCheckBox) {
      return;
    }

    _fixChildExpandingIssue(this.options);
  }

  // this function fix the syncfusion issue when we expand a checked parent node for the first time (in this case we load children by request), the parent expanded node
  // will be unchecked...
  private _fixCheckedNode(id: string) {
    var _checkParent = (node: any) => {
      if (node.idParent) {
        const parentNode = this.findNestedNode(node.idParent);
        const treeParentNode = this.treeview.getNode(parentNode.id);
        const isParentNodeChecked = this.isChecked(parentNode.id);

        if (
          (treeParentNode.isChecked === 'false' || treeParentNode.isChecked === '') &&
          isParentNodeChecked
        ) {
          treeParentNode.isChecked = isNodeChecked;
          nodesToCheck.push(parentNode.id);
        }

        _checkParent(parentNode);
      }
    };

    // if (!this.showCheckBox) {
    //   return;
    // }
    const nestedNode = this.findNestedNode(id);
    const treeNode = this.treeview.getNode(id);
    const isNodeChecked = this.isChecked(nestedNode.id);
    const nodesToCheck = [];
    const nodesToUnCheck = [];

    if ((treeNode.isChecked === 'false' || treeNode.isChecked === '') && isNodeChecked) {
      treeNode.isChecked = isNodeChecked;

      nodesToCheck.push(id);
    }

    if (treeNode.isChecked === 'true' && !isNodeChecked) {
      treeNode.isChecked = isNodeChecked;

      nodesToUnCheck.push(id);
    }

    // check its children
    if (nestedNode.child) {
      nestedNode.child.forEach((n: any) => {
        n.isChecked = this.isChecked(n.id);

        if (n.isChecked) {
          nodesToCheck.push('' + n.id);
        }
      });
    }

    // check if there are parent not checked anymore when we load new children
    _checkParent(nestedNode);

    if (nodesToCheck.length) {
      if (this.showCheckBox) {
        this.treeview.checkAll(nodesToCheck);
      } else {
        this.treeview.selectedNodes = nodesToCheck;
      }
    }

    if (nodesToUnCheck.length) {
      this.treeview.uncheckAll(nodesToUnCheck);
    }
  }

  private _getHiddenOptions(): any[] {
    return this.options.map((o, i) => o.hidden);
  }

  private _removeHiddenNode(objects: LegacyTxObject[]) {
    const hiddenOptions = this._getHiddenOptions();
    const hiddenOptionsId = hiddenOptions.map((o) => o.id);
    const hiddenOptionsIdToRemove: any[] = [];
    objects.forEach((object) => {
      if (hiddenOptionsId.indexOf(object.id) > -1) {
        hiddenOptionsIdToRemove.push('' + object.id);
      }
    });

    if (hiddenOptionsIdToRemove.length) {
      this.options = this.options.filter((o) => hiddenOptionsIdToRemove.indexOf(o.id) > -1);
    }
  }

  constructor(public linkObjectFieldService: TxLinkObjectFieldService) {
    super();
  }

  ngOnInit(): void {
    super.ngOnInit();
    if (this.txCheckedObjects.length) {
      this.txObjects = this.txCheckedObjects;
      this._updateSelectedOptions(this.txObjects);
    }
    if (this.options && this.options.length > 0) {
      if (!this.options[0].id) {
        this.field.value = 'name';
        this.useNameaAsId = true;
      }
      this.setOptionsTab();
    }
    if (!Array.isArray(this.checkedIds) && this.checkedIds) {
      this.checkedIds = [this.checkedIds];
    } else {
      this.checkedIds = [];
    }
    if (this.checkedIds.length) {
      this.checkedIds = this.checkedIds.map((o) => '' + o);
      //this.control.setValue(this.checkedIds);
      if (this.dropdownTree) {
        this.dropdownTree.fields.dataSource = this.options;
        this.dropdownTree.value = this.checkedIds;
        this.dropdownTree.dataBind();
      }
    }
    this.control.markAllAsTouched();
  }

  initValueChangeEmitter() {
    this.control.valueChanges.subscribe(() => {
      this.value = this.control.value;
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    super.ngOnChanges(changes);
    if (changes.selectedObjects?.currentValue) {
      this._updateSelectedOptions(changes.selectedObjects.currentValue);
    }
    if (changes.options) {
      if (this.options && this.options.length > 0 && this.options[0].id) {
        this.field.value = 'id';
        if (this.dropdownTree) {
          this.dropdownTree.fields.value = 'id';
        }
        this.useNameaAsId = false;
      }
      if (this.options && this.options.length > 0 && !this.options[0].id) {
        this.field.value = 'name';
        if (this.dropdownTree) {
          this.dropdownTree.fields.value = 'name';
        }
        this.useNameaAsId = true;
      }
      this.setOptionsTab();
    }
    // if(changes.checkedId && (!this.checkedIds || this.checkedIds.length < 1 || this.checkedIds === ([changes.checkedId.previousValue]))) {
    //   this.checkedIds = [ '' + this.checkedId];
    //   if (this.dropdownTree) {
    //     this.dropdownTree.fields.dataSource = this.options;
    //     this.dropdownTree.value = this.checkedIds;
    //     this.dropdownTree.dataBind();
    //   }
    // }
    if (changes.checkedIds) {
      if (!Array.isArray(this.checkedIds)) {
        this.checkedIds = this.checkedIds ? [this.checkedIds] : [];
      }
      this.checkedIds = this.checkedIds.map((o) => '' + o);
      if (this.dropdownTree) {
        this.dropdownTree.fields.dataSource = this.options;
        this.dropdownTree.value = this.checkedIds;
        this.dropdownTree.dataBind();
      }
    }
  }

  initValue(): void {
    this.control.setValue([]);
    if (!Array.isArray(this.checkedIds)) {
      this.checkedIds = this.checkedIds ? [this.checkedIds] : [];
    }
    if (this.checkedIds && this.checkedIds.length) {
      this.checkedIds = this.checkedIds.map((o) => '' + o);
      this.control.setValue(this.checkedIds);
    }
  }

  /**
   * Nodes functions
   */

  /**
   * Get a nested node
   * @param id
   * @returns
   */
  findNestedNode(id: number | string): any {
    function _find(nodeToFind: any) {
      if (nodeToFind.id == id) {
        node = nodeToFind;
        return true;
      }
      if (nodeToFind.child) {
        nodeToFind.child.find((n: any) => {
          if (_find(n)) return true;
        });
      }
      return false;
    }

    let node;

    this.options.find((n) => {
      if (_find(n)) return true;
    });

    return node;
  }

  setOptionsTab() {
    const _setOptionsTab = (option: any) => {
      for (const optionChild of option.child) {
        this.optionsInTab.push(optionChild);
        if (optionChild.child) {
          _setOptionsTab(optionChild);
        }
      }
    };

    this.optionsInTab = [];
    for (const option of this.options) {
      this.optionsInTab.push(option);
      if (option.child) {
        _setOptionsTab(option);
      }
    }
  }

  removeHiddenNode() {}

  findObject(param: number | string, idParam = 'id'): LegacyTxObject {
    return this.txObjects.find((o) => (o as any)[idParam] == param) as LegacyTxObject;
  }

  findOptions(param: number | string, idParam = 'id'): { [key: string]: Object } {
    return this.optionsInTab.find((o) => o[idParam] == param) as {
      [key: string]: Object;
    };
  }

  /**
   * Check if a node is checked according to it's id
   * @param id
   * @returns
   */
  isChecked(id: number | string) {
    return this.getCheckedIds().some((c) => c == id);
  }

  /**
   * Return checked objects ids
   * @returns
   */
  getCheckedObject(ids?: []): LegacyTxObject[] | { [key: string]: Object }[] {
    const checkedIds = ids
      ? this.useNameaAsId
        ? ids
        : ids.map((id) => parseInt(id, 10))
      : this.getCheckedIds();

    if (this.txObjects && this.txObjects.length > 0) {
      return checkedIds.map((id) => {
        return this.findObject(id, this.field.value);
      });
    } else {
      return checkedIds.map((id) => {
        return this.findOptions(id, this.field.value);
      });
    }
  }

  /**
   * Return checked node ids
   * @returns
   */
  getCheckedIds(): string[] | number[] {
    return this.dropdownTree && this.dropdownTree.value
      ? !this.useNameaAsId
        ? this.dropdownTree.value.map((v) => parseInt(v, 10))
        : this.dropdownTree.value
      : [];
  }

  clearSelection() {
    this.checkedIds = [];
    this.dropdownTree.clear();
    this.control.setValue([]);
  }

  /**
   * DropdownTree function
   */
  onCreated() {
    this.treeview = (this.dropdownTree as any).treeObj;
    this.treeview.enableHtmlSanitizer = true;

    // bind expanding event of the tree inside the combo
    this.treeview.nodeExpanding = (args: any) => {
      this.onExpandingNode(args);
    };

    // this.treeview.nodeChecked = function(args) {
    //   this.onCheckedNode(args);
    // }.bind(this);

    if (this.options.length > 0) {
      this.rootNodesLoaded = true;
      this.dropdownTree.fields.dataSource = this.options;

      setTimeout(() => {
        this.dropdownTree.value = this.checkedIds as string[];
      });
    }
  }

  private _updateSelectedOptions(selectedObjects: LegacyTxObject[]) {
    if (selectedObjects) {
      this.selectedObjects = selectedObjects;
      this.options = this.selectedObjects.map((o) => this.linkObjectFieldService.createOption(o));
      this.checkedIds = this.options.map((o) => '' + o.id);
      this.control.setValue(this.checkedIds);
      if (this.dropdownTree) {
        this.dropdownTree.fields.dataSource = this.options;
        this.dropdownTree.value = this.checkedIds;
        this.dropdownTree.dataBind();
      }
    }
  }

  private _hideCheckedOptions() {
    if (this.checkOptionsNotLoaded.length) {
      // hide children item options to keep it checked
      const elements = (this.dropdownTree as any).popupDiv.getElementsByClassName('e-list-item');
      const itemsIdToHide = this.checkOptionsNotLoaded.map((o) => o.id);

      Array.from(elements).forEach((element: any) => {
        if (itemsIdToHide.indexOf(element.dataset.uid) > -1) {
          element.classList.add('hide-item');
        } else {
          element.classList.remove('hide-item');
        }
      });
    }
  }

  onBeforeOpen(args?: DdtBeforeOpenEventArgs) {
    if (!this.rootNodesLoaded) {
      this.rootNodesLoaded = true;
      if (args) args.cancel = true;

      this.loadObjOnBeforeOpen.emit(this.idFilteringObject);
      // this.loadObjects(this.idFilteringObject).subscribe((options) => {
      //   this.options = options;

      //     // add hidden checked options which not are on root level
      //     this.checkOptionsNotLoaded = this.field.dataSource.filter(option => !_ArrayUtils.instanceFromId(this.options, option.id))
      //     if (this.checkOptionsNotLoaded.length) {
      //       this.options = this.options.concat(this.checkOptionsNotLoaded);
      //     }
      //     this.dropdownTree.fields.dataSource = this.options;

      //     setTimeout(() => {
      //       this.dropdownTree.value = this.checkedIds;
      //       this.dropdownTree.showPopup();

      //       this.beforeOpen.emit()
      //     });
      //   });
    }
  }

  onOpen(args: DdtPopupEventArgs) {
    // this._fixCheckedNode()
  }

  onChange(args: DdtChangeEventArgs) {
    //this.control.setValue((this.control.value ?? []).concat(args.value));
    this.change.emit(this.getCheckedObject());
    this.control.updateValueAndValidity();
  }

  onValueChange(args: []) {
    this.valueChange.emit(this.getCheckedObject(args));
    this.control.updateValueAndValidity();
  }

  onSelect(args: DdtSelectEventArgs) {
    this.select.emit(this.getCheckedObject());
  }

  onFiltering(args: DdtFilteringEventArgs) {
    args.preventDefaultAction = this.filteringWithRequest;

    if (_LegacyStringUtils.isEmpty(args.text)) {
      this.searchTreeDisplayed = false;
      this._prepareFixingExpandingIssue();
      args.fields.dataSource = this.options;
    } else if (args.preventDefaultAction) {
      this.searchTreeDisplayed = true;

      // let predicate = new Predicate('name', 'startswith', args.text, true);
      // this.searchOptions(args.text).subscribe((txObjects) => {
      //   // let filteredList = new DataManager(txObjects);

      //   args.fields.dataSource = txObjects
      // });
      // this.hasEnoughValueToSearch = args.text.length > 2;
      // args.fields.dataSource = [];

      // if (this.hasEnoughValueToSearch) {
      //   // args.cancel = true;
      //   this.searchObjects(args.text).subscribe((txObjects) => {
      //     console.log('options filtered', txObjects)
      //     console.log('options checked', this.getCheckedObject())
      //     if (txObjects.length) {
      //       this.spanNoData.style.display = 'none';
      //     } else {
      //       this.spanNoData.style.display = 'block';
      //     }
      //     this.searchTree.filter(txObjects);

      //     // update checked nodes
      //     const checkedIds = this.getCheckedIds();
      //     this.searchTree.setCheckedIds(checkedIds);
      //   });
      // } else {
      //   if(this.spanNoData) {
      //     this.spanNoData.style.display = 'block';
      //   }
      // }
    }
    this._hideCheckedOptions();
  }

  setCheckInput() {
    // let drop = document.getElementById("games").ej2_instances[0];
  }

  onFilteredNodeChecked(args: NodeCheckEventArgs) {
    const checked = args.action == 'check';
    const ids = args.data.map((o) => '' + o.id);
    this.checkedIds = this.searchTree.checkedIds.map((id) => '' + id);
    this.control.setValue(this.checkedIds);
    // this.treeview.uncheckAll();
    // this.dropdownTree.hidePopup();
    this.dropdownTree.value = this.checkedIds;
    this.dropdownTree.dataBind();
    // if (checked) {
    //   this.treeview.checkAll(this.checkedIds);
    // } else {

    // }
  }

  initTreeSearch(tree: TxTreeObjectsComponent, span: HTMLElement) {
    this.searchTree = tree;
    this.spanNoData = span;
  }

  onDataBounded(args: DdtDataBoundEventArgs) {
    this._hideCheckedOptions();
  }

  /**
   * Tree function
   */

  /**
   * Permit to add nodes in tree combo
   * @param nodes
   * @param idParentNode
   * @param startIndex
   * @param preventOnExpand
   */
  addNodes(
    nodes: { [key: string]: Object }[],
    idParentNode: number = 0,
    startIndex = undefined,
    preventOnExpand: boolean = false
  ) {
    if (idParentNode > 0) {
      const parentNode = this.findNestedNode(idParentNode);

      if (!parentNode.child) parentNode.child = [];

      parentNode.child = parentNode.child.concat(nodes);
      parentNode._fixExpandingIssue = false;
    }

    // add nodes
    this.treeview.addNodes(nodes, idParentNode.toString(), startIndex, preventOnExpand);
  }

  onCheckedNode(event: any) {
    const checked = event.action === 'check';

    checked ? this._addCheckedNode(event.data[0].id) : this._removeCheckedNode(event.data[0].id);
  }

  /**
   * Function called when a parent node opened
   * @param event
   * @returns
   */
  onExpandingNode(event: NodeExpandEventArgs) {
    const parentNodeId: number = parseInt((event.nodeData as any).id, 10);
    let parentNode;
    if (parentNodeId > 0) {
      parentNode = this.findNestedNode(parentNodeId);

      if (!parentNode || parentNode.childrenLoaded) {
        if (parentNode._fixExpandingIssue) {
          parentNode._fixExpandingIssue = false;
          this.treeview.addNodes(parentNode.child, parentNodeId.toString());
          this._fixCheckedNode('' + parentNodeId);
        }
        return;
      }

      parentNode.childrenLoaded = true;

      this.loadObjOnExpandingNodes.emit(parentNode);

      // this.loadObjects(parentNode.id).subscribe((options) => {
      //   if (!parentNode.child) parentNode.child = [];

      //   // clean hidden checked options truely loaded
      //   if (this.checkOptionsNotLoaded.length) {
      //     // const hiddenOptionsToRemove = this.checkOptionsNotLoaded.filter(option => _ArrayUtils.instanceFromId(options, option.id));

      //     // hide children item options to keep it checked
      //     // const elements = (this.dropdownTree as any).popupDiv.getElementsByClassName('e-list-item');
      //     // const itemsIdToRemove = hiddenOptionsToRemove.map((o) => o.id);

      //     // Array.from(elements).forEach((element: any) => {
      //     //   if (itemsIdToRemove.indexOf(element.dataset.uid) > -1) {
      //     //     element.remove();
      //     //   }
      //     // });

      //     // this.checkOptionsNotLoaded = this.checkOptionsNotLoaded.filter(option => !_ArrayUtils.instanceFromId(options, option.id));
      //   }

      //   if (options.length) {
      //     this.addNodes(options, parentNodeId);
      //     this._fixCheckedNode('' + parentNodeId);
      //   }
      // });
    }
  }

  setLoadedObjOnExpandingNodes(parentNode: any, options: { [key: string]: Object }[]) {
    if (!parentNode.child) parentNode.child = [];
    // clean hidden checked options truely loaded
    if (this.checkOptionsNotLoaded.length) {
      // const hiddenOptionsToRemove = this.checkOptionsNotLoaded.filter(option => _ArrayUtils.instanceFromId(options, option.id));
      // hide children item options to keep it checked
      // const elements = (this.dropdownTree as any).popupDiv.getElementsByClassName('e-list-item');
      // const itemsIdToRemove = hiddenOptionsToRemove.map((o) => o.id);
      // Array.from(elements).forEach((element: any) => {
      //   if (itemsIdToRemove.indexOf(element.dataset.uid) > -1) {
      //     element.remove();
      //   }
      // });
      // this.checkOptionsNotLoaded = this.checkOptionsNotLoaded.filter(option => !_ArrayUtils.instanceFromId(options, option.id));
    }

    if (options.length) {
      this.addNodes(options, parentNode.id);
      this._fixCheckedNode('' + parentNode.id);
    }
  }

  setLoadedObjOnBeforeOpen(options: { [key: string]: Object }[]) {
    this.options = options;
    // add hidden checked options which not are on root level
    this.checkOptionsNotLoaded = this.field.dataSource.filter(
      (option) => !_ArrayUtils.instanceFromId(this.options, option.id)
    );
    if (this.checkOptionsNotLoaded.length) {
      this.options = this.options.concat(this.checkOptionsNotLoaded);
    }
    this.dropdownTree.fields.dataSource = this.options;

    setTimeout(() => {
      this.dropdownTree.value = this.checkedIds as string[];
      this.dropdownTree.showPopup();

      this.beforeOpen.emit();
    });
  }
}
