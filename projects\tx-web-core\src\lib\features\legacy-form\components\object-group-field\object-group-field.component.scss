.form-fieldset {
  margin-bottom: 15px;
  -moz-border-radius: 8px;
  -webkit-border-radius: 8px;
  border-radius: 10px;
  overflow: hidden;
  // border-right: 1px solid transparent;
  // border-top: 1px solid transparent;
  // border-bottom: 1px solid transparent;
  transition: border 0.3s;
  transition: background-color 0.3s;

  .form-fieldset-content {
    opacity: 1;
    transition: opacity 0.4s;
  }

  .form-fieldset-content-hidden {
    opacity: 0;
  }
}

.display-group-icon {
  margin: auto;
  font-size: 12px;
}

// .form-fieldset:hover{
//     transition: border-color 0.3s;
//   }

.fieldset-legend {
  display: flex;
  cursor: pointer;
  padding-right: 8px;

  .fieldset-legend-text {
    padding-left: 16px;
    padding-right: 16px;
    margin: auto;
  }

  .form-button-field {
    transition: color 0.3s;
  }

  .display-group-icon-container {
    border-radius: 15px;
    height: 24px;
    width: 24px;
    display: flex;
    transition: background-color 0.3s;
  }
}
