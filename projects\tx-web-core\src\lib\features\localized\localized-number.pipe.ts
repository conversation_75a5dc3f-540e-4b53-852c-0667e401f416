import { formatNumber } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';
import { AbstractSessionService } from '@bassetti-group/tx-web-core/src/lib/data-access/session';

@Pipe({
  standalone: true,
  name: 'localizedNumber',
})
export class LocalizedNumberPipe implements PipeTransform {
  constructor(private session: AbstractSessionService) {}

  transform(value: number, format?: string): string | undefined {
    if (value == null) {
      return '';
    } // !value would also react to zeros.
    // format : decimal representation a.b-c with :
    // a = minimum number of integer digits before the decimal point
    // b = minimum number of digits after the decimal point
    // c = maximum numer of digits after the decimal point
    if (!format) {
      format = '1.0-2';
    }

    if (this.session.currentLang?.languageUsedCode) {
      return formatNumber(value, this.session.currentLang.languageUsedCode, format);
    }
  }
}
