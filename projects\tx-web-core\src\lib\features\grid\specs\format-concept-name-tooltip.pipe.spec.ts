import { TestBed } from '@angular/core/testing';
import { FormatConceptNameTooltipPipe } from '../pipes/format-concept-name-tooltip.pipe';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { TranslateService } from '@ngx-translate/core';

const data = {
  id: 1,
  name: 'name',
};
describe('FormatConceptNameTooltipPipe', () => {
  let pipe: FormatConceptNameTooltipPipe;
  let translateService: TranslateService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        TranslateTestingModule.withTranslations({
          en: {},
          fr: {},
        }),
      ],
    });
    translateService = TestBed.inject(TranslateService);
    pipe = new FormatConceptNameTooltipPipe(translateService);
  });
  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  describe('if concept has txObject', () => {
    it('should return only name if there is no explanation', () => {
      expect(pipe.transform({ id: 1, name: 'Contact', txObject: { id: 2 } })).toBe(
        'txWebCore.generic.id 2\nContact'
      );
    });

    it('should return concatenation of name & explanation with break line', () => {
      expect(
        pipe.transform({
          id: 1,
          name: 'Contact',
          icon: '/srcOfImage',
          txObject: { id: 3, explanation: 'This is explanation' },
        })
      ).toBe('txWebCore.generic.id 3\nContact\nThis is explanation');
    });
  });

  describe('if concept do not have txObject', () => {
    it('should get tooltip without explanation', () => {
      expect(pipe.transform({ id: 11, name: 'seriesType1', tags: [] })).toBe(
        'txWebCore.generic.id 11\nseriesType1'
      );
    });

    it('should get tooltip with an explanation', () => {
      expect(
        pipe.transform({
          id: 1,
          name: 'tableType1',
          explanation: 'This is an explanation',
          tags: [],
        })
      ).toBe('txWebCore.generic.id 1\ntableType1\nThis is an explanation');
    });
  });
});
