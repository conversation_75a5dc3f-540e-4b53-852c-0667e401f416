import { BaseEventArgs } from './base-event-args.model';

export interface TxContextMenuItem extends BaseEventArgs {
  /**
   * Defines class/multiple classes separated by a space for the menu Item that is used to include an icon.
   * Menu Item can include font icon and sprite image.
   *
   */
  iconCss?: string;

  /**
   * Specifies the id for menu item.
   *
   */
  id?: string;

  /**
   * Specifies separator between the menu items. Separator are either horizontal or vertical lines used to group menu items.
   *
   */
  separator?: boolean;

  /**
   * Specifies the sub menu items that is the array of MenuItem model.
   *
   */
  items?: TxContextMenuItem[];

  /**
   * Specifies text for menu item.
   *
   */
  label?: string;

  /**
   * Specifies url for menu item that creates the anchor link to navigate to the url provided.
   *
   */
  url?: string;
}

export const defaultContextMenuItemModel: TxContextMenuItem = {
  iconCss: '',
  separator: false,
  items: [],
  label: undefined,
};
