import { MockService } from 'ng-mocks';
import { of } from 'rxjs';
import { LinkTypeFilteringType, TxLinkType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TxLinkTypesService } from '../link-types.service';



export const MOCK_LINK_TYPES: TxLinkType[] = [
  {
    id: 1,
    name: "LinkType1",
    idSourceObjectType: 101,
    idDestinationObjectType: 201,
    isAssociative: true,
    idFilteringObject: 0,
    multiplicity: false,
    multiplicityInv: true,
    isStrongFiltered: false,
    isStrongFilteredInv: false,
    isTransposed: false,
    isTransposeInv: false,
    filteringType: LinkTypeFilteringType.parent,
    filteringTypeInv: LinkTypeFilteringType.none,
    tags : []
  },
  {
    id: 2,
    name: "LinkType2",
    idSourceObjectType: 102,
    idDestinationObjectType: 202,
    isAssociative: false,
    idFilteringObject: 0,
    multiplicity: false,
    multiplicityInv: false,
    isStrongFiltered: false,
    isStrongFilteredInv: false,
    isTransposed: false,
    isTransposeInv: false,
    filteringType: LinkTypeFilteringType.parent,
    filteringTypeInv: LinkTypeFilteringType.none,
    tags : []
  },
  {
    id: 3,
    name: "LinkType3",
    idSourceObjectType: 101,
    idDestinationObjectType: 203,
    isAssociative: true,
    idFilteringObject: 0,
    multiplicity: true,
    multiplicityInv: false,
    isStrongFiltered: true,
    isStrongFilteredInv: false,
    isTransposed: false,
    isTransposeInv: true,
    filteringType: LinkTypeFilteringType.parent,
    filteringTypeInv: LinkTypeFilteringType.none,
    tags : []
  },
];

export const MOCK_LINK_TYPE_SERVICE = MockService(TxLinkTypesService, {
  isReady: () => of(true),
  concepts: MOCK_LINK_TYPES,
  findAssociativeLinkType: (
    idLinkTypeToIgnore: number,
    idSourceObjectType: number
  ): TxLinkType | undefined => {
    return MOCK_LINK_TYPES.find(
      (c) =>
        c.isAssociative &&
        c.idSourceObjectType === idSourceObjectType &&
        c.id !== idLinkTypeToIgnore
    );
  },
});
