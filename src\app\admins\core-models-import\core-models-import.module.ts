import { NgModule } from '@angular/core';
import { CoreModelsImportComponent } from './core-models-import.component';
import { CoreModelsImportRoutingModule } from './core-models-import-routing.module';
import { CoreModelsImportHistoryComponent } from './components/core-models-import-history/core-models-import-history.component';
import { TestedCoreModelConceptsComponent } from './components/tested-core-model-concepts/tested-core-model-concepts.component';
import { ImportedCoreModelConceptsComponent } from './components/imported-core-model-concepts/imported-core-model-concepts.component';
import { CoreModelsImportService } from './services/core-models-import.service';
import { CoreModelsImportHttpService } from './services/core-models-import.http.service';
import { IsTestedConceptsPipe } from './pipes/is-tested-concepts.pipe';
import { CoreModelImportGatewayService } from './services/core-model-import-gateway.service';
import { CoreModelCommonModule } from '../core-model-common';

@NgModule({
  declarations: [
    CoreModelsImportComponent,
    CoreModelsImportHistoryComponent,
    TestedCoreModelConceptsComponent,
    ImportedCoreModelConceptsComponent,
    IsTestedConceptsPipe,
  ],
  imports: [CoreModelCommonModule, CoreModelsImportRoutingModule],
  providers: [
    CoreModelsImportService,
    {
      provide: CoreModelImportGatewayService,
      useClass: CoreModelsImportHttpService,
    },
  ],
})
export class CoreModelsImportModule {}
