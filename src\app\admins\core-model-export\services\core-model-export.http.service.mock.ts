import { MockService } from 'ng-mocks';
import { of } from 'rxjs';
import { CoreModelsExportHttpService } from './core-model-export.http.service';
import { CoreModelExportHistoryDTO } from '../models/core-model-export-history-object.dto';
import { CoreModelExportConceptDTO } from '../models/core-model-export-concept.dto';
import { CoreModelExportConcept } from '../models/core-model-export-concept.model';
import { TxObjectTypeType, ConceptType } from '@bassetti-group/tx-web-core';

export const CORE_MODEL_CONCEPTS_DTO: CoreModelExportConceptDTO[] = [
  {
    type: 'ObjectType',
    id: 82,
    name: 'AssoAttNoCMT',
    tag: 'otAssoAttNoCMT_CM8974C75B',
    missingConfigs: [
      {
        key: 'MISSING_ASSOCIATIVE_CLASS_LINK_TAG',
        missingObject: {
          tags: [],
          id: 596,
        },
      },
    ],
    explanation: '',
  },
  {
    type: 'ObjectType',
    id: 83,
    name: 'AssoToOTWithNoCMT',
    tag: 'otAssoToOTWithNoCMT_CM8974C75C',
    missingConfigs: [
      {
        key: 'MISSING_ASSOCIATIVE_CLASS_OT_TAG',
        missingObject: {
          tags: ['otTESTReadView'],
          id: 21,
        },
      },
    ],
    explanation: '',
  },
];
export const CORE_MODEL_CONCEPTS: CoreModelExportConcept[] = [
  {
    type: ConceptType.ObjectType,
    id: 82,
    icon: 250,
    name: 'AssoAttNoCMT',
    tags: ['otAssoAttNoCMT_CM8974C75B'],
    metaDataList: [],
    explanation: '',
    errors: ['errors.coreModels.MISSING_ASSOCIATIVE_CLASS_LINK_TAG.content'],
  },
  {
    type: ConceptType.ObjectType,
    id: 83,
    name: 'AssoToOTWithNoCMT',
    icon: 250,
    tags: ['otAssoToOTWithNoCMT_CM8974C75C'],
    metaDataList: [],
    explanation: '',
    errors: [],
  },
];

export const CORE_MODEL_EXPORT_HISTORY: CoreModelExportHistoryDTO[] = [
  {
    version: '0.0.1',
    name: 'Core Model Export test',
    explanation: 'Test',
    comment: 'Test',
    date: '2023-08-08T18:07:20.082Z',
    username: 'Administrator',
  },
  {
    version: '0.0.1',
    name: 'Core Model Export test',
    explanation: 'Test2',
    comment: 'Test2',
    date: '2023-08-08T18:08:10.482Z',
    username: 'Administrator',
  },
  {
    version: '0.0.1',
    name: 'Core Model Export test',
    explanation: 'Test3',
    comment: 'Test3',
    date: '2023-08-08T18:21:02.966Z',
    username: 'Administrator',
  },
];
export const EXPECTED_CORE_MODEL_CONCEPTS = [
  {
    type: ConceptType.ObjectType,
    id: 82,
    name: 'AssoAttNoCMT',
    tags: ['otAssoAttNoCMT_CM8974C75B'],
    missingConfigs: [
      {
        key: 'MISSING_ASSOCIATIVE_CLASS_LINK_TAG',
        missingObject: {
          tags: [],
          id: 596,
        },
      },
    ],
    metaDataList: [],
    errors: ['Missing associative class link tag.'],
    explanation: '',
    icon: 268,
  },
  {
    type: ConceptType.ObjectType,
    id: 83,
    name: 'AssoToOTWithNoCMT',
    tags: ['otAssoToOTWithNoCMT_CM8974C75C'],
    missingConfigs: [
      {
        key: 'MISSING_ASSOCIATIVE_CLASS_OT_TAG',
        missingObject: {
          tags: ['otTESTReadView'],
          id: 21,
        },
      },
    ],
    errors: ['MISSING_ASSOCIATIVE_CLASS_OT_TAG'],
    metaDataList: [],
    explanation: '',
    icon: 268,
  },
  {
    type: ConceptType.ObjectType,
    id: 85,
    name: 'Lsting with CMT',
    tags: ['otLstingWithCMT_CM8974C761'],
    missingConfigs: [],
    metaDataList: [],
    errors: [],
    explanation: '',
    icon: 283,
  },
  {
    type: ConceptType.ObjectType,
    id: 182,
    name: 'New',
    tags: ['objTxtbNew_CM8974C75D'],
    missingConfigs: [
      {
        key: 'MISSING_OT_TAG',
        missingObject: {
          tags: ['OTTxTV'],
          id: 51,
        },
      },
    ],
    objectType: {
      name: 'TxTableView',
      order: 14,
      icon: 269,
      isFolder: false,
      type: TxObjectTypeType.Standard,
    },
    metaDataList: [],
    errors: ['Missing object type core model tag.'],
  },
  {
    type: ConceptType.Object,
    id: 226,
    name: 'ObjWithCMT',
    tags: ['objLswcObjWithCMT_CM8974C761'],
    missingConfigs: [],
    objectType: {
      name: 'Lsting with CMT',
      order: 10,
      icon: 283,
      isFolder: false,
      type: TxObjectTypeType.Information,
    },
    metaDataList: [],
    errors: [],
  },
];
const arrayBuffer = new ArrayBuffer(2);
const dataView = new DataView(arrayBuffer);
dataView.setInt8(0, 104);
dataView.setInt8(1, 105);
const blob = new Blob([arrayBuffer]);
const file = new File([blob], 'core-model-exported.zip', {
  type: 'application/zip',
});
export const CORE_MODELS_EXPORT_SERVICE_MOCK = MockService(CoreModelsExportHttpService, {
  loadCoreModelsExportHistoryList: () => of(CORE_MODEL_EXPORT_HISTORY),
  loadCoreModelsConceptData: () => of(CORE_MODEL_CONCEPTS_DTO),
  exportCoreModels: () => of(file),
});
