export const dateToFloat = (date: Date): number =>
  Math.ceil(
    (25569.0 + (date.getTime() - date.getTimezoneOffset() * 60 * 1000) / (1000 * 60 * 60 * 24)) *
      100000
  ) / 100000;

export const floatToDate = (dateFloat: number | string): Date => {
  let float: number;
  if (typeof dateFloat === 'string') {
    float = parseFloat(dateFloat.replace(',', '.'));
  } else {
    float = dateFloat;
  }
  return new Date(0, 0, 0, 0, 0, float * 24 * 60 * 60 - 24 * 60 * 60);
};

export const getDateWithoutTime = (date: Date): Date => {
  return new Date(date.toDateString());
};

export * as DateUtils from './date.utils';
