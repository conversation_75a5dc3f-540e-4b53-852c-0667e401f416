import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import {
  UntypedFormGroup,
  UntypedFormControl,
  Validators,
  ValidatorFn,
  ValidationErrors,
  AbstractControl,
} from '@angular/forms';
import { Observable, takeUntil } from 'rxjs';
import { TagsDescriptionsFormComponent } from 'src/app/shared/components/tags-descriptions-form/tags-descriptions-form.component';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import {
  RowCollapsedEventArgs,
  RowExpandedEventArgs,
  TreeGridComponent,
} from '@syncfusion/ej2-angular-treegrid';
import { RowDeselectEventArgs, RowSelectEventArgs } from '@syncfusion/ej2-angular-grids';
import { TranslateService } from '@ngx-translate/core';
import { Details } from 'src/app/core/help-box/help-box-models';
import { HelpBoxService } from 'src/app/core/services/help-box/help-box.service';
import { TaggedConcepts } from 'src/app/shared/models/tags';
import {
  TxAttributesService,
  TxLockingType,
  TxObjectType,
  TxObjectTypeFormPane,
  TxObjectTypeIconService,
  TxObjectTypeType,
  TxObjectsTypeService,
  TxTreeGrid,
} from '@bassetti-group/tx-web-core';
import { FormPaneDynComponent } from 'src/app/shared/components/form-pane/form-pane-dyn.component';

@Component({
  selector: 'app-object-type-form',
  templateUrl: './object-type-form.component.html',
  styleUrls: ['./object-type-form.component.scss'],
})
export class ObjectTypeFormComponent
  extends FormPaneDynComponent<TxObjectTypeFormPane>
  implements OnInit, AfterViewInit
{
  @ViewChild(TagsDescriptionsFormComponent) public tagsEditor:
    | TagsDescriptionsFormComponent
    | undefined;
  @ViewChild('treeGrid0') public treeGrid0: TreeGridComponent | undefined;
  @ViewChild('treeGrid1') public treeGrid1: TreeGridComponent | undefined;

  public isNatureExplanationDisplayed = false;
  public isVisibilityExplanationDisplayed = false;
  public isLockingExplanationDisplayed = false;
  public currentExplanation?: Details;
  public concepts: any[] = [];
  public tabIcons?: any[];
  public selectedIcon = 0;

  public tagConcept = TaggedConcepts.ObjectType;

  // For Associative selection :
  public objectTypesStandardTree?: TxTreeGrid<any>[];
  public objectTypesStandardTree2?: TxTreeGrid<any>[];
  public idsOTTreeGrid: number[] = [];

  isObjectTypeConvertible = false;
  types: any[] = [];
  lockingTypes: any[] = [];

  public form = new UntypedFormGroup({
    id: new UntypedFormControl(0),
    idType: new UntypedFormControl(0),
    name: new UntypedFormControl({ value: '' }, [Validators.required]),
    lockingDuration: new UntypedFormControl(0, [Validators.required, this.isValueNullOrZero()]),
    hasDistinctName: new UntypedFormControl(false),
    isVisible: new UntypedFormControl(true),
    lockingType: new UntypedFormControl(TxLockingType.None),
    displayResultInTextSearch: new UntypedFormControl(true),
    order: new UntypedFormControl(0),
    idObjectTypeParent: new UntypedFormControl(0),
    icon: new UntypedFormControl(0),
    isFolder: new UntypedFormControl(false),
    idOTAssociativity0: new UntypedFormControl(),
    idOTAssociativity1: new UntypedFormControl(),
    descriptions: new UntypedFormGroup({
      tags: new UntypedFormControl(''),
      explanation: new UntypedFormControl(''),
      description: new UntypedFormControl(''),
    }),
  });

  protected indewRowTreeGrid0 = 0;
  protected indewRowTreeGrid1 = 0;

  private allOT: TxObjectType[] | undefined;

  constructor(
    private otService: TxObjectsTypeService,
    private translate: TranslateService,
    private helpboxService: HelpBoxService,
    private attributeService: TxAttributesService,
    private objectTypeIconService: TxObjectTypeIconService
  ) {
    super();
  }
  get id() {
    return this.form.get('id') as UntypedFormControl;
  }
  get idType() {
    return this.form.get('idType') as UntypedFormControl;
  }
  get name() {
    return this.form.get('name') as UntypedFormControl;
  }
  get isUniqueObjectName() {
    return this.form.get('isUniqueObjectName') as UntypedFormControl;
  }
  get hasDistinctName() {
    return this.form.get('hasDistinctName') as UntypedFormControl;
  }
  get lockingDuration() {
    return this.form.get('lockingDuration') as UntypedFormControl;
  }
  get isVisible() {
    return this.form.get('isVisible') as UntypedFormControl;
  }
  get lockingType() {
    return this.form.get('lockingType') as UntypedFormControl;
  }
  get displayResultInTextSearch() {
    return this.form.get('displayResultInTextSearch') as UntypedFormControl;
  }
  get icon() {
    return this.form.get('icon');
  }
  get explanation() {
    return this.form.get('descriptions')?.get('explanation') as UntypedFormControl;
  }
  get description() {
    return this.form.get('descriptions')?.get('description') as UntypedFormControl;
  }
  get tags() {
    return this.form.get('descriptions')?.get('tags') as UntypedFormControl;
  }
  get idObjectTypeParent() {
    return this.form.get('idObjectTypeParent') as UntypedFormControl;
  }
  get order() {
    return this.form.get('order') as UntypedFormControl;
  }
  get isFolder() {
    return this.form.get('isFolder') as UntypedFormControl;
  }
  get idOTAssociativity0() {
    return this.form.get('idOTAssociativity0') as UntypedFormControl;
  }
  get idOTAssociativity1() {
    return this.form.get('idOTAssociativity1') as UntypedFormControl;
  }

  ngAfterViewInit(): void {
    if (this.treeGrid0 && this.treeGrid1) {
      if (this.settings.isEditMode) {
        this.treeGrid0.element.classList.add('disabletreegrid');
        this.treeGrid1.element.classList.add('disabletreegrid');
        document.getElementById('treeGridsParent')?.classList.add('wrapper');
      }
    }
  }

  ngOnInit(): void {
    this.initializeForm();
    this.disableFormEditMode();

    this.tabIcons = Array.from(Array(299), (x, i) => ({
      id: i,
      path: this.objectTypeIconService.getIconPath(i),
    }));

    this.otService
      .listAll()
      .pipe(takeUntil(this._destroying))
      .subscribe((ot) => {
        this.allOT = ot;
        this.init();
        this.name.markAsTouched();
        this.name.markAsDirty();
      });

    this.idType?.valueChanges.pipe(takeUntil(this._destroying)).subscribe((data) => {
      this.onIdTypeChange(data);
    });

    this.isObjectTypeConvertible = this.otService.isObjectTypeConvertible(this.settings.object);

    if (this.settings && this.isObjectTypeConvertible) {
      this.attributeService
        .doesObjectTypeHaveOnlyTabOrGroupAttributes(this.settings.object.id)
        .pipe(takeUntil(this._destroying))
        .subscribe((isOTConvertible) => {
          this.isObjectTypeConvertible = isOTConvertible;
          if (this.isObjectTypeConvertible) {
            this.enableFormObjectTypeConvertible();
          }
        });
    }
    if (this.settings) {
      this.types = this.getTypesTranslated(this.settings.isEditMode);
      this.translate.onLangChange.pipe(takeUntil(this._destroying)).subscribe(() => {
        if (this.settings) {
          this.types = this.getTypesTranslated(this.settings.isEditMode);
        }
        this.lockingTypes = this.getLockingTypesTranslated();
      });
      this.types = this.getTypesTranslated(this.settings.isEditMode);
    }
    this.lockingTypes = this.getLockingTypesTranslated();

    this.helpboxService
      .getMultipleStates()
      .pipe(takeUntil(this._destroying))
      .subscribe(([hsState, mhsState, exp]) => {
        this.currentExplanation = exp;
        this.setExplanationStates(hsState, mhsState, exp);
      });
  }

  disableFormEditMode(): void {
    if (this.settings.isEditMode) {
      if (!this.isObjectTypeConvertible && this.idType) {
        this.idType.disable();
      }
      if (
        this.settings.object.type === TxObjectTypeType.Source ||
        this.settings.object.type === TxObjectTypeType.Information ||
        this.settings.object.type === TxObjectTypeType.User
      ) {
        this.hasDistinctName?.disable();
      }
      if (this.settings.object.type === TxObjectTypeType.Portal) {
        this.isVisible?.disable();
        this.displayResultInTextSearch?.disable();
      }
    }
  }

  enableFormObjectTypeConvertible(): void {
    if (this.isObjectTypeConvertible && this.idType) {
      this.idType.enable();
    }
  }

  getTypesTranslated(isEditMode: boolean): any[] {
    const types = [
      { id: 0, viewValue: this.translate.instant(_('admins.dataModel.standard')) },
      { id: -1, viewValue: this.translate.instant(_('admins.dataModel.folder')) },
      { id: 1, viewValue: this.translate.instant(_('admins.dataModel.listing')) },
      { id: 2, viewValue: this.translate.instant(_('admins.dataModel.associative')) },
    ];

    if (!isEditMode) {
      return types;
    }
    if (this.isObjectTypeConvertible) {
      return [
        { id: 0, viewValue: this.translate.instant(_('admins.dataModel.standard')) },
        { id: 1, viewValue: this.translate.instant(_('admins.dataModel.listing')) },
      ];
    }
    types.push(
      { id: 3, viewValue: this.translate.instant(_('admins.dataModel.user')) },
      { id: 4, viewValue: this.translate.instant(_('admins.dataModel.source')) },
      { id: 5, viewValue: this.translate.instant(_('admins.dataModel.information')) },
      { id: 6, viewValue: this.translate.instant(_('admins.dataModel.portal')) }
    );
    return types;
  }

  getLockingTypesTranslated(): any[] {
    return [
      { type: TxLockingType.None, viewValue: this.translate.instant(_('admins.dataModel.none')) },
      {
        type: TxLockingType.Auto,
        viewValue: this.translate.instant(_('admins.dataModel.automatic')),
      },
      {
        type: TxLockingType.Manual,
        viewValue: this.translate.instant(_('admins.dataModel.manual')),
      },
    ];
  }

  isLockingTypeNone(lockingType: TxLockingType) {
    return lockingType === TxLockingType.None;
  }

  getExplanation(globalExpId: string, expId: string, active: boolean): void {
    this.helpboxService.setExplanationsFromId(globalExpId, expId, active);
    if (expId === 'expNature') {
      this.isNatureExplanationDisplayed = true;
      this.isVisibilityExplanationDisplayed = false;
      this.isLockingExplanationDisplayed = false;
    } else if (expId === 'expVisibility') {
      this.isNatureExplanationDisplayed = false;
      this.isVisibilityExplanationDisplayed = true;
      this.isLockingExplanationDisplayed = false;
    } else if (expId === 'expLocking') {
      this.isNatureExplanationDisplayed = false;
      this.isVisibilityExplanationDisplayed = false;
      this.isLockingExplanationDisplayed = true;
    } else {
      this.isNatureExplanationDisplayed = false;
      this.isVisibilityExplanationDisplayed = false;
      this.isLockingExplanationDisplayed = false;
    }
  }

  selectingRowAgain(row: RowDeselectEventArgs, idTree: number) {
    if (row.rowIndex !== undefined) {
      if (idTree === 0 && row.rowIndex === this.indewRowTreeGrid0) {
        this.treeGrid0?.selectRow(row.rowIndex);
      } else if (idTree === 1 && row.rowIndex === this.indewRowTreeGrid1) {
        this.treeGrid1?.selectRow(row.rowIndex);
      }
    }
  }

  storeIndexRow(row: RowSelectEventArgs, idTree: number) {
    if (row.rowIndex !== undefined) {
      if (idTree === 0) {
        this.indewRowTreeGrid0 = row.rowIndex;
      } else if (idTree === 1) {
        this.indewRowTreeGrid1 = row.rowIndex;
      }
    }
  }

  public setIcon(data: number): void {
    this.selectedIcon = data;
    this.icon?.setValue(data);
  }

  public dataBound(event: any, idTree: number) {
    let idOTAssociativity: any;
    let treeGrid: any;
    if (idTree === 0) {
      if (this.idOTAssociativity0) {
        idOTAssociativity = this.idOTAssociativity0.value;
      }
      treeGrid = this.treeGrid0;
    } else {
      if (this.idOTAssociativity1) {
        idOTAssociativity = this.idOTAssociativity1.value;
      }
      treeGrid = this.treeGrid1;
    }

    const index = treeGrid
      .getVisibleRecords()
      .findIndex((el: { txObject: TxObjectType }) => el.txObject.id === idOTAssociativity);
    if (index !== -1) {
      setTimeout(() => {
        treeGrid.selectRow(index);
      }, 400);
    } else {
      setTimeout(() => {
        treeGrid.selectRow(0);
      }, 400);
    }
  }

  public onNodeExpanded(event: RowExpandedEventArgs, data: TxTreeGrid<any>[] | undefined) {
    const obj = data?.find((ot) => ot.id === (event.data as TxTreeGrid<any>).id);
    if (obj) {
      obj.expanded = true;
    }
  }

  public onNodeCollapsed(event: RowCollapsedEventArgs, data: TxTreeGrid<any>[] | undefined) {
    const obj = data?.find((ot) => ot.id === (event.data as TxTreeGrid<any>).id);
    if (obj) {
      obj.expanded = false;
    }
  }

  public changeObjectType(event: RowSelectEventArgs, idTreeGrid: number): void {
    let objectType;
    if (this.allOT) {
      objectType = this.allOT.find((ot) => (event.data as TxTreeGrid<any>).id === ot.id);
    }
    if (objectType) {
      if (idTreeGrid === 0 && this.idOTAssociativity0) {
        this.idOTAssociativity0.setValue(objectType.id);
      }
      if (idTreeGrid === 1 && this.idOTAssociativity1) {
        this.idOTAssociativity1.setValue(objectType.id);
      }
    }
  }

  protected init(): void {
    if (this.allOT) {
      this.allOT.sort((a, b) => a.order - b.order); // a voir
      this.objectTypesStandardTree = this.allOT
        .filter((o) => o.type === TxObjectTypeType.Standard || o.type === TxObjectTypeType.User)
        .map((o) => this.createOTinGrid(o, this.objectTypesStandardTree));
      this.objectTypesStandardTree2 = this.allOT
        .filter((o) => o.type === TxObjectTypeType.Standard || o.type === TxObjectTypeType.User)
        .map((o) => this.createOTinGrid(o, this.objectTypesStandardTree));
      Array.prototype.push.apply(
        this.objectTypesStandardTree2,
        this.allOT
          ?.filter((o) => o.type === TxObjectTypeType.Listing)
          .map((o) => this.createOTinGrid(o, this.objectTypesStandardTree))
      );
    }
  }

  protected convertLockingDurationIntoDayDuration(min: number): number {
    return min / 60 / 24;
  }

  protected convertLockingDurationIntoMinDuration(day: number): number {
    return day * 60 * 24;
  }

  protected formatFormData(data: any): TxObjectTypeFormPane {
    const ot = data;

    // Type
    ot.type = this.convertIdTypeIntoObjectTypeType(ot.idType);
    delete ot.idType;

    // Locking
    if (ot.lockingDuration > 0) {
      ot.lockingDuration = this.convertLockingDurationIntoDayDuration(ot.lockingDuration);
    }

    // Description & tags
    if (ot.descriptions) {
      ot.tags = this.tagsEditor?.getTags();
      ot.description = ot.descriptions.description;
      ot.explanation = ot.descriptions.explanation;
      delete ot.descriptions;
    }

    // Id associativity
    ot.associatedObjectTypesIds = [];
    if (
      ot.type === TxObjectTypeType.Associativity &&
      ot.idOTAssociativity0 &&
      ot.idOTAssociativity1
    ) {
      ot.associatedObjectTypesIds = [ot.idOTAssociativity0, ot.idOTAssociativity1];
    }
    delete ot.idOTAssociativity0;
    delete ot.idOTAssociativity1;

    // Order
    if (
      this.settings &&
      !this.areTypesEqual(ot.type, this.settings.object.type, this.settings.isEditMode)
    ) {
      ot.order = this.otService.getLastFreePositionOfTypeObjectType(ot.type);
      ot.idObjectTypeParent = null;
    } else if (ot.idObjectTypeParent) {
      ot.order = this.otService.getLastFreePositionOfObjectType(ot.idObjectTypeParent);
    } else {
      ot.order = this.otService.getLastFreePositionOfTypeObjectType(ot.type);
    }

    return ot;
  }

  protected save(data: any): Observable<any> {
    return this.otService.convertAndEditOT(data, this.settings.object.type);
  }

  protected add(data: any): Observable<any> {
    return this.otService.addObjectType(data);
  }

  protected createOTinGrid(ot: TxObjectType, otGrid?: TxTreeGrid<any>[]): TxTreeGrid<any> {
    return {
      id: ot.id,
      idParent: ot.idObjectTypeParent,
      icon: this.otService.getIconPath(ot.id),
      name: ot.name,
      tags: ot.tags,
      expanded: this.getExpandedState(ot, otGrid),
      txObject: ot,
    };
  }

  protected getExpandedState(obj: any, objGrid?: TxTreeGrid<any>[]): boolean {
    if (objGrid) {
      const o = objGrid.find((og) => og.id === obj.id);
      if (o?.expanded) {
        return o ? o.expanded : true;
      }
    }
    return true;
  }

  private initializeForm(): void {
    this.idType?.setValue(this.returnIdType(this.settings.object));
    this.idObjectTypeParent?.setValue(this.settings.object.idObjectTypeParent);

    if (this.settings.isEditMode) {
      this.order.setValue(this.settings.object.order);
      this.id.setValue(this.settings.object.id);
      this.name.setValue(this.settings.object.name);
      this.explanation.setValue(this.settings.object.explanation);
      this.description.setValue(this.settings.object.description);
      this.tags.setValue(this.settings.object.tags.join('|'));
      this.setIcon(this.settings.object.icon);
      this.isFolder.setValue(this.settings.object.isFolder);
      this.isVisible.setValue(this.settings.object.isVisible);
      this.hasDistinctName.setValue(this.settings.object.hasDistinctName);
      this.lockingType.setValue(this.settings.object.lockingType);
      this.lockingDuration.setValue(
        Math.round(this.convertLockingDurationIntoMinDuration(this.settings.object.lockingDuration))
      );
      this.displayResultInTextSearch.setValue(this.settings.object.displayResultInTextSearch);
      if (
        this.settings.object.type === TxObjectTypeType.Associativity &&
        this.settings.object.associatedObjectTypesIds &&
        this.settings.object.associatedObjectTypesIds.length === 2
      ) {
        this.idOTAssociativity0.setValue(this.settings.object.associatedObjectTypesIds[0]);
        this.idOTAssociativity1.setValue(this.settings.object.associatedObjectTypesIds[1]);
      }
    } else {
      this.name.setValue('New object type');
      this.idOTAssociativity0?.setValue(0);
      this.idOTAssociativity1?.setValue(0);
    }
  }

  private returnIdType(objectType: any): number {
    if (objectType.type === TxObjectTypeType.Listing) {
      return 1;
    }
    if (objectType.type === TxObjectTypeType.Associativity) {
      return 2;
    }
    if (objectType.isFolder) {
      return -1;
    }

    if (this.settings.isEditMode) {
      if (objectType.type === TxObjectTypeType.User) {
        return 3;
      }
      if (objectType.type === TxObjectTypeType.Source) {
        return 4;
      }
      if (objectType.type === TxObjectTypeType.Information) {
        return 5;
      }
      if (objectType.type === TxObjectTypeType.Portal) {
        return 6;
      }
    }
    return 0;
  }

  private areTypesEqual(
    typeInput: TxObjectTypeType,
    typeOutput: TxObjectTypeType,
    isEditMode: boolean
  ) {
    if (typeInput === typeOutput) {
      return true;
    }
    if (isEditMode) {
      return false;
    } else {
      const standardTypes = [
        TxObjectTypeType.Standard,
        TxObjectTypeType.Portal,
        TxObjectTypeType.User,
      ];
      if (standardTypes.includes(typeInput) && standardTypes.includes(typeOutput)) {
        return true;
      }
      return false;
    }
  }

  private convertIdTypeIntoObjectTypeType(idType: number): TxObjectTypeType | undefined {
    if (idType === 0 || idType === -1) {
      return TxObjectTypeType.Standard;
    }
    if (idType === 1) {
      return TxObjectTypeType.Listing;
    }
    if (idType === 2) {
      return TxObjectTypeType.Associativity;
    }
    if (idType === 3) {
      return TxObjectTypeType.User;
    }
    if (idType === 4) {
      return TxObjectTypeType.Source;
    }
    if (idType === 5) {
      return TxObjectTypeType.Information;
    }
    if (idType === 6) {
      return TxObjectTypeType.Portal;
    }
  }

  private onIdTypeChange(idType: number) {
    // If type == Folder, set isFolder = true
    if (idType === -1) {
      this.isFolder.setValue(true);
    } else {
      this.isFolder.setValue(false);
    }

    // If type change, set "maximum time before expiration" to 0 :
    if (
      this.lockingDuration?.value === undefined ||
      this.lockingDuration.value < 0 ||
      this.lockingDuration.value === null
    ) {
      this.lockingDuration?.setValue(0);
    }
  }

  private isValueNullOrZero(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.valueChanges || control.pristine) {
        return null;
      } else if (this.lockingDuration?.value < 0) {
        return { greaterThanZero: true };
      } else {
        return null;
      }
    };
  }

  private setExplanationStates(hsState: boolean, mhsState: boolean, exp: Details) {
    this.isNatureExplanationDisplayed = false;
    this.isVisibilityExplanationDisplayed = false;
    this.isLockingExplanationDisplayed = false;

    if (!hsState && !mhsState) {
      // All variables are already set to false, no need to change anything.
    } else if (exp.id === 'expNature') {
      this.isNatureExplanationDisplayed = true;
    } else if (exp.id === 'expVisibility') {
      this.isVisibilityExplanationDisplayed = true;
    } else if (exp.id === 'expLocking') {
      this.isVisibilityExplanationDisplayed = true;
      this.isLockingExplanationDisplayed = true;
    }
  }
}
