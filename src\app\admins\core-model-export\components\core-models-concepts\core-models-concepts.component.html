<ng-container *ngIf="flatConcepts">
  <div class="table-grid">
    <tx-grid
      class="table-grid__table"
      #coreModelGrid
      [data]="flatConcepts"
      [columns]="txGridColumns"
      [filterColumns]="filterColumns"
      primaryKey="id"
      groupByField="type"
      [enableFiltering]="allowFiltering"
      [filterOptions]="filterOptionsList"
      [isRowSelectable]="true"
      [enableSearching]="true"
      rowHeight="auto"
      (searchInputChange)="searchItem($event)"
      (searchClear)="resetSearch()">
      <tx-grid-toolbar>
        <ng-template>
          <div class="filters">
            <div *ngIf="onlyErrorsBtnIsShow" class="filters__display-only-concepts-in-error">
              <tx-slide-toggle [formControl]="slideToggleControl">
                <span>
                  {{ 'generic.showOnlyErrors' | translate }}
                </span>
              </tx-slide-toggle>
            </div>
          </div>
        </ng-template>
      </tx-grid-toolbar>

      <tx-grid-group>
        <ng-template let-data let-key="key">
          <span
            [innerHTML]="
              'concepts.' + (data[key] | firstLetterLowercase)
                | translate
                | escapeHtml
                | highlightSearch : (inputSearchValue | escapeHtml)
            "
            class="groupHeader text-color"></span>
          <span class="badge background-grey20 text-color">{{
            data.totalCounts | localizedNumber
          }}</span>
          <span
            *ngIf="data.items | toErroneous : 'errors' | nbErrors : 'errors' as nb"
            class="badge group-nb-error background-e-error"
            >{{
              nb > 1
                ? nb + ' ' + ('generic.errors' | translate)
                : nb + ' ' + ('generic.error' | translate)
            }}</span
          >
        </ng-template>
      </tx-grid-group>

      <tx-grid-column fieldName="name">
        <ng-template let-data>
          <tx-concept-name-table-cell
            [searchById]="searchById"
            [id]="data.id"
            [name]="data['name']"
            [searchValue]="inputSearchValue"
            [icon]="data.icon">
          </tx-concept-name-table-cell>
        </ng-template>
      </tx-grid-column>
      <tx-grid-column fieldName="objectType">
        <ng-template let-data>
          <ng-container *ngIf="data['objectType']">
            <p class="text-with-icon">
              <img alt="object type icon" [src]="data.objectTypeIcon | getIconPath" />
              <span
                [matTooltip]="data['objectType']"
                [innerHTML]="
                  data['objectType']
                    | escapeHtml
                    | highlightSearch : (inputSearchValue | escapeHtml)
                "></span>
            </p>
          </ng-container>
        </ng-template>
      </tx-grid-column>
      <tx-grid-column fieldName="tags">
        <ng-template let-data>
          <tx-texts-to-copy
            [texts]="data['tags']"
            [searchValue]="inputSearchValue"></tx-texts-to-copy>
        </ng-template>
      </tx-grid-column>
      <tx-grid-column fieldName="explanation">
        <ng-template let-data>
          <div
            [matTooltip]="data['explanation']"
            matTooltipClass="tooltip-line-break"
            [innerHTML]="
              data['explanation'] | escapeHtml | highlightSearch : (inputSearchValue | escapeHtml)
            "></div>
        </ng-template>
      </tx-grid-column>
      <tx-grid-column fieldName="metaDataList">
        <ng-template let-data>
          <div class="meta-data-container">
            <p class="meta-data" *ngFor="let metaData of data['metaDataList'] | formatMetaData">
              <span class="meta-data-header">
                {{ metaData.header | translate }}
              </span>
              <span class="two-points">:</span>
              <mat-chip-list [matTooltip]="metaData.value">
                <mat-chip disableRipple="true" class="chip-table chip-filled">
                  {{ metaData.value }}
                </mat-chip>
              </mat-chip-list>
            </p>
          </div>
        </ng-template>
      </tx-grid-column>
      <tx-grid-column fieldName="errors">
        <ng-template let-data>
          <ng-container
            *ngIf="data[errorsColumn.field] | formatErrors as formattedErrors; else noErrors">
            <div class="error-cell is-in-error">
              <p class="error-container" [innerHTML]="formattedErrors"></p>
            </div>
          </ng-container>
        </ng-template>

        <ng-template #headerTemplate let-data>
          <p class="error-header-container">
            <span>{{ errorsColumn.headerText | translate }}</span>
            <span
              *ngIf="nbErrors && nbErrors.errors > 0"
              class="badge background-e-error error-header">
              {{ nbErrors.errors | localizedNumber }}
            </span>
          </p>
        </ng-template>
      </tx-grid-column>
      <tx-grid-column fieldName="view">
        <ng-template let-data>
          <tx-view
            [toFocus]="{
              toFocus: data[conceptTypeColumn.field],
              id: data.id,
              viewTooltip: viewTooltip
            }"
            [target]="'_blank'"></tx-view>
        </ng-template>
      </tx-grid-column>
      <tx-grid-column fieldName="type">
        <ng-template let-data>
          <p>
            <span [matTooltip]="data['type'] | translate">{{ data['type'] | translate }}</span>
          </p>
        </ng-template>
      </tx-grid-column>
    </tx-grid>
    <div class="table-grid__table-info border-grey">
      {{ dataSourceLength }} {{ 'generic.item-s' | translate }}
    </div>
  </div>
</ng-container>

<ng-template #noErrors>
  <div class="error-cell is-ok">
    <p class="ok-container">{{ 'generic.ok' | translate }}</p>
  </div>
</ng-template>
