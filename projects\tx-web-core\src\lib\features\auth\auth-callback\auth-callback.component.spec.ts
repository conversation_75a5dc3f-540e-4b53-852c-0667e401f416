// @ts-strict-ignore
import { HttpClient } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { MockSessionService, AbstractSessionService } from '../../../data-access/session';
import { AuthenticationService } from '../../../data-access/authentication';
import { AuthCallbackComponent } from './auth-callback.component';
import { TX_ENVIRONMENT_TOKEN } from '../../../utilities/environment';

describe('Auth-Callback Component', () => {
  let component: AuthCallbackComponent;
  let fixture: ComponentFixture<AuthCallbackComponent>;
  let authenticationService: AuthenticationService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [AuthCallbackComponent],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: { snapshot: { queryParams: { r: 'user', lgCode: 'fr' } } },
        },
        { provide: Router, useValue: { navigateByUrl: jest.fn() } },
        { provide: AbstractSessionService, useClass: MockSessionService },
        { provide: HttpClient, useClass: HttpClientTestingModule },
        { provide: AuthenticationService, useClass: AuthenticationService },
        {
          provide: TX_ENVIRONMENT_TOKEN,
          useValue: {
            production: false,
            apiUrl: 'https://localhost:44336/',
            authUrl: 'https://auth.teexma.local',
          },
        },
      ],
    }).compileComponents();
    authenticationService = TestBed.inject(AuthenticationService);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AuthCallbackComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should update Session Token', () => {
    const spyToken = jest.spyOn(authenticationService, 'retrieveToken');
    fixture.detectChanges();
    expect(spyToken).toHaveBeenCalled();
  });
});
