import { inject } from '@angular/core';
import {
  CTxAttributeSetLevel,
  TxAttribute,
  TxDataType,
  TxObjectType,
} from '@bassetti-group/tx-web-core/src/lib/business-models';
import {
  TxAttributesService,
  TxLinkTypesService,
} from '@bassetti-group/tx-web-core/src/lib/data-access/structure';
import {
  catchError,
  combineLatest,
  concatMap,
  filter,
  finalize,
  forkJoin,
  from,
  map,
  Observable,
  of,
  switchMap,
  take,
  tap,
} from 'rxjs';
import {
  AsyncTreeGridTXDataSource,
  AsyncTxTreeGrid,
} from '../data-sources/async-tree-tx-data-source';
import { TxAttributeSelectionGrid, TxTreeGrid } from '../tree-grid.models';
import { flattenTree, flattenTreeWithChild } from '../tree-utils';
export class AttributeTreeGridDataSource extends AsyncTreeGridTXDataSource<TxAttribute> {
  public objectType?: TxObjectType;
  public uniqueIdSeparator = '_';
  public allowLoadingLinkedAttributes = false;
  public displayInheritedAttributes = false;
  public attributeSetLevels: CTxAttributeSetLevel[] = [];
  public attributeSetLevelsDisabled: CTxAttributeSetLevel[] = [];
  public currentExpandedAttributes: AsyncTxTreeGrid<TxAttribute> | undefined;
  public allowOnlyOneOTDestination?: number;
  private readonly attributeService: TxAttributesService;
  dataTypes: Set<TxDataType> = new Set();
  enableCheckbox: boolean = false;
  allowSelectionDataType?: TxDataType[];
  selectedAttributes: TxAttributeSelectionGrid[] = [];
  disableLinkLoading: boolean = false;
  allFoldersCloseOnInit: boolean = false;
  markToViewAttribute?: TxTreeGrid<TxAttribute>;

  constructor(
    private readonly linkTypeService: TxLinkTypesService,
    dataTypes: TxDataType[],
    endPoint = of([]),
    primaryKey: 'uniqueId' = 'uniqueId'
  ) {
    super(endPoint, primaryKey);
    this.attributeService = inject(TxAttributesService);
    this.dataTypes = this.initDataTypes(dataTypes);
    this.loadChildrenFn = this.loadAttributesFromObjectType;
    this.createTreeGridObjects = this.formatTreeObject;
  }
  override addData(data?: TxTreeGrid<TxAttribute>[] | Observable<TxTreeGrid<TxAttribute>[]>) {
    if (data == undefined) {
      this.clearExpandedNodes();
      data = this.loadTreeData();
    }
    if (data) {
      super.addData(data as any);
    }
  }
  override isChildLoaded(node: TxAttribute & { children: TxAttribute[] }): boolean {
    if (typeof node !== 'object') {
      return flattenTree(this.dataList)
        .filter((child) => child.hasChildren)
        .map((child) => child.uniqueId)
        .includes(node);
    } else if (!node.children?.length) {
      return false;
    }
    return node.children != undefined && node.children.length > 0;
  }
  override clearSelection() {
    this.selectedAttributes = [];
    this.attributeSetLevels = [];
    super.clearSelection();
  }

  updateDataByPK(
    data: AsyncTxTreeGrid<TxAttribute>[],
    id: string,
    attribute: { [x: string]: any }
  ) {
    for (const node of data) {
      if (node[this.primaryKey as keyof AsyncTxTreeGrid<TxAttribute>] === id) {
        Object.assign(node, attribute);
        return;
      }
      if (node.children) {
        this.updateDataByPK(node.children, id, attribute);
      }
    }
  }

  toggleMultipleNode(nodes: (number | string)[], isChildPresent?: boolean): void {
    if (this.primaryKey === undefined) {
      throw new Error('primaryKey not defined');
    }
    if (this.idObjectType === undefined) {
      throw new Error('idObjectType not defined');
    }
    this._isLoading.next(true);
    const loadObservables: Observable<any>[] = [];
    nodes.forEach((node) => {
      const isExpanded: boolean = this.expandedNodes.has(node);
      const isChildLoaded: boolean = this.isChildLoaded(node as any);
      if (isExpanded) {
        this.expandedNodes.delete(node);
        super.triggerUpdatePageRequest();
        return;
      }
      this.expandedNodes.add(node);
      if (isChildLoaded || isChildPresent) {
        super.triggerUpdatePageRequest();
        return;
      }

      const loadObservable = this.loadAttributesFromObjectType(
        this.objectType?.id as number,
        node
      ).pipe(
        take(1),
        map((children) => {
          const parentAttribute = flattenTree(this.dataList).find((a) => a.uniqueId === node);

          const treeGridObjects = this.createRows(children, parentAttribute);
          const updatedPage = this.updatePage(treeGridObjects, node);

          this.triggerUpdatePageRequest(updatedPage);
        }),
        catchError((err) => {
          console.error('Error loading attributes for node:', node, err);
          return of(null);
        })
      );
      loadObservables.push(loadObservable);
    });
    if (loadObservables.length === 0) {
      this._isLoading.next(false);
      return;
    }
    forkJoin(loadObservables).subscribe({
      complete: () => {
        this._isLoading.next(false);
      },
    });
  }

  separateParentChildren(ids: string[]) {
    const parents = new Set(
      flattenTree(this.dataList)
        .filter((child) => child.isParent ?? child.children ?? child.hasChildren)
        .map((child) => child.uniqueId)
    );
    const childrenIds: string[] = [];
    const parentIds: string[] = [];
    ids.forEach((id) => {
      if (parents.has(id)) {
        parentIds.push(id);
      } else {
        childrenIds.push(id);
      }
    });
    return { parentIds, childrenIds };
  }
  showOnlyChecked() {
    if (!this.objectType && this.idObjectType) {
      this.otService.isReady().subscribe(() => {
        this.objectType = this.idObjectType ? this.otService.getByID(this.idObjectType) : undefined;
      });
    }
    const hasSelectionValue = this.selection.hasValue() && this.selectedAttributes.length > 0;

    const data: TxTreeGrid<TxAttribute>[] = [];
    let attributes: TxAttribute[] = [];
    const _fillData = (levels: CTxAttributeSetLevel[] = [], parentId?: string) => {
      levels.forEach((attributeSetLevel) => {
        const uniqueId: string = parentId
          ? parentId + this.uniqueIdSeparator + attributeSetLevel.idAttribute
          : '' + attributeSetLevel.idAttribute;
        const attribute = attributes.find((a) => a.id === attributeSetLevel.idAttribute);
        if (!hasSelectionValue) {
          this.selectedAttributes.push({
            uniqueId: uniqueId,
            uniqueIdParent: parentId,
            idAttribute: attributeSetLevel.idAttribute,
          });
          this.select(uniqueId);
        }

        if (attribute) {
          const row = {
            id: attribute.id,
            uniqueId,
            idParent: attribute.idAttributeParent === 0 ? undefined : attribute.idAttributeParent,
            uniqueIdParent: parentId,
            icon: this.attributeService.getIconPath(attribute.id),
            tags: attribute.tags,
            dataType: attribute.dataType,
            isParent: attributeSetLevel.childLevels?.length > 0,
            disabled: this.isLevelDisabled(uniqueId),
            isDisabled:
              this.isLevelDisabled(uniqueId) ||
              this.isCheckBoxDisabled(attribute) ||
              this.initializeCheckboxForLinkType(attribute),
            name: attribute.name,
            expanded: attributeSetLevel.childLevels?.length > 0,
            attribute,
            objectType: this.objectType,
            txObject: { ...attribute },
          };

          data.push(row);
        }

        if (attributeSetLevel.childLevels) {
          _fillData(attributeSetLevel.childLevels, uniqueId);
        }
      });
    };

    const ids: number[] = [];
    this.fillIds(ids, this.attributeSetLevels);
    if (ids.length > 0) {
      this._isLoading.next(true);
      this.attributeService.listFromIds(Array.from(ids)).subscribe((atts) => {
        attributes = atts;
        _fillData(this.attributeSetLevels);
        this.addData(data.slice());
        this._isLoading.next(false);
      });
    } else {
      this.addData([]);
    }
  }
  showOnlyCheckedSelection() {
    // Early return if selection already exists
    if (this.selection.hasValue() && this.selectedAttributes.length > 0) {
      return;
    }

    const selectLevel = (level: CTxAttributeSetLevel, parentId: string = '') => {
      const uniqueId = parentId
        ? `${parentId}${this.uniqueIdSeparator}${level.idAttribute}`
        : String(level.idAttribute);

      // Add to selected attributes and select in one go
      this.selectedAttributes.push({
        uniqueId,
        uniqueIdParent: parentId || undefined,
        idAttribute: level.idAttribute,
      });
      this.select(uniqueId);

      // Process child levels if they exist
      level.childLevels?.forEach((child) => selectLevel(child, uniqueId));
    };

    // Process all top-level attributes
    this.attributeSetLevels.forEach((level) => selectLevel(level));
  }
  //private method to be used in showOnlyChecked
  private fillIds(ids: number[], levels: CTxAttributeSetLevel[] = []) {
    levels.forEach((l) => {
      if (!ids.includes(l.idAttribute)) {
        ids.push(l.idAttribute);
      }

      if (l.childLevels) {
        this.fillIds(ids, l.childLevels);
      }
    });
  }
  isCheckBoxDisplayed(data: TxTreeGrid<TxAttribute>): boolean {
    if (!data.txObject) {
      return false;
    }

    if (!this.enableCheckbox) {
      return false;
    }

    if ([TxDataType.Tab, TxDataType.Group].includes(data.txObject.dataType)) {
      return false;
    }

    return true;
  }
  isCheckBoxDisabled(txObject: TxAttribute): boolean {
    if (
      this.isCheckBoxDisplayed({ txObject } as TxTreeGrid<TxAttribute>) &&
      this.allowSelectionDataType &&
      !this.allowSelectionDataType.includes(txObject.dataType)
    ) {
      return true;
    }

    return false;
  }
  isAttributeDisableUnchecked(att?: TxTreeGrid<TxAttribute>) {
    if (
      att &&
      this.isCheckBoxDisplayed(att) &&
      att.isDisabled &&
      !this.isSelected(att.uniqueId ?? '')
    ) {
      return true;
    }
    return false;
  }
  loadNextLevelSelectAll(emitCheckedChanged: () => void, nodes?: string[]): void {
    if (this.objectType === undefined) {
      throw new Error('ObjectType not defined');
    }
    this.changeLoadingState(true);
    if (nodes && nodes?.length > 0) {
      const loadObservables: Observable<any>[] = [];
      const flattenTree = flattenTreeWithChild(this.dataList);
      nodes.forEach((node) => {
        let attribute = flattenTree.find((a) => a.uniqueId === node);
        if (!attribute) {
          return;
        }
        //get checkbox parents of the attribute
        const childToRootCheckboxIds = this.rootToParentCheckableNodeIds(attribute, flattenTree);

        if (this.isCheckBoxDisplayed(attribute) && !attribute.children?.length) {
          if (!attribute.isParent && !attribute.isDisabled) {
            // Case 1: Non-parent and Non-disable attribute with no children
            this.addAttributeSelection([...childToRootCheckboxIds].reverse());
          } else if (attribute.isParent) {
            // Case 2: Parent attribute with no children (load children asynchronously)
            const loadObservable = this.loadAttributesFromObjectType(
              this.objectType?.id as number,
              node
            ).pipe(
              take(1),
              map((children) => {
                const treeGridObjects = this.createRows(
                  children,
                  attribute,
                  true,
                  false,
                  childToRootCheckboxIds
                );
                const updatedPage = this.updatePage(treeGridObjects, node);
                this.triggerUpdatePageRequest(updatedPage);
              }),
              catchError((err) => {
                console.error('Error loading attributes for node:', node, err);
                return of(null);
              })
            );
            loadObservables.push(loadObservable);
          }
        } else {
          // Case 3: Attribute with children or Folder attribute cases
          const selectableIds = this.processSelectableChildrenIds(
            attribute,
            childToRootCheckboxIds[0]?.uniqueId || ''
          );
          if (selectableIds?.length) {
            this.addAttributeSelection([...childToRootCheckboxIds].reverse());
            this.addAttributeSelection(selectableIds, true);
          }
        }
      });
      if (loadObservables.length === 0) {
        this.changeLoadingState(false);
        emitCheckedChanged();

        return;
      }

      forkJoin(loadObservables).subscribe({
        complete: () => {
          this.changeLoadingState(false);
          emitCheckedChanged();
        },
      });
    } else {
      this.getAttributesFromOT(this.objectType?.id)
        .pipe(
          map((attributes) =>
            this.createRows(attributes, this.currentExpandedAttributes, true, true)
          ),
          finalize(() => {
            this.changeLoadingState(false);
            emitCheckedChanged();
          })
        )
        .subscribe((attributes) => attributes);
    }
  }

  loadMarkAttribute(markToViewAttribute: TxTreeGrid<TxAttribute>) {
    let rootToParentCheckboxIds: string[] = [];
    const flattenData = flattenTree(this.dataList);
    rootToParentCheckboxIds = this.rootToParentCheckableNodeIds(markToViewAttribute, flattenData)
      .map((item) => item.uniqueId)
      .reverse();
    rootToParentCheckboxIds.pop();
    this.changeLoadingState(true);

    return this.getAttributesFromOT(this.idObjectType as number).pipe(
      map((attributes) => super.addData(this.formatTreeObject(attributes))),
      switchMap(() => this.loadAttributesSequentially(rootToParentCheckboxIds)),
      finalize(() => this.changeLoadingState(false))
    );
  }
  loadAttributesSequentially(ids: string[]): Observable<void> {
    if (!ids.length) {
      return of();
    }
    return from(ids).pipe(
      concatMap((node) =>
        this.loadAttributesFromObjectType(this.objectType?.id as number, node).pipe(
          take(1),
          map((children) => {
            this.expandedNodes.add(node);
            const parentAttribute = flattenTree(this.dataList).find((a) => a.uniqueId === node);
            const treeGridObjects = this.createRows(children, parentAttribute);
            const updatedPage = this.updatePage(treeGridObjects, node);

            this.triggerUpdatePageRequest(updatedPage);
          }),
          catchError((err) => {
            console.error('Error loading attributes for node:', node, err);
            return of();
          })
        )
      )
    );
  }

  processSelectableChildrenIds(attribute: AsyncTxTreeGrid<TxAttribute>, parentUniqueId?: string) {
    if (attribute.children) {
      let selectableIds: TxAttributeSelectionGrid[] = [];
      const processAttribute = (attribute: AsyncTxTreeGrid<TxAttribute>) => {
        if (this.isCheckBoxDisplayed(attribute)) {
          if (!attribute.isDisabled) {
            selectableIds.push({
              uniqueId: attribute.uniqueId ?? '',
              idAttribute: attribute.id,
              uniqueIdParent: parentUniqueId,
            });
          }
        } else if (attribute.children) {
          attribute.children.forEach((child: AsyncTxTreeGrid<TxAttribute>) => {
            processAttribute(child);
          });
        }
      };

      attribute.children.forEach((att: any) => {
        processAttribute(att);
      });
      return selectableIds;
    }
  }
  addAttributeSelection(ids: TxAttributeSelectionGrid[], isGroupSelection: boolean = false): void {
    for (let i = 0; i < ids.length; i++) {
      let existingNode = this.selectedAttributes.find((node) => node.uniqueId === ids[i].uniqueId);
      if (!existingNode) {
        let uniqueIdParent: string | undefined;
        if (isGroupSelection) {
          uniqueIdParent = ids[i].uniqueIdParent;
        } else if (i > 0) {
          uniqueIdParent = ids[i - 1]?.uniqueId;
        } else {
          uniqueIdParent = undefined;
        }

        this.selectedAttributes.push({
          ...ids[i],
          uniqueIdParent,
        });
        this.select(ids[i].uniqueId);
      }
    }
    this.buildSelectionTree(this.selectedAttributes);
  }
  removeAttributeSelection(attId: string, flattenData: TxTreeGrid<TxAttribute>[]): void {
    const idsToRemove = new Set<string>();

    // Helper function to recursively collect IDs to remove and deselect them
    const collectChildrenIdsToRemove = (id: string) => {
      idsToRemove.add(id);
      this.selection.deselect(id);

      // Collect and deselect children
      this.selectedAttributes
        .filter((item) => item.uniqueIdParent === id)
        .forEach((item) => collectChildrenIdsToRemove(item.uniqueId));
    };

    // Helper function to remove disabled parents if no children are selected
    const removeDisabledParents = (id: string) => {
      const attribute = this.selectedAttributes.find((item) => item.uniqueId === id);
      if (!attribute) return;

      const parentAtt = flattenData?.find((att) => att.uniqueId === attribute.uniqueIdParent);
      if (parentAtt?.isDisabled) {
        const hasSelectedChildren = this.selectedAttributes.some(
          (a) => a.uniqueIdParent === parentAtt.uniqueId && !idsToRemove.has(a.uniqueId)
        );

        if (!hasSelectedChildren && parentAtt.uniqueId) {
          idsToRemove.add(parentAtt.uniqueId);
          this.selection.deselect(parentAtt.uniqueId);
          removeDisabledParents(parentAtt.uniqueId);
        }
      }
    };

    // Collect IDs to remove and deselect them
    collectChildrenIdsToRemove(attId);
    removeDisabledParents(attId);
    this.selectedAttributes = this.selectedAttributes.filter(
      (item) => !idsToRemove.has(item.uniqueId)
    );
    this.buildSelectionTree(this.selectedAttributes);
  }
  rootToParentCheckableNodeIds(
    attribute: TxTreeGrid<TxAttribute>,
    flattenData: TxTreeGrid<TxAttribute>[]
  ): TxAttributeSelectionGrid[] {
    const parentIds: TxAttributeSelectionGrid[] = [];
    let isDisableAndUnChecked = false;
    const findParent = (attribute: TxTreeGrid<TxAttribute>) => {
      if (this.isCheckBoxDisplayed(attribute)) {
        parentIds.push({ uniqueId: attribute.uniqueId as string, idAttribute: attribute.id });
      }
      const parentAttr = flattenData.find((item) => item.uniqueId === attribute.uniqueIdParent);
      if (parentAttr) {
        findParent(parentAttr);
      }
    };
    findParent(attribute);
    return isDisableAndUnChecked ? [] : parentIds;
  }

  getObjectType(idObjectType: number) {
    return this.otService.getByID(idObjectType);
  }
  isPortalOT(): boolean {
    return this.objectType ? this.otService.isPortalOT(this.objectType) : false;
  }

  isEnumerationOT(): boolean {
    return this.objectType ? this.otService.isListingOT(this.objectType) : false;
  }
  protected getLevelIdsToDisable(): string[] {
    const ids: string[] = [];
    this.fillIdsWithAttributeSetLevels(ids, this.attributeSetLevelsDisabled);
    return ids;
  }

  protected isLevelDisabled(uniqueId: string): boolean {
    const ids = this.getLevelIdsToDisable();
    return ids.includes(uniqueId);
  }
  protected getExpandedState(obj: any, objGrid: TxTreeGrid<TxAttribute>[]): boolean {
    if (objGrid) {
      const o = objGrid.find((og) => og.uniqueId === obj.uniqueId);
      return o?.expanded ?? true;
    }
    return true;
  }
  protected getDataTypes(): TxDataType[] {
    const tabsIncluded = [...this.dataTypes].includes(TxDataType.Tab);
    const groupsIncluded = [...this.dataTypes].includes(TxDataType.Group);
    let types: number[] = [];

    if ([...this.dataTypes].length) {
      // force the adding of tab and group to load children
      if (!tabsIncluded) {
        types.push(TxDataType.Tab);
      }

      if (!groupsIncluded) {
        types.push(TxDataType.Group);
      }

      types = types.concat([...this.dataTypes]);
    }

    return types;
  }
  protected filterAttributesFromDataTypes(attributes: TxAttribute[]): TxAttribute[] {
    if (this.getDataTypes().length) {
      const targetedAttributes = attributes.filter((a) => {
        if (!this.displayInheritedAttributes && a.isInherited) {
          return false;
        }

        return [...this.dataTypes].includes(a.dataType);
      });
      let parentIds: number[] = [];
      targetedAttributes.forEach((a) => {
        parentIds = parentIds.concat(this.attributeService.getParentsIds(a));
        parentIds.push(a.id);
      });

      if (parentIds.length) {
        attributes = attributes.filter((a) => parentIds.includes(a.id));
      }
    }

    return attributes;
  }
  private fillIdsWithAttributeSetLevels(
    ids: string[],
    levels: CTxAttributeSetLevel[] = [],
    idParent: string = ''
  ) {
    levels.forEach((attributeSetLevel) => {
      const uniqueId: string = idParent
        ? idParent + this.uniqueIdSeparator + attributeSetLevel.idAttribute
        : '' + attributeSetLevel.idAttribute;
      ids.push(uniqueId);

      if (attributeSetLevel.childLevels) {
        this.fillIdsWithAttributeSetLevels(ids, attributeSetLevel.childLevels, uniqueId);
      }
    });
  }
  private loadTreeData() {
    if (!this.objectType && !this.idObjectType) {
      console.warn('idObjectType or idObjectType are mandatory');
      return;
    }
    this.changeLoadingState(true);
    if (!this.objectType && this.idObjectType) {
      return combineLatest([of(this.idObjectType), this.otService.isReady()]).pipe(
        switchMap(([idObjectType]) => of(this.otService.getByID(idObjectType))),
        map((objectType) => objectType),
        filter((objectType: TxObjectType | undefined): objectType is TxObjectType => {
          const result = objectType !== undefined;
          if (!result) {
            throw new Error(
              'idObjectType supplied to ObjectTreeGridComponent does not correspond to any ObjectType'
            );
          }
          return objectType !== undefined;
        }),
        tap((objectType) => {
          this.objectType = objectType;
        }),
        switchMap(() =>
          this.getAttributesFromOT(this.objectType?.id as number).pipe(
            map((attribute) => this.formatTreeObject(attribute))
          )
        ),
        finalize(() => {
          this.changeLoadingState(false);
        })
      );
    } else {
      return this.getAttributesFromOT(this.objectType?.id as number).pipe(
        map((attributes) => this.formatTreeObject(attributes)),
        finalize(() => {
          this.changeLoadingState(false);
        })
      );
    }
  }

  private getAttributesFromOT(idOT: number): Observable<TxAttribute[]> {
    return this.attributeService.listAttributesFromObjectType(idOT || -1, [...this.getDataTypes()]);
  }
  private initDataTypes(dataTypes: TxDataType[]): Set<TxDataType> {
    return new Set([...this.dataTypes, ...(dataTypes || [])]);
  }
  private loadAttributesFromObjectType(idOT: number, uniqueIdParent: number | string) {
    this.currentExpandedAttributes = flattenTree(this.dataList).find(
      (e) => e.uniqueId === uniqueIdParent.toString()
    );
    const attribute = this.currentExpandedAttributes?.txObject;
    let idObjectType: number | undefined;
    if (attribute) {
      const linkInv = attribute.dataType === TxDataType.LinkInv;

      if (attribute.linkType) {
        idObjectType = linkInv
          ? attribute.linkType.idSourceObjectType
          : attribute.linkType.idDestinationObjectType;
      }

      if (linkInv && attribute?.linkType?.isAssociative && idObjectType) {
        const associativeLinkType = this.linkTypeService.findAssociativeLinkType(
          attribute.linkType.id,
          idObjectType
        );
        if (associativeLinkType) {
          idObjectType = associativeLinkType.idDestinationObjectType;
        }
      }
    }
    return this.attributeService.listAttributesFromObjectType(idObjectType ?? -1, [
      ...this.getDataTypes(),
    ]);
  }

  private formatTreeObject(attributes: TxAttribute[]): TxTreeGrid<TxAttribute>[] {
    if (this.dataList.length === 0) {
      return this.createRows(attributes);
    }

    return this.createRows(attributes, this.currentExpandedAttributes).reduce(
      (acc, val) => acc.concat(val),
      [] as Array<TxTreeGrid<TxAttribute>>
    );
  }

  private createRows(
    attributes: TxAttribute[],
    parentNode?: TxTreeGrid<TxAttribute>,
    allowSelection?: boolean,
    skipUpdateExpand?: boolean,
    childToRootCheckboxIds?: TxAttributeSelectionGrid[]
  ): TxTreeGrid<TxAttribute>[] {
    attributes = this.filterAttributesFromDataTypes(attributes);
    const rowsAdding: TxTreeGrid<TxAttribute>[] = [];
    let selectableRowIds: TxAttributeSelectionGrid[] = [];

    let rows: TxTreeGrid<TxAttribute>[] = attributes.map((att) => {
      const hasDynamicLoading = this.hasDynamicLoading(att);
      const id = this.generateUniqueId(att, parentNode);
      let idParent;

      if (parentNode && att.idAttributeParent) {
        const child = rowsAdding.find((d) => d.id === att.idAttributeParent);
        idParent = child ? child.uniqueId : parentNode.uniqueId;
      } else if (parentNode) {
        idParent = parentNode.uniqueId;
      } else if (att.idAttributeParent) {
        idParent = '' + att.idAttributeParent;
      } else {
        idParent = undefined;
      }

      const row = {
        id: att.id,
        uniqueId: id,
        idParent: att.idAttributeParent === 0 ? undefined : att.idAttributeParent,
        uniqueIdParent: idParent,
        icon: this.attributeService.getIconPath(att.id),
        tags: att.tags,
        dataType: att.dataType,
        isParent: hasDynamicLoading,
        disabled: this.isLevelDisabled(id),
        isDisabled: this.getDisabledState(att, id),
        name: att.name,
        expanded: this.getExpandedStateForRow(att, hasDynamicLoading),
        objectType: this.objectType,
        txObject: { ...att },
      };
      rowsAdding.push(row);
      if (row.expanded && !skipUpdateExpand) {
        this.expandedNodes.add(row.uniqueId);
      }
      if (allowSelection && this.isCheckBoxDisplayed(row) && !row.isDisabled) {
        selectableRowIds.push({
          uniqueId: row.uniqueId,
          idAttribute: row.id,
          uniqueIdParent: childToRootCheckboxIds ? childToRootCheckboxIds[0]?.uniqueId : undefined,
        });
      }
      this.currentExpandedAttributes = undefined;
      return row;
    });

    rows.sort((a, b) =>
      a.txObject?.order != null && b.txObject?.order != null
        ? a.txObject?.order - b.txObject?.order
        : 1
    );
    this.currentExpandedAttributes = undefined;
    if (allowSelection && selectableRowIds.length) {
      if (childToRootCheckboxIds?.length) {
        this.addAttributeSelection([...childToRootCheckboxIds].reverse());
      }
      this.addAttributeSelection(selectableRowIds, true);
    }
    return rows;
  }

  private buildSelectionTree(nodes: TxAttributeSelectionGrid[]) {
    const nodeMap: { [key: string]: CTxAttributeSetLevel } = {};
    const rootNodes: CTxAttributeSetLevel[] = [];
    // Create a map of uniqueId to node for quick lookup
    nodes.forEach((node) => {
      const attributeSetLevel = new CTxAttributeSetLevel();
      attributeSetLevel.idAttribute = node.idAttribute;
      nodeMap[node.uniqueId] = attributeSetLevel;
    });

    // Build the tree structure
    nodes.forEach((node) => {
      if (node.uniqueIdParent) {
        const parent = nodeMap[node.uniqueIdParent];
        if (parent) {
          parent.childLevels.push(nodeMap[node.uniqueId]);
        }
      } else {
        rootNodes.push(nodeMap[node.uniqueId]);
      }
    });
    this.attributeSetLevels = rootNodes;
  }
  private initializeCheckboxForLinkType(txObject: TxAttribute) {
    if (!this.enableCheckbox || !this.allowOnlyOneOTDestination) {
      return false;
    }

    if (
      ((txObject.dataType === TxDataType.LinkDirect || txObject.dataType === TxDataType.LinkBi) &&
        txObject.linkType?.idDestinationObjectType !== this.allowOnlyOneOTDestination) ||
      (txObject.dataType === TxDataType.LinkInv &&
        txObject.linkType?.idSourceObjectType !== this.allowOnlyOneOTDestination)
    ) {
      return true;
    } else {
      return false;
    }
  }

  private getDisabledState(att: TxAttribute, id: string): boolean {
    return (
      this.isLevelDisabled(id) ||
      this.isCheckBoxDisabled(att) ||
      this.initializeCheckboxForLinkType(att)
    );
  }
  private hasDynamicLoading(att: TxAttribute): boolean {
    return (
      !this.disableLinkLoading &&
      this.attributeService.isLinkAttribute(att) &&
      att.dataType !== TxDataType.Listing
    );
  }
  private generateUniqueId(att: TxAttribute, parentNode?: TxTreeGrid<TxAttribute>): string {
    return parentNode ? parentNode.uniqueId + this.uniqueIdSeparator + att.id : '' + att.id;
  }
  private getExpandedStateForRow(att: TxAttribute, hasDynamicLoading: boolean): boolean {
    if (hasDynamicLoading) return false;
    if (
      this.allFoldersCloseOnInit &&
      !this.isCheckBoxDisplayed({ txObject: att } as TxTreeGrid<TxAttribute>) &&
      !this.markToViewAttribute
    ) {
      return false;
    }
    return this.getExpandedState(att, this.dataList);
  }
}
