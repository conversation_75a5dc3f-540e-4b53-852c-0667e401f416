import { LegacyTxObjectTypeService } from './structure/services/object-type.service';
import { Injectable } from '@angular/core';
import { TxApiService } from './structure/services/api.service';
import { TxUnitService } from './structure/services/unit.service';
import { TxStructureService } from './structure/services/structure.service';
import { LegacyTxFormsService } from './forms.service';

@Injectable({
  providedIn: 'root',
})
export class TxLibService {
  constructor(
    public apiService: TxApiService,
    public objectTypeService: LegacyTxObjectTypeService,
    public structureService: TxStructureService,
    public formService: LegacyTxFormsService,
    public unitService: TxUnitService
  ) {}

  start(apiUrl: string, formRules?: any[]) {
    this.apiService.start(apiUrl);
    this.objectTypeService.start();
    this.structureService.start();
    this.formService.start(formRules);
  }
}
