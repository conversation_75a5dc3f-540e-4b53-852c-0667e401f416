import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TxTreeViewComponent } from '../trees';
import { TxSidebarComponent } from './sidebar.component';

describe('TxSidebarComponent', () => {
  let component: TxSidebarComponent<any>;
  let fixture: ComponentFixture<TxSidebarComponent<any>>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NoopAnimationsModule, TxSidebarComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(TxSidebarComponent);
    component = fixture.componentInstance;
    component.fields = [
      { id: '1', name: 'Node 1', children: [] },
      { id: '2', name: 'Node 2', children: [] },
    ];
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set selected nodes in TreeViewComponent', () => {
    const treeViewComponent = TestBed.createComponent(TxTreeViewComponent).componentInstance;
    component.tree = treeViewComponent;

    const selectedNode = '2';
    component.selectedNode = selectedNode;

    fixture.detectChanges();
    setTimeout(() => {
      expect(treeViewComponent.selectedRowPKValue).toEqual(selectedNode);
    }, 0);
  });
});
