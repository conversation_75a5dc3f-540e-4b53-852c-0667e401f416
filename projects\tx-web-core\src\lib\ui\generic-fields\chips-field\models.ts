import { IconName } from '@fortawesome/fontawesome-svg-core';
import { omit } from 'ramda';

export interface TxChipStandard {
  type: 'standard';
  name: string;
  selectable?: boolean;
  selected?: boolean;
  removable?: boolean;
  icon?: ChipIcon;
}
export interface TxChipFile {
  type: 'file';
  name: string;
  selectable?: boolean;
  selected?: boolean;
  removable?: boolean;
  icon?: ChipIcon;
  viewableFile?: boolean;
  fileUrl?: string;
  idFile?: number;
}
export type TxChipType = 'standard' | 'file';
export type TxChip = TxChipFile | TxChipStandard;
export interface ChipsOptions {
  selectable: boolean;
  removable: boolean;
  selected?: boolean;
  icon: IconName;
}
export type ChipIcon = ['fal' | 'fas' | 'fab', IconName];

export function txChipStandard(chip?: Partial<Omit<TxChipStandard, 'type'>>): TxChipStandard {
  return {
    type: 'standard',
    name: chip?.name ?? '',
    selectable: chip?.selectable,
    selected: chip?.selectable ? chip?.selected ?? false : undefined,
    removable: chip?.removable,
    icon: chip?.icon,
  };
}
export function txChipFile(chip?: Partial<Omit<TxChipFile, 'type'>>): TxChipFile {
  return {
    type: 'file',
    name: chip?.name ?? '',
    selectable: chip?.selectable,
    selected: chip?.selectable ? chip?.selected ?? false : undefined,
    removable: chip?.removable,
    icon: chip?.icon,
    viewableFile: chip?.viewableFile,
    fileUrl: chip?.viewableFile ? chip.fileUrl : undefined,
    idFile: chip?.idFile,
  };
}
export function txChip(chip?: Partial<TxChip>): TxChipStandard | TxChipFile {
  const type = chip?.type ?? 'standard';
  const restChip = chip ? omit(['type'], chip) : undefined;
  return type === 'standard' ? txChipStandard(restChip) : txChipFile(restChip);
}

export function chipsOptions(options?: Partial<ChipsOptions>): ChipsOptions {
  return {
    selectable: options?.selectable ?? false,
    removable: options?.removable ?? false,
    selected: options?.selectable ? options.selected ?? false : undefined,
    icon: options?.icon ?? 'adn',
  };
}

export function isChipFile(value: TxChip): value is TxChipFile {
  return value.type === 'file';
}
