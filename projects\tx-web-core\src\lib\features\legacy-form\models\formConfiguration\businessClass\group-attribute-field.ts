import { LegacyTxAttribute } from '../../../services/structure/models/attribute';
import { TxEditionMode } from './form-enum';
import { TxVirtualAttributeField } from './virtual-attribute-field';

export class TxGroupAttributeField extends TxVirtualAttributeField {
  dynamicGroupSettings!: TxDynamicGroupSettings;
  children!: TxVirtualAttributeField[];

  constructor(public editionMode: TxEditionMode) {
    super();
  }

  reset() {
    super.reset();
    this.children = [];
  }

  assign(grpAttField?: Partial<TxGroupAttributeField>) {
    super.assign(grpAttField);
    this.dynamicGroupSettings = grpAttField?.dynamicGroupSettings as TxDynamicGroupSettings;
  }
}

export class TxDynamicGroupSettings {
  lnkAttHandlingDynamicGroups: LegacyTxAttribute;
  valid: boolean;

  constructor(dynamicGrpSettings?: Partial<TxDynamicGroupSettings>) {
    this.lnkAttHandlingDynamicGroups =
      dynamicGrpSettings?.lnkAttHandlingDynamicGroups as LegacyTxAttribute;
    this.valid = dynamicGrpSettings?.valid as boolean;
  }
}
