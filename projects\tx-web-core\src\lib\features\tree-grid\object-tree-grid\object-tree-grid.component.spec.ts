import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import {
  AbstractSessionService,
  TX_ENVIRONMENT_TOKEN,
  TxConfigService,
  TxObjectsTypeService,
  TxObjectTypeIconService,
} from '@bassetti-group/tx-web-core';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { of } from 'rxjs';
import { ObjectTreeGridDataSource } from './object-tree-grid-data-source';
import { ObjectTreeGridComponent } from './object-tree-grid.component';

describe('ObjectTreeGridComponent', () => {
  let component: ObjectTreeGridComponent;
  let fixture: ComponentFixture<ObjectTreeGridComponent>;
  let dataSource: jest.Mocked<ObjectTreeGridDataSource>;

  const mockData = [
    {
      id: 1,
      idObjectParent: 28,
      order: 2,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2022-09-04T16:42:31.0000000Z',
      searchName: '',
      databaseRight: 'dbrNone',
      name: 'Administrator',
      tags: [],
    },
    {
      id: 28,
      idObjectParent: 0,
      order: 0,
      isParent: true,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2013-05-21T00:00:00.0000000Z',
      searchName: '',
      databaseRight: 'dbrNone',
      name: 'Node1',
      tags: [],
      children: [],
    },
    {
      id: 29,
      idObjectParent: 28,
      order: 1,
      isParent: true,
      isFolder: true,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2013-05-21T00:00:00.0000000Z',
      searchName: '',
      databaseRight: 'dbrNone',
      name: 'Node2',
      tags: [],
    },
    {
      id: 174,
      idObjectParent: 29,
      order: 3,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2014-04-28T16:09:06.0000000Z',
      searchName: '',
      databaseRight: 'dbrNone',
      name: 'New2',
      tags: [],
    },
    {
      id: 198,
      idObjectParent: 0,
      order: 4,
      isParent: true,
      isFolder: true,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2017-09-13T15:03:19.0000000Z',
      searchName: '',
      databaseRight: 'dbrNone',
      name: 'Node3',
      tags: [],
    },
    {
      id: 199,
      idObjectParent: 198,
      order: 5,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2017-09-13T15:03:25.0000000Z',
      searchName: '',
      databaseRight: 'dbrNone',
      name: 'UserRole2',
      tags: [],
    },
    {
      id: 200,
      idObjectParent: 198,
      order: 6,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2017-09-13T15:03:31.0000000Z',
      searchName: '',
      databaseRight: 'dbrNone',
      name: 'UserRole3',
      tags: [],
    },
  ];

  const mockEnvironment = {
    apiUrl: 'https://mock-api.com',
    production: false,
    version: '1.0.0',
  };

  const mockSessionService = {
    getCurrentUser: jest.fn().mockReturnValue(of({ id: 1, name: 'Test User' })),
    isAuthenticated: jest.fn().mockReturnValue(of(true)),
    getToken: jest.fn().mockReturnValue(of('mock-token')),
    getUserPreferences: jest.fn().mockReturnValue(of({})),
  };

  const mockTxObjectsTypeService = {
    getObjectsType: jest.fn().mockReturnValue(of([])),
    getObjectType: jest.fn().mockReturnValue(of({})),
    getObjectsTypeTree: jest.fn().mockReturnValue(of([])),
    isReady: jest.fn().mockReturnValue(of(true)),
    apiUrl: 'https://mock-api.com',
  };

  const mockConfigService = {
    getApiUrl: jest.fn().mockReturnValue('https://mock-api.com'),
    getEnvironment: jest.fn().mockReturnValue(mockEnvironment),
  };

  const mockObjectTypeIconService = {
    getIconPath: jest.fn().mockReturnValue('mock-icon'),
  };

  const mockDataSource = {
    isLoading$: of(false),
    changes: of(null),
    clearSelection: jest.fn(),
    getSelectedElements: jest.fn().mockReturnValue([]),
    prepareAddData: jest.fn(),
    addData: jest.fn(),
    expandedNodes: new Set(),
    loadSearchInputData: jest.fn(),
    loadSearchStringData: jest.fn(),
    triggerUpdatePageRequest: jest.fn(),
    selection: {
      hasValue: jest.fn().mockReturnValue(false),
      select: jest.fn(),
      deselect: jest.fn(),
    },
    isSelected: jest.fn().mockReturnValue(false),
    toggleSelection: jest.fn(),
    dataList: [],
    loadRootToParentIds: jest.fn().mockReturnValue(of([])),
    select: jest.fn(),
    deselectElement: jest.fn(),
    loadChildrenSelectAll: jest.fn(),
    childSelection: jest.fn(),
    loadAllNodeData: jest.fn(),
    loadAllLocalNodeData: jest.fn(),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        TranslateTestingModule.withTranslations({
          en: {},
          fr: {},
        }),
        FontAwesomeTestingModule,
        HttpClientTestingModule,
      ],
      providers: [
        { provide: ObjectTreeGridDataSource, useValue: mockDataSource },
        { provide: TxObjectsTypeService, useValue: mockTxObjectsTypeService },
        { provide: TxObjectTypeIconService, useValue: mockObjectTypeIconService },
        { provide: TxConfigService, useValue: mockConfigService },
        { provide: TX_ENVIRONMENT_TOKEN, useValue: mockEnvironment },
        { provide: AbstractSessionService, useValue: mockSessionService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ObjectTreeGridComponent);
    component = fixture.componentInstance;
    component.data = mockData;
    component.idObjectType = 1;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('should set data input', () => {
    fixture.detectChanges();
    const matRowDes = fixture.debugElement.queryAll(By.css('.mat-mdc-row'));
    const matRowEls = matRowDes.map((row) => row.nativeElement);

    expect(matRowEls.length).toBeGreaterThan(0);
  });
});
