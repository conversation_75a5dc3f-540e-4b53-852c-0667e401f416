import {
  Preview,
  applicationConfig,
  componentWrapperDecorator,
  moduleMetadata,
} from '@storybook/angular';
import { setCompodocJson } from '@storybook/addon-docs/angular';
import docJson from '../documentation.json';
import { provideHttpClient } from '@angular/common/http';
import { APP_INITIALIZER } from '@angular/core';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { fal } from '@fortawesome/pro-light-svg-icons';
import { StorybookTranslateModule } from '../src/stories/storybook-translate-module';
import { MatTooltipModule } from '@angular/material/tooltip';
import { provideAnimations } from '@angular/platform-browser/animations';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatStepperModule } from '@angular/material/stepper';
import { MatTabsModule } from '@angular/material/tabs';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { UploaderModule } from '@syncfusion/ej2-angular-inputs';
import { MatNativeDateModule, MatOptionModule, MatRippleModule } from '@angular/material/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DropDownTreeModule } from '@syncfusion/ej2-angular-dropdowns';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MockService } from 'ng-mocks';
import { fas } from '@fortawesome/pro-solid-svg-icons';
import {
  MOCK_LINK_TYPE_SERVICE,
  MOCK_OBJECTS_SERVICE,
  MOCK_OBJECTS_TYPE_SERVICE,
  TxAttributesService,
  TxCommonService,
  TxLinkTypesService,
  TxObjectsService,
  TxObjectsTypeService,
} from '../src/lib/data-access/structure';
import { AbstractSessionService, TxObjectTypeIconService } from '../src/public-api';
setCompodocJson(docJson);

import { registerLicense } from '@syncfusion/ej2-base';
import { MOCK_ATTRIBUTES_SERVICE } from '../src/lib/data-access/structure/specs/mock-attributes.service';
import { MOCK_OBJECTS_TYPE_ICON_SERVICE } from '../src/lib/data-access/structure/specs/mock-object-type-icon.service';

// Registering Syncfusion license key.
registerLicense(
  'ORg4AjUWIQA/Gnt2VVhkQlFadVdJXGFWfVJpTGpQdk5xdV9DaVZUTWY/P1ZhSXxQdk1iWH9ac3dRRGJZWEY='
);

const preview: Preview = {
  parameters: {
    //actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    layout: 'centered',
  },
  decorators: [
    applicationConfig({
      providers: [
        provideHttpClient(),
        provideAnimations(),
        {
          provide: APP_INITIALIZER,
          useFactory: (library: FaIconLibrary) => async () => {
            library.addIconPacks(fal);
            library.addIconPacks(fas);
          },
          deps: [FaIconLibrary],
          multi: true,
        },
        {
          provide: AbstractSessionService,
          useValue: MockService(AbstractSessionService),
        },
        {
          provide: TxAttributesService,
          useValue: MOCK_ATTRIBUTES_SERVICE,
        },
        {
          provide: TxObjectsTypeService,
          useValue: MOCK_OBJECTS_TYPE_SERVICE,
        },
        {
          provide: TxLinkTypesService,
          useValue: MOCK_LINK_TYPE_SERVICE,
        },
        {
          provide: TxObjectsService,
          useValue: MOCK_OBJECTS_SERVICE,
        },
        {
          provide: TxCommonService,
          useValue: MockService(TxCommonService),
        },
        {
          provide: TxObjectTypeIconService,
          useValue: MOCK_OBJECTS_TYPE_ICON_SERVICE,
        },
      ],
    }),
    moduleMetadata({
      imports: [
        StorybookTranslateModule,
        MatTooltipModule,
        MatFormFieldModule,
        MatStepperModule,
        MatTabsModule,
        MatIconModule,
        MatInputModule,
        MatSlideToggleModule,
        MatButtonToggleModule,
        MatChipsModule,
        MatButtonModule,
        MatSelectModule,
        MatDividerModule,
        MatProgressBarModule,
        UploaderModule,
        MatRippleModule,
        ReactiveFormsModule,
        DropDownTreeModule,
        MatDatepickerModule,
        MatNativeDateModule,
        MatDialogModule,
        MatMenuModule,
        MatCheckboxModule,
        MatOptionModule,
        MatDividerModule,
        FormsModule,
        MatProgressBarModule,
      ],
    }),
    componentWrapperDecorator((story) => `<div class="blue-theme-light" >  ${story} </div>`),
  ],
};

export default preview;
