import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChange,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import {
  MatSlideToggle,
  MatSlideToggleChange,
  MatSlideToggleModule,
} from '@angular/material/slide-toggle';
import {
  CTxAttributeSetLevel,
  DataTypePipe,
  TxAttribute,
  TxDataType,
} from '@bassetti-group/tx-web-core/src/lib/business-models';
import {
  DataSourceInitialized,
  dataSourceInitialized,
  InputSearchEventInfo,
  TxGridColumn,
  TxGridColumnTemplate,
  TxGridRowSelectArgs,
} from '@bassetti-group/tx-web-core/src/lib/features/grid';
import {
  BeforeOpenCloseMenuEventArgs,
  TxContextMenuComponent,
  TxContextMenuEventArgs,
  TxContextMenuItem,
} from '@bassetti-group/tx-web-core/src/lib/ui/context-menu';
import { NoRecordComponent } from '@bassetti-group/tx-web-core/src/lib/ui/no-record';
import { TxTextsToCopyComponent } from '@bassetti-group/tx-web-core/src/lib/ui/texts-to-copy';
import { ArrayUtils } from '@bassetti-group/tx-web-core/src/lib/utilities';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { uniq } from 'ramda';
import { Subscription, take } from 'rxjs';
import { AsyncTxTreeGrid } from '../data-sources/async-tree-tx-data-source';
import { TxDataTreeGridDirective } from '../data-tree-grid.directive';
import { TxObjectContextMenu } from '../object-tree-grid/object-tree-grid.model';
import { provideTreeGridDataSource, TxTreeGridComponent } from '../tree-grid.component';
import { TxTreeExpandState, TxTreeGridChildren } from '../tree-grid.interface';
import {
  TxAttributeCheckChangeEvent,
  TxAttributeSelectionEvent,
  TxAttributeSelectionGrid,
  TxTreeGrid,
} from '../tree-grid.models';
import { TxTreeGridModule } from '../tree-grid.module';
import { flattenTree, flattenTreeWithChild } from '../tree-utils';
import { AttributeTreeGridDataSource } from './attribute-tree-grid-data-source';
export interface AttributeTreeGridColumn extends TxAttribute {
  type: string;
  width: string;
}
export interface AttributeAdditionalTreeGridColumn extends TxGridColumn<any> {
  template?: TemplateRef<Element>;
}
@Component({
  selector: 'tx-attributes-tree-grid',
  standalone: true,
  imports: [
    TxTreeGridModule,
    CommonModule,
    MatCheckboxModule,
    MatButtonModule,
    TranslateModule,
    FontAwesomeModule,
    MatSlideToggleModule,
    TxContextMenuComponent,
    TxTextsToCopyComponent,
    DataTypePipe,
    NoRecordComponent,
  ],
  templateUrl: './attribute-tree-grid.component.html',
  styleUrls: ['./attribute-tree-grid.component.scss'],
  providers: provideTreeGridDataSource<AsyncTxTreeGrid<TxAttribute>>(AttributeTreeGridDataSource),
})
export class AttributeTreeGridComponent
  extends TxDataTreeGridDirective<AsyncTxTreeGrid<TxAttribute>>
  implements OnInit, AfterViewInit, OnChanges
{
  @Input() visibleDataType: TxDataType[] = [];
  @Input() selectableDataType?: TxDataType[];
  @Input() enableSearching: boolean = true;
  @Input() showDataTypeColumn = true;
  @Input() showTagsColumn = true;
  @Input() displayMasterCheckBox: boolean = false;
  @Input() displayCheckAllButton: boolean = true;
  @Input() displayUnCheckAllButton: boolean = true;
  @Input() reloadAttributesOnSameOT = false;
  @Input() enableToggleSelection = true;
  @Input() additionalColumns: TxGridColumn<AttributeTreeGridColumn>[] = [];
  @Input() disableLinkLoading: boolean = true;
  @Input() attributeSetLevels: CTxAttributeSetLevel[] = []; // allow to defined which nodes are checked
  @Input() attributeSetLevelsDisabled: CTxAttributeSetLevel[] = []; // allow to defined which nodes are checked and disabled
  @Input() attributeNameColumnWidth: string | undefined = undefined;
  @Input() allowOnlyOneOTDestination?: number; // an input to disable attribute type link which doesnt point to the specified ot
  @Input() showCheckedFirst: boolean = true;
  @Input() disableRowsCheckedOnInit: boolean = false;
  @Input() allowCellEditOnDblClick?: boolean;
  @Input() disableEditOnCellClick?: boolean;
  @Input() allFoldersCloseOnInit = false;
  @Input() hideTooltipId = false;

  @Output() ready: EventEmitter<void> = new EventEmitter();
  @Output() dataInitialized: EventEmitter<AsyncTxTreeGrid<TxAttribute>[]> = new EventEmitter();
  @Output() checkChange: EventEmitter<TxAttributeCheckChangeEvent<TxAttribute>> =
    new EventEmitter();
  @Output() changeSelection: EventEmitter<TxAttributeSelectionEvent<TxAttribute>> =
    new EventEmitter();
  @Output() actionCellComplete: EventEmitter<object> = new EventEmitter();

  @ViewChild(TxTreeGridComponent) treeGrid!: TxTreeGridComponent<
    TxAttribute & TxTreeGridChildren<TxAttribute>
  >;
  @ViewChild(TxContextMenuComponent) contextMenu!: TxContextMenuComponent;
  @ViewChild('toggle') toggle!: MatSlideToggle;
  public noObjectTypeSelected = _('txWebCore.admins.languages.noObjectTypeSelected');
  public noAttributesForPortal = _('txWebCore.admins.languages.noAttributesForPortal');
  public noAttributesForListings = _('txWebCore.admins.languages.noAttributesForListings');
  inputSearchValue: string = '';
  contextMenuDataIds?: string[];
  contextOnBlank: boolean = false;
  TxObjectContextMenu = TxObjectContextMenu;
  searchById: number | undefined;
  initEvent: DataSourceInitialized<AsyncTxTreeGrid<TxAttribute>> | undefined;
  isAllSelected: boolean = false;
  menuItems: TxContextMenuItem[] = [];

  txColumns: TxGridColumn<AttributeTreeGridColumn>[] = [];

  get selectionCount(): string {
    const totalCounts = this.dataSource.getSelectedElements().length;
    return totalCounts ? `(${totalCounts})` : '';
  }
  get allData() {
    return flattenTree(this.dataSource.dataList);
  }
  constructor(
    private readonly elementRef: ElementRef,
    private readonly cdr: ChangeDetectorRef,
    private readonly dataSource: AttributeTreeGridDataSource,
    private readonly translate: TranslateService
  ) {
    super();
    this.showSpinner$ = dataSource.isLoading$;
  }
  ngOnInit(): void {
    this.updateColumns();
    this.updateMenuItems();
  }
  ngAfterViewInit(): void {
    this.ready.emit();
    this.initEvent = this.initDataSource();
    this.dataSource.checkboxSelectDisableOnSearch = true;
    this.treeGrid.gridComponent.skipResetDblClick = true;
    setTimeout(() => {
      if (this.disableRowsCheckedOnInit && !this.attributeSetLevelsDisabled.length)
        this.dataSource.attributeSetLevelsDisabled = this.attributeSetLevelsDisabled = (
          this.attributeSetLevelsDisabled || []
        ).concat([...this.attributeSetLevels]);
    }, 0);
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.additionalColumns) {
      this.updateColumns();
    }

    const multipleSelection: SimpleChange = changes.multipleSelection;
    if (multipleSelection && !multipleSelection.firstChange) {
      this.updateMenuItems();
    }
    this.handleAttributeSetLevelsDisabled(changes.attributeSetLevelsDisabled);
    this.handleAttributeSetLevels(changes.attributeSetLevels);
    this.handleObjectTypeChanges(changes.objectType);
    this.handleIdObjectTypeChanges(changes.idObjectType);
    this.handleSimpleChange(changes.reloadAttributesOnSameOT);
    this.handleVisibleDataTypeChanges(changes.visibleDataType);
    this.handleSimpleChange(changes.allowOnlyOneOTDestination);
  }

  handleSelection(element: AsyncTxTreeGrid<TxAttribute>, event: MatCheckboxChange): void {
    const elementId = element.uniqueId as string;
    const isSelected = this.dataSource.isSelected(elementId);
    const flattenData = flattenTree(this.dataSource.dataList);
    const selectedIds = this.dataSource.getSelectedElements();

    if (!this.multipleSelection) {
      // Clear all selections except disable attribute
      const removeIds = new Map();
      flattenData.forEach((item) => {
        removeIds.set(item.uniqueId, item);
      });
      selectedIds.forEach((id) => {
        const attribute = removeIds.get(id);
        if (attribute && !attribute.isDisabled) {
          this.dataSource.removeAttributeSelection(id as string, flattenData);
        }
      });
    }
    if (!isSelected) {
      const rootToParentCheckboxIds = this.dataSource.rootToParentCheckableNodeIds(
        element,
        flattenData
      );
      this.dataSource.addAttributeSelection([...rootToParentCheckboxIds].reverse());
    } else {
      this.dataSource.removeAttributeSelection(elementId, flattenData);
    }
    this.isAllSelected = false;
    this.treeGrid.gridComponent.selectedRowPKValue = elementId;

    this.emitCheckedChanged(element, !isSelected);
  }
  protected initDataSource(): DataSourceInitialized<AsyncTxTreeGrid<TxAttribute>> {
    //clear selection
    if (this.showCheckedFirst) {
      this.dataSource.clearSelection();
    }

    this.dataSource.objectType = this.objectType;
    this.dataSource.idObjectType = this.idObjectType ?? this.objectType?.id;
    this.dataSource.idMapping = 'uniqueId';
    this.dataSource.parentMapping = 'uniqueIdParent';
    this.dataSource.expandState = TxTreeExpandState.Expanded;
    this.dataSource.buildTree = true;
    this.dataSource.enableCheckbox = this.enableCheckbox;
    this.dataSource.disableLinkLoading = this.disableLinkLoading;
    this.dataSource.allFoldersCloseOnInit = this.allFoldersCloseOnInit;
    this.dataSource.dataTypes = new Set(this.visibleDataType);
    this.dataSource.allowSelectionDataType = this.selectableDataType;
    this.dataSource.attributeSetLevels = this.attributeSetLevels;
    this.dataSource.attributeSetLevelsDisabled = this.attributeSetLevelsDisabled;
    this.dataSource.allowOnlyOneOTDestination = this.allowOnlyOneOTDestination;
    if (this.enableCheckbox) {
      const isChecked =
        this.attributeSetLevels?.length > 0 &&
        this.attributeSetLevels[0]?.idAttribute !== -1 &&
        this.showCheckedFirst;
      setTimeout(() => {
        this.toggle.checked = isChecked;
      }, 0);
      isChecked ? this.dataSource.showOnlyChecked() : this.dataSource.addData();
    } else {
      this.dataSource.addData();
    }
    let dataLoadedSubscription: Subscription;
    dataLoadedSubscription = this.dataSource.data$.pipe(take(5)).subscribe((data) => {
      if (data.length > 0) {
        this.dataInitialized.emit(flattenTree(this.dataSource.dataList));
        dataLoadedSubscription?.unsubscribe();
      }
    });

    return dataSourceInitialized(this.dataSource as any);
  }

  handleMasterCheckbox() {
    this.isAllSelected = !this.isAllSelected;

    if (!this.isAllSelected) {
      return this.handleDeselect(true);
    }
    this.toggle.checked
      ? this.selectAllShowSelection()
      : this.dataSource.loadNextLevelSelectAll(this.emitCheckedChanged.bind(this));
  }
  showSelectionToggle(state: MatSlideToggleChange): void {
    this.treeGrid.gridComponent.selectedRowPKValue = undefined;
    this.treeGrid.gridComponent.highlightRowsPK = [];
    this.dataSource.clearExpandedNodes();
    const selectedState = state.checked;

    if (selectedState) {
      this.dataSource.expandState = TxTreeExpandState.Expanded;
      this.dataSource.showOnlyChecked();
    } else if (this.dataSource.markToViewAttribute) {
      this.dataSource.loadMarkAttribute(this.dataSource.markToViewAttribute).subscribe({
        complete: () => {
          if (this.dataSource.markToViewAttribute) {
            this.scrollToRow(this.dataSource.markToViewAttribute.uniqueId as string);
          }
        },
      });
    } else {
      this.dataSource.expandState = TxTreeExpandState.Collapsed;
      this.dataSource.addData();
    }

    // Handle row selection logic based on `selectedState`
    if (selectedState || !this.dataSource.markToViewAttribute) {
      this.dataSource.markToViewAttribute = undefined;
    }
  }
  handleSearch(searchEvent: InputSearchEventInfo): void {
    this.inputSearchValue = searchEvent.inputSearch.nativeElement.value;
    if (this.treeGrid && this.inputSearchValue.length) {
      const result = this.treeGrid.searchByTextSelect(
        searchEvent.event as KeyboardEvent,
        this.inputSearchValue,
        false,
        true
      );
      if (result?.searchedById) {
        this.searchById = result.selected?.id;
      } else {
        this.searchById = undefined;
      }
      this.treeGrid.detectChanges();
    }
  }
  handleSearchClear() {
    this.searchById = undefined;
    this.inputSearchValue = '';
  }
  handleNativeSelect(args: TxGridRowSelectArgs<AsyncTxTreeGrid<TxAttribute>>) {
    const event = args.event as PointerEvent;
    this.contextMenu?.clickedOut(event);

    const currentId = args.data[0].uniqueId;
    const highlights = new Set(this.treeGrid.gridComponent.highlightRowsPK);
    const isReset = !event.ctrlKey;

    if (isReset) highlights.clear(); // Clear highlights if CTRL isn't pressed

    if (this.handleDeselection(highlights, currentId as string)) return;

    if (this.multipleSelection) {
      this.handleMultiSelection(event, currentId as string, highlights);
    }

    this.handleSingleSelection(event, currentId as string, args.data[0]);

    // Update final highlights
    this.treeGrid.gridComponent.highlightRowsPK = Array.from(highlights);
  }

  selectFolderChildren(deselect?: boolean) {
    let highlightedPK = this.treeGrid.gridComponent.highlightRowsPK;
    if (this.treeGrid.gridComponent.selectedRowPKValue) {
      highlightedPK.push(this.treeGrid.gridComponent.selectedRowPKValue);
    }
    uniq(highlightedPK);
    if (deselect) {
      this.handleDeselect();
    } else {
      this.handleSelect();
    }
  }
  isSelected(element: AsyncTxTreeGrid<TxAttribute>) {
    return this.dataSource.isSelected(element.uniqueId as string);
  }
  hasSelectedItems(): boolean {
    return this.dataSource.selection.hasValue();
  }

  beforeContextMenu(args: BeforeOpenCloseMenuEventArgs): void {
    const targetElement = args.event.target as HTMLElement;
    const matRow = targetElement.closest('mat-row');
    if (matRow) {
      const dataId = matRow.getAttribute('data-id');
      if (!dataId) {
        return;
      }
      const grid = this.treeGrid.gridComponent;

      grid.selectedRowPKValue = dataId;

      if (grid.highlightRowsPK.includes(dataId)) {
        this.contextMenuDataIds = uniq([...grid.highlightRowsPK, grid.selectedRowPKValue]);
      } else {
        grid.highlightRowsPK = [];
        this.contextMenuDataIds = [grid.selectedRowPKValue];
      }

      this.treeGrid.detectChanges();

      return;
    }
    this.contextOnBlank = true;
  }
  selectOnContextMenu(args: TxContextMenuEventArgs): void {
    switch (args.item.id) {
      case this.TxObjectContextMenu.Expand:
        this.handleExpand();
        break;
      case this.TxObjectContextMenu.Collapse:
        this.handleCollapse();
        break;
      case this.TxObjectContextMenu.Select:
        this.handleSelect();
        break;
      case this.TxObjectContextMenu.Deselect:
        this.handleDeselect();
        break;
    }
    this.resetContextMenu();
  }
  isCheckBoxDisplayed(data: TxTreeGrid<TxAttribute>): boolean {
    return this.dataSource.isCheckBoxDisplayed(data);
  }
  getSelectedItems() {
    return this.dataSource.getSelectedElements();
  }
  updateData(attribute: Array<TxTreeGrid<TxAttribute> & { [x: string]: any }>) {
    this.dataSource.addData(attribute);
  }
  updateDataByPK(id: string, attribute: { [x: string]: any }) {
    this.dataSource.updateDataByPK(this.dataSource.dataList, id, attribute);
    this.treeGrid.detectChanges();
  }
  cancelCellEditing() {
    this.treeGrid?.gridComponent.cancelCellEditing();
  }
  currObjectType() {
    return this.dataSource.objectType;
  }
  scrollToRow(id: number | string) {
    this.cdr.detectChanges();
    const rowElement = this.elementRef.nativeElement.querySelector(`mat-row[data-id="${id}"]`);

    if (rowElement) {
      rowElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      rowElement.classList.add('table-row-selected');
      setTimeout(() => {
        rowElement.classList.remove('table-row-selected');
      }, 1000);
    }
  }
  isPortalOT(): boolean {
    return this.dataSource.isPortalOT();
  }

  isEnumerationOT(): boolean {
    return this.dataSource.isEnumerationOT();
  }
  private resetContextMenu(): void {
    this.contextMenuDataIds = undefined;
    this.contextOnBlank = false;
  }
  private handleExpand(): void {
    if (this.contextOnBlank) {
      if (this.toggle.checked) {
        this.dataSource.showOnlyChecked();
      } else {
        this.dataSource.addData();
      }
      return;
    }
    let { parentIds } = this.dataSource.separateParentChildren(this.contextMenuDataIds || []);
    parentIds = parentIds.filter((id) => !this.dataSource.expandedNodes.has(id));

    this.dataSource.toggleMultipleNode(parentIds);
  }

  private handleCollapse(): void {
    if (this.contextOnBlank) {
      this.dataSource.clearExpandedNodes();
      return;
    }
    const { parentIds } = this.dataSource.separateParentChildren(this.contextMenuDataIds || []);
    parentIds.forEach((id) => {
      if (this.dataSource.expandedNodes.has(id) && id !== undefined) {
        this.dataSource.toggleNode(id);
      }
    });
  }
  private handleSelect() {
    this.isAllSelected = false;
    let selectedIds = uniq([
      ...this.treeGrid.gridComponent.highlightRowsPK,
      ...(this.contextMenuDataIds || []),
    ]);
    if (this.contextOnBlank || selectedIds.length === 0) {
      if (this.toggle.checked) {
        this.selectAllShowSelection();
      } else {
        this.dataSource.loadNextLevelSelectAll(this.emitCheckedChanged.bind(this));
      }
      this.isAllSelected = true;
      return;
    }
    this.dataSource.loadNextLevelSelectAll(this.emitCheckedChanged.bind(this), selectedIds);
  }
  private handleDeselect(isMasterDeselect: boolean = false) {
    const flattenData = flattenTreeWithChild(this.dataSource.dataList);
    const flattenTreeMap = new Map(flattenData.map((a) => [a.uniqueId, a]));
    const idsToRemove = new Set<string>();
    this.isAllSelected = false;
    let selectedIds = uniq([
      ...this.treeGrid.gridComponent.highlightRowsPK,
      ...(this.contextMenuDataIds || []),
    ]);
    if (isMasterDeselect || this.contextOnBlank || selectedIds.length == 0) {
      (this.dataSource.getSelectedElements() as string[]).forEach((item) => {
        const attribute = flattenTreeMap.get(item);
        if (attribute && !attribute.isDisabled) {
          idsToRemove.add(item);
        }
      });
    } else {
      selectedIds.forEach((id) => {
        const attribute = flattenTreeMap.get(id);
        if (!attribute) return;

        if (this.isCheckBoxDisplayed(attribute) && !attribute.isDisabled) {
          idsToRemove.add(id);
        }
        if (attribute.children) {
          this.dataSource
            .processSelectableChildrenIds(attribute)
            ?.forEach((item) => idsToRemove.add(item.uniqueId));
        }
      });
    }
    if (idsToRemove.size > 0) {
      idsToRemove.forEach((id) => this.dataSource.removeAttributeSelection(id, flattenData));
      this.emitCheckedChanged();
    }
  }
  private emitCheckedChanged(checkedRow?: TxTreeGrid<TxAttribute>, isChecked?: boolean) {
    this.checkChange.emit({
      attributeSetLevels: this.dataSource.attributeSetLevels,
      rowChecked: checkedRow,
      isChecked,
    });
  }
  private selectAllShowSelection() {
    let selectableIds: TxAttributeSelectionGrid[] = [];
    flattenTree(this.dataSource.dataList).forEach((item) => {
      if (item.uniqueId) {
        selectableIds.push({
          uniqueId: item.uniqueId,
          idAttribute: item.id,
          uniqueIdParent: item.uniqueIdParent,
        });
      }
    });
    this.dataSource.addAttributeSelection(selectableIds, true);
    this.emitCheckedChanged();
  }
  private updateMenuItems() {
    this.menuItems = [
      {
        label: this.translate.instant(_('txWebCore.contextMenu.expand')),
        id: this.TxObjectContextMenu.Expand,
      },
      {
        label: this.translate.instant(_('txWebCore.contextMenu.collapse')),
        id: this.TxObjectContextMenu.Collapse,
      },
    ];
    if (this.multipleSelection && (this.displayCheckAllButton || this.displayUnCheckAllButton)) {
      this.menuItems.push(
        {
          label: this.translate.instant(_('txWebCore.contextMenu.select')),
          id: this.TxObjectContextMenu.Select,
        },
        {
          label: this.translate.instant(_('txWebCore.contextMenu.deselect')),
          id: this.TxObjectContextMenu.Deselect,
        }
      );
    }
  }
  private updateColumns(): void {
    this.txColumns = [
      {
        headerText: _('txWebCore.admins.columns.name'),
        field: 'name',
        visible: true,
        resize: true,
        isSearchable: true,
        width: this.attributeNameColumnWidth,
      },
      {
        headerText: _('txWebCore.admins.columns.type'),
        field: 'type',
        visible: this.showDataTypeColumn,
        resize: true,
      },
      {
        headerText: _('txWebCore.admins.columns.tags'),
        field: 'tags',
        isSearchable: true,
        visible: this.showTagsColumn,
      },
      ...this.additionalColumns,
    ];

    const colDef = this.treeGrid?.gridComponent.columnDefinitions;
    if (colDef) {
      const allTemplate = colDef.toArray();
      this.additionalColumns.forEach((element: any) => {
        const newColumn = new TxGridColumnTemplate();
        newColumn.fieldName = element.field;
        newColumn.columnTemplate = element.template;
        allTemplate.push(newColumn);
      });
      colDef.reset(allTemplate);
      this.treeGrid.gridComponent.columnDefinitions = colDef;
    }
  }

  private handleAttributeSetLevelsDisabled(change?: SimpleChange): void {
    if (change?.currentValue.length > 0) {
      this.dataSource.attributeSetLevelsDisabled = this.attributeSetLevelsDisabled;
      this.dataSource.addData();
    }
  }

  private handleAttributeSetLevels(change?: SimpleChange): void {
    if (change) {
      this.dataSource.attributeSetLevels = change.currentValue;
      if (this.attributeSetLevels.length > 0 || this.showCheckedFirst) {
        this.dataSource.showOnlyCheckedSelection();
      } else if (!this.toggle?.checked) {
        this.dataSource.addData();
      }
    }
  }

  private handleObjectTypeChanges(change?: SimpleChange): void {
    if (change && !change.firstChange) {
      if (change.currentValue) {
        this.idObjectType = change.currentValue.id;
        this.initDataSource();
      } else {
        this.idObjectType = undefined;
        this.objectType = undefined;
        this.dataSource.idObjectType = undefined;
        this.dataSource.objectType = undefined;
      }
    }
  }

  private handleIdObjectTypeChanges(change?: SimpleChange): void {
    if (change && !change.firstChange && change.currentValue) {
      if (this.dataSource) {
        this.objectType = this.dataSource.getObjectType(change.currentValue);
      }
      this.initDataSource();
    }
  }

  private handleSimpleChange(change?: SimpleChange): void {
    if (change && !change.firstChange && change.currentValue) {
      this.initDataSource();
    }
  }

  private handleVisibleDataTypeChanges(change?: SimpleChange): void {
    if (
      change &&
      !change.firstChange &&
      !ArrayUtils.equalsCheck(change.currentValue, change.previousValue)
    ) {
      this.initDataSource();
    }
  }

  private handleDeselection(highlights: Set<string>, currentId: string): boolean {
    if (highlights.has(currentId)) {
      highlights.delete(currentId);
      this.treeGrid.gridComponent.selectedRowPKValue = undefined;
      this.treeGrid.gridComponent.highlightRowsPK = Array.from(highlights);
      return true;
    }
    return false;
  }

  private handleMultiSelection(event: PointerEvent, currentId: string, highlights: Set<string>) {
    if (event.ctrlKey) {
      this.toggleCtrlSelection(highlights, currentId);
    } else if (event.shiftKey) {
      this.handleShiftSelection(currentId, highlights);
    }
  }

  private toggleCtrlSelection(highlights: Set<string>, currentId: string) {
    const selected = this.treeGrid.gridComponent.selectedRowPKValue;
    if (selected !== undefined) highlights.add(selected);
    highlights.add(currentId);
    this.treeGrid.gridComponent.selectedRowPKValue = undefined;
  }

  private handleShiftSelection(currentId: string, highlights: Set<string>) {
    if (this.treeGrid.gridComponent.highlightRowsPK.length > 1) return;

    const selected = this.treeGrid.gridComponent.selectedRowPKValue;
    const dataIds = this.getDataRowIds();
    if (!dataIds.length || selected === undefined) return;

    const selectedIdx = dataIds.indexOf(selected);
    const currentIdx = dataIds.indexOf(currentId);
    if (selectedIdx >= 0 && currentIdx >= 0) {
      for (let i = Math.min(selectedIdx, currentIdx); i <= Math.max(selectedIdx, currentIdx); i++) {
        highlights.add(dataIds[i]);
      }
    }
  }

  private getDataRowIds(): string[] {
    const matRowElements = this.elementRef.nativeElement.getElementsByClassName('mat-mdc-row');
    return matRowElements
      ? Array.from(matRowElements).map((el: any) => el.getAttribute('data-id'))
      : [];
  }

  private handleSingleSelection(event: PointerEvent, currentId: string, data: any) {
    if (this.toggle.checked && !event.ctrlKey && !event.shiftKey) {
      this.dataSource.markToViewAttribute = data;
    }

    if (this.shouldToggleSelection(event, currentId)) {
      this.treeGrid.gridComponent.selectedRowPKValue = undefined;
      this.dataSource.markToViewAttribute = undefined;
    } else {
      this.treeGrid.gridComponent.selectedRowPKValue = currentId;
      this.changeSelection.emit({
        data,
        isChecked: this.dataSource.isSelected(currentId ?? ''),
      });
    }
  }

  private shouldToggleSelection(event: PointerEvent, currentId: string): boolean {
    return (
      currentId === this.treeGrid.gridComponent.selectedRowPKValue &&
      !event.ctrlKey &&
      this.treeGrid.gridComponent.highlightRowsPK.length === 0 &&
      this.enableToggleSelection
    );
  }
}
