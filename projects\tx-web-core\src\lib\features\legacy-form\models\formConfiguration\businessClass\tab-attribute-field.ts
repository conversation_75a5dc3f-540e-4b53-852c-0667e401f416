import { TxEditionMode } from './form-enum';
import { TxGroupAttributeField } from './group-attribute-field';

export class TxTabAttributeField extends TxGroupAttributeField {
  templatePath!: string;
  tooltip!: string;

  constructor(public editionMode: TxEditionMode) {
    super(editionMode);
  }

  assign(tabAttField?: Partial<TxTabAttributeField>) {
    super.assign(tabAttField);
    this.templatePath = tabAttField?.templatePath as string;
  }
}
