import { TxFileFieldBoxComponent } from './components/generic-fields/file-field-alt/file-field-box/file-field-box.component';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { NgModule } from '@angular/core';
import { TxBaseFieldComponent } from './components/generic-fields/base-field/base-field.component';
import { TxUploadFileFieldComponent } from './components/generic-fields/file-field-alt/upload-file-field.component';
import { MatIconModule } from '@angular/material/icon';
import { UploaderModule } from '@syncfusion/ej2-angular-inputs';
import { MatInputModule } from '@angular/material/input';
import { CommonModule } from '@angular/common';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatRippleModule } from '@angular/material/core';
import { LegacyTxDragDropDirective } from './directives/dragDrop.directive';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TxInputTextFieldComponent } from './components/generic-fields/input-text-field/input-text-field.component';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatDividerModule } from '@angular/material/divider';
import { MatSelectModule } from '@angular/material/select';
import { MatStepperModule } from '@angular/material/stepper';
import { MatTabsModule } from '@angular/material/tabs';
import { TxReadTextFieldComponent } from './components/generic-fields/read-text-field/read-text-field.component';
import { ReactiveFormsModule } from '@angular/forms';
import { TxInputNumbersFieldComponent } from './components/generic-fields/input-numbers-field/input-numbers-field.component';
import { TxInputNumbersControlComponent } from './components/generic-fields/input-numbers-field/input-numbers-control/input-numbers-control.component';
import { TxDropdownListFieldComponent } from './components/generic-fields/dropdown-list-field/dropdown-list-field.component';
import { DropDownTreeModule } from '@syncfusion/ej2-angular-dropdowns';
import { TxChipsFieldComponent } from './components/generic-fields/chips-field/chips-field.component';
import { TxTreesModule } from './trees/trees.module';

@NgModule({
  imports: [
    CommonModule,
    FontAwesomeModule,
    // angular material
    MatStepperModule,
    MatTabsModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSlideToggleModule,
    MatButtonToggleModule,
    MatChipsModule,
    MatButtonModule,
    MatSelectModule,
    MatDividerModule,
    MatTooltipModule,
    MatProgressBarModule,
    UploaderModule,
    MatInputModule,
    MatIconModule,
    MatProgressBarModule,
    MatSlideToggleModule,
    MatRippleModule,
    MatTooltipModule,
    ReactiveFormsModule,
    DropDownTreeModule,
    TxTreesModule,
  ],
  declarations: [
    TxBaseFieldComponent,
    TxFileFieldBoxComponent,
    TxUploadFileFieldComponent,
    LegacyTxDragDropDirective,
    TxInputTextFieldComponent,
    TxReadTextFieldComponent,
    TxInputNumbersFieldComponent,
    TxInputNumbersControlComponent,
    TxDropdownListFieldComponent,
    TxChipsFieldComponent,
  ],
  exports: [
    TxBaseFieldComponent,
    TxFileFieldBoxComponent,
    TxUploadFileFieldComponent,
    TxInputTextFieldComponent,
    TxReadTextFieldComponent,
    TxInputNumbersFieldComponent,
    TxInputNumbersControlComponent,
    TxDropdownListFieldComponent,
    TxChipsFieldComponent,
  ],
})
export class LegacyTxGenericFieldsModule {}
