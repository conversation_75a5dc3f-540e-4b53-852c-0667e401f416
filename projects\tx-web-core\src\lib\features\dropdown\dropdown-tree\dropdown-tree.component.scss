.dropdown-input-search-container {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .dropdown-tree-input-search {
    width: calc(100% - 35px);
    border: 0px;
    outline: 0px;
    height: 35px;
    padding-left: 8px;
  }

  .dropdown-tree-search-clear-input {
    margin-right: 8px;
    cursor: pointer;
  }
}

.dropdown-clear-input {
  position: absolute;
  padding: 5px 10px;
  margin-right: 4px;
  font-size: 16px;
  right: 10px;
  top: 9px;
  cursor: pointer;
  border-radius: 24px;
}

.explanation-message {
  font-size: small;
  font-style: italic;
}

.selection-counter-icon {
  position: absolute;
  top: 1rem;
  right: -20px;
  width: 32px;
}

.selection-counter-background {
  border-radius: 16px;
  height: 16px;
  width: 16px;
  position: absolute;
  display: flex;
  top: 8px;
  right: -14px;
  justify-content: center;

  .selection-counter-content {
    margin: 1px;
  }
}

.chip-select {
  height: 26px;
  overflow: hidden;

  .chip-select-content {
    display: flex;
    align-items: center;
    column-gap: 6px;
    max-width: 152px;
  }

  .chip-select-text {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .chip-select-icon {
    font-size: 16px;
    padding-left: 0px;
  }
}
.chip-select:not(:last-child) {
  margin-bottom: 0px !important;
}
.dropdown-tree-select-small {
  display: none;
}
