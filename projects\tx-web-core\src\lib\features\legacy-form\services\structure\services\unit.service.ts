import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { TxUnit } from '../models/unit';
import { TxApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class TxUnitService {
  units: TxUnit[] = [];
  unitsLoaded: Subject<boolean> = new Subject<boolean>();

  initialized = false;

  private fill(unit: any): TxUnit {
    const newunit = new TxUnit(unit);

    this.units.push(newunit);

    return newunit;
  }

  constructor(public apiService: TxApiService) {
    this.unitsLoaded.subscribe((value) => {
      this.initialized = true;
    });
  }

  private add(units: any[]): TxUnit[] {
    return units.map((l) => this.fill(l));
  }

  start() {
    this.apiService.listUnits().subscribe((units: TxUnit[]) => {
      //setTimeout(() => {
      this.add(units);

      this.unitsLoaded.next(true);
      //}, 3000);
    });
  }

  get(id: number) {
    return this.units.find((u) => u.id === id);
  }

  Convert(value: number, idUnitSource: number, idUnitDestination: number): number {
    // console.log('call convert for value ', value, ' with idUnitSource : ', idUnitSource, ' and idUnitDestination : ', idUnitDestination);
    return value;
  }
}
