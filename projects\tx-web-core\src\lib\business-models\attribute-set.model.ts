import { TxCommonUtils } from '@bassetti-group/tx-web-core/src/lib/utilities';
import { TxConcept } from './tx-concept.model';

export enum TxAttributeSetLevelType {
  ///In case of a level belonging to an Advanced Duplication: the data of the Attribute are duplicated.
  DuplicateData = 'ltDuplicateData',
  ///In case of a level belonging to an Advanced Duplication or an Advanced Creation: the data of the Attribute are to be modified (and therefore displayed in a form).
  ModifyData = 'ltModifyData',
  ///In case of a level belonging to an Advanced Duplication and a link Attribute: the linked objects are to be duplicated.
  DuplicateLkdObjects = 'ltDuplicateLkdObjects',
  ///In case of an Advanced Duplication (versioning) and a link Attribute: the existing linked Object must be replaced by the Object used for the duplication.
  ReplaceLkdObject = 'ltReplaceLkdObject',
  ///In case of an Advanced Duplication (versionning) and a link Attribute: the object used for the duplication must be added to the list of linked objects.
  AddDuplicatingObjectToLnk = 'ltAddDuplicatingObjectToLnk',
  ///In case of an Advanced Creation or an Advanced Duplication, the Attribute must be filled with a constant value (defined into an xml format).
  DefinedValue = 'ltDefinedValue',
  ///In case of an Advanced Creation or an Advanced Duplication, the Date Attribute must be filled with the current date.
  CurrentDate = 'ltCurrentDate',
  ///In case of an Advanced creation or an Advanced Duplication, the link Attribute must be filled with the object associated to the current user.
  ActiveUser = 'ltActiveUser',
}

export enum TxAttributeSetType {
  ///Business view.
  BusinessView = 'astBusinessView',
  ///Applied Input/Output.
  AppliedInputOutput = 'astAppliedInputOutput',
  ///Standard Attribute Set.
  Standard = 'astStandard',
  ///Advanced Duplication.
  AdvancedDuplication = 'astAdvancedDuplication',
  ///Advanced Deletion.
  AdvancedDeletion = 'astAdvancedDeletion',
  ///Exportation settings.
  AdvancedExportation = 'astAdvancedExportation',
  ///Advanced creation.
  AdvancedCreation = 'astAdvancedCreation',
  ///dummy
  ast7 = 'ast7',
  ///dummy
  ast8 = 'ast8',
  ///Advanced_Comparison
  AdvancedComparison = 'astAdvancedComparison',
  ///Link_Type
  LinkType = 'astLinkType',
  ///Link_Type_inv
  LinkTypeInv = 'astLinkTypeInv',
}

export interface TxAttributeSet extends TxConcept {
  attributeSetLevels: TxAttributeSetLevel[];
  idObjectType: number;
  type: TxAttributeSetType;
  idAppliedIO?: number;
  idExportation?: number;
  isModelApplicationTriggered: boolean;
  isDefault: boolean;
  idLinkType?: number;
  order: number;
}

export interface TxAttributeSetLevel {
  id?: number;
  idParent?: number;
  idAttribute: number;
  type?: TxAttributeSetLevelType;
  childLevels?: TxAttributeSetLevel[];
  column?: number;
  dateFormat?: string;
  order?: number;
  xml?: string;
}

export class CTxAttributeSetLevel {
  public id: number;
  public idParent: number;
  public idAttribute: number;
  public type: TxAttributeSetLevelType;
  public childLevels: CTxAttributeSetLevel[] = [];
  public column: number;
  public dateFormat: string;
  public order: number;
  public xml: string;

  constructor(attributeSetLevel?: any) {
    this.id = TxCommonUtils.getValue(attributeSetLevel?.id, 0);
    this.idParent = TxCommonUtils.getValue(attributeSetLevel?.idParent, 0);
    this.idAttribute = TxCommonUtils.getValue(attributeSetLevel?.idAttribute, 0);
    this.type = TxCommonUtils.getValue(
      attributeSetLevel?.type,
      TxAttributeSetLevelType.DuplicateData
    );
    this.column = TxCommonUtils.getValue(attributeSetLevel?.column, 0);
    this.dateFormat = TxCommonUtils.getValue(attributeSetLevel?.dateFormat, '');
    this.order = TxCommonUtils.getValue(attributeSetLevel?.order, 0);
    this.xml = TxCommonUtils.getValue(attributeSetLevel?.xml, '');
    this.createLinkedLevels(attributeSetLevel?.childLevels);
  }

  private createLinkedLevels(childLevels: any[] = []) {
    this.childLevels = childLevels.map((childLevel) => new CTxAttributeSetLevel(childLevel));
  }
}

export class CTxAttributeSet implements TxAttributeSet {
  public id: number;
  public name: string;
  public tags: string[];
  public order: number;
  public options: any = {};
  public idObjectType: number;
  public description: string;
  public idAttributeSet: number;
  public type: TxAttributeSetType;
  public idAppliedIO: number;
  public idExportation: number;
  public isModelApplicationTriggered: boolean;
  public isDefault: boolean;
  public idLinkType: number;
  public explanation: string;
  public idCMObject: number;
  public imagePath?: string;
  public attributeSetLevels: CTxAttributeSetLevel[] = [];

  constructor(businessView?: any) {
    this.id = TxCommonUtils.getValue(businessView?.id, 0);
    this.name = TxCommonUtils.getValue(businessView?.name, '');
    this.tags = TxCommonUtils.getValue(businessView?.tags, []);
    this.order = TxCommonUtils.getValue(businessView?.order, 0);
    this.idObjectType = TxCommonUtils.getValue(businessView?.idObjectType, 0);
    this.description = TxCommonUtils.getValue(businessView?.description, '');
    this.idAttributeSet = TxCommonUtils.getValue(businessView?.idAttributeSet, 0);
    this.type = TxCommonUtils.getValue(businessView?.type, TxAttributeSetType.BusinessView);
    this.idAppliedIO = TxCommonUtils.getValue(businessView?.idAppliedIO, 0);
    this.idExportation = TxCommonUtils.getValue(businessView?.idExportation, 0);
    this.isModelApplicationTriggered = TxCommonUtils.getValue(
      businessView?.isModelApplicationTriggered,
      true
    );
    this.isDefault = TxCommonUtils.getValue(businessView?.isDefault, false);
    this.idLinkType = TxCommonUtils.getValue(businessView?.idLinkType, 0);
    this.explanation = TxCommonUtils.getValue(businessView?.explanation, '');
    this.idCMObject = TxCommonUtils.getValue(businessView?.idCMObject, 0);
    this.imagePath = '';
    this.attributeSetLevels = [];
    if (businessView?.attributeSetLevels) {
      this.setAttributeSetLevels(businessView.attributeSetLevels);
    }
  }

  public setAttributeSetLevels(attributeSetLevels: any = []) {
    this.attributeSetLevels = attributeSetLevels.map(
      (attributeSetLevel: any) => new CTxAttributeSetLevel(attributeSetLevel)
    );
  }
}
