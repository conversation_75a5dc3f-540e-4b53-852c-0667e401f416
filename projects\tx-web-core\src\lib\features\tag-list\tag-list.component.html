<div *ngIf="isTagListNeeded" class="tag-list-container">
  <button *ngIf="arrowsNeeded" type="button" class="text-color" (click)="scrollLeft()">
    <fa-icon [icon]="['fal', 'chevron-left']"></fa-icon>
  </button>
  <span *ngIf="arrowsNeeded"></span>
  <div #tagList class="tag-list">
    <div *ngFor="let tag of tags" [id]="tag" class="tag-and-icon">
      <ng-container *ngIf="false; else tagName"></ng-container>
      <ng-template #tagName>
        <span
          [matTooltip]="tag"
          class="grid-text-with-icon annotation"
          [innerHTML]="tag | escapeHtml | highlightSearch : (tagListSearchValue | escapeHtml)"
          >{{ tag }}</span
        >
      </ng-template>
      <fa-icon
        [matTooltip]="'txWebCore.tooltip.copy' | translate"
        [icon]="['fal', 'copy']"
        size="lg"
        class="color-grey40 grid-icon-action"
        [cdkCopyToClipboard]="tag"
        (click)="copyTag()"></fa-icon>
    </div>
  </div>
  <span *ngIf="arrowsNeeded"></span>
  <button *ngIf="arrowsNeeded" type="button" class="text-color" (click)="scrollRight()">
    <fa-icon [icon]="['fal', 'chevron-right']"></fa-icon>
  </button>
</div>
