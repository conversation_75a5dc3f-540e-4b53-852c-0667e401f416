<div #popup [@openClose]="isOpen ? 'open' : 'closed'" class="tx-esc-popup">
  <div class="tx-esc-sub">
    <div class="e-float-input e-input-group tx-esc-search">
      <!-- Focus and FocusOut Event binding Floating input label -->
      <span #spanSearch class="e-input-group-icon e-icons e-search tx-span-search"></span>
      <input
        type="text"
        #inputSearch
        placeholder="Search..."
        class="tx-esc-input-search"
        (keyup)="onInputSearchKeyUp($event)"
        (focus)="onInputSearchFocusIn($event.target)"
        (blur)="onInputSearchFocusOut($event.target)" />
      <span class="e-float-line"></span>
      <ng-container *ngIf="isRemoveSpanVisible">
        <span
          #spanRemove
          class="e-input-group-icon e-icons e-cross-close tx-span-cross-close"
          title="Remove the input search"
          (click)="onRemoveInputSearchValue()"></span>
      </ng-container>
    </div>
    <div class="tx-esc-popup-content">
      <mat-tab-group
        (animationDone)="onAnimationDone()"
        (selectedTabChange)="onselectedTabChange($event.index)"
        dynamicHeight
        class="mat-tab-group-short">
        <mat-tab class="popup-link-tabs">
          <ng-template mat-tab-label>
            <fa-icon
              *ngIf="treeVisible"
              [matTooltip]="'Tree view'"
              matTooltipShowDelay="500"
              matTooltipPosition="above"
              [icon]="['far', 'folder-tree']"
              aria-hidden="true"></fa-icon>
            <fa-icon
              *ngIf="!treeVisible"
              [matTooltip]="'Search result(s)'"
              matTooltipShowDelay="500"
              matTooltipPosition="above"
              [icon]="['far', 'list-alt']"
              aria-hidden="true"></fa-icon>
          </ng-template>
          <div class="form-tab">
            <div
              class="tx-esc-filtered-results"
              [style.display]="filteredListVisible ? 'block' : 'none'">
              <tx-escf-filtered-elements
                #filteredList
                [multiple]="multiple"
                [valueKey]="valueKey"
                [textKey]="textKey"
                [height]="height"
                [groupByKey]="groupByKey"
                [idObjectType]="idObjectType"
                (elementChecked)="onFilteredElementChecked($event)"></tx-escf-filtered-elements>
            </div>
            <div class="tx-esc-tree" [style.display]="treeVisible ? 'block' : 'none'">
              <tx-escf-tree
                #treeElements
                [multiple]="multiple"
                [height]="height"
                [elements]="elements"
                [selectedElementsValues]="selectedElementsValues"
                [idObjectType]="idObjectType"
                (elementChecked)="onElementChecked($event)"
                (elementSelected)="onElementSelected($event)"></tx-escf-tree>
            </div>
          </div>
        </mat-tab>
        <mat-tab *ngIf="displayFavorites">
          <div
            [matTooltip]="'My favorites'"
            matTooltipClass="mat-tooltip-multiline"
            matTooltipShowDelay="500"
            matTooltipPosition="above">
            <fa-icon [icon]="['far', 'star']" aria-hidden="true"></fa-icon>
          </div>
        </mat-tab>
        <mat-tab>
          <ng-template mat-tab-label>
            <div
              [matTooltip]="'Checked Object(s)'"
              matTooltipClass="mat-tooltip-multiline"
              matTooltipShowDelay="500"
              matTooltipPosition="above">
              <fa-icon [icon]="['far', 'tasks']" aria-hidden="true"></fa-icon>
            </div>
          </ng-template>
          <tx-escf-filtered-elements
            #selectionList
            [height]="height"
            [multiple]="multiple"
            [valueKey]="valueKey"
            [textKey]="textKey"
            (elementChecked)="onFilteredElementChecked($event)"></tx-escf-filtered-elements>
        </mat-tab>
      </mat-tab-group>
    </div>
  </div>
</div>
