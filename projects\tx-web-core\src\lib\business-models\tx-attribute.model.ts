import { TxConcept } from './tx-concept.model';
import { TxFileType } from './tx-file-type.model';
import { TxLinkType } from './tx-link-type.model';
import { TxDataBaseAction, TxDataType } from './tx-data.model';
import { TableType } from './table-types.model';

export enum TxAttributeRight {
  None = 0,
  Read = 1,
  Add = 2,
  Modify = 3,
  Structure = 4,
}

export enum TxNumericalFloatFormat {
  General = 'ffGeneral',
  Exponent = 'ffExponent',
  Fixed = 'ffFixed',
  Number = 'ffNumber',
  Currency = 'ffCurrency',
}

export enum TxLinkDisplayMode {
  ComboTree = 'ComboTree',
  Combo = 'Combo',
  Chips = 'Chips',
  List = 'List',
  Matrix = 'Matrix',
  OneFieldPerRaw = 'OneFieldPerRaw',
  ChipsRead = 'ChipsRead',
}

export interface TxFile {
  name: string;
  size: number;
  view: boolean;
  idArchivedFile?: number;
  action: TxDataBaseAction;
  file?: File;
  uploadProgress?: number;
  icon?: string[];
}

export interface TxAttribute extends TxConcept {
  color?: string;
  dataType: TxDataType;
  digits?: number;
  floatFormat?: TxNumericalFloatFormat;
  idAttributeParent?: number;
  idFileType?: number;
  idInheritedAttribute?: number;
  idLinkType?: number;
  idObjectInformation?: number;
  idObjectType: number;
  idTableType?: number;
  idUnit?: number;
  isDisplayedInMainUnit?: boolean;
  isIndexesDisplayed?: boolean;
  isInherited?: boolean;
  isLBInclusive?: boolean;
  isList?: boolean;
  isSeriesNameDisplayed?: boolean;
  isTableDisplayed?: boolean;
  isTrackable?: boolean;
  isTransposed?: boolean;
  isUBInclusive?: boolean;
  isUnderlined?: boolean;
  linkDisplayMode?: any;
  lowerBound?: number;
  precision?: number;
  upperBound?: number;
  right?: TxAttributeRight;
  option?: any;
  order: number;

  fileType?: TxFileType;
  tableType?: TableType;
  linkType?: TxLinkType;
}

export interface EmitAttributeType {
  attribute: TxAttribute;
  ischeked: boolean;
}
