import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxDateObjectFieldComponent } from './date-object-field.component';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { AttributesMockService } from '../../../testing.mock';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { LegacyTxDataString, LegacyTxDataType } from '../../../services/structure/models/data';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import {
  DatePicker,
  DatePickerModule,
  DateTimePicker,
  DateTimePickerModule,
} from '@syncfusion/ej2-angular-calendars';
import {
  LegacyTxAttribute,
  LegacyTxAttributeRight,
} from '../../../services/structure/models/attribute';

const mockDateAttribute: LegacyTxAttribute = new LegacyTxAttribute({
  id: 0,
  name: '',
  dataType: LegacyTxDataType.Date,
  idObjectType: 0,
  idAttributeParent: 0,
  tags: [],
  right: LegacyTxAttributeRight.None,
  isInherited: false,
  order: 0,
  idInheritedAttribute: 0,
  idLinkType: 0,
  option: {},
});
const mockDateTimeAttribute: LegacyTxAttribute = new LegacyTxAttribute({
  id: 0,
  name: '',
  dataType: LegacyTxDataType.DateTime,
  idObjectType: 0,
  idAttributeParent: 0,
  tags: [],
  right: LegacyTxAttributeRight.None,
  isInherited: false,
  order: 0,
  idInheritedAttribute: 0,
  idLinkType: 0,
  option: {},
});

describe('TxDateObjectFieldComponent', () => {
  let component: TxDateObjectFieldComponent;
  let fixture: ComponentFixture<TxDateObjectFieldComponent>;
  let element;
  let attributesService: LegacyTxAttributesService;
  let dateAttribute: LegacyTxAttribute;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxDateObjectFieldComponent],
      providers: [{ provide: LegacyTxAttributesService, useClass: AttributesMockService }],
      imports: [
        MatTooltipModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatInputModule,
        BrowserAnimationsModule,
        DatePickerModule,
        DateTimePickerModule,
      ],
    }).compileComponents();
    attributesService = TestBed.inject(LegacyTxAttributesService);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxDateObjectFieldComponent);
    component = fixture.componentInstance;
    element = fixture.nativeElement;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('testing initProperties...', () => {
    it('should set timePicker to true if attribute type is Date&Time attribute', () => {
      component.attribute = mockDateTimeAttribute;
      component.initProperties();
      expect(component.timePicker).toBeTruthy();
    });

    it('should set timePicker to false if attribute type is Date attribute', () => {
      component.attribute = mockDateAttribute;
      component.initProperties();
      expect(component.timePicker).toBeFalsy();
    });
  });

  describe('testing initValue...', () => {
    it('should set value from data', () => {
      component.data = new LegacyTxDataString(0, 0, 'test');
      component.value = '';
      component.initValue();
      expect(component.value).toBe('test');
    });
  });

  describe('testing ngAfterViewInit...', () => {
    beforeEach(() => {
      // component.pickerElement.dispatchEvent(new MouseEvent('mouseover', {
      //   view: window,
      //   bubbles: true,
      //   cancelable: true}));
    });

    it('should set pickerElement to datePickerComponent', () => {
      component.timePicker = false;
      component.datePickerComponent = new DatePicker();
      component.datePickerComponent.element = document.createElement('div');
      component.ngAfterViewInit();
      expect(component.pickerElement).toEqual(component.datePickerComponent.element);
    });

    it('should set pickerElement to dateTimePickerComponent', () => {
      component.timePicker = true;
      component.dateTimePickerComponent = new DateTimePicker();
      component.dateTimePickerComponent.element = document.createElement('div');
      component.ngAfterViewInit();
      expect(component.pickerElement).toEqual(component.dateTimePickerComponent.element);
    });
  });
});
