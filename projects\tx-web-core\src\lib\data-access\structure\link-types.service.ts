import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { TxConfigService } from '@bassetti-group/tx-web-core/src/lib/data-access/config';
import { TxLinkType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TxAbstractConceptService } from './abstract-concept.service';
import { TxObjectTypeIconService } from '@bassetti-group/tx-web-core/src/lib/data-access/session';

@Injectable({
  providedIn: 'root',
})
export class TxLinkTypesService extends TxAbstractConceptService<TxLinkType> {
  protected urlListAllConcepts = 'api/Structure/linkType';

  constructor(
    configService: TxConfigService,
    http: HttpClient,
    objectTypeIcon: TxObjectTypeIconService
  ) {
    super(configService, http, objectTypeIcon);
  }

  findAssociativeLinkType(
    idLinkTypeToIgnore: number,
    idSourceObjectType: number
  ): TxLinkType | undefined {
    return this.concepts.find(
      (c) =>
        c.isAssociative &&
        c.idSourceObjectType === idSourceObjectType &&
        c.id !== idLinkTypeToIgnore
    );
  }
}
