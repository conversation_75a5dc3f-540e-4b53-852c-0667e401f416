import { ViewTooltip } from '../view-tooltip.model';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';

export interface ViewToFocus {
  toFocus: string;
  id?: number;
  viewTooltip?: ViewTooltip;
}

export const CONCEPT_TYPE_VIEW_TOOLTIP: ViewTooltip = {
  view: _('txWebCore.admins.columns.view'),
  noSufficientRight: _('txWebCore.search.noSufficientRight'),
  notAccessible: _('txWebCore.search.conceptNotAccessible'),
};
