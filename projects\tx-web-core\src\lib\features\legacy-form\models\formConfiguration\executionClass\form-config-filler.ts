import { LegacyTxLinkDisplayMode } from '../../../services/structure/models/attribute';
import { TxLinkTypeService } from '../../../services/structure/services/link-type.service';
import { LegacyTxAttribute, TxAttributeLink } from '../../../services/structure/models/attribute';
import { LegacyTxDataType } from '../../../services/structure/models/data';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { Observable } from 'rxjs';
import { TxIdConcept } from '../../generics/Id-concept';
import { TxAttributeField } from '../businessClass/attribute-field';
import { TxFormConfiguration } from '../businessClass/form-configuration';
import { TxGroupAttributeField } from '../businessClass/group-attribute-field';
import { TxLinkField } from '../businessClass/linked-field';
import { TxTabAttributeField } from '../businessClass/tab-attribute-field';
import { TxVirtualAttributeField } from '../businessClass/virtual-attribute-field';
import { TxFormSettings } from '../businessClass/form-settings';
import { TxEditionMode, TxFieldSize } from '../businessClass/form-enum';
import { _ArrayUtils } from '../../../utilities/legacy-array-utils';

export class TxFormConfigFiller {
  idCount = -1;
  formSettings!: TxFormSettings;
  globalRules: any = {};

  constructor(
    private attributesService: LegacyTxAttributesService,
    public linkTypeService: TxLinkTypeService
  ) {}

  loadAttributesAndFillForm(
    formConfig: TxFormConfiguration,
    idObjectType: number,
    treatInheritedAttributes: boolean
  ): Observable<TxFormConfiguration> {
    return new Observable<TxFormConfiguration>((observer) => {
      this.attributesService.listObjectTypeAttributes(idObjectType).subscribe((attributes) => {
        const linkTypes = attributes
          .filter((a) => a.idLinkType > 0)
          .map((a) => this.linkTypeService.get(a.idLinkType))
          .filter((l) => l.isAssociative);

        if (linkTypes.length) {
          let numberOfRequest = linkTypes.length;
          linkTypes.forEach((linkType) => {
            this.attributesService
              .listObjectTypeAttributes(linkType.idSourceObjectType)
              .subscribe((atts) => {
                numberOfRequest--;
                if (numberOfRequest < 1) {
                  this.fillForm(formConfig, attributes, false, true, treatInheritedAttributes);

                  observer.next(formConfig);
                  observer.complete();
                }
              });
          });
        } else {
          this.fillForm(formConfig, attributes, false, true, treatInheritedAttributes);

          observer.next(formConfig);
          observer.complete();
        }
      });
    });
  }

  fillForm(
    formConfig: TxFormConfiguration,
    attributes: LegacyTxAttribute[],
    generateFromTags: boolean,
    addChildrenAttributes: boolean,
    treatInheritedAttributes: boolean,
    keepStructureOrder = false
  ) {
    function _sortFields(fields: any[]) {
      fields.sort((a, b) => a.attribute?.order - b.attribute?.order);

      fields.forEach((f) => {
        if (f.children) {
          _sortFields(f.children);
        }
      });
    }
    if (!generateFromTags || keepStructureOrder) {
      attributes = _ArrayUtils.sortByOrder(attributes);
    }
    if (generateFromTags) {
      const listAttributId = attributes.map((attr) => attr.id);
      listAttributId.push(0); //id=0 : form root
      const orphanAttributes = attributes.filter(
        (attr) => !listAttributId.includes(attr.idAttributeParent)
      );
      if (orphanAttributes.length) {
        const fictivTabAttribute = new LegacyTxAttribute({
          id: -1,
          name: 'Description',
          tags: [],
        } as unknown as LegacyTxAttribute);
        const fictivTab = this.addTab(
          formConfig,
          fictivTabAttribute,
          attributes,
          false,
          treatInheritedAttributes
        );
        this.addFields(fictivTab, orphanAttributes, attributes, treatInheritedAttributes);
      }
    }
    this.addTabs(formConfig, attributes, addChildrenAttributes, treatInheritedAttributes);

    // sort tabs by order
    // _sortFields(formConfig.tabs);
  }

  addTabs(
    formConfig: TxFormConfiguration,
    attributes: LegacyTxAttribute[],
    addChildrenAttributes: boolean,
    treatInheritedAttributes: boolean
  ) {
    for (const attr of attributes) {
      if (attr.dataType === LegacyTxDataType.Tab) {
        attr.idAttributeParent = 0; // child of formConfig
        this.addTab(formConfig, attr, attributes, addChildrenAttributes, treatInheritedAttributes);
      }
    }
  }

  addTab(
    formConfig: TxFormConfiguration,
    tabAttr: LegacyTxAttribute,
    attributes: LegacyTxAttribute[],
    addChildrenAttributes: boolean,
    treatInheritedAttributes: boolean
  ) {
    const tabField = new TxTabAttributeField(formConfig.editionMode);
    tabField.attribute = tabAttr;
    this.setId(tabField);
    if (addChildrenAttributes) {
      this.addChildrenAttributes(tabField, attributes, treatInheritedAttributes);
      formConfig.tabs.push(tabField);
    } else {
      formConfig.tabs.push(tabField);
    }
    return tabField;
  }

  addChildrenAttributes(
    grpField: TxGroupAttributeField,
    listAttr: LegacyTxAttribute[],
    treatInheritedAttributes: boolean
  ): void {
    if (grpField.attribute) {
      const rootAttributes = this.filterChildren(
        grpField.attribute.id,
        false,
        listAttr,
        treatInheritedAttributes
      );
      const attributes = this.filterChildren(
        grpField.attribute.id,
        true,
        listAttr,
        treatInheritedAttributes
      );
      this.addFields(grpField, rootAttributes, attributes, treatInheritedAttributes);
    }
  }

  filterChildren(
    idParent: number,
    recursive: boolean,
    attributeListObject: any[],
    treatInheritedAttributes: boolean
  ): LegacyTxAttribute[] {
    return this.doFilter([], attributeListObject, idParent, recursive, treatInheritedAttributes);
  }

  doFilter(
    listAttributeToFill: LegacyTxAttribute[],
    listOtherAttribute: LegacyTxAttribute[],
    idParent: number,
    recursive: boolean,
    treatInheritedAttributes: boolean
  ): LegacyTxAttribute[] {
    for (const attribute of listOtherAttribute) {
      if (!treatInheritedAttributes && attribute.isInherited) {
        continue;
      }
      if (attribute.idAttributeParent === idParent) {
        listAttributeToFill.push(attribute);

        // if recursive and if attribute is a container
        if (
          recursive &&
          [LegacyTxDataType.Tab, LegacyTxDataType.Group].includes(attribute.dataType)
        ) {
          this.doFilter(
            listAttributeToFill,
            listOtherAttribute,
            attribute.id,
            true,
            treatInheritedAttributes
          );
        }
      }
    }
    return listAttributeToFill;
  }

  addField(grpField: TxGroupAttributeField, attribute: LegacyTxAttribute): TxVirtualAttributeField {
    let field: TxVirtualAttributeField;
    let addField = true;
    if (!attribute || attribute.dataType === LegacyTxDataType.Group) {
      field = new TxGroupAttributeField(grpField.editionMode);
      field.attribute = attribute;
      this.setId(field);
    } else {
      field = TxAttributeField.assign({ attribute });
      this.setId(field);
      const readMode = grpField.editionMode === TxEditionMode.read;
      if (
        !attribute.isInherited &&
        [
          LegacyTxDataType.Link,
          LegacyTxDataType.LnkDirect,
          LegacyTxDataType.LnkBi,
          LegacyTxDataType.LnkInv,
          LegacyTxDataType.Enum,
        ].includes(attribute.dataType) &&
        (attribute as TxAttributeLink).idLinkType
      ) {
        const lnkField = new TxLinkField();
        const linkAttribute = attribute as TxAttributeLink;
        const linkType = linkAttribute.linkType;

        if (linkType) {
          const isInverted = linkAttribute.dataType === LegacyTxDataType.LnkInv;
          lnkField.idDestinationObjectType = (
            isInverted
              ? linkAttribute?.linkType?.idSourceObjectType
              : linkAttribute?.linkType?.idDestinationObjectType
          ) as number;
          lnkField.multiple = (
            isInverted
              ? linkAttribute?.linkType?.multiplicityInv
              : linkAttribute?.linkType?.multiplicity
          ) as boolean;

          if (linkType.isAssociative) {
            lnkField.linkViewMode = LegacyTxLinkDisplayMode.Matrix;
            lnkField.linkedFields = [];
            // this.attributesService.findAttributeFromAssociativeAttribute(linkAttribute.id).subscribe(result => {
            const result = _ArrayUtils.sortByOrder(
              this.attributesService.findAttributeFromAssociativeAttribute(linkAttribute.id)
            );
            result.forEach((a) => {
              const assoField = new TxAttributeField();
              assoField.attribute = a;
              lnkField.addLinkedField(assoField);
            });
            // });
            if (!field.properties.fieldWidth) {
              field.properties.fieldWidth = TxFieldSize.Full;
            }
          } else {
            if (readMode) {
              lnkField.linkViewMode = LegacyTxLinkDisplayMode.ChipsRead;
            } else {
              lnkField.linkViewMode = (attribute as TxAttributeLink).linkDisplayMode;
            }
          }
        }
        (field as TxAttributeField).linkField = lnkField;
      } else if (attribute.isInherited && attribute.idLinkType && readMode) {
        const linkType = this.linkTypeService.get(attribute.idLinkType);

        if (linkType) {
          const linkFieldSource: TxAttributeField = grpField.children.find(
            (c: TxVirtualAttributeField) =>
              !c.attribute.isInherited && c.attribute.idLinkType === attribute.idLinkType
          ) as unknown as TxAttributeField;
          if (!linkFieldSource) {
            addField = false;
          } else {
            if (linkType.multiplicity) {
              // this inherited attribute will be displayed in a matrix
              if (linkFieldSource) {
                linkFieldSource.linkField.linkViewMode = LegacyTxLinkDisplayMode.Matrix;

                // update the size of the matrix field
                if (!linkFieldSource.properties.fieldWidth) {
                  linkFieldSource.properties.fieldWidth = TxFieldSize.Full;
                  this.setClasses(linkFieldSource);
                }

                linkFieldSource.linkField.addLinkedField(field);
                addField = false;
              }
            } else {
              // this inherited attribute will be displayed as a field
              const linkFieldSourceDuplicated = TxAttributeField.assign({ ...linkFieldSource });
              this.setId(linkFieldSourceDuplicated);
              const lnkField = new TxLinkField();
              linkFieldSourceDuplicated.linkField = lnkField;
              lnkField.linkViewMode = LegacyTxLinkDisplayMode.OneFieldPerRaw;
              linkFieldSourceDuplicated.linkField.addLinkedField(field);
              field = linkFieldSourceDuplicated;
              (field as TxAttributeField).linkField = lnkField;
            }
          }
        }
      }
    }
    this.setClasses(field);

    // set properties from global rules
    field.properties = { ...field.properties, ...this.globalRules };

    // overload properties from attribute
    // if (attribute.properties){
    //     field.properties = {...field.properties, ...attribute.properties.properties};
    // }

    // overload properties from formSettings
    for (const rule of this.formSettings.rules) {
      if (rule.id === field.attribute.id || field.attribute.tags.includes(rule.tag)) {
        field.properties = { ...field.properties, ...rule.properties };
      }
    }

    if (addField) {
      grpField.children.push(field);
    }
    return field;
  }

  addFields(
    grpField: TxGroupAttributeField,
    listAttributeToAdd: LegacyTxAttribute[],
    listOtherAttribute: LegacyTxAttribute[],
    treatInheritedAttributes: boolean
  ) {
    const attributeToTreat: LegacyTxAttribute[] = [];
    const dummy: LegacyTxAttribute[] = [];
    let field: TxVirtualAttributeField;

    this.prepareLkdAttributes(
      listAttributeToAdd,
      listAttributeToAdd,
      treatInheritedAttributes,
      attributeToTreat,
      dummy
    );
    for (const attribute of attributeToTreat) {
      field = this.addField(grpField, attribute);
      if (attribute.dataType === LegacyTxDataType.Group) {
        const childrenAttribute = this.getChildrenList(listOtherAttribute, attribute.id);
        if (childrenAttribute) {
          this.addFields(
            field as TxGroupAttributeField,
            childrenAttribute,
            listOtherAttribute,
            treatInheritedAttributes
          );
        } else {
          // Todo: remove the item
        }
      } /*
        else if (attribute.type in [TxDataType.DirectLink, TxDataType.InverseLink, TxDataType.BidirectionalLink]){

        }*/
    }
  }

  prepareLkdAttributes(
    attributeList: LegacyTxAttribute[],
    headerList: LegacyTxAttribute[],
    treatInheritedAttributes: boolean,
    listAttributeToTreat: LegacyTxAttribute[],
    listHeaderToTreat: LegacyTxAttribute[]
  ) {
    for (let i = 0; i < attributeList.length; i++) {
      const attribute = attributeList[i];
      const header = headerList[i];

      if (!treatInheritedAttributes && attribute.isInherited) {
        continue;
      }

      listAttributeToTreat.push(attribute);
      listHeaderToTreat.push(header);
    }
  }

  getChildrenList(objectList: LegacyTxAttribute[], idParent: number): LegacyTxAttribute[] {
    const childList: LegacyTxAttribute[] = [];

    for (const attribute of objectList) {
      if (attribute.idAttributeParent === idParent) {
        childList.push(attribute);
      }
    }

    return childList;
  }

  setMandatories(
    formConfig: TxFormConfiguration,
    listMandatoriesAttrIds: number[],
    listMandatoriesTag: string[]
  ): void {
    for (const tab of formConfig.tabs) {
      this.doSetMandatories(tab, listMandatoriesAttrIds, listMandatoriesTag);
    }
  }

  doSetMandatories(
    grpField: TxGroupAttributeField,
    listMandatoriesAttrIds: number[],
    listMandatoriesTags: string[]
  ): void {
    if (grpField && Array.isArray(grpField.children)) {
      for (const child of grpField.children) {
        if (
          listMandatoriesAttrIds.includes(child.attribute.id) ||
          child.attribute.tags.some((tag) => listMandatoriesTags.includes(tag))
        ) {
          child.properties.mandatory = true;
        }
        if ([LegacyTxDataType.Group, LegacyTxDataType.Tab].includes(child.attribute.dataType)) {
          this.doSetMandatories(
            child as TxGroupAttributeField,
            listMandatoriesAttrIds,
            listMandatoriesTags
          );
        }
      }
    }
  }

  setRightManagement(formConfig: TxFormConfiguration, rightManagementValue: string): void {
    formConfig.properties.rightManagement = rightManagementValue;
  }

  setId(field: TxIdConcept) {
    field.id = this.idCount;
    this.idCount--;
  }

  setClosedGroups(formConfig: TxFormConfiguration, listGroupeClosedTags: string[]) {
    for (const tab of formConfig.tabs) {
      this.doSetClosedGroups(tab, listGroupeClosedTags);
    }
  }

  doSetClosedGroups(grpField: TxGroupAttributeField, listGroupeClosedTags: string[]): void {
    for (const child of grpField.children) {
      if (child.attribute.dataType === LegacyTxDataType.Group) {
        child.attribute.tags.forEach((tag) => {
          if (listGroupeClosedTags.includes(tag)) {
            child.properties.closedByDefault = true;
          }
        });
        this.doSetClosedGroups(child as TxGroupAttributeField, listGroupeClosedTags);
      }
    }
  }

  setClasses(field: TxVirtualAttributeField) {
    let classes = '';
    if (field.attribute) {
      classes += 'generic-form-field ';
      if (this.isFieldFullWidth(field)) {
        classes += 'generic-large-field ';
      } else {
        classes += 'generic-short-field ';
      }
    }
    field.classes = classes;
  }

  isFieldFullWidth(field: TxVirtualAttributeField) {
    if (
      field.attribute.dataType === LegacyTxDataType.Text ||
      field.attribute.dataType === LegacyTxDataType.Group
    ) {
      return true;
    }

    if (field.properties.fieldWidth) {
      return field.properties.fieldWidth === TxFieldSize.Full;
    }
    // return field.attribute.id === 96; for test, to remove
    return false;
  }
}
