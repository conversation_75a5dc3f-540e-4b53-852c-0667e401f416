import { Inject } from '@angular/core';
import { ToastData } from './toast.models';
import { TOAST_DATA_TOKEN } from './toast-data-token';
import { MockService } from 'ng-mocks';
import { ToastService } from './toast.service';
import { ToastComponent } from './toast.component';
class ToastMockComponent {
  constructor(@Inject(TOAST_DATA_TOKEN) public data: ToastData) {}
}
const NOTIFICATIONS: ToastData[] = [
  {
    type: 'success',
    interval: 8000,
    title: 'Title1',
    description: 'notification1',
    date: new Date(),
    isPersistent: true,
    isUnread: true,
  },
  {
    type: 'warning',
    interval: 8000,
    title: 'Title2',
    description: 'notification2',
    date: new Date(),
    isPersistent: true,
    isUnread: false,
  },
  {
    type: 'loading',
    interval: 0,
    title: 'Title3',
    description: 'notification3',
    date: new Date(),
    isPersistent: true,
    isUnread: true,
  },
];
export const MOCK_TOAST_SERVICE = MockService(ToastService, {
  show: (data: ToastData) =>
    new ToastMockComponent({
      type: data.type,
      interval: data.interval,
      title: data.title,
      description: data.description,
      isPersistent: data.isPersistent,
      isUnread: data.isUnread,
    }) as ToastComponent,

  getNotifications: (): ToastData[] => NOTIFICATIONS,
  hasUnreadNotifications: (): boolean => true,
});
