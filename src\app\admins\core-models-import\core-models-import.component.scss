@use '../../styles/variables' as variables;
* {
  box-sizing: inherit;
}

:host {
  display: block;
  height: 94.2vh;
  width: 100%;
  box-sizing: border-box;
}
.core-models-import {
  margin-top: 1rem;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 2.5rem minmax(50%, 1fr) variables.$responsive-bottom-margin;
  padding: 0 2rem;
  height: 94%;
  max-height: 94%;
  &__admin-header {
    grid-column: 1/17;
    grid-row: 1;
  }
  &__main {
    grid-row: 2;
  }
  &__tab-group {
    height: 100%;
  }
  &__import-tab {
    display: grid;
    grid-template-columns: 3fr 7fr;
    grid-template-rows: 100%;
    column-gap: 1.875rem;
    height: 99%;
  }

  &__file-information,
  &__check {
    grid-row: 1;
  }
  &__history-tab {
    width: 100%;
    height: 100%;
  }
  &__archive-details {
    margin: 0;
    padding: 0;
    display: grid;
    grid-template-columns: max-content max-content 1fr;
    grid-auto-rows: minmax(1.35rem, max-content);
    row-gap: 0.45rem;
    column-gap: 0.3rem;
    flex: 1 1 auto;
    max-height: 100%;
  }
  &__archive-details-items {
    display: contents;
    overflow: auto;
    &:last-of-type {
      .core-models-import__archive-details-items {
        &--icon:last-of-type {
          @extend %last-info-icon-label;
        }
        &--label:nth-last-of-type(2) {
          @extend %last-info-margin-top;
          @extend %last-info-icon-label;
        }
        &--info:last-of-type {
          @extend %last-info-margin-top;
          @extend %last-info-icon-label;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    &--icon {
      justify-self: center;
      @extend %align-archive-info;
    }
    &--label {
      overflow: hidden;
      text-overflow: ellipsis;
      @extend %align-archive-info;
    }
    &--info {
      @extend %align-archive-info;
    }
  }
}
.content-tab {
  padding-top: 1rem;
  box-sizing: border-box;
}
.test-file {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.admin-header {
  &__h1-title {
    margin: 0;
  }
  &__divider {
    margin-top: 0.5rem;
  }
}
%last-info-icon-label {
  align-self: start;
  display: block;
}
%align-archive-info {
  align-self: end;
}
%last-info-margin-top {
  margin-top: 0.22rem;
}
