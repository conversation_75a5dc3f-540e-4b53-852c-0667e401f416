<div [ngClass]="{ 'read-field': readMode }">
  <tx-input-numbers-field
    *ngIf="!readMode; else readField"
    [form]="form"
    [control]="control"
    [id]="id"
    [label]="label"
    [required]="required"
    [disabled]="disabled"
    [order]="order"
    [information]="information"
    [icon]="icon"
    [classes]="classes"
    [readMode]="readMode"
    [lowerBoundValue]="lowerBoundValue"
    [upperBoundValue]="upperBoundValue"
    [minLength]="minLength"
    [maxLength]="maxLength"
    [maxFormControl]="maxFormControl"
    [meanFormControl]="meanFormControl"
    [withSecondInput]="withSecondValue"
    [withThirdInput]="withThirdValue"
    [units]="[mainUnit]"
    [idUnitSelected]="unitIdSelected">
  </tx-input-numbers-field>

  <ng-template #readField>
    <div *ngIf="readMode" class="form-field">
      <mat-label
        class="read-form-label form-label mat-form-label"
        matTooltipClass="mat-label-tooltip"
        matTooltipShowDelay="500"
        matTooltipPosition="above">
        {{ label }}
      </mat-label>
      <div class="read-form-field">
        <div class="read-point-values-container">
          <div
            class="read-point-value-container"
            [ngClass]="{
              'simple-value-container': !withSecondValue && !withThirdValue,
              'double-value-container': withSecondValue && !withThirdValue,
              'triple-value-container': withThirdValue
            }"
            [matTooltip]="control.value"
            matTooltipClass="mat-label-tooltip"
            matTooltipShowDelay="500"
            matTooltipPosition="above">
            <span class="read-point-value">{{ control.value }}</span>
          </div>
          <span *ngIf="withSecondValue" class="read-point-value-separator"> to </span>
          <div
            class="read-point-value-container"
            [ngClass]="{
              'double-value-container': !withThirdValue,
              'triple-value-container': withThirdValue
            }"
            *ngIf="withSecondValue"
            [matTooltip]="maxFormControl.value"
            matTooltipClass="mat-label-tooltip"
            matTooltipShowDelay="500"
            matTooltipPosition="above">
            <span class="read-point-value">{{ maxFormControl.value }}</span>
          </div>
          <span *ngIf="withThirdValue" class="read-point-value-separator"> / </span>
          <div
            class="read-point-value-container triple-value-container"
            *ngIf="withThirdValue"
            [matTooltip]="meanFormControl.value"
            matTooltipClass="mat-label-tooltip"
            matTooltipShowDelay="500"
            matTooltipPosition="above">
            <span class="read-point-value">{{ meanFormControl.value }}</span>
          </div>
          <div *ngIf="mainUnit && !secondUnits && notEmpty" class="one-unit-text">
            <span>{{ mainUnit.name }}</span>
          </div>
        </div>

        <mat-form-field
          *ngIf="secondUnits && notEmpty"
          class="form-unit-select form-field-no-underline"
          color="accent">
          <mat-select
            [formControl]="unitFormControl"
            (selectionChange)="updateBoundValue()"
            [(value)]="unitIdSelected">
            <mat-option
              class="form-unit-option"
              *ngFor="let unit of secondUnits"
              [value]="unit.id"
              >{{ unit.name }}</mat-option
            >
          </mat-select>
        </mat-form-field>
      </div>
    </div>
  </ng-template>
</div>
