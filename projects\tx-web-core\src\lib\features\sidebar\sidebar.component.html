<mat-sidenav-container ngClass="sidenav-container">
  <!-- sidebar element -->
  <mat-sidenav
    id="sidebar-treeview"
    class="background-grey5"
    [ngClass]="isSideNavExpanded ? 'sidenav' : 'sidenav-closed'"
    #sidenav
    mode="side"
    [opened]="true">
    <div class="sidenav-wrapper">
      <ng-container
        *ngIf="headerTemplate"
        [ngTemplateOutlet]="headerTemplate.template"></ng-container>
      <div class="sideNav-treeview-container">
        <tx-tree-view
          #treeViewInstance
          *ngIf="showTreeView"
          [primaryKey]="'id'"
          [parentMapping]="'pid'"
          [hasChildren]="'hasChild'"
          [data]="fields"
          [onInitTreeExpanded]="true"
          [enableNodeClickToggle]="true"
          (dataBound)="dataBound.emit($event)"
          (nodeSelecting)="nodeSelecting.emit($event)"
          (nodeSelected)="nodeSelected.emit($event)">
          <tx-tree-node>
            <ng-template let-data class="tree-text">
              <a>
                <div [matTooltip]="data.tooltipTrad | translate" class="main-item-sidebar">
                  <fa-icon [icon]="data.adminIcon" [size]="data.isParent ? 'lg' : ''"></fa-icon>
                  <span *ngIf="isSideNavExpanded" class="text-item-sidebar">{{
                    data.adminName | translate
                  }}</span>
                </div>
              </a>
            </ng-template>
          </tx-tree-node>
        </tx-tree-view>
      </div>
      <div class="navbar-bottom-description">
        <img class="navbar-logo" alt="logo" [src]="srcLogo" />
        <div class="annotation" *ngIf="actualYear">© {{ actualYear }} - BASSETTI</div>
      </div>
    </div>
  </mat-sidenav>
  <!-- end of sidebar element -->

  <!-- main-content sidebar -->
  <mat-sidenav-content
    class="background content-layer"
    [ngClass]="isSideNavExpanded ? 'sidenav-content-expand' : 'sidenav-content-collapse'">
    <ng-container
      *ngIf="contentTemplate"
      [ngTemplateOutlet]="contentTemplate.template"></ng-container>
  </mat-sidenav-content>
  <!-- end of main-content -->
</mat-sidenav-container>
