import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { TxConfigService } from '@bassetti-group/tx-web-core';
import {
  Configuration,
  ConfigurationSubConceptElement,
} from 'src/app/shared/components/configurations-framework/models/configurations-framework-model';

@Injectable()
export class TxAutomaticNamingService {
  private API_URL?: string;
  constructor(private http: HttpClient, private configService: TxConfigService) {
    this.API_URL = this.configService.getApiUrl();
  }

  getConfigurations(): Observable<any> {
    return this.http.get<any>(`${this.API_URL}api/Naming`);
  }

  addConfiguration(config: Configuration) {
    const dataItems: ConfigurationSubConceptElement[] = [];
    config.subConcept?.elements?.forEach((scElement) => dataItems.push(scElement));
    const dataConfig = {
      toAdd: [
        {
          sName: config.name,
          sTag: config.tag,
          sOTTag: config.objectTypeTag,
          items: dataItems,
        },
      ],
    };
    return this.http.post<{ toAdd: any[]; toModify: any[]; toDelete: string[] }>(
      `${this.API_URL}api/Naming`,
      dataConfig
    );
  }

  setConfiguration(config: Configuration) {
    const dataItems: ConfigurationSubConceptElement[] = [];
    config.subConcept?.elements?.forEach((scElement) => dataItems.push(scElement));
    const dataConfig = {
      toAdd: [],
      toModify: [
        {
          sName: config.name,
          sTag: config.tag,
          sOTTag: config.objectTypeTag,
          items: dataItems,
        },
      ],
      toDelete: [],
    };
    return this.http.post<{ toAdd: any[]; toModify: any[]; toDelete: string[] }>(
      `${this.API_URL}api/Naming`,
      dataConfig
    );
  }

  deleteConfiguration(tag: string) {
    return this.http.post<{ toAdd: any[]; toModify: any[]; toDelete: string[] }>(
      `${this.API_URL}api/Naming`,
      { toDelete: [tag] }
    );
  }
}
