import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CoreModelExportHistoryComponent } from './core-model-export-history.component';
import { MockComponent } from 'ng-mocks';
import { CoreModelsHistoryComponent } from 'src/app/admins/core-model-common';

describe('CoreModelsExportHistoryComponent', () => {
  let component: CoreModelExportHistoryComponent;
  let fixture: ComponentFixture<CoreModelExportHistoryComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CoreModelExportHistoryComponent, MockComponent(CoreModelsHistoryComponent)],
    }).compileComponents();

    fixture = TestBed.createComponent(CoreModelExportHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
