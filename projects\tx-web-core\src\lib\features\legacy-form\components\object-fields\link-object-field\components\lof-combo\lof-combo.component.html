<!-- <div class="form-field" [ngClass]="{'form-field-error': control && !control.valid && !disabled}">
  <ejs-dropdowntree
    #dropdownTree
    [id]="uniqueId"
    [allowFiltering]="allowFiltering"
    [fields]="field"
    [mode]="mode"
    [matTooltip]="labelTooltip"
    [placeholder]="placeholder"
    [ngClass]="{
      'mandatory-label': required && control && control.valid, 
      'date-time-picker-error': control && !control.valid && !disabled, 
      'mandatory-label-error-field': control && control.hasError('required') && required
    }"
    [popupHeight]="popupHeight"
    [showCheckBox]="showCheckBox"
    [treeSettings]="treeSettings"
    [floatLabelType]="floatLabelType"
    [filterType]="filterType"
    [value]="checkedIds"
    (created)="onCreated()"
    (filtering)="onFiltering($event)"
    (open)="onOpen($event)"
    (beforeOpen)="onBeforeOpen($event)"
    (valueChange)="onValueChange($event)"
    (change)="onChange($event)"
    (select)="onSelect($event)"
    (dataBound)="onDataBounded($event)"
  >
    <ng-container *ngIf="filteringWithRequest" >
      <ng-template #noRecordsTemplate>
        <span #spanNoData class="norecord">{{ hasEnoughValueToSearch ? 'No object(s) found' : 'You must fill in a minimum of 3 characters' }}</span>
        <tx-tree-objects
          [idObjectType]="idObjectType"
          [silent]="true"
          [showCheckBox]="showCheckBox"
          [folderCheckable]="false"
          [cssRules]="'border-width: 0px !important; text-align: left !important;' "
          (afterInit)="initTreeSearch($event, spanNoData)"
          (nodeChecked)="onFilteredNodeChecked($event)"

        ></tx-tree-objects>
      </ng-template>
    </ng-container>
  </ejs-dropdowntree>
  <div class="field-error-container">
    <mat-error class="field-error" *ngIf="control && control.hasError('required')"><strong>Required</strong></mat-error>
  </div>
</div> -->

<tx-dropdown-list-field
  #dropDownListField
  [field]="field"
  [showCheckBox]="showCheckBox"
  [placeholder]="placeholder"
  [label]="label"
  [labelTooltip]="labelTooltip"
  [required]="required"
  [disabled]="disabled"
  [control]="control"
  [allowFiltering]="true"
  [displayIconOption]="displayIconOption"
  [idObjectType]="idObjectType"
  [idFilteringObject]="idFilteringObject"
  [sortedBy]="sortedBy"
  [treeMode]="treeMode"
  [checkedIds]="checkedIds"
  [txCheckedObjects]="txCheckedObjects"
  (valueChange)="onValueChange($event)"
  (beforeOpen)="onBeforeOpen()"
  (loadObjOnBeforeOpen)="loadObjOnBeforeOpen($event)"
  (loadObjOnExpandingNodes)="loadObjOnExpandingNodes($event)"></tx-dropdown-list-field>
