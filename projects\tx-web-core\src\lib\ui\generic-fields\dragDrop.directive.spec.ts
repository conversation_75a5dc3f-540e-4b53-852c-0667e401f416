import { TestBed, ComponentFixture, waitForAsync } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { createMockFileList } from 'src/app/app.testing.mock';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { TxDragDropDirective } from './dragDrop.directive';
import { Component, DebugElement } from '@angular/core';

@Component({
  template: `<input type="file " txDragDrop />`,
})
class TestTxDragDropComponent {}

describe('Directive: txDragDrop', () => {
  let component: TestTxDragDropComponent;
  let fixture: ComponentFixture<TestTxDragDropComponent>;
  let element: DebugElement;
  let directiveElement: DebugElement;
  let directive: TxDragDropDirective;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TestTxDragDropComponent],
      imports: [DragDropModule, TxDragDropDirective],
    }).compileComponents();
  }));

  beforeEach(waitForAsync(() => {
    fixture = TestBed.createComponent(TestTxDragDropComponent);
    component = fixture.componentInstance;
    element = fixture.debugElement.query(By.css('input'));
    fixture.detectChanges();
    directiveElement = fixture.debugElement.query(By.directive(TxDragDropDirective));
    directive = directiveElement.injector.get(TxDragDropDirective);
  }));

  it('should create an instance', () => {
    expect(component).toBeTruthy();
  });

  it('should return true - evt containts files', () => {
    const fileList = createMockFileList([
      {
        body: 'test',
        mimeType: 'text/plain',
        name: 'TxAudit.xml',
      },
    ]);
    const event: any = { fileList, dataTransfer: { types: ['Files'] } };
    expect(directive.containsFiles(event)).toBe(true);
  });

  it('should return false - evt does not containts files', () => {
    const ramdonObject = {};
    const event: any = { ramdonObject, dataTransfer: { types: [''] } };
    expect(directive.containsFiles(event)).toBe(false);
  });

  it('should return false - evt does not containts files type', () => {
    const ramdonObject = {};
    const event: any = { ramdonObject, dataTransfer: {} };
    expect(directive.containsFiles(event)).toBe(undefined);
  });

  it('dragover input', () => {
    const event = { preventDefault: () => {}, stopPropagation: () => {} };
    jest.spyOn(event, 'preventDefault');
    jest.spyOn(event, 'stopPropagation');
    const spyDragOver = jest.spyOn(directive, 'onDragOver');
    element.triggerEventHandler('dragover', event);
    fixture.detectChanges();
    expect(directive).not.toBeNull();
    expect(event.preventDefault).toHaveBeenCalledTimes(1);
    expect(event.stopPropagation).toHaveBeenCalledTimes(1);
    expect(spyDragOver).toBeCalledWith(event);
  });

  it('dragleave input', () => {
    const event = { preventDefault: () => {}, stopPropagation: () => {} };
    jest.spyOn(event, 'preventDefault');
    jest.spyOn(event, 'stopPropagation');
    const spyDragLeave = jest.spyOn(directive, 'onDragLeave');
    element.triggerEventHandler('dragleave', event);
    fixture.detectChanges();
    expect(directive).not.toBeNull();
    expect(event.preventDefault).toHaveBeenCalledTimes(1);
    expect(event.stopPropagation).toHaveBeenCalledTimes(1);
    expect(spyDragLeave).toBeCalledWith(event);
  });

  it('drop input - containsFiles => false ', () => {
    const event = { preventDefault: () => {}, stopPropagation: () => {} };
    jest.spyOn(event, 'preventDefault');
    jest.spyOn(event, 'stopPropagation');
    const spyOndrop = jest.spyOn(directive, 'ondrop');
    const spyContainsFiles = jest.spyOn(directive, 'containsFiles');
    const spyFileDropped = jest.spyOn(directive.fileDropped, 'emit');
    spyFileDropped.mockImplementation(jest.fn());
    spyContainsFiles.mockImplementation(jest.fn(() => false));
    element.triggerEventHandler('drop', event);
    fixture.detectChanges();
    expect(spyOndrop).toBeCalledWith(event);
    expect(event.preventDefault).toHaveBeenCalledTimes(1);
    expect(event.stopPropagation).toHaveBeenCalledTimes(1);
    expect(spyContainsFiles).toBeCalledWith(event);
    expect(directive.fileDropped.emit).not.toBeCalled();
  });
  it('drop input - containsFiles => true ', () => {
    const event = {
      preventDefault: () => {},
      stopPropagation: () => {},
      dataTransfer: { files: ['Files'] },
    };
    jest.spyOn(event, 'preventDefault');
    jest.spyOn(event, 'stopPropagation');
    const spyOndrop = jest.spyOn(directive, 'ondrop');
    const spyContainsFiles = jest.spyOn(directive, 'containsFiles');
    const spyFileDropped = jest.spyOn(directive.fileDropped, 'emit');
    spyFileDropped.mockImplementation(jest.fn());
    spyContainsFiles.mockImplementation(jest.fn(() => true));
    element.triggerEventHandler('drop', event);
    fixture.detectChanges();
    expect(spyOndrop).toBeCalledWith(event);
    expect(event.preventDefault).toHaveBeenCalledTimes(1);
    expect(event.stopPropagation).toHaveBeenCalledTimes(1);
    expect(spyContainsFiles).toBeCalledWith(event);
    expect(directive.fileDropped.emit).toBeCalledTimes(1);
  });
});
