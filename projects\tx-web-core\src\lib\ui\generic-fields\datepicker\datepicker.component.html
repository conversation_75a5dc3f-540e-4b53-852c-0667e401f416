<mat-form-field
  *ngIf="startDateCtrl && endDateCtrl; else simplePicker"
  [style]="{ width: width }"
  color="accent">
  <mat-label>{{ label }}</mat-label>
  <mat-date-range-input [rangePicker]="rangePicker">
    <input
      matStartDate
      [formControl]="startDateCtrl"
      [placeholder]="'txWebCore.components.datepicker.startDate' | translate"
      (dateChange)="onDateChange($event)" />
    <input
      matEndDate
      [formControl]="endDateCtrl"
      [placeholder]="'txWebCore.components.datepicker.endDate' | translate"
      (dateChange)="onDateChange($event)" />
  </mat-date-range-input>
  <mat-hint *ngIf="showDateFormatHint" class="legend"
    >{{ getCurrentDateFormat() }} - {{ getCurrentDateFormat() }}</mat-hint
  >
  <mat-datepicker-toggle matIconSuffix [for]="rangePicker"></mat-datepicker-toggle>
  <mat-date-range-picker #rangePicker></mat-date-range-picker>

  <mat-error *ngIf="startDateCtrl.hasError('matStartDateInvalid')">{{
    'txWebCore.components.datepicker.invalidStartDate' | translate
  }}</mat-error>
  <mat-error *ngIf="endDateCtrl.hasError('matEndDateInvalid')">{{
    'txWebCore.components.datepicker.invalidEndDate' | translate
  }}</mat-error>
</mat-form-field>

<ng-template #simplePicker>
  <mat-form-field [style]="{ width: width }" *ngIf="singleDateCtrl" color="accent">
    <mat-label>{{ label }}</mat-label>
    <input
      matInput
      [matDatepicker]="simplePicker"
      [formControl]="singleDateCtrl"
      [min]="minDate"
      [max]="maxDate"
      (dateChange)="onDateChange($event)" />
    <mat-datepicker #simplePicker></mat-datepicker>
    <div matIconSuffix class="icon-suffix">
      <fa-icon
        *ngIf="singleDateCtrl.value"
        class="clear-icon"
        size="lg"
        [icon]="['fal', 'times']"
        (click)="clearDate()"></fa-icon>
      <mat-datepicker-toggle [for]="simplePicker"></mat-datepicker-toggle>
    </div>
    <mat-hint *ngIf="showDateFormatHint" class="legend">{{ getCurrentDateFormat() }}</mat-hint>
    <mat-error *ngIf="singleDateCtrl.hasError('required')" class="e-error">{{
      'txWebCore.input.fieldRequired' | translate
    }}</mat-error>
  </mat-form-field>
</ng-template>
