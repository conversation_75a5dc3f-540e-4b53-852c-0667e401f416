import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import {
  AbstractSessionService,
  MockSessionService,
  AdminRights,
} from '../../../data-access/session';
import { PathService } from './path.service';

const routerMock = {
  config: [
    {
      path: 'user',
      data: {
        openWith: [
          {
            key: 'concept',
            params: { isUser: 'false' },
          },
        ],
        adminRights: AdminRights.IsAdmin,
      },
    },
  ],
};

describe('ConceptPathService', () => {
  let service: PathService;
  let router: Router;
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        PathService,
        { provide: AbstractSessionService, useClass: MockSessionService },
        { provide: Router, useValue: routerMock },
      ],
    });
    service = TestBed.inject(PathService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
  it('should get path', () => {
    expect(service.getPath('concept')).toEqual({
      path: 'user',
      params: { isUser: 'false' },
      canNavigate: true,
    });
  });
});
