import { NgModule } from '@angular/core';
import { CoreModelRoutingModule } from './core-model-export-routing.module';
import { GridModule } from '@syncfusion/ej2-angular-grids';
import { CoreModelExportHistoryComponent } from './components/core-model-export-history/core-model-export-history.component';
import { CoreModelExportComponent } from './components/core-model-export/core-model-export.component';
import { CoreModelExportFormComponent } from './components/core-model-export-form/core-model-export-form.component';
import { CoreModelsExportHttpService } from './services/core-model-export.http.service';
import { CoreModelsExportGatewayService } from './services/core-model-export-gateway.service';
import { CoreModelExportService } from './services/core-model-export.service';
import { CoreModelCommonModule } from '../core-model-common';
import { CoreModelsConceptsComponent } from './components/core-models-concepts/core-models-concepts.component';

@NgModule({
  declarations: [
    CoreModelsConceptsComponent,
    CoreModelExportComponent,
    CoreModelExportHistoryComponent,
    CoreModelExportFormComponent,
  ],
  imports: [CoreModelCommonModule, GridModule, CoreModelRoutingModule],
  providers: [
    CoreModelExportService,
    {
      provide: CoreModelsExportGatewayService,
      useClass: CoreModelsExportHttpService,
    },
  ],
})
export class CoreModelExportModule {}
