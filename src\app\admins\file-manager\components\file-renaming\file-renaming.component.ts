import { Component, EventEmitter, Output, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { FileDescription, FileTree } from '../../models/file-models';

@Component({
  selector: 'app-file-renaming',
  templateUrl: './file-renaming.component.html',
  styleUrls: ['./file-renaming.component.scss'],
})
export class FileRenamingComponent {
  @Output() confirm = new EventEmitter();
  @Output() cancel = new EventEmitter();

  @ViewChild('dialogContent') private dialogContent?: TemplateRef<any>;

  public oldName = '';
  public newName = '';
  public path?: string;
  public alias?: string;
  public nodeToChange?: FileTree;
  public selectedRowGrid?: FileDescription;

  constructor(public dialog: MatDialog) {}

  public valid() {
    this.confirm.emit({
      oldName: this.oldName,
      newName: this.newName,
      node: this.nodeToChange,
      rowGrid: this.selectedRowGrid,
      path: this.path,
      alias: this.alias,
    });
  }

  public close(): void {
    this.cancel.emit({
      name: this.oldName,
      node: this.nodeToChange,
      rowGrid: this.selectedRowGrid,
      oldName: this.oldName,
      path: this.path,
      alias: this.alias,
    });
  }

  public show(
    fileName: string,
    newName: string,
    path: string,
    alias: string,
    node?: FileTree,
    selectedRowGrid?: FileDescription
  ) {
    this.oldName = fileName;
    this.newName = newName;
    this.nodeToChange = node;
    this.path = path;
    this.alias = alias;
    this.selectedRowGrid = selectedRowGrid;
    if (this.dialogContent) {
      this.dialog.open(this.dialogContent, { disableClose: true, panelClass: 'customDialog' });
    }
  }
}
