import type { Meta, StoryObj } from '@storybook/angular';
import { InputSearchComponent } from './input-search.component';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';

const meta: Meta<InputSearchComponent> = {
  component: InputSearchComponent,
  title: 'InputSearchComponent',
  tags: ['autodocs'],
};
export default meta;
type Story = StoryObj<InputSearchComponent>;

export const Primary: Story = {
  args: {
    fieldWidth: '280px',
    focusInput: false,
    placeholderValue: _('txWebCore.input.search'),
    inputValue: '',
    isFiltered: false,
  },
};
