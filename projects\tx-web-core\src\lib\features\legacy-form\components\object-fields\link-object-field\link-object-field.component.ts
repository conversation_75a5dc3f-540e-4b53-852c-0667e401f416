import { TxLofMatrixComponent } from './components/lof-matrix/lof-matrix.component';
import { TxVirtualAttributeField } from './../../../models/formConfiguration/businessClass/virtual-attribute-field';
import { TxLofComboComponent } from './components/lof-combo/lof-combo.component';
import { TxLinkObjectFieldService } from './services/link-object-field.service';
import { LegacyTxDataType } from '../../../services/structure/models/data';
import { TxAttributeLink } from '../../../services/structure/models/attribute';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
  OnChanges,
  SimpleChanges,
  AfterContentInit,
} from '@angular/core';
import { TxObjectFieldComponent } from '../_system/object-field/object-field.component';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { LegacyTxObjectsService } from '../../../services/structure/services/objects.service';
import { LegacyTxLinkDisplayMode } from '../../../services/structure/models/attribute';
import { LegacyTxDataLink } from '../../../services/structure/models/data';
import { LegacyTxObject } from '../../../services/structure/models/object';
import { TxChipsFieldComponent } from '../../generic-fields/chips-field/chips-field.component';

@Component({
  selector: 'tx-link-object-field',
  templateUrl: './link-object-field.component.html',
  styleUrls: ['./link-object-field.component.scss'],
})
export class TxLinkObjectFieldComponent
  extends TxObjectFieldComponent
  implements OnChanges, AfterContentInit
{
  @Input() data!: LegacyTxDataLink;
  @Input() attribute!: TxAttributeLink;
  @Input() displayMode: LegacyTxLinkDisplayMode = LegacyTxLinkDisplayMode.Combo;
  @Input() idDestinationObjectType!: number;
  @Input() txObjects: LegacyTxObject[] = [];
  @Input() multiple!: boolean;
  @Input() idFilteringObject = 0;
  @Input() displayIconOption = true;
  @Input() sortedBy = 'order';
  @Input() treeMode = true;

  @Output() displayPaneEvent = new EventEmitter<any>();
  @Output() select = new EventEmitter();
  @Output() beforeOpen = new EventEmitter();

  @ViewChild('chipsField') chipsField!: TxChipsFieldComponent;
  @ViewChild('lofCombo') lofCombo!: TxLofComboComponent;
  @ViewChild('lofMatrix') lofMatrix!: TxLofMatrixComponent;

  txLinkDisplayMode = LegacyTxLinkDisplayMode;
  isAssociative = false;
  isMatrix = false;
  mainIcon!: string;
  linkedObjects: LegacyTxObject[] = [];
  selectedIds!: string[];
  selectedObjects: LegacyTxObject[] = [];
  transposed = false;
  linkedFields: TxVirtualAttributeField[] = [];
  comboField = {
    dataSource: [] as any[],
    value: 'id',
    text: 'name',
    child: 'child',
    hasChildren: 'isParent',
    iconCss: 'icon',
    imageUrl: 'image',
  };

  constructor(
    public attributeService: LegacyTxAttributesService,
    public objectService: LegacyTxObjectsService,
    public linkObjectFieldService: TxLinkObjectFieldService
  ) {
    super(attributeService);
  }
  ngAfterContentInit(): void {
    if (this.data) {
      this.linkedObjects = this.data.linkedObjects as LegacyTxObject[];
      this.comboField.dataSource = this.linkObjectFieldService.createOptions(
        this.linkedObjects,
        false,
        this.displayIconOption,
        undefined,
        true
      );
      this.selectedIds = this.linkedObjects.map((o) => '' + o.id);
      this.updateSelectedObjects(this.linkedObjects);
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // console.log('ngOnChanges', changes);
    // if (changes.data.currentValue) {
    //   this.data = changes.data.currentValue;
    //   this.initValue();
    // }
  }

  initPropertiesFromField() {
    super.initPropertiesFromField();

    if (this.field.linkField) {
      this.displayMode = this.field.linkField.linkViewMode;
      this.linkedFields = this.field.linkField.linkedFields;

      this.isMatrix = this.displayMode === LegacyTxLinkDisplayMode.Matrix;
    }
  }

  initPropertiesFromAttribute() {
    super.initPropertiesFromAttribute();

    const isInverted = this.attribute.dataType === LegacyTxDataType.LnkInv;

    if (!this.idDestinationObjectType) {
      this.idDestinationObjectType = (
        isInverted
          ? this.attribute.linkType?.idSourceObjectType
          : this.attribute.linkType?.idDestinationObjectType
      ) as number;
    }

    this.multiple = (
      isInverted ? this.attribute.linkType?.multiplicityInv : this.attribute.linkType?.multiplicity
    ) as boolean;
    this.transposed = (
      isInverted ? this.attribute.linkType?.isTransposeInv : this.attribute.linkType?.isTransposed
    ) as boolean;
    this.idFilteringObject = this.attribute.linkType?.idFilteringObject as number;

    this.isAssociative = this.attribute.linkType?.isAssociative as boolean;

    this.mainIcon = `tx-object-type-icon-${this.idDestinationObjectType}`;
  }

  initValue() {
    if (this.data) {
      this.linkedObjects = this.data.linkedObjects as LegacyTxObject[];
      this.comboField.dataSource = this.linkObjectFieldService.createOptions(this.linkedObjects);
    }
  }

  onCheckObject(args: any) {}

  setData(data: LegacyTxDataLink) {
    if (data) {
      this.data = data;
      this.linkedObjects = this.data.linkedObjects as LegacyTxObject[];
      this.selectedIds = this.linkedObjects.map((o) => '' + o.id);
      this.updateSelectedObjects(this.linkedObjects);
    }
    if (this.chipsField) {
      this.chipsField.data = data;
      this.chipsField.setData();
    } else if (this.lofCombo && this.lofCombo.dropDownListField) {
      if (!(this.lofCombo.dropDownListField.dropdownTree.fields.dataSource as []).length) {
        const options = this.linkObjectFieldService.createOptions(this.linkedObjects);
        this.lofCombo.dropDownListField.dropdownTree.fields.dataSource = options;
      }
      setTimeout(() => {
        this.lofCombo.dropDownListField.dropdownTree.value = this.selectedIds;
        this.lofCombo.dropDownListField.checkedIds = this.selectedIds;
      });
    } else if (this.lofMatrix) {
      this.lofMatrix.updateData(data);
    }
  }

  getData(): LegacyTxDataLink {
    const idsLinkObject: number[] = this.selectedObjects.map((o) => o.id) as number[];
    return new LegacyTxDataLink(
      this.idObject,
      this.idAttribute,
      idsLinkObject,
      this.selectedObjects
    );
  }

  displayRightPane(event: any) {
    this.displayPaneEvent.emit(event);
  }

  updateSelectedObjects(objects: LegacyTxObject[]) {
    if (!objects) {
      return;
    }
    this.selectedObjects = objects;
    // this.control?.setValue(objects);
  }

  clearControl() {
    super.clearControl();

    if (this.lofCombo && this.lofCombo.dropDownListField) {
      this.lofCombo.dropDownListField.clearSelection();
      this.updateSelectedObjects([]);
    }
  }

  onSelect(args: any) {
    this.select.emit(args);
  }

  onChanges(args: any) {
    this.onChange.emit(args);
  }

  haveToBeHidden() {
    if (this.isMatrix) {
      return false;
    } else {
      return super.haveToBeHidden();
    }
  }

  onBeforeOpen() {
    this.beforeOpen.emit();
  }
}
