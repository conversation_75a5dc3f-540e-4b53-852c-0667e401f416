import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TxTreeViewComponent } from './tree-view.component';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';

describe('MatTreeViewComponent', () => {
  let component: TxTreeViewComponent<any>;
  let fixture: ComponentFixture<TxTreeViewComponent<any>>;
  const TRANSLATIONS = {
    en: {},
    fr: {},
  };
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TxTreeViewComponent,
        TranslateTestingModule.withTranslations(TRANSLATIONS),
        FontAwesomeTestingModule,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TxTreeViewComponent);
    component = fixture.componentInstance;
    component.primaryKey = 'id';
    component.data = [{ name: 'Node 1', id: 1, children: [] }];
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
