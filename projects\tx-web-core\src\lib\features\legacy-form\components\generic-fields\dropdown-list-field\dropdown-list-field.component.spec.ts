import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DropDownTreeModule } from '@syncfusion/ej2-angular-dropdowns';
import { TxDropdownListFieldComponent } from './dropdown-list-field.component';
import { MockComponent, MockDirective } from 'ng-mocks';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { MatError } from '@angular/material/form-field';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ReactiveFormsModule } from '@angular/forms';

describe('DropdownListFieldComponent', () => {
  let component: TxDropdownListFieldComponent;
  let fixture: ComponentFixture<TxDropdownListFieldComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        TxDropdownListFieldComponent,
        MockComponent(FaIconComponent),
        MockDirective(MatError),
      ],
      imports: [DropDownTreeModule, MatTooltipModule, ReactiveFormsModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxDropdownListFieldComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
