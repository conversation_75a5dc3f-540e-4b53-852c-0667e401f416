import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { AdminConfig } from './admin-config';
import { Observable, Subject, tap } from 'rxjs';
import { TX_ENVIRONMENT_TOKEN, TxEnvironment } from '@bassetti-group/tx-web-core/src/lib/utilities';

@Injectable({
  providedIn: 'root',
})
export class TxConfigService {
  public config: AdminConfig = {};
  public resetConceptsSub: Subject<void> = new Subject();

  private environment = null;
  private configPath = './assets/configs/';

  constructor(
    private http: HttpClient,
    @Inject(TX_ENVIRONMENT_TOKEN) private appEnvironment: TxEnvironment
  ) {}

  getConfigPreferences(key: string) {
    return this.config.preferences ? (this.config as any).preferences[key] : null;
  }

  /**
   * Use to get the data found in the second file (config file)
   */
  getConfig(key: string) {
    return (this.config as any)[key];
  }

  /**
   * Use to get the data found in the first file (env file)
   */
  getEnv(key: string) {
    return this.environment?.[key];
  }

  getUrlFormated(urlValue: string): string {
    // force a "/" at then end of the url
    if (!urlValue.endsWith('/')) {
      urlValue += '/';
    }

    // manage web site application
    if (!urlValue.includes('http://') && !urlValue.includes('https://')) {
      // force a "/" at then begin of the application name
      if (!urlValue.startsWith('/')) {
        urlValue = '/' + urlValue;
      }

      urlValue = window.location.origin + urlValue;
    }

    return urlValue;
  }

  getApiUrl(): string | undefined {
    if (this.config.businessRestUrl) {
      return this.getUrlFormated(this.config.businessRestUrl);
    }
  }

  getAuthUrl(): string | undefined {
    if (this.config.authenticationRestUrl) {
      return this.getUrlFormated(this.config.authenticationRestUrl);
    }
  }

  getOfficeRestUrl(): string | undefined {
    if (this.config.officeRestUrl) {
      return this.getUrlFormated(this.config.officeRestUrl);
    }
  }

  getFormRules(): any[] {
    const formConfig = this.getConfig('form');

    if (formConfig) {
      return formConfig['globalRules'];
    }
    return undefined as unknown as any;
  }

  cleanCachedStructure(): Observable<any> {
    return this.http.delete(`${this.getApiUrl()}api/Structure/cache`).pipe(
      tap(() => {
        this.resetConceptsSub.next();
      })
    );
  }

  public load() {
    return new Promise((resolve, reject) => {
      const reqHeader = new HttpHeaders({ 'Content-Type': 'application/json', 'No-Auth': 'True' });
      this.http.get(this.configPath + 'config.json', { headers: reqHeader }).subscribe({
        next: (responseData: any) => {
          this.config = responseData;

          if (this.appEnvironment.production) {
            this.config.businessRestUrl = location.origin + '/TxBusinessRest/';
            this.config.authenticationRestUrl = location.origin + '/TxAuthentication/';
            this.config.teexmaUrl = location.origin;
          } else if (!this.config.teexmaUrl) {
            this.config.teexmaUrl = location.origin;
          }
          resolve(true);
        },
        error: (error) => {
          if (this.appEnvironment.production) {
            this.config = {
              businessRestUrl: location.origin + '/TxBusinessRest/',
              authenticationRestUrl: location.origin + '/TxAuthentication/',
              teexmaUrl: location.origin,
              preferences: {
                oldIcons: true,
              },
            };
            resolve(true);
          } else {
            console.error(
              `Error reading the configuration file "/assets/configs/config.json": ${error}`
            );
            reject(error);
          }
        },
      });
    });
  }
}
