export interface CoreModelExportConceptDTO {
  type: string;
  id: number;
  name: string;
  tag: string;
  idObjectType?: number;
  idSourceObjectType?: number;
  idDestinationObjectType?: number;
  explanation?: string;
  idModel?: number;
  modelName?: string;
  missingConfigs: {
    key: string;
    missingObject?: {
      tags: string[];
      id: number;
    } | null;
  }[];
  filePath?: string;
}

export enum CoreModelConceptRequiredProps {
  Id = 'id',
  Type = 'type',
  Name = 'name',
  Tag = 'tag',
  Explanation = 'explanation',
  IdObjectType = 'idObjectType',
  MissingConfigs = 'missingConfigs',
}

export enum CoreModelConceptOptionalIds {
  IdSourceObjectType = 'idSourceObjectType',
  IdDestinationObjectType = 'idDestinationObjectType',
  IdModel = 'idModel',
}
