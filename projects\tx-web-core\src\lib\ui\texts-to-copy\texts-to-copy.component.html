<ng-container *ngFor="let text of formattedTexts">
  <span
    class="text-with-icon"
    *ngIf="searchValue !== undefined && searchValue !== ''; else noSearchValue"
    [matTooltip]="text"
    [innerHTML]="text | escapeHtml | highlightSearch : (searchValue | escapeHtml)">
  </span>
  <ng-template #noSearchValue>
    <span class="text-with-icon" [matTooltip]="text">{{ text }}</span>
  </ng-template>
  <tx-copy-icon [text]="text"> </tx-copy-icon>
</ng-container>
