import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  TxObjectTypeTreeViewComponent,
  FlatTreeNode,
  TreeData,
  TreeDataOptions,
} from '@bassetti-group/tx-web-core/src/lib/features/trees';
import {
  StringUtils,
  TxClickableElementDirective,
} from '@bassetti-group/tx-web-core/src/lib/utilities';
import { MatFormFieldModule } from '@angular/material/form-field';
import { CommonModule } from '@angular/common';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';

@Component({
  standalone: true,
  imports: [
    MatFormFieldModule,
    CommonModule,
    MatSelectModule,
    TranslateModule,
    FontAwesomeModule,
    ReactiveFormsModule,
    TxObjectTypeTreeViewComponent,
    MatDividerModule,
    MatTooltipModule,
    MatChipsModule,
    TxClickableElementDirective,
  ],
  selector: 'tx-dropdown-tree',
  templateUrl: './dropdown-tree.component.html',
  styleUrls: ['./dropdown-tree.component.scss'],
})
export class TxDropdownTreeComponent<T extends TreeData & { id?: number }>
  implements OnInit, OnChanges
{
  @ViewChild('treeSearchInput') treeSearchInput: ElementRef | undefined;
  @ViewChild('treeView') treeView: TxObjectTypeTreeViewComponent<T> | undefined;
  @ViewChild('treeSelect') treeSelect: MatSelect | undefined;

  @Input() control: FormControl | undefined;
  @Input() dataSource: TreeData[] = [];
  @Input() dataOptions: TreeDataOptions | undefined;
  @Input() label = '';
  @Input() treeNodeTemplate: TemplateRef<any> | undefined;
  @Input() width? = '100%';
  @Input() readyToLoad? = true;
  @Input() allowFiltering? = true;
  @Input() showClearButton? = true;
  @Input() required? = false;
  @Input() returnIds? = false;
  @Input() disabled? = false;
  @Input() readonly? = false;
  @Input() displayHint? = true;
  @Input() enableRemoveSelection? = true;
  @Input() message: string | undefined;
  @Input() isSmallDropdown = false;
  @Input() useChipsForLabels = false;

  /* TreeView inputs */
  @Input() checkedIds?: number[];
  @Input() disabledIds?: number[];
  @Input() showCheckBox?: boolean;
  @Input() multipleSelection?: boolean;
  @Input() hierarchicalSelection?: boolean;

  @Output() selectionChange = new EventEmitter<TreeData[] | string[]>();

  public filteredDataSource: TreeData[] = [];
  public selectedNodes: TreeData[] = [];
  public selectedNodesNames: string[] = [];
  public selectedNodesLength = 0;
  public previousSearchInputValue = '';

  ngOnInit() {
    this.filteredDataSource = [...this.dataSource];
    if (this.required) {
      this.control?.setValidators(Validators.required);
    }
    this.readyToLoad ??= true;
    if (this.disabled || this.readonly) {
      this.control?.disable();
    }

    this.control?.markAsTouched();
    this.control?.markAsDirty();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.dataSource) {
      this.dataSource = [...changes.dataSource.currentValue];
      if (this.dataSource.length > 0) {
        this.fillInputWithSelectedNodes();
        this.fillInputWithDisabledNodes();
      }
    }
    if (changes.checkedIds && this.dataSource.length > 0) {
      this.fillInputWithSelectedNodes();
    }
    if (changes.disabledIds && this.dataSource.length > 0) {
      this.fillInputWithDisabledNodes();
    }
  }

  fillInputWithSelectedNodes() {
    this.checkedIds = this.checkedIds?.map((id) => (typeof id === 'number' ? id : parseInt(id)));
    const selectedNodeObjects = this.dataSource.filter((srcObj) =>
      this.checkedIds?.includes(srcObj[this.dataOptions?.idProperty ?? 'id'] as number)
    );
    this.selectedNodes = selectedNodeObjects;
    this.selectedNodesNames = selectedNodeObjects.map((obj) => obj.name);
    this.selectedNodesLength = selectedNodeObjects.length;
    this.control?.setValue(this.selectedNodesNames);
    this.control?.updateValueAndValidity();
  }

  fillInputWithDisabledNodes() {
    this.disabledIds = this.disabledIds?.map((id) => (typeof id === 'number' ? id : parseInt(id)));
  }

  onSelectionChange(selectedNodes: FlatTreeNode<T>[]) {
    this.selectedNodes = selectedNodes.map((node) => node.objectData);
    this.selectedNodesNames = selectedNodes.map((node) => node.name);
    this.selectedNodesLength = selectedNodes.length;
    this.control?.setValue(this.selectedNodesNames);
    this.control?.updateValueAndValidity();
    if (!this.showCheckBox || this.multipleSelection === false) {
      this.treeSelect?.close();
    }
    this.emitSelection();
  }

  displaySelectedNodes(node: FlatTreeNode<T>) {
    return node ? node.name : '';
  }

  clickOnSelect() {
    this.treeView?.treeControl.expandAll();
    setTimeout(() => {
      this.treeSearchInput?.nativeElement.focus();
    }, 300);
  }

  filterDataSource(value: string) {
    const cleanedSearchInputValue = StringUtils.cleanTextForSearch(value);
    if (StringUtils.cleanTextForSearch(this.previousSearchInputValue) !== cleanedSearchInputValue) {
      this.filteredDataSource = this.dataSource.filter(
        (obj) =>
          this.checkedIds?.includes(obj[this.dataOptions?.idProperty ?? 'id'] as number) ||
          StringUtils.cleanTextForSearch(obj.name).includes(cleanedSearchInputValue)
      );
      this.previousSearchInputValue = value;
    }
  }

  clearSelection(event: MouseEvent) {
    this.treeView?.uncheckAll(true);
    event.stopPropagation();
  }

  removeChip(event: MouseEvent, node: TreeData) {
    this.checkedIds = this.checkedIds?.filter(
      (id) => id !== node[this.dataOptions?.idProperty ?? 'id']
    );
    this.fillInputWithSelectedNodes();
    this.emitSelection();
    event.stopPropagation();
  }

  clearSearchInput() {
    if (this.treeSearchInput) {
      this.treeSearchInput.nativeElement.value = '';
    }
    this.filterDataSource('');
  }

  /* CTRL + a not working natively, dunno why */
  checkForSelection(event: KeyboardEvent) {
    if (event.key === 'a' && event.ctrlKey) {
      this.treeSearchInput?.nativeElement.select();
    }
    event.stopPropagation();
  }

  private emitSelection() {
    if (this.returnIds) {
      this.selectionChange.emit(
        this.selectedNodes.map((node) => '' + node[this.dataOptions?.idProperty ?? 'id'])
      );
    } else {
      this.selectionChange.emit(this.selectedNodes);
    }
  }
}
