import { ComponentHarness, HarnessPredicate } from '@angular/cdk/testing';
import { MatChipHarness } from '@angular/material/chips/testing';
import { MatButtonHarness } from '@angular/material/button/testing';
import { MatSelectHarness } from '@angular/material/select/testing';
import { MatInputHarness } from '@angular/material/input/testing';

export class TxGridFilterMenuHarness extends ComponentHarness {
  static readonly hostSelector = 'tx-grid-filter-menu';

  static with(options: {} = {}): HarnessPredicate<TxGridFilterMenuHarness> {
    return new HarnessPredicate(TxGridFilterMenuHarness, options);
  }

  async getActiveFilters(): Promise<string[]> {
    const filterChips = await this.locatorForAll(MatChipHarness.with({}))();
    return Promise.all(filterChips.map((chip) => chip.getText()));
  }

  async getFilterableColumns(): Promise<string[]> {
    const select = await this.locatorFor(MatSelectHarness.with({}))();
    const options = await select.getOptions();
    return Promise.all(options.map((option) => option.getText()));
  }

  async selectColumn(columnName: string): Promise<void> {
    const select = await this.locatorFor(MatSelectHarness.with({}))();
    await select.clickOptions({ text: columnName });
  }

  async selectFilterType(filterType: string): Promise<void> {
    const select = await this.locatorFor(MatSelectHarness.with({}))();
    await select.clickOptions({ text: filterType });
  }

  async enterFilterValue(value: string): Promise<void> {
    const input = await this.locatorFor(MatInputHarness.with({}))();
    await input.setValue(value);
  }

  async applyNewFilter(column: string, filterType: string, value: string): Promise<void> {
    await this.selectColumn(column);
    await this.selectFilterType(filterType);
    await this.enterFilterValue(value);
    const addButton = await this.locatorFor(MatButtonHarness.with({ text: 'Add Filter' }))();
    await addButton.click();
  }

  async removeFilter(filterName: string): Promise<void> {
    const filterChips = await this.locatorForAll(MatChipHarness.with({ text: filterName }))();
    for (const chip of filterChips) {
      await chip.remove();
    }
  }

  async removeAllFilters(): Promise<void> {
    const deleteAllButton = await this.locatorFor(MatButtonHarness.with({ text: 'Clear All' }))();
    await deleteAllButton.click();
  }
}
