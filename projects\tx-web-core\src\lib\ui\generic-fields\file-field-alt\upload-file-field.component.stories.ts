import { Args, type Meta, type StoryObj } from '@storybook/angular';
import { FormControl } from '@angular/forms';
import { TxUploadFileFieldComponent } from './upload-file-field.component';

const control = new FormControl([]);

const meta: Meta<TxUploadFileFieldComponent> = {
  component: TxUploadFileFieldComponent,
  title: 'Generic Fields/TxUploadFileFieldComponent',
  tags: ['autodocs'],
};
export default meta;

export const Primary: StoryObj = {
  render: (args: Partial<Args>) => ({
    props: { ...args, formControl: control },
    template: `
    <div>
    <tx-upload-file-field
    [formControl]="formControl"
    [label]="label"
    [labelTooltip]="labelTooltip"
    [required]="required"
    [requiredFileType]="requiredFileType"
    [multiple]="multiple"
    [maxMoFileSize]="maxMoFileSize"
    [idAttribute]="idAttribute"
    [hideVisualisationToggle]="hideVisualisationToggle"
    ></tx-upload-file-field>
    </div>
   `,
  }),
};

Primary.args = {
  label: 'label upload file',
  labelTooltip: 'tooltip upload file',
  required: true,
  multiple: true,
  requiredFileType: '',
  maxMoFileSize: 40,
  idAttribute: 1,
  hideVisualisationToggle: false,
};
