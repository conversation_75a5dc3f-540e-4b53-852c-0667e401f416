import { TxHighlightSearchPipe } from './highlight-search.pipe';

describe('HighlightSearchPipe', () => {
  const pipe = new TxHighlightSearchPipe();

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return value if no argument', () => {
    expect(pipe.transform('This is my text', undefined)).toBe('This is my text');
  });

  it('should add a mark for text', () => {
    expect(pipe.transform('This is my text', 'text')).toBe('This is my <mark>text</mark>');
  });

  it('should add a mark with special characters', () => {
    expect(pipe.transform('This is my "#text\\&"', '"#text\\&"')).toBe(
      'This is my <mark>"#text\\&"</mark>'
    );
  });

  it('should NOT add a mark if not found', () => {
    expect(pipe.transform('This is my "#text\\&"', '"#text"')).toBe('This is my "#text\\&"');
  });

  it('should add a mark for text with accent even if the input is text without accent', () => {
    expect(pipe.transform('This is my tâche', 'tache')).toBe('This is my <mark>tâche</mark>');
  });

  it('should add a mark for text with accent even if the input is text without accent and in majuscule', () => {
    expect(pipe.transform('This is my tâche', 'tAche')).toBe('This is my <mark>tâche</mark>');
  });
});
