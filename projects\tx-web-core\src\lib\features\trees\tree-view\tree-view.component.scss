.mat-treeview {
  overflow: hidden;
}
.mat-tree-node {
  user-select: none;
  cursor: pointer;
  font-size: 13px;
}
.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 32px;
}
.tree-content {
  display: flex;
  align-items: center;
  width: 100%;
}
.spacer {
  padding-left: 24px;
}
.tree-chevron-icon {
  font-size: 11px;
  justify-content: center;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
}
