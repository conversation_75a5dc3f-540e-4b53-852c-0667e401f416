import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxSlideToggleComponent } from './tx-slide-toggle.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FormControl, ReactiveFormsModule } from '@angular/forms';

describe('TxSlideToggleComponent', () => {
  let component: TxSlideToggleComponent;
  let fixture: ComponentFixture<TxSlideToggleComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MatSlideToggleModule, TxSlideToggleComponent, ReactiveFormsModule],
    }).compileComponents();

    fixture = TestBed.createComponent(TxSlideToggleComponent);
    component = fixture.componentInstance;
    component.formControl = new FormControl();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
