import { Pipe, PipeTransform } from '@angular/core';
import { Invalid, TxGridColumn } from '../grid.interface';
@Pipe({
  standalone: true,
  name: 'gridCellClass',
})
export class GridCellClass implements PipeTransform {
  transform<T extends { invalid: Invalid; isEditActive: boolean }>(
    column: TxGridColumn<T>,
    element: T,
    cell: HTMLElement
  ) {
    return {
      'flex-right': column.textAlign === 'right',
      'flex-center': column.textAlign === 'center',
      'warning-cell':
        (element.isEditActive === false || element.isEditActive === undefined) &&
        element?.invalid !== undefined &&
        element?.invalid?.value === true &&
        [element?.invalid?.field].flat().some((field) => field === column.field),
    };
  }
}
