<!-- <tx-container-field #container [fields]="fields" (displayPaneEvent)="displayRightPane($event)" [idObject]="idObject" [readMode]="readMode" [form]="form" class="field-container" ></tx-container-field> -->

<div class="fields-container">
  <!-- <div [formGroup]="form"> -->
  <div
    *ngFor="let field of fields; let index = index"
    [ngSwitch]="field.attribute.dataType"
    [class]="field.classes"
    [ngClass]="{ 'hidden-field': readMode && haveToBeHidden(index) }">
    <tx-boolean-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.Boolean"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-boolean-object-field>
    <tx-date-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.Date"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-date-object-field>
    <tx-date-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.DateTime"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-date-object-field>
    <tx-link-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.Link"
      (displayPaneEvent)="displayRightPane($event)"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-link-object-field>
    <tx-link-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.LnkDirect"
      (displayPaneEvent)="displayRightPane($event)"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-link-object-field>
    <tx-link-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.LnkInv"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-link-object-field>
    <tx-link-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.Enum"
      (displayPaneEvent)="displayRightPane($event)"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-link-object-field>
    <tx-link-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.LnkBi"
      (displayPaneEvent)="displayRightPane($event)"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-link-object-field>
    <tx-long-text-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.Text"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-long-text-object-field>
    <!-- <tx-table-object-field #fieldComponent *ngSwitchCase="fieldType.Table" [idObject]="idObject" [field]="field" [form]="form" ></tx-table-object-field> -->
    <tx-short-string-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.String"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-short-string-object-field>
    <tx-object-group-field
      #groupComponent
      *ngSwitchCase="fieldType.Group"
      (displayPaneEvent)="displayRightPane($event)"
      [idObject]="idObject"
      [readMode]="readMode"
      [groupField]="field"
      [form]="form"
      class="form-group-field"></tx-object-group-field>
    <tx-file-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.File"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-file-object-field>
    <tx-mail-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.Email"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-mail-object-field>
    <tx-point-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.DecRange"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-point-object-field>
    <tx-point-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.DecRangeMean"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-point-object-field>
    <tx-point-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.DecUnique"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-point-object-field>
    <tx-url-object-field
      #fieldComponent
      *ngSwitchCase="fieldType.URL"
      [idObject]="idObject"
      [readMode]="readMode"
      [field]="field"
      [form]="form"></tx-url-object-field>
  </div>
</div>
