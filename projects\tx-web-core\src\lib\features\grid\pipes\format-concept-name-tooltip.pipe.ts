import { Pipe, PipeTransform } from '@angular/core';
import { TxObjectType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TranslateService } from '@ngx-translate/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';

@Pipe({
  standalone: true,
  name: 'formatConceptNameTooltip',
})
export class FormatConceptNameTooltipPipe implements PipeTransform {
  constructor(private readonly translate: TranslateService) {}
  /**
   *
   * @param concept
   * @param args
   * @returns
   */
  transform<
    T extends {
      id: number;
      name: string;
      explanation?: string;
      objectType?: TxObjectType;
      txObject?: { id: number; explanation?: string; creationDate?: string; description?: string };
    }
  >(concept: T, showMore?: boolean, hideId?: boolean): string {
    const id = concept.txObject ? concept.txObject.id : concept.id;
    const explanation = concept.txObject ? concept.txObject.explanation : concept.explanation;
    const stringId = this.translate.instant(_('txWebCore.generic.id'));

    if (showMore) {
      const descriptionTR = this.translate.instant(_('txWebCore.generic.description'));
      const objectTypeTR = this.translate.instant(_('txWebCore.dataType.-1'));
      const creationDateTR = this.translate.instant(_('txWebCore.generic.creationDate'));
      let processedDate;
      if (concept.txObject?.creationDate) {
        processedDate = new Date(concept.txObject.creationDate)
          .toDateString()
          .split(' ')
          .slice(1)
          .join(' ');
      }

      const details = [
        !hideId ? `${stringId} ${id}` : null,
        concept.name,
        concept.objectType?.name ? `${objectTypeTR}: ${concept.objectType.name}` : null,
        concept.txObject?.description ? `${descriptionTR}: ${concept.txObject.description}` : null,
        processedDate ? `${creationDateTR}: ${processedDate}` : null,
      ]
        .filter(Boolean)
        .join('\n ');

      return details;
    }
    return explanation
      ? `${stringId} ${id}\n${concept.name}\n${explanation}`
      : `${stringId} ${id}\n${concept.name}`;
  }
}
