import { ChangeDetectionStrategy, Component, Input, ViewChild } from '@angular/core';
import {
  FlatImportedCoreModelConcept,
  ImportedCoreModelConceptFieldEnum,
} from '../../models/imported-core-model-concept.model';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { GroupSettingsModel } from '@syncfusion/ej2-angular-grids';
import { TranslateService } from '@ngx-translate/core';
import { SUPPORTED_CONCEPTS } from 'src/app/admins/core-model-common';
import { TextUtils } from 'src/app/core/utils/text.utils';
import {
  FilterOptionsList,
  GridFilterType,
  InputSearchEventInfo,
  TxGridColumn,
  TxGridComponent,
  TxGridDataType,
  TxObjectTypeType,
} from '@bassetti-group/tx-web-core';
import { FlatCoreModelExportConcept } from 'src/app/admins/core-model-export/models/core-model-export-concept.model';

@Component({
  selector: 'app-imported-core-model-concepts',
  templateUrl: './imported-core-model-concepts.component.html',
  styleUrls: ['./imported-core-model-concepts.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ImportedCoreModelConceptsComponent {
  private readonly defaultActiveFilter = true;
  public inputSearchValue: string = '';
  public searchById: number | undefined;
  @Input() flatConcepts: FlatImportedCoreModelConcept[] = [];
  @ViewChild('coreModelGrid') coreModelsGrid:
    | TxGridComponent<FlatCoreModelExportConcept>
    | undefined;
  columns: TxGridColumn<FlatImportedCoreModelConcept>[] = Object.values(
    ImportedCoreModelConceptFieldEnum
  ).map((field) => {
    if (field === ImportedCoreModelConceptFieldEnum.Id) {
      return {
        field,
        headerText: _(`admins.columns.${field}`),
        type: TxGridDataType.TEXT,
        isPrimaryKey: true,
        isVisible: false,
        visible: false,
        isSearchable: true,
      };
    } else if (field === ImportedCoreModelConceptFieldEnum.Tags) {
      return {
        field,
        headerText: _(`admins.coreModels.tag`),
        type: TxGridDataType.TEXT,
        activeFilter: this.defaultActiveFilter,
        width: this.getColumnWidth(field),
        isSearchable: true,
        sorting: true,
      };
    } else if (field === ImportedCoreModelConceptFieldEnum.Type) {
      return {
        field,
        headerText: _(`admins.columns.${field}`),
        type: TxGridDataType.TEXT,
        activeFilter: this.defaultActiveFilter,
        visible: false,
      };
    }
    return {
      field,
      headerText: _(`admins.columns.${field}`),
      type: TxGridDataType.TEXT,
      activeFilter: this.defaultActiveFilter,
      isSearchable: true,
      sorting: true,
    };
  });
  filterColumns: TxGridColumn<FlatImportedCoreModelConcept>[] = this.columns.filter(
    (data) => data.field !== ImportedCoreModelConceptFieldEnum.Id
  );

  groupSettings: GroupSettingsModel = {
    columns: [ImportedCoreModelConceptFieldEnum.Type],
    showDropArea: false,
  };
  gridFilterOptions: FilterOptionsList[] = [
    {
      column: ImportedCoreModelConceptFieldEnum.Type,
      options: Object.values(SUPPORTED_CONCEPTS).map((type) => ({
        value: type,
        text: this.translate.instant(_(`concepts.${TextUtils.firstLetterLowercase(type)}`)),
      })),
      hideFilterType: true,
      filterType: GridFilterType.FilterSelectLarge,
    },
    {
      column: ImportedCoreModelConceptFieldEnum.ObjectType,
      hideFilterType: true,
      filterType: GridFilterType.ObjectType,
      settings: {
        types: [TxObjectTypeType.Standard, TxObjectTypeType.User, TxObjectTypeType.Portal],
        onlyVisible: false,
      },
      options: [],
    },
  ];
  ImportedCoreModelConceptFieldEnum = ImportedCoreModelConceptFieldEnum;
  constructor(private readonly translate: TranslateService) {}

  private getColumnWidth(field: ImportedCoreModelConceptFieldEnum): string | undefined {
    if (field === ImportedCoreModelConceptFieldEnum.Tags) {
      return '20%';
    }
    return undefined;
  }
  searchItem(inputSearchEventInfo: InputSearchEventInfo): void {
    if (this.coreModelsGrid) {
      this.inputSearchValue = inputSearchEventInfo.inputSearch.nativeElement.value;
      const event = inputSearchEventInfo.event;
      const eventCode = event instanceof KeyboardEvent ? event.code : '';
      if (event.type === 'keyup' && (eventCode === 'Enter' || eventCode === 'NumpadEnter')) {
        this.coreModelsGrid.searchByTextSelect(this.inputSearchValue);
      }
    }
  }
}
