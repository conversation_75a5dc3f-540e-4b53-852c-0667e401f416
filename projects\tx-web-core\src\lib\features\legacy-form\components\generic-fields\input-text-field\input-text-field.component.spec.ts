import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { TxInputTextFieldComponent } from './input-text-field.component';

describe('TxWriteTextFieldComponent', () => {
  let component: TxInputTextFieldComponent;
  let fixture: ComponentFixture<TxInputTextFieldComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TxInputTextFieldComponent],
      imports: [
        MatTooltipModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatInputModule,
        BrowserAnimationsModule,
        MatChipsModule,
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxInputTextFieldComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('testing initValue...', () => {
    it('should init value to empty string if no value and no control value', () => {
      component.initValue();
      expect(component.value).toBe('');
    });

    it('should init control value to empty string if no value and no control value', () => {
      component.initValue();
      expect(component.control.value).toBe('');
    });

    it('should init value to control value there is a control value and no value', () => {
      component.control.setValue('test'), component.initValue();
      expect(component.value).toBe('test');
    });

    it('should init control value to value', () => {
      component.control.setValue('test1');
      (component.value = 'test'), component.initValue();
      expect(component.control.value).toBe('test');
    });
  });

  describe('testing onFieldChange...', () => {
    it('should affect control value to value', () => {
      component.value = 'falseValue';
      component.control.setValue('test');
      component.onFieldChange();
      expect(component.value).toBe('test');
    });
  });
});
