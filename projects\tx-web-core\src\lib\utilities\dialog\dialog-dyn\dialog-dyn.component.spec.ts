import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxDialogDynComponent } from './dialog-dyn.component';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateTestingModule } from 'ngx-translate-testing';

describe('TxDialogConfirmDynComponent', () => {
  let component: TxDialogDynComponent;
  let fixture: ComponentFixture<TxDialogDynComponent>;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        MatDialogModule,
        FontAwesomeTestingModule,
        MatButtonModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
        TxDialogDynComponent,
      ],
      providers: [
        { provide: MatDialogRef, useValue: {} },
        { provide: MAT_DIALOG_DATA, useValue: {} },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxDialogDynComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
