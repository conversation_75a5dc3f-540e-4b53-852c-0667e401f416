<mat-chip
  class="data-chip"
  *ngIf="file"
  [matTooltip]="file.name"
  [matTooltipShowDelay]="500"
  matTooltipPosition="above"
  [ngClass]="{ 'uploaded-file': file?.idArchivedFile <= 0 }"
  [disableRipple]="true">
  <div class="chip-content">
    <div class="box">
      <fa-icon class="chip-icon chip-icon-left" [icon]="icon"></fa-icon>
      <div
        class="file-name-text-container"
        [ngClass]="{
          'file-name-text-container-without-view-toggle':
            hideVisualisationToggle && (!file || !file.uploadProgress)
        }">
        <span class="file-name-text">{{ file.name }}</span>
      </div>
      <div *ngIf="file && file.uploadProgress" class="progress">
        <mat-progress-bar
          class="progress-bar"
          mode="determinate"
          [value]="file.uploadProgress"></mat-progress-bar>
      </div>

      <div *ngIf="!file || !file.uploadProgress" class="size-and-toggle-container">
        <span *ngIf="file.file" class="field-form-hint">{{ getFileSize() }}</span>

        <!-- <mat-icon class="cancel-upload" (click)="cancelUpload()">delete</mat-icon> -->
        <mat-slide-toggle
          *ngIf="!hideVisualisationToggle"
          class="form-switch"
          disableRipple="true"
          [checked]="file.view"
          (toggleChange)="changeView()"></mat-slide-toggle>
      </div>
      <div class="icon-container">
        <mat-icon class="chip-icon-right remove-chip-icon" matChipRemove (click)="removeFile()"
          >cancel</mat-icon
        >
        <!-- <fa-icon class="chip-icon chip-icon-right" [icon]="['fal', 'times']" (click)="removeFile()"></fa-icon> -->
      </div>
    </div>
  </div>
</mat-chip>
