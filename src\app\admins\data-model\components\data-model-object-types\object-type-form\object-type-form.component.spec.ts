import { AttributesServiceMock } from '../../../../../app.testing.mock';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { TreeGridComponent } from '@syncfusion/ej2-angular-treegrid';
import { MockComponent, MockService } from 'ng-mocks';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { HelpBoxServiceMock, ObjectsTypeServiceMock } from 'src/app/app.testing.mock';
import { FormPaneTemplateComponent } from 'src/app/shared/components/form-pane/form-pane-template.component';
import { ObjectTypeFormComponent } from './object-type-form.component';
import { FormPaneDynComponent } from 'src/app/shared/components/form-pane/form-pane-dyn.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatInputModule } from '@angular/material/input';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSelectModule } from '@angular/material/select';
import { MatDividerModule } from '@angular/material/divider';
import { TagsDescriptionsFormComponent } from 'src/app/shared/components/tags-descriptions-form/tags-descriptions-form.component';
import { FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { Component } from '@angular/core';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HelpBoxService } from 'src/app/core/services/help-box/help-box.service';
import {
  DataBaseRights,
  TxAttributesService,
  TxLockingType,
  TxObjectTypeIconService,
  TxObjectTypeType,
  TxObjectsTypeService,
} from '@bassetti-group/tx-web-core';

const TRANSLATIONS = {
  en: {},
  fr: {},
};

@Component({
  selector: 'app-host-component',
  template: `<app-object-type-form
    [settings]="settings"
    (hideForm)="rightPaneHidden()"></app-object-type-form>`,
})
class TestHostComponent {
  public settings = {
    object: {
      id: 1,
      name: 'Contact',
      order: 0,
      icon: 0,
      isFolder: false,
      type: TxObjectTypeType.Standard,
      hasDistinctName: false,
      isVisible: false,
      tags: [],
      lockingType: TxLockingType.Auto,
      lockingDuration: 0,
      displayResultInTextSearch: true,
      right: DataBaseRights.DbrStructure,
    },
    isEditMode: false,
  };

  rightPaneHidden() {
    return;
  }
}

describe('ObjectTypeFormComponent', () => {
  let component: ObjectTypeFormComponent;
  let hostComponent: TestHostComponent;
  let hostFixture: ComponentFixture<TestHostComponent>;
  let formGroup: FormGroupDirective;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        TestHostComponent,
        FormPaneDynComponent,
        FormPaneTemplateComponent,
        ObjectTypeFormComponent,
        TreeGridComponent,
        MockComponent(TagsDescriptionsFormComponent),
      ],
      imports: [
        FontAwesomeTestingModule,
        MatTooltipModule,
        MatFormFieldModule,
        MatInputModule,
        MatTabsModule,
        MatSelectModule,
        MatDividerModule,
        MatExpansionModule,
        MatSlideToggleModule,
        ReactiveFormsModule,
        BrowserAnimationsModule,
        TranslateTestingModule.withTranslations(TRANSLATIONS),
      ],
      providers: [
        { provide: TxObjectsTypeService, useClass: ObjectsTypeServiceMock },
        { provide: HelpBoxService, useClass: HelpBoxServiceMock },
        { provide: TxAttributesService, useClass: AttributesServiceMock },
        { provide: TxObjectTypeIconService, useValue: MockService(TxObjectTypeIconService) },
        { provide: FormGroupDirective, useValue: formGroup },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    hostFixture = TestBed.createComponent(TestHostComponent);
    hostComponent = hostFixture.componentInstance;
    component = hostFixture.debugElement.children[0].componentInstance;
    hostFixture.detectChanges();
  });

  it('should create', () => {
    expect(hostComponent).toBeTruthy();
    expect(component).toBeTruthy();
  });

  describe('Add mode', () => {
    it('should generate a creation form', () => {
      hostFixture.detectChanges();
      if (component.id) {
        expect(component.id.value).toBe(0);
      }
    });
  });
});
