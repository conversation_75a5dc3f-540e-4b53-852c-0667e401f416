import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TxInputTextFieldComponent } from './input-text-field.component';
import { By } from '@angular/platform-browser';
import { Component, DebugElement } from '@angular/core';
import { TranslateTestingModule } from 'ngx-translate-testing';

@Component({
  template: `<tx-input-text-field
      [formControl]="controlOnInput"
      [required]="true"
      [maxLength]="3"></tx-input-text-field>
    <tx-input-text-field
      [formControl]="controlOnTextArea"
      [isTextArea]="true"></tx-input-text-field>`,
})
class HostInputTextFieldComponent {
  controlOnInput = new FormControl('testInput');
  controlOnTextArea = new FormControl('testTextArea');
}
describe('TxInputTextFieldComponent', () => {
  let componentWithInput: TxInputTextFieldComponent;
  let componentWithTextArea: TxInputTextFieldComponent;
  let componentWithInputDebugElement: DebugElement;
  let componentWithTextAreaDebugElement: DebugElement;
  let hostComponent: HostInputTextFieldComponent;
  let hostFixture: ComponentFixture<HostInputTextFieldComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TxInputTextFieldComponent,
        ReactiveFormsModule,
        NoopAnimationsModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
      ],
      declarations: [HostInputTextFieldComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    hostFixture = TestBed.createComponent(HostInputTextFieldComponent);
    hostComponent = hostFixture.componentInstance;
    const componentDebugElement = hostFixture.debugElement.queryAll(
      By.directive(TxInputTextFieldComponent)
    );
    componentWithInputDebugElement = componentDebugElement[0];
    componentWithTextAreaDebugElement = componentDebugElement[1];
    componentWithInput = componentWithInputDebugElement.componentInstance;
    componentWithTextArea = componentWithTextAreaDebugElement.componentInstance;
    hostFixture.detectChanges();
  });

  it('should create', () => {
    expect(hostComponent).toBeTruthy();
    expect(componentWithInput).toBeTruthy();
    expect(componentWithTextArea).toBeTruthy();
  });

  it('input is required', () => {
    const inputEl = componentWithInputDebugElement.query(By.css('input'));
    inputEl.nativeElement.value = '';
    inputEl.triggerEventHandler('input', { target: inputEl.nativeElement });
    hostFixture.detectChanges();
    expect(hostComponent.controlOnInput.errors).toEqual({ required: true });
  });

  it('error if length of text greater than maxLength', () => {
    const inputEl = componentWithInputDebugElement.query(By.css('input'));
    inputEl.nativeElement.value = 'test';
    inputEl.triggerEventHandler('input', { target: inputEl.nativeElement });
    hostFixture.detectChanges();
    expect(hostComponent.controlOnInput.errors).toEqual({
      maxlength: { requiredLength: 3, actualLength: 4 },
    });
  });

  it('value input entered', () => {
    const inputEl = componentWithInputDebugElement.query(By.css('input'));
    inputEl.nativeElement.value = 'input test';
    inputEl.triggerEventHandler('input', { target: inputEl.nativeElement });
    hostFixture.detectChanges();
    expect(hostComponent.controlOnInput.value).toBe('input test');
  });

  it('isTextArea is working correctly', () => {
    const inputEl = componentWithTextAreaDebugElement.query(By.css('textarea'));
    inputEl.nativeElement.value = 'textarea test';
    inputEl.triggerEventHandler('input', { target: inputEl.nativeElement });
    hostFixture.detectChanges();
    expect(hostComponent.controlOnTextArea.value).toBe('textarea test');
  });
});
