import { addLevelsToTree, generateTree, assignTxId, flattenTree } from '../tree-utils';
import { TxTreeGridChildren } from '../tree-grid.interface';
import { Observable, of } from 'rxjs';

interface TestNode extends TxTreeGridChildren<TestNode> {
  id: number;
}
interface TestItem {
  [key: string]: any;
  children?: TestItem[];
}

describe('addLevelsToTree', () => {
  it('should add level 0 to a single root node', () => {
    const treeData: TestNode[] = [{ id: 1, children: [] }];

    const result = addLevelsToTree(treeData, 'children');

    expect(result).toEqual([{ id: 1, children: [], level: 0 }]);
  });

  it('should add levels to nodes with multiple levels', () => {
    const treeData: TestNode[] = [
      {
        id: 1,
        children: [
          { id: 2, children: [] },
          { id: 3, children: [] },
        ],
      },
    ];

    const result = addLevelsToTree(treeData, 'children');

    expect(result).toEqual([
      {
        id: 1,
        children: [
          { id: 2, children: [], level: 1 },
          { id: 3, children: [], level: 1 },
        ],
        level: 0,
      },
    ]);
  });

  it('should handle deeply nested nodes', () => {
    const treeData: TestNode[] = [
      {
        id: 1,
        children: [
          {
            id: 2,
            children: [{ id: 3, children: [] }],
          },
        ],
      },
    ];

    const result = addLevelsToTree(treeData, 'children');

    expect(result).toEqual([
      {
        id: 1,
        children: [
          {
            id: 2,
            children: [{ id: 3, children: [], level: 2 }],
            level: 1,
          },
        ],
        level: 0,
      },
    ]);
  });

  it('should handle empty tree data', () => {
    const treeData: TestNode[] = [];

    const result = addLevelsToTree(treeData, 'children');

    expect(result).toEqual([]);
  });

  it('should handle nodes without children', () => {
    const treeData: TestNode[] = [{ id: 1 }] as any;

    const result = addLevelsToTree(treeData, 'children');

    expect(result).toEqual([{ id: 1, level: 0 }]);
  });
});

describe('generateTree', () => {
  it('should generate a flat list when no parent relationships exist', () => {
    const input: TestItem[] = [
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' },
      { id: 3, name: 'Item 3' },
    ];

    const result = generateTree<TestItem>(input, 'id', 'parentId');

    expect(result).toHaveLength(3);
    expect(result).toEqual([
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' },
      { id: 3, name: 'Item 3' },
    ]);
  });

  it('should generate a simple parent-child tree', () => {
    const input: TestItem[] = [
      { id: 1, name: 'Parent' },
      { id: 2, name: 'Child 1', parentId: 1 },
      { id: 3, name: 'Child 2', parentId: 1 },
    ];

    const result = generateTree<TestItem>(input, 'id', 'parentId');

    expect(result).toHaveLength(1);
    expect(result[0].children).toHaveLength(2);
    expect(result).toEqual([
      {
        id: 1,
        name: 'Parent',
        children: [
          { id: 2, name: 'Child 1', parentId: 1 },
          { id: 3, name: 'Child 2', parentId: 1 },
        ],
      },
    ]);
  });

  it('should handle multiple levels of nesting', () => {
    const input: TestItem[] = [
      { id: 1, name: 'Grandparent' },
      { id: 2, name: 'Parent', parentId: 1 },
      { id: 3, name: 'Child', parentId: 2 },
    ];

    const result = generateTree<TestItem>(input, 'id', 'parentId');

    expect(result).toHaveLength(1);
    expect(result[0].children?.[0].children).toHaveLength(1);
    expect(result).toEqual([
      {
        id: 1,
        name: 'Grandparent',
        children: [
          {
            id: 2,
            name: 'Parent',
            parentId: 1,
            children: [{ id: 3, name: 'Child', parentId: 2 }],
          },
        ],
      },
    ]);
  });

  it('should handle items with missing parent references', () => {
    const input: TestItem[] = [
      { id: 1, name: 'Root' },
      { id: 2, name: 'Orphan', parentId: 999 }, // Non-existent parent
      { id: 3, name: 'Child', parentId: 1 },
    ];

    const result = generateTree<TestItem>(input, 'id', 'parentId');

    expect(result).toHaveLength(1);
    expect(result[0].children).toHaveLength(1);
    expect(result).toEqual([
      {
        id: 1,
        name: 'Root',
        children: [{ id: 3, name: 'Child', parentId: 1 }],
      },
    ]);
  });

  it('should handle empty input array', () => {
    const input: TestItem[] = [];
    const result = generateTree<TestItem>(input, 'id', 'parentId');
    expect(result).toEqual([]);
  });

  it('should handle non-sequential IDs', () => {
    const input: TestItem[] = [
      { id: 100, name: 'Parent' },
      { id: 200, name: 'Child', parentId: 100 },
    ];

    const result = generateTree<TestItem>(input, 'id', 'parentId');

    expect(result).toHaveLength(1);
    expect(result[0].children).toHaveLength(1);
    expect(result).toEqual([
      {
        id: 100,
        name: 'Parent',
        children: [{ id: 200, name: 'Child', parentId: 100 }],
      },
    ]);
  });
});

describe('assignTxId', () => {
  // Test data
  const flatData: TestItem[] = [{ name: 'Item 1' }, { name: 'Item 2' }, { name: 'Item 3' }];

  const nestedData: TestItem[] = [
    {
      name: 'Parent 1',
      children: [
        { name: 'Child 1.1' },
        {
          name: 'Child 1.2',
          children: [{ name: 'Grandchild 1.2.1' }],
        },
      ],
    },
    {
      name: 'Parent 2',
      children: [{ name: 'Child 2.1' }],
    },
  ];

  describe('Array input', () => {
    it('should assign sequential IDs to flat array', () => {
      const result = assignTxId(flatData, 'children', 'id');

      expect(result).toEqual([
        { id: 0, name: 'Item 1' },
        { id: 1, name: 'Item 2' },
        { id: 2, name: 'Item 3' },
      ]);
    });

    it('should assign sequential IDs to nested array', () => {
      const result = assignTxId(nestedData, 'children', 'id') as TestItem[];

      expect(result[0].id).toBe(0);
      expect(result[0].children![0].id).toBe(1);
      expect(result[0].children![1].id).toBe(2);
      expect(result[0].children![1].children![0].id).toBe(3);
      expect(result[1].id).toBe(4);
      expect(result[1].children![0].id).toBe(5);
    });

    it('should handle empty arrays', () => {
      const emptyData: TestItem[] = [];
      const result = assignTxId(emptyData, 'children', 'id');

      expect(result).toEqual([]);
    });

    it('should handle items with empty children arrays', () => {
      const dataWithEmptyChildren: TestItem[] = [{ name: 'Parent', children: [] }];

      const result = assignTxId(dataWithEmptyChildren, 'children', 'id') as TestItem[];

      expect(result[0].id).toBe(0);
      expect(result[0].children).toEqual([]);
    });
  });

  describe('Observable input', () => {
    it('should handle Observable input with flat data', (done) => {
      const observable = of(flatData);
      const result = assignTxId<TestItem>(observable, 'children', 'id') as Observable<TestItem[]>;

      result.subscribe({
        next: (data) => {
          expect(data[0].id).toBe(0);
          expect(data[1].id).toBe(1);
          expect(data[2].id).toBe(2);
          expect(flatData[0].id).toBe(0);
          done();
        },
        error: done,
      });
    });

    it('should handle Observable input with nested data', (done) => {
      const observable = of(nestedData);
      const result = assignTxId<TestItem>(observable, 'children', 'id') as Observable<TestItem[]>;

      result.subscribe({
        next: (data) => {
          expect(data[0].id).toBe(0);
          expect(data[0].children![0].id).toBe(1);
          expect(data[0].children![1].id).toBe(2);
          expect(data[0].children![1].children![0].id).toBe(3);
          expect(data[1].id).toBe(4);
          expect(data[1].children![0].id).toBe(5);
          done();
        },
        error: (error) => done(error),
      });
    });

    it('should handle Observable with empty array', (done) => {
      const observable = of([] as TestItem[]);
      const result = assignTxId<TestItem>(observable, 'children', 'id') as Observable<TestItem[]>;

      result.subscribe({
        next: (data) => {
          expect(data).toEqual([]);
          done();
        },
        error: done,
      });
    });
  });

  describe('Edge cases', () => {
    it('should handle undefined children property', () => {
      const dataWithUndefinedChildren: TestItem[] = [{ name: 'Parent' }];

      const result = assignTxId<TestItem>(dataWithUndefinedChildren, 'children', 'id');
      const typedResult = result as TestItem[];

      expect(typedResult[0].id).toBe(0);
    });

    it('should maintain existing object properties', () => {
      const dataWithExtraProps: TestItem[] = [
        { name: 'Item', extra: 'value', children: [{ name: 'Child', flag: true }] },
      ];

      const result = assignTxId<TestItem>(dataWithExtraProps, 'children', 'id');
      const typedResult = result as TestItem[];

      expect(typedResult[0]['extra']).toBe('value');
      expect(typedResult[0].children![0]['flag']).toBe(true);
    });
  });
});

describe('flattenTree', () => {
  it('should flatten a simple tree structure', () => {
    const input = [
      {
        id: 1,
        name: 'Parent',
        children: [
          { id: 2, name: 'Child 1' },
          { id: 3, name: 'Child 2' },
        ],
      },
    ];

    const expected = [
      { id: 1, name: 'Parent', hasChildren: true },
      { id: 2, name: 'Child 1', hasChildren: false },
      { id: 3, name: 'Child 2', hasChildren: false },
    ];

    expect(flattenTree(input)).toEqual(expected);
  });

  it('should flatten a deeply nested tree structure', () => {
    const input = [
      {
        id: 1,
        name: 'Parent',
        children: [
          {
            id: 2,
            name: 'Child 1',
            children: [{ id: 4, name: 'Grandchild 1' }],
          },
          { id: 3, name: 'Child 2' },
        ],
      },
    ];

    const expected = [
      { id: 1, name: 'Parent', hasChildren: true },
      { id: 2, name: 'Child 1', hasChildren: true },
      { id: 4, name: 'Grandchild 1', hasChildren: false },
      { id: 3, name: 'Child 2', hasChildren: false },
    ];

    expect(flattenTree(input)).toEqual(expected);
  });

  it('should handle empty input array', () => {
    expect(flattenTree([])).toEqual([]);
  });

  it('should handle nodes without children', () => {
    const input = [
      { id: 1, name: 'Node 1' },
      { id: 2, name: 'Node 2' },
    ];
    const expected = [
      { id: 1, name: 'Node 1', hasChildren: false },
      { id: 2, name: 'Node 2', hasChildren: false },
    ];

    expect(flattenTree(input)).toEqual(expected);
  });

  it('should work with custom children key', () => {
    const input = [
      {
        id: 1,
        name: 'Parent',
        subItems: [
          { id: 2, name: 'Child 1' },
          { id: 3, name: 'Child 2' },
        ],
      },
    ];

    const expected = [
      { id: 1, name: 'Parent', hasChildren: true },
      { id: 2, name: 'Child 1', hasChildren: false },
      { id: 3, name: 'Child 2', hasChildren: false },
    ];

    expect(flattenTree(input, 'subItems')).toEqual(expected);
  });

  it('should handle multiple root nodes', () => {
    const input = [
      {
        id: 1,
        name: 'Parent 1',
        children: [{ id: 3, name: 'Child 1' }],
      },
      {
        id: 2,
        name: 'Parent 2',
        children: [{ id: 4, name: 'Child 2' }],
      },
    ];

    const expected = [
      { id: 1, name: 'Parent 1', hasChildren: true },
      { id: 3, name: 'Child 1', hasChildren: false },
      { id: 2, name: 'Parent 2', hasChildren: true },
      { id: 4, name: 'Child 2', hasChildren: false },
    ];

    expect(flattenTree(input)).toEqual(expected);
  });
});
