import type { Meta, StoryObj } from '@storybook/angular';
import { TxColumnNameTemplateComponent } from './column-name-template.component';

const meta: Meta<TxColumnNameTemplateComponent> = {
  component: TxColumnNameTemplateComponent,
  title: 'ColumnNameTemplateComponent',
  tags: ['autodocs'],
};
export default meta;
type Story = StoryObj<TxColumnNameTemplateComponent>;

export const Primary: Story = {
  args: {
    data: { id: 1, name: 'test', icon: 'plus', explanation: 'explain', isDisabled: false },
    hasNoIcon: true,
    searchId: 1,
    dataLength: 10,
    hasMainParentObject: true,
    inputSearchValue: 'input',
  },
};
