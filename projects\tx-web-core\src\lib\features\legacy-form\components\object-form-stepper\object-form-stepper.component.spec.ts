import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LegacyTxObjectFormStepperComponent } from './object-form-stepper.component';
import { MockService } from 'ng-mocks';
import { LegacyTxFormsService } from '../../services/forms.service';
import { of } from 'rxjs';

describe('ObjectFormStepperComponent', () => {
  let component: LegacyTxObjectFormStepperComponent;
  let fixture: ComponentFixture<LegacyTxObjectFormStepperComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [LegacyTxObjectFormStepperComponent],
      providers: [
        {
          provide: LegacyTxFormsService,
          useValue: MockService(LegacyTxFormsService, {
            loadConfig: () => of(),
          }),
        },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(LegacyTxObjectFormStepperComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
