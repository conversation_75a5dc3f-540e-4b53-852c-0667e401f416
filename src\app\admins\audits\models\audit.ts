export interface Section {
  name: string;
  auditPoints: Audit[];
}

export interface Audit {
  id: number;
  explanation: string;
  name: string;
  status?: string;
  result: string;
  isMultiLine: boolean;
  allResult: string;
  isShowAllLine: boolean;
}

export interface DatabaseInformation {
  revision: number;
  version: string;
}

export interface Worksheets {
  // templatePath: string;
  worksheets: Worksheet[];
}
export interface Worksheet {
  name: string;
  cells: Cell[];
}

export interface Cell {
  row: number;
  col: number;
  data?: string;
}

export interface InformationAudit {
  isFileUploaded: boolean;
  numberAuditPoints: number;
  fileName?: string;
}
