import { S } from '@angular/cdk/keycodes';
import {
  Component,
  Input,
  TemplateRef,
  ViewChild,
  ElementRef,
  OnChanges,
  SimpleChanges,
  AfterViewInit,
  Output,
  EventEmitter,
  HostListener,
} from '@angular/core';

@Component({
  selector: 'tx-right-pane',
  templateUrl: './right-pane.component.html',
  styleUrls: ['./right-pane.component.scss'],
})
export class TxRightPaneComponent implements AfterViewInit, OnChanges {
  @ViewChild('paneContainer') paneContainer!: ElementRef;
  @ViewChild('wholeContainer') wholeContainer!: ElementRef;

  @Input() templateContent!: TemplateRef<any>;
  @Input() visible = false;
  @Input() width!: string;
  @Input() backgroundDarkened: boolean = true;
  @Input() hideOnClickout: boolean = false;

  @Output() hide = new EventEmitter();
  @Output() show = new EventEmitter();

  constructor() {}

  @HostListener('document:click', ['$event'])
  clickout(event: any) {
    if (
      !this.paneContainer.nativeElement.contains(event.target) &&
      this.hideOnClickout &&
      this.visible
    ) {
      this.hidePane();
    }
  }

  ngAfterViewInit(): void {
    if (this.paneContainer) {
      this.paneContainer.nativeElement.style.display = 'none';
      this.paneContainer.nativeElement.style.width = this.width;
    }

    if (this.visible) {
      this.displayPane();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.templateContent && changes.templateContent.currentValue) {
    }
  }

  toggle() {
    if (this.visible) {
      this.hidePane();
    } else {
      this.displayPane();
    }
  }

  displayPane(): void {
    this.paneContainer.nativeElement.style.display = 'block';
    this.visible = true;
    this.wholeContainer.nativeElement.style.right = '0px';
    this.show.emit();
  }

  hidePane(): void {
    this.visible = false;
    this.wholeContainer.nativeElement.style.right = '-200%';
    setTimeout(() => {
      this.paneContainer.nativeElement.style.display = 'none';
    }, 100);
    this.hide.emit();
  }
}
