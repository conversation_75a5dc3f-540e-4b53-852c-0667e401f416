import { Pipe, PipeTransform } from '@angular/core';
import { FlatTreeNode, TreeData, TreeDataOptions } from './object-type-tree-view.model';

@Pipe({
  standalone: true,
  name: 'isNodeDisabled',
})
export class TxIsNodeDisabledPipe implements PipeTransform {
  transform(
    node: FlatTreeNode<TreeData>,
    checkList: (string | number)[],
    treeDataOptions?: TreeDataOptions
  ): boolean {
    return checkList.includes(
      node.objectData[treeDataOptions?.idProperty ?? 'id'] as string | number
    );
  }
}
