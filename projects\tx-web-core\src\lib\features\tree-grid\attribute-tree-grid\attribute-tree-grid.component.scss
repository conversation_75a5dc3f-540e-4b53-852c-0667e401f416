.att-tree-grid {
  height: 100%;
}
.ttreegrid-no-grid {
  height: 100%;
}
.toolbar-align {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  mat-slide-toggle {
    margin-left: 0.5rem;
  }
}

.user-selection-none {
  user-select: none;
}
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  width: 100%;
}

.m-checkbox {
  margin-left: -0.5rem;
}

.attribute-tree-grid {
  ::ng-deep .table-row {
    height: 1.75rem;
    min-height: 1.75rem;
  }
}
