import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormControl,
  FormGroup,
  FormsModule,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validator,
  Validators,
} from '@angular/forms';
import 'zone.js';
import { Subject, startWith, takeUntil, tap } from 'rxjs';
import {
  InputNumberFieldsType,
  MinMax,
  MinMaxForm,
  MinMaxMean,
  MinMaxMeanForm,
  TxUnit,
} from './models';
import { isMinMax, isMinMaxMean } from './utils';
import { betweenLowerAndUpper, lowerThanUpper } from './validators';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TxInputNumberComponent } from './input-number.component';
import { MatInputModule } from '@angular/material/input';
import { TxInputSelectComponent } from '../input-select/input-select.component';
import { TranslateModule } from '@ngx-translate/core';
import { concatenateErrors } from '../validators.utils';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
@Component({
  selector: 'tx-min-max-mean',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    MatTooltipModule,
    MatFormFieldModule,
    TxInputNumberComponent,
    TxInputSelectComponent,
    MatInputModule,
    TranslateModule,
  ],
  templateUrl: './input-min-max-mean.component.html',
  styleUrls: ['./input-min-max-mean.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: TxInputMinMaxMeanComponent,
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: TxInputMinMaxMeanComponent,
      multi: true,
    },
  ],
})
export class TxInputMinMaxMeanComponent implements ControlValueAccessor, OnDestroy, Validator {
  @Input() label = '';
  @Input() minLabel = _('txWebCore.generic.min');
  @Input() maxLabel = _('txWebCore.generic.max');
  @Input() meanLabel = _('txWebCore.generic.mean');
  @Input() labelTooltip: string | undefined;
  @Input() units: TxUnit[] = [];
  @Input() required: boolean = false;

  @Input() lowerBound: number | undefined;
  @Input() upperBound: number | undefined;
  @Input() lowerBoundIncluded: boolean = false;
  @Input() upperBoundIncluded: boolean = false;
  @Input() idUnitRef: number = 1;

  @Output() focusEvent = new EventEmitter();

  focused = false;
  disabled: boolean = false;
  form: FormGroup | undefined;
  fieldsType: InputNumberFieldsType = InputNumberFieldsType.MinMax;
  hintBound = '';

  InputNumberFieldsType = InputNumberFieldsType;
  _destroying$ = new Subject<void>();
  private _onValidationChange: () => void = () => {};
  private _onTouched: () => void = () => {};

  get displayFormLabel(): boolean {
    return this.form?.controls['max'] !== undefined;
  }
  get minControl() {
    return this.form?.get('min') as FormControl<number>;
  }
  get maxControl() {
    return this.form?.get('max') as FormControl<number>;
  }
  get meanControl() {
    return this.form?.get('mean') as FormControl<number>;
  }
  get unitControl() {
    return this.form?.get('unit') as FormControl<number>;
  }

  writeValue(value: MinMax | MinMaxMean): void {
    if (!isMinMax(value) && !isMinMaxMean(value)) {
      return undefined;
    }
    if (this.form) {
      this.form.setValue(value);
    } else {
      const [form, fieldsType] = this.initForm(value);
      this.form = form;
      this.fieldsType = fieldsType;
    }
  }

  registerOnChange(fn: any): void {
    this.form?.valueChanges
      .pipe(
        takeUntil(this._destroying$),
        startWith(this.form?.value),
        tap(() => {
          if (this.lowerBound !== undefined || this.upperBound !== undefined) {
            this.updateHintBound();
          }
        })
      )
      .subscribe(fn);
  }

  changeFocus(result: boolean) {
    this.autocompleteValues();
    this.focused = result;
    if (!this.focused) {
      this._onTouched();
      this._onValidationChange();
    }
  }

  registerOnTouched(fn: () => void): void {
    this._onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    isDisabled ? this.form?.disable() : this.form?.enable();
    this.disabled = isDisabled;
  }

  ngOnDestroy(): void {
    this._destroying$.next();
  }

  validate(control: AbstractControl): ValidationErrors | null {
    const requiredError = this.getRequiredError(control);
    const betweenLowerAndUpperErrors = isMinMaxMean(control.value)
      ? betweenLowerAndUpper(control)
      : null;
    const lowerThanUpperErrors = lowerThanUpper(control);
    const inputMinMaxError =
      !requiredError &&
      !betweenLowerAndUpperErrors &&
      !lowerThanUpperErrors &&
      !this.minControl.errors &&
      !this.maxControl.errors &&
      !this.meanControl?.errors &&
      !this.unitControl.errors
        ? null
        : concatenateErrors(
            requiredError,
            betweenLowerAndUpperErrors,
            lowerThanUpperErrors,
            this.minControl.errors,
            this.maxControl.errors,
            this.meanControl?.errors,
            this.unitControl.errors
          );
    this.form?.setErrors(inputMinMaxError);
    return inputMinMaxError;
  }

  registerOnValidatorChange?(fn: () => void): void {
    this._onValidationChange = fn;
  }
  private initForm(
    value: MinMax | MinMaxMean
  ): [FormGroup<MinMaxForm> | FormGroup<MinMaxMeanForm>, InputNumberFieldsType] {
    if (isMinMaxMean(value)) {
      return [
        new FormGroup<MinMaxMeanForm>({
          min: new FormControl(value.min),
          max: new FormControl(value.max),
          mean: new FormControl(value.mean),
          unit: new FormControl(this.idUnitRef),
        }),
        InputNumberFieldsType.MinMaxMean,
      ];
    } else {
      return [
        new FormGroup<MinMaxForm>({
          min: new FormControl(value.min),
          max: new FormControl(value.max),
          unit: new FormControl(this.idUnitRef),
        }),
        InputNumberFieldsType.MinMax,
      ];
    }
  }

  private getRequiredError(control: AbstractControl): ValidationErrors | null {
    return Object.values(this.form?.controls ?? {}).reduce((errors, control) => {
      const controlRequiredError = Validators.required(control);
      const concatenatedError =
        !errors && !Validators.required(control)
          ? null
          : concatenateErrors(errors, controlRequiredError);
      return concatenatedError ? { required: true } : null;
    }, null as ValidationErrors | null);
  }
  private autocompleteValues() {
    if (this.meanControl?.value === null && this.maxControl.value !== null) {
      this.meanControl.setValue((this.minControl.value + this.maxControl.value) / 2);
    }
  }
  private updateHintBound() {
    // update information message about bounds under the field
    this.hintBound = '';
    if (this.lowerBound) {
      this.hintBound += this.lowerBound.toString();
      if (this.lowerBoundIncluded) {
        this.hintBound += ' <= _';
      } else {
        this.hintBound += ' < _';
      }
    }
    if (this.upperBound) {
      if (!this.lowerBound) {
        this.hintBound += '_';
      }
      if (this.upperBoundIncluded) {
        this.hintBound += ' <= ';
      } else {
        this.hintBound += ' < ';
      }
      this.hintBound += this.upperBound.toString();
    }
  }
}
