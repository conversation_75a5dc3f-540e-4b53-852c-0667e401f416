import { LegacyTxObjectType } from '../../../services/structure/models/object-type';
import { TxFormType, TxShowMode, TxEditionMode } from './form-enum';
import { TxTabAttributeField } from './tab-attribute-field';

export class TxFormConfiguration {
  objectType!: LegacyTxObjectType;
  formType!: TxFormType;
  tabs: TxTabAttributeField[];
  showMode!: TxShowMode;
  replaceDefault!: boolean;
  useForANewObject!: boolean;
  properties: any = {};

  constructor(public tag: string, public editionMode: TxEditionMode) {
    this.tabs = [];
    this.properties = [];
  }

  assign(frmConfig?: Partial<TxFormConfiguration>) {
    this.objectType = frmConfig?.objectType as LegacyTxObjectType;
    this.formType = frmConfig?.formType as TxFormType;
    this.tag = frmConfig?.tag as string;
    this.tabs = frmConfig?.tabs as TxTabAttributeField[];
    this.showMode = frmConfig?.showMode as TxShowMode;
    this.replaceDefault = frmConfig?.replaceDefault as boolean;
    this.useForANewObject = frmConfig?.useForANewObject as boolean;
    this.editionMode = frmConfig?.editionMode as TxEditionMode;
    this.properties = frmConfig?.properties;
  }

  public setTag(tag: string) {
    this.tag = tag;
  }

  public setEditionMode(editionMode: TxEditionMode) {
    this.editionMode = editionMode;
  }

  public setObjectType(objectType: LegacyTxObjectType) {
    this.objectType = objectType;
  }
}
