:host {
  --default-grid-border-bottom-width: 1px;
  display: flex;
  flex-direction: column;
  height: 100%;
}
* {
  box-sizing: border-box;
}

.drag-cursor {
  margin-right: 1rem;
  cursor: move;
}

.group-header {
  font-weight: bold;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 0.25rem;
  box-shadow: 0 0.3rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2),
    0 0.5rem 0.625rem 0.06rem rgba(0, 0, 0, 0.14), 0 0.2rem 0.88rem 0.125rem rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  .mat-badge-content {
    opacity: 0 !important;
  }

  .ng-fa-icon {
    opacity: 0 !important;
  }
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .mat-row:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.tx-grid-table {
  flex: 1 1 calc(100% - 3rem);
  max-height: 100%;
  overflow: auto;
  width: 0;
}

.table-row-header {
  height: 2.5rem;
  min-height: 2.5rem;
}

.table-row {
  height: 2.5rem;
  min-height: 2.5rem;
  &:focus-visible {
    outline-style: none;
  }
}

.table-row-cell {
  font-size: 0.75rem;
  border-bottom-width: var(--grid-border-bottom-width, var(--default-grid-border-bottom-width));
}

.table-row-hover {
  &:hover {
    cursor: pointer;
  }
}

.table-row-header-cell {
  font-size: 0.75rem;
}

.table-row-selected {
  cursor: pointer;
  &:hover {
    cursor: pointer;
  }
}

.hide-placeholder {
  &.cdk-drag-placeholder {
    height: 1px !important;
    min-height: 1px !important;
  }
}

.table-paginator {
  height: 2.5rem;
  font-size: 0.75rem;
}

.resize-handle {
  display: inline-block;
  position: absolute;
  top: -1px;
  right: 0;
  height: 105%;
  opacity: 1;
  transform: translateX(16px);
  width: 5px;
}

.mat-column-groupHeader {
  margin: 5px 14px;
  border-radius: 8px;
  min-height: 38px;
  padding: 0 0 0 10px;
  border-bottom-style: none;
}

.expand-btn {
  height: 32px;
  padding: 0 10px 0 16px;
  border: none !important;
  border-radius: 10px;
  display: grid;
  place-items: center;
  margin-left: auto;
  margin-right: 0;
  cursor: pointer;
}

.flex-right {
  justify-content: end;
}

.flex-center {
  justify-content: center;
}

.m-right {
  margin-left: auto;
}

.m-left {
  margin-right: auto;
}

.table-top-area {
  display: flex;
  justify-content: space-between;
  width: 100%;
  border-bottom: 0;
}

.search-filter {
  margin-bottom: -0.8rem;
  margin-top: 0.5rem;
  margin-right: 0.5rem;
}

.content-template {
  height: 100%;
  margin: 0.5rem;
}

.mat-column-drag-drop {
  width: 3rem;
  max-width: 3rem;
}

.mat-column-select {
  width: 4rem;
  max-width: 4rem;
}

.header-padding {
  padding: 1rem;
}

.filter-overlay-btn {
  display: flex;
  justify-content: space-around;
}

.filter-search-pane {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-flow: row wrap;
  padding-left: 0.25rem;
  padding-right: 0.25rem;
  border-bottom: none;
  flex-wrap: nowrap;
  .filter-template-wrapper {
    display: flex;
    justify-content: flex-start;
    .filters {
      display: flex;
      align-items: center;

      app-grid-filter-menu {
        padding-top: 0.16rem;
        padding-inline-start: 0.2rem;
      }
    }
  }

  .input-search {
    display: flex;
    justify-content: flex-end;
    flex: 1;
  }
}

::ng-deep .table-paginator {
  .mat-mdc-paginator-outer-container {
    height: 2.5rem;
    align-items: center;
  }

  .mat-mdc-paginator-container {
    min-height: 2.5rem;
    max-height: 2.5rem;
    height: 2.5rem;
  }

  .mat-mdc-form-field-infix {
    padding-top: 0 !important;
    display: flex;
    height: 2.5rem;
    align-items: center;
  }

  .mat-mdc-paginator-range-actions {
    height: 2.5rem;
  }
}

.empty-record-message {
  padding: 0.7rem;
  font-size: 0.813rem;
}

.display-hide {
  opacity: 0.5;
}

:host ::ng-deep .mat-sort-header-container {
  width: 100% !important;
  height: 95% !important;
}

:host ::ng-deep .mat-sort-header-content {
  width: 100% !important;
}
::ng-deep .mat-pseudo-checkbox-minimal {
  display: none;
}

.cursor-resize {
  cursor: col-resize;
}

.mat-option-padding {
  padding-left: 12px !important;
  padding-right: 12px !important;
}

.hide-text-overflow {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
