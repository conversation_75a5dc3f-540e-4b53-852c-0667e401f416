import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { TxBaseFieldComponent } from '../base-field/base-field.component';

@Component({
  selector: 'tx-input-text-field',
  templateUrl: './input-text-field.component.html',
  styleUrls: ['./input-text-field.component.scss'],
})
export class TxInputTextFieldComponent extends TxBaseFieldComponent implements OnInit {
  @Input() inTextArea = false;
  @Input() maxLength!: number;
  @Input() textAreaHeight!: number;

  @Output() keyPress = new EventEmitter<KeyboardEvent>();
  @ViewChild('input') input: any;

  withMaxLength = false;
  mouseInFormField = false;
  focusState!: boolean;
  appearance = 'legacy';

  constructor() {
    super();
  }

  initValue(): void {
    if (!this.value && !this.control.value) {
      this.value = '';
      this.control.setValue('');
    } else if (!this.value && this.control.value) {
      this.value = this.control.value;
    } else {
      this.control.setValue(this.value);
    }
  }

  onMouseEnter() {
    this.mouseInFormField = true;
  }

  onMouseLeave() {
    this.mouseInFormField = false;
  }

  onFocus(): void {}

  onBlur(): void {}

  checkValue(): void {}

  onFieldChange(param?: any): void {
    super.onFieldChange(param);
    this.value = this.control.value;
  }

  onKeyPress(event: KeyboardEvent) {
    this.keyPress.emit(event);
  }
}
