import {
  Component,
  EventEmitter,
  HostListener,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { delay } from 'rxjs';
import {
  FileDescription,
  FileItem,
  FileItemType,
  FilesManager,
  FilesState,
  FileTree,
} from '../../models/file-models';
import {
  EditService,
  EditSettingsModel,
  RowDataBoundEventArgs,
  RowDragEventArgs,
  RowSelectingEventArgs,
  SaveEventArgs,
} from '@syncfusion/ej2-angular-grids';
import { TranslateService } from '@ngx-translate/core';
import { FileManagerComponent } from '../file-manager.component';
import { FileManagerService } from '../../services/file-manager.service';
import { FilesUtils } from 'src/app/core/utils/files';
import { ResourcesService } from 'src/app/admins/file-manager/services/resources.service';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { animate, style, transition, trigger } from '@angular/animations';
import {
  TxContextMenuEventArgs,
  TxContextMenuItem,
  TxCommonUtils,
  ToastComponent,
  DataBaseRights,
  TxContextMenuComponent,
  TxGridColumn,
  TxGridComponent,
  TxGridDoubleClickArgs,
  TxDragOption,
} from '@bassetti-group/tx-web-core';
import { Router } from '@angular/router';
import { RightPaneService } from 'src/app/core/right-pane/right-pane.service';
import { RightPaneSettings, RightPaneWidth } from 'src/app/core/right-pane/right-pane.models';
import { RightPaneRef } from 'src/app/core/right-pane/right-pane.ref';
import { TreeViewComponent } from '@syncfusion/ej2-angular-navigations';

@Component({
  selector: 'app-file-grid',
  templateUrl: './file-grid.component.html',
  styleUrls: ['./file-grid.component.scss'],
  animations: [
    trigger('insertTrigger', [
      transition(':leave', [style({ opacity: 1 }), animate('300ms', style({ opacity: 0 }))]),
    ]),
    trigger('insertDrop', [transition(':leave', [animate('300ms', style({ opacity: 0 }))])]),
  ],
  providers: [EditService],
})
export class FileGridComponent implements OnInit {
  @Input() isLangLoading = false;
  @Input() isGridLoading = false;
  @Input() isGridUpdatable = false;
  @Input() treeview: TreeViewComponent | undefined;

  @Output() displayDialog = new EventEmitter<{
    message?: string;
    annotation?: string;
    object?: any;
    showAdditionalInput?: boolean;
  }>();
  @Output() indicatorHiddenGrid = new EventEmitter<any>();
  @Output() indicatorShowGrid = new EventEmitter<any>();
  @Output() createFolderFromGrid = new EventEmitter();
  @Output() addNewFolderFromGrid = new EventEmitter();
  @Output() refreshTreeFromGrid = new EventEmitter();
  @Output() handleFileInputFromGrid = new EventEmitter();
  @Output() openHistoryFromGrid = new EventEmitter<{
    alias: string;
    fileName: string;
    path: string;
  }>();
  @Output() renameItemFromGrid = new EventEmitter<{
    oldName: string;
    newName: string;
    folder: FileTree | undefined;
    path: string;
    alias: string;
    rowGrid?: FileDescription;
  }>();
  @Output() deleteFakeNodeFromGrid = new EventEmitter<{ node: FileTree }>();
  @Output() moveFile = new EventEmitter<{
    filesControlled: FilesState;
    sourceParentNode: FileTree;
    destinationNode: FileTree;
    nodes?: FileTree[];
  }>();
  @Output() changeSelectedNodes = new EventEmitter<FileDescription>();
  @Output() showRenamingInformation = new EventEmitter<{
    fileName: string;
    newName: string;
    path: string;
    alias: string;
    node?: FileTree;
    selectedRowGrid?: FileDescription;
  }>();
  @Output() currentFileHistoryChange = new EventEmitter<any>();
  @Output() displayDialogReplaceFile = new EventEmitter<{
    files?: FilesState;
    isMovingItems: boolean;
    isPasteItems: boolean;
    sourceParentNode?: FileTree;
    destinationParentNode?: FileTree;
    data?: FileDescription[];
    nodeMoved?: FileDescription;
  }>();

  @ViewChild('filesGrid') public filesGrid: TxGridComponent<FileDescription> | undefined;
  @ViewChild('fileEditor') public fileEditor!: TemplateRef<HTMLInputElement>;
  @ViewChild(TxContextMenuComponent) public txContextMenu!: TxContextMenuComponent;

  public isDropzoneHovered = false;
  public editSettings: EditSettingsModel;
  public nbrFilesTreated = 0;
  public nbrFilesUpload = 0;
  public supportedFileFormat: string[] = [
    '.csv',
    '.doc',
    '.docx',
    '.docm',
    '.dotx',
    '.dotm',
    '.gif',
    '.jpeg',
    '.jpg',
    '.json',
    '.pdf',
    '.png',
    '.ppt',
    '.pptx',
    '.pptm',
    '.potx',
    '.potm',
    '.ppam',
    '.ppsx',
    '.ppsm',
    '.sldx',
    '.sldm',
    '.thmx',
    '.tsv',
    '.txt',
    '.xls',
    '.xlsx',
    '.xlsm',
    '.xml',
    '.xltx',
    '.xltm',
    '.xlsb',
    '.xlam',
    '.bmp',
    '.raw',
    '.skc',
    '.tif',
    '.tiff',
    '.svg',
    '.wmf',
    'xlt',
  ];
  public editorSupportedFileFormats: string[] = ['.xml'];
  public menuFiles: TxContextMenuItem[] = [];
  public filesCopied: FileItem[] = [];
  public disableMenuItems: string[] = [];
  public selectedFile: FileDescription | undefined;
  public fileEditorPaneRef: RightPaneRef | undefined;
  public isEditorOpened = false;
  public isCurrentFileEditable = false;
  public dragOption: TxDragOption = {
    blockTransformation: true,
    hidePlaceholder: true,
    multiple: true,
  };
  public txGridColumns: TxGridColumn<FileDescription>[] = [
    { field: 'name', headerText: _('admins.columns.name'), editable: true },
    { field: 'extension', headerText: _('admins.resources.extension'), width: '120px' },
    {
      field: 'lastWriteTime',
      headerText: _('admins.resources.dateLastModification'),
      width: '180px',
    },
    { field: 'length', headerText: _('admins.columns.size'), width: '120px' },
    { field: 'isTemp', headerText: '', checkMuted: true, visible: false },
  ];
  private _filesInFolder: FileDescription[] = [];

  constructor(
    private readonly router: Router,
    private readonly fileManagerService: FileManagerService,
    private readonly fileManagerComponent: FileManagerComponent,
    private readonly translate: TranslateService,
    private readonly resourcesService: ResourcesService,
    private readonly rightPaneService: RightPaneService
  ) {
    this.editSettings = {
      allowEditing: true,
      allowAdding: true,
      allowDeleting: true,
      allowEditOnDblClick: false,
    };
  }

  get filesInFolder() {
    return this._filesInFolder;
  }
  @Input() set filesInFolder(filesInFolder: FileDescription[]) {
    this._filesInFolder = filesInFolder;
    if (this.filesGrid) {
      this.filesGrid.dataSource.selection.clear();
    }
  }
  @HostListener('document:keydown.F2', ['$event'])
  onF2Key(event: KeyboardEvent) {
    event.preventDefault(); // Prevents the default behavior of the F2 key
    if (!this.isEditorOpened && this.filesGrid?.getSelectedRows().length) {
      this.filesGrid?.startEdit();
      this.onCellEditing(this.filesGrid?.editingElement);
    }
  }

  @HostListener('window:keydown', ['$event'])
  onKeyPress($event: KeyboardEvent) {
    if (!this.isEditorOpened) {
      if (($event.ctrlKey || $event.metaKey) && $event.key === 'c') {
        this.copyFiles();
      } else if (($event.ctrlKey || $event.metaKey) && $event.key === 'v') {
        this.pasteFiles();
      }
    }
  }

  ngOnInit(): void {
    this.menuFiles = this.getMenuItemsFile();
    this.translate.onLangChange.subscribe(() => {
      this.menuFiles = this.getMenuItemsFile();
    });
  }

  getMenuItemsFile(): TxContextMenuItem[] {
    return [
      { id: 'copy', label: this.translate.instant(_('admins.resources.copy')) },
      { id: 'paste', label: this.translate.instant(_('admins.resources.paste')) },
      { id: 'edit', label: this.translate.instant(_('admins.resources.openFileInEditor')) },
    ];
  }

  hideIndicator(event: any) {
    this.indicatorHiddenGrid.emit(event);
  }
  showIndicator(event: any) {
    this.indicatorShowGrid.emit(event);
  }
  isFolderGrid(type: string): boolean {
    return this.fileManagerService.isFolder(type);
  }
  formatFileSizeInGrid(size: number): number {
    return this.fileManagerService.formatFileSize(size);
  }

  renameWithNameInGrid(data: { name: string; rowGrid: FileDescription }): void {
    data.rowGrid.name = data.name;
  }

  public containsFiles(event: any) {
    if (event.dataTransfer.types) {
      if (event.dataTransfer.types.some((type: string) => type === 'Files')) {
        this.isDropzoneHovered = true;
      } else {
        this.isDropzoneHovered = false;
      }
    }
  }
  public setFileAfterSuccessMovingGrid(
    destinationFolder: FileTree,
    file: FileDescription,
    parentNodeMoved: FileTree
  ) {
    if (file.newName) {
      file.name = file.newName;
    }

    if (
      this.fileManagerService.selectedNode &&
      this.fileManagerService.selectedNode === destinationFolder
    ) {
      let index: number;
      if (file.type === FileItemType.directory) {
        index = this.filesInFolder.findIndex((f) => f.name === file.name);
        if (index !== -1) {
          this.filesInFolder.splice(index, 1);
        } else {
          index = 0;
        }
      } else {
        index = this.fileManagerService.getIndexFirstFileInGrid(this.filesInFolder);
      }
      this.filesInFolder.splice(index, 0, file);
    } else if (
      this.fileManagerService.selectedNode &&
      this.fileManagerService.selectedNode === parentNodeMoved
    ) {
      const index = this.filesInFolder.findIndex((f) => f.name === file.name);
      if (index !== -1) {
        this.filesInFolder.splice(index, 1);
      }
    }
  }
  public getSupportedFormat(): string {
    return this.supportedFileFormat.join(', ');
  }

  rowBound(args: RowDataBoundEventArgs): void {
    if ((args.data as FileDescription).isTemp) {
      args?.row?.classList.add('fm-row-isTemp');
    }
  }
  createFolder() {
    this.createFolderFromGrid.emit(this.filesInFolder);
  }
  fileDropGrid(event: any) {
    const files = event.dataTransfer.files;
    this.handleFileInputFromGrid.emit({ files });
  }

  onCellDoubleClick(event: TxGridDoubleClickArgs<FileDescription>) {
    const targetCell: FileDescription = event.rowData;

    if (targetCell.type === FileItemType.directory) {
      // To delete when the bug of the API has been fixed :
      // (Bug : we can only expand the nodes of the tree if the first one is expanded (= if the first has children))
      if (
        this.fileManagerService.selectedNode &&
        this.fileManagerService.selectedNode.id === this.fileManagerService.treeData[0].id
      ) {
        this.deleteFakeNodeFromGrid.emit({ node: this.fileManagerService.selectedNode });
      }

      if (this.fileManagerService.selectedNode?.expanded === false) {
        this.fileManagerComponent
          .getFilesInFolder(this.fileManagerService.selectedNode)
          .pipe(delay(200))
          .subscribe((data) => {
            this.changeSelectedNodes.emit(targetCell);
          });
      } else {
        this.changeSelectedNodes.emit(targetCell);
      }
    }

    if (this.canOpenFileInEditor(targetCell)) {
      this.openFileInEditor(targetCell);
    }
  }
  onActionComplete(args: SaveEventArgs): void {
    if (args.requestType === 'save' && args.action === 'edit') {
      this.onCellEdited(args);
    } else if (args.requestType === 'refresh') {
      this.onRefreshingGrid();
    }
  }
  onCellEditing(args: any) {
    const parentNode = this.fileManagerService.selectedNode;
    const file = args as FileDescription;
    if (parentNode) {
      if (
        parentNode.resource.right !== DataBaseRights.DbrStructure &&
        parentNode.resource.right !== DataBaseRights.DbrWrite
      ) {
        this.filesGrid?.disableEditing();
        const message =
          file.type === 'File'
            ? this.translate.instant(_('admins.resources.noRightsToRenameFile'), {
                name: file.name,
              })
            : this.translate.instant(_('admins.resources.noRightsToRenameFolder'), {
                name: file.name,
              });
        this.fileManagerService.createNotification('information', message, false, 8000);
      }
    }
    if (file.type === 'File') {
      const fileNameWithoutExtension = file.name.split('.').slice(0, -1).join('.');
      if (this.filesGrid?.editingElement)
        this.filesGrid.editingElement.name = fileNameWithoutExtension;
    }
  }

  public isFileNameExistInGrid(
    name: string,
    oldName: string,
    parentNode: FileTree | undefined,
    rowGrid: FileDescription | undefined
  ) {
    // assuming parentNode exist because root elements cannot be renamed
    const rowGridToChange = this.filesInFolder.find((f) => f.name === oldName);
    let folderToChange: FileTree | undefined;
    if (!parentNode?.children) {
      folderToChange = this.fileManagerService.treeData.find((f) => f.name === oldName);
    } else {
      folderToChange = parentNode.children.find((f) => f.name === oldName);
    }
    const alias = this.fileManagerService.getAlias(this.fileManagerService?.selectedNode?.id);
    let path = this.fileManagerService.selectedNode
      ? this.fileManagerService.findPath(this.fileManagerService.selectedNode)
      : '';

    // if the item to edit is a folder
    if (folderToChange) {
      path = this.fileManagerService.removeLastElementPathIfFolder(
        this.fileManagerService.findPath(folderToChange),
        folderToChange.isFolder
      );
    }
    if (!rowGridToChange) {
      // row was renamed, moved or deleted
      this.fileManagerService.createNotification(
        'information',
        this.translate.instant(_('admins.resources.fileNotExistOrRenamed'), { name: oldName }),
        false,
        8000
      );
      return;
    }
    if (this.filesInFolder.find((f) => f.name.toLowerCase() === name.toLowerCase())) {
      name = FilesManager.createUniqueFileNameGrid(name, this.filesInFolder, 2);
      this.showRenamingInformation.emit({
        fileName: oldName,
        newName: name,
        path,
        alias,
        node: folderToChange,
        selectedRowGrid: rowGrid,
      });
    } else {
      this.renameItemFromGrid.emit({
        oldName,
        newName: name,
        folder: folderToChange,
        path,
        rowGrid,
        alias,
      });
    }
  }

  onCellEdited(args: SaveEventArgs): void {
    const fileNewName = args.data as FileDescription;
    const newName = this.fileManagerService.getNameWithExtension(
      fileNewName.name,
      fileNewName.extension
    );
    const oldName = (args.previousData as FileDescription).name;
    const parentNode = this.fileManagerService.selectedNode;
    const rowGrid = this.filesInFolder.find((f) => f.name === oldName);

    if (newName.trim() === '') {
      args.cancel = true;
      if (rowGrid?.type === FileItemType.directory) {
        this.fileManagerService.createNotification(
          'information',
          _('admins.resources.folderNameNotEmpty'),
          false,
          5000
        );
      } else {
        this.fileManagerService.createNotification(
          'information',
          _('admins.resources.fileNameNotEmpty'),
          false,
          5000
        );
      }
    } else if (oldName !== newName) {
      this.isFileNameExistInGrid(newName, oldName, parentNode, rowGrid);
    }
  }

  onRefreshingGrid(): void {
    if (this.fileManagerService.selectedNode) {
      if (this.fileManagerService.filesUploading[this.fileManagerService.selectedNode.id]) {
        this.fileManagerService.filesUploading[this.fileManagerService.selectedNode.id].forEach(
          (fileUploading) => {
            if (!this.filesInFolder.includes(fileUploading)) {
              this.createTemporaryRowGrid(
                fileUploading,
                this.fileManagerService.getIndexFirstFileInGrid(this.filesInFolder)
              );
            }
          }
        );
      }
    }
    document.body.style.cursor = 'unset';
  }

  onCellDragStart(event: FileDescription | FileDescription[]): void {
    if (event) {
      const parentNode = this.fileManagerService.selectedNode;
      if (parentNode?.resource.right !== DataBaseRights.DbrStructure) {
        if (this.filesGrid) this.filesGrid.dragDisabled = true;
      }
    }
  }
  onRowSelecting(event: RowSelectingEventArgs): void {
    let targetCell;

    if (Array.isArray(event.data)) {
      targetCell = event.data as FileDescription[];
      targetCell.forEach((file) => {
        if (file.isTemp) {
          event.cancel = true;
        }
      });
    } else {
      targetCell = event.data as FileDescription;
      if (targetCell.isTemp) {
        event.cancel = true;
      }
    }
  }

  onCellDrag = (event: RowDragEventArgs): void => {
    this.hideIndicator(event);
    const targetFileTree = event.target?.closest('#fileTree');
    const targetFilesGrid = event.target?.closest('#filesGrid');

    const parentNode = this.fileManagerService.selectedNode;
    const handleDragToTree = (): void => {
      if (event?.target?.querySelector('.tree-id')) {
        const destinationNodeID = event?.target?.querySelector('.tree-id')?.textContent;
        const destinationNode = TxCommonUtils.isNotNil(destinationNodeID)
          ? this.fileManagerService.findNode(destinationNodeID, this.fileManagerService.treeData)
          : undefined;
        if (destinationNode?.resource.right !== DataBaseRights.DbrStructure) {
          event.cancel = true;
        }
      }

      const nodeElement = event?.target?.closest('.e-list-item');
      if (nodeElement) {
        nodeElement?.querySelector('.e-fullrow')?.classList.add('border-accent-dashed');
      }
    };

    const handleDragToGrid = (): void => {
      if (parentNode?.resource.right !== DataBaseRights.DbrStructure) {
        event.cancel = true;
      }

      const cellElement = event?.target?.closest('.e-row');
      if (cellElement) {
        cellElement.classList.add('border-accent-dashed');
      }
    };

    if (targetFileTree) {
      handleDragToTree();
    } else if (targetFilesGrid) {
      handleDragToGrid();
    }
  };

  onCellDragStop = (event: RowDragEventArgs): void => {
    this.hideIndicator(event);
    // ---------------------------------
    // From grid to tree :
    // ---------------------------------
    const fileTree = event.target?.closest('#fileTree');
    const treeId = fileTree?.querySelector('.e-text-content .tree-id');
    if (fileTree && treeId) {
      this.onCellDragStopGridToTree({
        ...event,
        data: this.filesGrid?.getSelectedRows().length
          ? this.filesGrid.getSelectedRows()
          : event.data,
      });
    }

    // ---------------------------------
    // From grid to grid :
    // ---------------------------------
    const filesGridTarget = event.target?.closest('#filesGrid');
    const table = filesGridTarget?.querySelector('.mat-mdc-table');
    if (filesGridTarget && table) {
      this.onCellDragStopGridToGrid(event);
    } else {
      event.cancel = true;
    }
  };

  moveFilesInGrid(
    sourceParentNode: FileTree,
    destinationNode: FileTree,
    filesMoved: FileDescription[]
  ): void {
    this.resourcesService
      .onExpandingFolder(
        this.fileManagerService.getAlias(destinationNode.id),
        this.fileManagerService.findPath(destinationNode)
      )
      .subscribe((data) => {
        const filesControlled = this.controlFilesDescToDrag(filesMoved, destinationNode, data);
        const nodesMoved: FileTree[] = filesMoved.flatMap((file) => {
          const node = sourceParentNode?.children?.find((child) => child.name === file.name);
          if (!node) {
            return [];
          }
          return [node];
        });
        this.moveFile.emit({
          filesControlled,
          sourceParentNode,
          destinationNode,
          nodes: nodesMoved,
        });
      });
  }

  public handleFileInputGrid(event: any): void {
    const filesToUpload: FileList = event.dataTransfer
      ? event.dataTransfer.files
      : event.target.files;
    this.handleFileInputFromGrid.emit(filesToUpload);
  }

  controlFilesDescToDrag(
    files: FileDescription[],
    selectedNode: FileTree,
    filesInFolder: FileDescription[]
  ): FilesState {
    const result = {
      goodFiles: [] as FileDescription[],
      sameFiles: [] as FileDescription[],
      existingFiles: [] as FileDescription[],
    };

    // iterate over each files selected by user to upload
    Array.from(files).forEach((file) => {
      // if addition and modification is not allowed
      if (selectedNode.resource.right !== DataBaseRights.DbrStructure) {
        this.fileManagerService.createNotification(
          'error',
          this.translate.instant(_('admins.resources.fileAddAndModifNotAllow')),
          false,
          8000
        );
        return;
      }
      const existingRowGrid: FileDescription | undefined = filesInFolder.find(
        (child) => child.name === file.name
      );
      if (existingRowGrid) {
        result.existingFiles.push(existingRowGrid);
        result.sameFiles.push(file);
      } else {
        result.goodFiles.push(file);
      }
    });
    return result;
  }

  doAddFilesInGrid(args: {
    filesToAdd: FileDescription[];
    filesToReplace: FileDescription[];
    node: FileTree;
  }) {
    const nbTotalFiles = args.filesToAdd.length + args.filesToReplace.length;
    const indexFirstFileInGrid = this.fileManagerService.getIndexFirstFileInGrid(
      this.filesInFolder
    );

    if (nbTotalFiles > 0) {
      const selection: FileTree = { ...args.node };
      const toast = this.fileManagerService.createNotification(
        'loading',
        this.translate.instant(_('admins.resources.uploadingFiles'), {
          number: this.nbrFilesUpload,
          total: nbTotalFiles,
        }),
        false,
        0,
        nbTotalFiles > 1 ? 0 : undefined
      );
      // upload new files
      args.filesToAdd.forEach((file) => {
        this.fileManagerService.addInListFilesUploading(file, selection.id);
        this.createTemporaryRowGrid(file, indexFirstFileInGrid);
        const fileToUpload = file.file;
        if (fileToUpload) {
          this.resourcesService
            .onUploadingFile(
              this.fileManagerService.getAlias(selection.id),
              fileToUpload,
              this.fileManagerService.findPath(selection)
            )
            .subscribe(
              (data) => {
                this.nbrFilesUpload++;
                this.actionsForAddFilesInFolder({
                  nbFilesUpload: this.nbrFilesUpload,
                  nbFilesTreated: this.nbrFilesTreated,
                  nbTotalFiles,
                  toast,
                  file,
                  selection,
                  uploaded: true,
                  node: selection,
                });
              },
              (error) => {
                this.actionsAfterErrorInAddFiles({
                  nbFilesUpload: this.nbrFilesUpload,
                  nbFilesTreated: this.nbrFilesTreated,
                  nbTotalFiles,
                  toast,
                  file,
                  selection,
                  uploaded: false,
                  node: selection,
                });
              }
            );
        }
      });
      // replace files
      args.filesToReplace.forEach((file) => {
        this.fileManagerService.addInListFilesUploading(file, selection.id);
        this.createTemporaryRowGrid(file, indexFirstFileInGrid);
        const fileToReplace = file.file;
        if (fileToReplace) {
          this.resourcesService
            .onReplacingFile(
              this.fileManagerService.getAlias(selection.id),
              fileToReplace,
              this.fileManagerService.findPath(selection)
            )
            .subscribe(
              (data) => {
                this.nbrFilesUpload++;
                this.actionsForAddFilesInFolder({
                  nbFilesUpload: this.nbrFilesUpload,
                  nbFilesTreated: this.nbrFilesTreated,
                  nbTotalFiles,
                  toast,
                  file,
                  selection,
                  uploaded: true,
                  node: selection,
                });
              },
              (error) => {
                this.actionsAfterErrorInAddFiles({
                  nbFilesUpload: this.nbrFilesUpload,
                  nbFilesTreated: this.nbrFilesTreated,
                  nbTotalFiles,
                  toast,
                  file,
                  selection,
                  uploaded: false,
                  node: selection,
                });
              }
            );
        }
      });
    }
    this.nbrFilesTreated = 0;
    this.nbrFilesUpload = 0;
  }
  actionsForAddFilesInFolder(args: {
    nbFilesUpload: number;
    nbFilesTreated: number;
    nbTotalFiles: number;
    toast: ToastComponent;
    file: FileDescription;
    node: FileTree;
    selection: FileTree;
    uploaded: boolean;
  }) {
    this.nbrFilesTreated++;
    this.fileManagerService.afterUploadFile(
      args.nbFilesUpload,
      this.nbrFilesTreated,
      args.nbTotalFiles,
      args.toast,
      args.file,
      args.selection
    );
    this.replaceTemporaryRowGrid(args.file, args.selection, args.uploaded);
    this.fileManagerService.removeFromListFilesUploading(args.file, args.selection.id);
  }
  actionsAfterErrorInAddFiles(args: {
    nbFilesUpload: number;
    nbFilesTreated: number;
    nbTotalFiles: number;
    toast: ToastComponent;
    file: FileDescription;
    node: FileTree;
    selection: FileTree;
    uploaded: boolean;
  }) {
    this.nbrFilesTreated++;
    this.fileManagerService.afterErrorUploadFile(
      args.nbFilesUpload,
      this.nbrFilesTreated,
      args.nbTotalFiles,
      args.toast,
      args.file,
      args.selection,
      args.uploaded
    );
    this.replaceTemporaryRowGrid(args.file, args.selection, args.uploaded);
    this.fileManagerService.removeFromListFilesUploading(args.file, args.selection.id);
  }

  createTemporaryRowGrid(file: FileDescription, indexFirstFileInGrid: number, isTemp = true): void {
    file.isTemp = isTemp;
    let index = this.filesInFolder.findIndex((f) => f.name === file.name);
    if (index !== -1) {
      this.filesInFolder.splice(index, 1);
    } else if (file.type === FileItemType.file) {
      index = indexFirstFileInGrid;
    }
    this.filesInFolder.splice(index === -1 ? 0 : index, 0, file);
    this.filesInFolder = [...this.filesInFolder];
  }

  replaceTemporaryRowGrid(file: FileDescription, node: FileTree, uploaded: boolean) {
    if (
      this.fileManagerService.selectedNode &&
      node.id === this.fileManagerService.selectedNode.id
    ) {
      if (uploaded) {
        this.filesInFolder.forEach((child) => {
          if (child.name === file.name) {
            child.isTemp = false;
          }
        });
      } else {
        const index = this.filesInFolder.findIndex((f) => f.name === file.name);
        if (index !== -1) {
          this.filesInFolder.splice(index, 1);
        }
      }
    }
  }

  replaceTemporaryNode(file: File, selectedNode: FileTree, uploaded: boolean): void {
    const orignalNode = this.fileManagerService.findNode(
      selectedNode.id,
      this.fileManagerService.treeData
    );
    if (orignalNode?.isFolder) {
      if (uploaded) {
        orignalNode?.children?.forEach((child) => {
          if (child.name === file.name) {
            child.isTemp = false;
          }
        });
      } else {
        orignalNode.children = orignalNode?.children?.filter((child) => child.name !== file.name);
      }
    }
    this.refreshTreeFromGrid.emit();
  }
  addFileInGrid() {
    this.addNewFolderFromGrid.emit();
  }
  openHistoryInGrid(): void {
    this.selectedFile = this.filesGrid?.getSelectedRows()[0] as FileDescription;
    const currentFileHistory = {
      name: this.selectedFile?.name,
      type: this.selectedFile?.type,
    };
    this.currentFileHistoryChange.emit(currentFileHistory);
    if (this.selectedFile === undefined) {
      throw new Error('selectedFile is undefined');
    }
    this.openHistoryFromGrid.emit({
      alias: this.fileManagerService.getAlias(this.fileManagerService?.selectedNode?.id),
      fileName: this.selectedFile.name,
      path: this.fileManagerService.selectedNode
        ? this.fileManagerService.findPath(this.fileManagerService.selectedNode)
        : '',
    });
  }

  canOpenHistoryInGrid(): boolean {
    if (
      !this.fileManagerService.selectedNode ||
      !this.filesGrid ||
      this.filesGrid.getSelectedRows().length !== 1
    ) {
      return false;
    }
    return true;
  }

  canRenameFileInGrid(): boolean {
    if (
      !this.fileManagerService.selectedNode ||
      !this.filesGrid ||
      this.filesGrid.getSelectedRows().length !== 1
    ) {
      return false;
    }
    return this.fileManagerService.selectedNode.resource.right === DataBaseRights.DbrStructure;
  }

  canDeleteFileInGrid(): boolean {
    if (
      !this.fileManagerService.selectedNode ||
      !this.filesGrid ||
      this.filesGrid.getSelectedRows().length === 0
    ) {
      return false;
    }
    return this.fileManagerService.selectedNode.resource.right === DataBaseRights.DbrStructure;
  }

  canCreateFolderInGrid(): boolean {
    if (!this.fileManagerService.selectedNode || !this.filesGrid) {
      return false;
    }
    return this.fileManagerService.selectedNode.resource.right === DataBaseRights.DbrStructure;
  }

  canDownloadFileInGrid(): boolean {
    let nbrFiles = 0;
    let nbrFolder = 0;
    if (!this.filesGrid || this.filesGrid?.getSelectedRows()?.length === 0) {
      return false;
    }
    if (
      this.fileManagerService.selectedNode &&
      this.fileManagerService.selectedNode.resource.right !== DataBaseRights.DbrRead &&
      this.fileManagerService.selectedNode.resource.right !== DataBaseRights.DbrStructure &&
      this.fileManagerService.selectedNode.resource.right !== DataBaseRights.DbrWrite
    ) {
      return false;
    }

    (this.filesGrid.getSelectedRows() as FileDescription[]).forEach((file) => {
      if (file.type === FileItemType.directory) {
        nbrFolder++;
      } else if (file.type === FileItemType.file) {
        nbrFiles++;
      }
    });
    if (nbrFolder > 1) {
      return false;
    }
    if (nbrFolder === 0 && nbrFiles >= 1) {
      return true;
    }
    if (nbrFolder + nbrFiles > 1) {
      return false;
    }
    return true;
  }

  // to review
  canCopyFileInGrid(): boolean {
    if (!this.filesGrid || this.filesGrid.getSelectedRows().length < 1) {
      return false;
    }
    if (
      this.fileManagerService.selectedNode &&
      this.fileManagerService.selectedNode.resource.right !== DataBaseRights.DbrStructure
    ) {
      return false;
    }
    return !this.filesGrid
      .getSelectedRows()
      .some((item) => (item as FileDescription).type === FileItemType.directory);
  }

  canPasteFileInGrid(): boolean {
    if (this.fileManagerService.selectedNode?.resource.right !== DataBaseRights.DbrStructure) {
      return false;
    }
    if (this.filesCopied.length > 0) {
      return true;
    }
    return false;
  }

  getOpenHistoryTooltip(): string {
    if (
      !this.fileManagerService.selectedNode ||
      !this.filesGrid ||
      this.filesGrid.getSelectedRows().length === 0
    ) {
      return _('admins.resources.noFileSelected');
    } else {
      return '';
    }
  }

  getOpenEditorTooltip(): string {
    if (
      !this.fileManagerService.selectedNode ||
      !this.filesGrid ||
      this.filesGrid.getSelectedRows().length !== 1
    ) {
      return _('admins.resources.noFileSelected');
    }
    if (
      !this.editorSupportedFileFormats.includes(
        (this.filesGrid.getSelectedRows() as FileDescription[])[0].extension
      )
    ) {
      return _('admins.resources.fileNotCompatibleWithEditor');
    }
    return '';
  }

  // file parameter here is used for the double click event because the event does not always select a row
  canOpenFileInEditor(file?: FileDescription) {
    return (
      this.fileManagerService.selectedNode &&
      this.filesGrid &&
      (file
        ? file.type === FileItemType.file &&
          this.editorSupportedFileFormats.includes(file.extension)
        : this.filesGrid.getSelectedRows().length === 1 &&
          this.editorSupportedFileFormats.includes(
            (this.filesGrid.getSelectedRows() as FileDescription[])[0].extension
          ))
    );
  }

  // file parameter here is used for the double click event
  openFileInEditor(file?: FileDescription) {
    this.selectedFile = file || (this.filesGrid?.getSelectedRows()[0] as FileDescription);
    const rpSettings: RightPaneSettings = {
      width: RightPaneWidth.Large,
      data: this.selectedFile,
      template: this.fileEditor,
    };
    if (this.selectedFile === undefined) {
      throw new Error('selectedFile is undefined');
    }
    this.isCurrentFileEditable =
      this.fileManagerService.selectedNode?.resource.right !== DataBaseRights.DbrStructure;
    this.fileEditorPaneRef = this.rightPaneService.showNewPaneWithTemplate(rpSettings);
    this.fileEditorPaneRef.afterClosed.subscribe(() => {
      this.isEditorOpened = false;
    });
    this.isEditorOpened = true;
  }

  rename() {
    this.selectedFile = this.filesGrid?.getSelectedRows()[0] as FileDescription;
    if (this.selectedFile === undefined) {
      throw new Error('firstRecord is undefined');
    }
    this.filesGrid?.startEdit();
    this.onCellEditing(this.filesGrid?.editingElement);
  }
  getNameWithoutExtensionInGrid(file: FileDescription): string {
    return this.fileManagerService.getNameWithoutExtension(file);
  }
  canAddFile() {
    return this.fileManagerService.canAddFileInTreeOrGrid();
  }

  getAddTooltip(): string {
    if (!this.fileManagerService.selectedNode || !this.filesGrid) {
      return _('admins.resources.noFolderSelected');
    }
    if (this.fileManagerService.selectedNode.resource.right !== DataBaseRights.DbrStructure) {
      return _('admins.resources.fileAddAndModifNotAllow');
    } else {
      return '';
    }
  }

  getCreateFolderTooltip(): string {
    if (!this.fileManagerService.selectedNode || !this.filesGrid) {
      return _('admins.resources.noFolderSelected');
    }
    return this.fileManagerService.selectedNode.resource.right === DataBaseRights.DbrStructure
      ? ''
      : _('admins.resources.fileAddAndModifNotAllow');
  }

  getDeleteTooltip(): string {
    if (
      this.fileManagerService.selectedNode &&
      this.fileManagerService.selectedNode.resource.right !== DataBaseRights.DbrStructure
    ) {
      return _('admins.resources.fileAddAndModifNotAllow');
    }
    if (
      !this.fileManagerService.selectedNode ||
      !this.filesGrid ||
      this.filesGrid.getSelectedRows().length === 0
    ) {
      return _('admins.resources.noFileSelected');
    } else {
      return '';
    }
  }

  getDownloadTooltip(): string {
    let nbrFolders = 0;
    let nbrFiles = 0;
    if (
      !this.fileManagerService.selectedNode ||
      !this.filesGrid ||
      this.filesGrid.getSelectedRows().length === 0
    ) {
      return _('admins.resources.noItemSelected');
    }

    if (
      this.fileManagerService.selectedNode &&
      this.fileManagerService.selectedNode.resource.right !== DataBaseRights.DbrRead &&
      this.fileManagerService.selectedNode.resource.right !== DataBaseRights.DbrStructure &&
      this.fileManagerService.selectedNode.resource.right !== DataBaseRights.DbrWrite
    ) {
      return _('admins.resources.fileDownloadNotAllow');
    }

    (this.filesGrid.getSelectedRows() as FileDescription[]).forEach((file) => {
      if (file.type === FileItemType.directory) {
        nbrFolders++;
      } else {
        nbrFiles++;
      }
    });
    if (nbrFolders >= 1 && nbrFiles >= 1) {
      return _('admins.resources.foldersFilesSelected');
    }
    if (nbrFolders >= 2) {
      return _('admins.resources.foldersSelected');
    }
    return '';
  }

  getRenameTooltip(): string {
    if (
      !this.fileManagerService.selectedNode ||
      !this.filesGrid ||
      this.filesGrid.getSelectedRows().length !== 1
    ) {
      return _('admins.resources.noFileSelected');
    }
    if (this.fileManagerService.selectedNode.resource.right === DataBaseRights.DbrStructure) {
      return '';
    } else {
      return _('admins.resources.fileAddAndModifNotAllow');
    }
  }

  download() {
    const selection = this.filesGrid?.getSelectedRows();
    const filesToDownload: FileItem[] = [];
    let folderToDownload: FileItem = { alias: '', itemName: '', path: '' };

    (selection as FileDescription[])?.forEach((item) => {
      if (item.type === FileItemType.directory) {
        folderToDownload = this.fileManagerService.createFileItem(item);
      } else if (item.type === FileItemType.file) {
        filesToDownload.push(this.fileManagerService.createFileItem(item));
      }
    });

    if (folderToDownload.alias !== '') {
      this.downloadDirectory(folderToDownload);
    }
    if (filesToDownload.length >= 1) {
      this.downloadFiles(filesToDownload);
    }
  }

  downloadDirectory(folderToDownload: FileItem) {
    const toast = this.fileManagerService.createNotification(
      'loading',
      this.translate.instant(_('admins.resources.downloadingFolder'), {
        name: folderToDownload.itemName,
      }),
      false,
      0
    );
    this.resourcesService.onDownloadingFolder(folderToDownload).subscribe({
      next: (blob) => {
        FilesUtils.downloadBlobFile(blob, folderToDownload.itemName, FileItemType.directory);
        this.fileManagerService.afterDownloadFolder(folderToDownload.itemName, toast);
      },
      error: () => {
        this.fileManagerService.afterErrorDownloadFile(toast);
      },
    });
  }

  downloadFiles(filesToDownload: FileItem[]) {
    const itemNameFile =
      filesToDownload.length === 1
        ? filesToDownload[0].itemName
        : filesToDownload[0].itemName.split('.')[0];
    const itemType = filesToDownload.length === 1 ? FileItemType.file : FileItemType.directory;
    const toast = this.fileManagerService.createNotification(
      'loading',
      this.translate.instant(_('admins.resources.downloadingFiles'), {
        number: filesToDownload.length,
      }),
      false,
      0
    );
    this.resourcesService.onDownloadingFiles(filesToDownload).subscribe({
      next: (blob) => {
        FilesUtils.downloadBlobFile(blob, itemNameFile, itemType);
        this.fileManagerService.afterDownloadFile(filesToDownload.length, toast);
      },
      error: () => {
        this.fileManagerService.afterErrorDownloadFile(toast);
      },
    });
  }

  // to review
  copyFiles() {
    this.filesCopied = [];
    const selectedFiles = this.filesGrid?.getSelectedRows();
    const alias = this.fileManagerService.getAlias(this.fileManagerService?.selectedNode?.id);
    const path = this.fileManagerService.selectedNode
      ? this.fileManagerService.findPath(this.fileManagerService.selectedNode)
      : '';
    if (
      !(selectedFiles as FileDescription[])?.find((item) => item.type === FileItemType.directory)
    ) {
      (selectedFiles as FileDescription[])?.forEach((file) => {
        this.filesCopied.push({
          alias,
          itemName: file.name,
          path,
          size: file.length,
        } as FileItem);
      });
      if (this.filesCopied.length > 0) {
        this.fileManagerService.createNotification(
          'information',
          this.translate.instant(_('admins.resources.copyFiles'), {
            numberFiles: this.filesCopied.length,
          }),
          false,
          4000
        );
      }
    }
  }

  pasteFiles() {
    const selection = this.filesGrid?.getSelectedRows() as FileDescription[];
    const foldersSelection = selection?.filter((elm) => elm.type === FileItemType.directory);

    if (foldersSelection?.length) {
      // At least one folder selected
      foldersSelection.forEach((folder) => {
        this.pasteFilesInFolder(folder);
      });
    } else {
      // no files selected or file(s) but NO folders
      const filesAdded: FileItem[] = [];
      const folderPath = this.fileManagerService.selectedNode
        ? this.fileManagerService.findPath(this.fileManagerService.selectedNode)
        : undefined;
      this.filesCopied.forEach((file) => {
        filesAdded.push(
          this.createObjet(
            file,
            folderPath,
            this.filesInFolder.some((copyItem) => copyItem.name === file.itemName)
          )
        );
      });
      if (filesAdded.length > 0) {
        this.copingFilesAddInGrid(filesAdded, true);
      }
    }
  }

  deleteFileInGrid(): void {
    const parentNode = this.fileManagerService.selectedNode;
    const alias = this.fileManagerService.getAlias(parentNode?.id);
    const path = parentNode ? this.fileManagerService.findPath(parentNode) : '';
    const selectedFiles = this.filesGrid?.getSelectedRows() as FileDescription[];

    let message = '';
    if (selectedFiles?.length === 1) {
      if (selectedFiles[0].type === FileItemType.directory) {
        message = this.translate.instant(_('admins.resources.folderDelete'), {
          name: selectedFiles[0].name,
        });
      } else {
        message = this.translate.instant(_('admins.resources.fileDelete'), {
          name: selectedFiles[0].name,
        });
      }
    } else {
      message = this.translate.instant(_('admins.resources.itemsDelete'), {
        number: selectedFiles?.length,
      });
    }
    this.displayDialog.emit({
      message,
      annotation: undefined,
      object: {
        files: selectedFiles,
        parentNode,
        alias,
        path,
        isDeletingFromTree: false,
      },
    });
  }

  onCellDragStopGridToTree(event: any): void {
    const destinationNodeID = event.target
      .closest('.e-text-content')
      .querySelector('.tree-id').textContent;
    const destinationNode = this.fileManagerService.findNode(
      destinationNodeID,
      this.fileManagerService.treeData
    );

    // check if the user have sufficient rights :
    if (
      destinationNode?.resource?.right === DataBaseRights.DbrStructure ||
      this.fileManagerService?.selectedNode?.resource.right === DataBaseRights.DbrStructure
    ) {
      const filesMoved: FileDescription[] = [];

      (event.data as FileDescription[]).forEach((file) => {
        filesMoved.push(file);
      });

      const aliasNodeMoved = this.fileManagerService.getAlias(
        this.fileManagerService?.selectedNode?.id
      );
      const destinationFolder = destinationNode
        ? this.fileManagerService.findNode(destinationNode.id, this.fileManagerService.treeData)
        : undefined;
      const aliasFolder = this.fileManagerService.getAlias(destinationNode?.id);
      const parentNodeMoved = this.fileManagerService.selectedNode;
      const pathDestinationFolder = destinationFolder
        ? this.fileManagerService.findPath(destinationFolder)
        : undefined;
      const pathParentNodeMoved = parentNodeMoved
        ? this.fileManagerService.findPath(parentNodeMoved)
        : '';

      if (aliasNodeMoved !== aliasFolder) {
        // trying to move the folder to another alias
        this.moveFolderToAnotherAlias(event, filesMoved, aliasNodeMoved);
      } else if (pathDestinationFolder && destinationFolder) {
        // trying to move in the same folder where it comes from
        this.moveFolderInTheSameFolder(
          event,
          pathParentNodeMoved,
          pathDestinationFolder,
          filesMoved,
          destinationFolder,
          aliasNodeMoved
        );
      }
    } else {
      this.fileManagerService.createNotification(
        'error',
        this.translate.instant(_('admins.resources.noRightsToMoveItem')),
        false,
        8000
      );
      event.cancel = true;
    }
  }

  moveFolderToAnotherAlias(event: any, filesMoved: FileDescription[], aliasNodeMoved: string) {
    event.cancel = true;
    if (filesMoved.length > 1) {
      this.fileManagerService.createNotification(
        'information',
        this.translate.instant(_('admins.resources.itemsNotMovedAlias'), { alias: aliasNodeMoved }),
        false,
        8000
      );
    } else if (filesMoved[0].type === FileItemType.directory) {
      this.fileManagerService.createNotification(
        'information',
        this.translate.instant(_('admins.resources.folderNotMovedAlias'), {
          alias: aliasNodeMoved,
        }),
        false,
        8000
      );
    } else {
      this.fileManagerService.createNotification(
        'information',
        this.translate.instant(_('admins.resources.fileNotMovedAlias'), { alias: aliasNodeMoved }),
        false,
        8000
      );
    }
  }

  moveFolderInTheSameFolder(
    event: any,
    pathParentNodeMoved: string,
    pathDestinationFolder: string,
    filesMoved: FileDescription[],
    destinationFolder: FileTree,
    aliasNodeMoved: string
  ) {
    if (pathDestinationFolder === pathParentNodeMoved) {
      event.cancel = true;
      return;
    }

    const isMovable = this.isFolderMovable(pathParentNodeMoved, pathDestinationFolder, filesMoved);

    if (!isMovable) {
      event.cancel = true;
      this.fileManagerService.createNotification(
        'information',
        this.translate.instant(_('admins.resources.folderNotMovedInItselfOrChildren'), {
          alias: aliasNodeMoved,
        }),
        false,
        8000
      );
    } else if (this.isGridUpdatable && this.fileManagerService.selectedNode) {
      this.moveFilesInGrid(this.fileManagerService.selectedNode, destinationFolder, filesMoved);
    }
  }

  isFolderMovable(
    pathParentNodeMoved: string,
    pathDestinationFolder: string,
    filesMoved: FileDescription[]
  ): boolean {
    for (const file of filesMoved) {
      let pathNodeMoved = `${pathParentNodeMoved}\\${file.name}`;

      if (pathNodeMoved.startsWith('\\')) {
        pathNodeMoved = pathNodeMoved.substring(1);
      }

      if (
        pathNodeMoved === pathDestinationFolder ||
        pathDestinationFolder.startsWith(`${pathNodeMoved}\\`)
      ) {
        return false;
      }
    }

    return true;
  }

  getExtensionForFileGrid(file: FileDescription) {
    return this.fileManagerService.getExtension(file);
  }

  onCellDragStopGridToGrid(event: any): void {
    if (
      this.fileManagerService.selectedNode &&
      this.fileManagerService.selectedNode.resource.right !== DataBaseRights.DbrStructure
    ) {
      this.fileManagerService.createNotification(
        'error',
        this.translate.instant(_('admins.resources.noRightsToMoveItem')),
        false,
        8000
      );
      event.cancel = true;
      return;
    }
    // Cancel the movement animation implemented by Syncfusion on the grid
    event.cancel = true;

    const dropIndex = event.dropIndex;
    const filesInFolder = this.filesInFolder;

    // Check if the drop index is defined and the file type is not a directory
    if (dropIndex === undefined || filesInFolder[dropIndex].type !== FileItemType.directory) {
      event.cancel = true;
      return;
    }

    const filesMoved: FileDescription[] = event.data as FileDescription[];
    const parentNode = this.fileManagerService.selectedNode;
    const aliasParentNode = this.fileManagerService.getAlias(parentNode?.id);
    const destinationCellFolder = filesInFolder[dropIndex];
    const aliasDestinationFolder = aliasParentNode;
    // Check if the folder is being moved to a different alias
    if (aliasParentNode !== aliasDestinationFolder) {
      event.cancel = true;
      return;
    }

    // Check if the files/folders are already at their original location
    if (
      this.filesGrid?.dataSource.selection.isSelected(filesInFolder[dropIndex]) ||
      (filesMoved.length === 1 && filesMoved[0] === destinationCellFolder)
    ) {
      event.cancel = true;
      return;
    }

    // Check if the grid is updatable and the selected node is defined
    if (this.isGridUpdatable && this.fileManagerService.selectedNode) {
      let destinationFolder;
      // Expand the folder to get the information of the files and folders inside the source folder
      if (parentNode && !parentNode?.children) {
        this.fileManagerComponent.getFilesInFolder(parentNode).subscribe((data) => {
          destinationFolder = parentNode?.children?.find(
            (item) => item.name === destinationCellFolder.name
          );
          if (destinationFolder) {
            this.moveFilesInGrid(parentNode, destinationFolder, filesMoved);
          }
        });
      } else {
        destinationFolder = parentNode?.children?.find(
          (item) => item.name === destinationCellFolder.name
        );
        if (destinationFolder && parentNode) {
          this.moveFilesInGrid(parentNode, destinationFolder, filesMoved);
        }
      }
    }
  }

  onContextMenu(event: MouseEvent): void {
    event.preventDefault();
    this.updateMenuItems();
    // open context menu
    this.txContextMenu.onContextMenu(event);
  }

  contextMenuClick(args: TxContextMenuEventArgs): void {
    switch (args.item.id) {
      case 'copy':
        this.copyFiles();
        break;
      case 'paste':
        this.pasteFiles();
        break;
      case 'edit':
        this.openFileInEditor();
        break;
    }
  }

  beforeOpenFile() {
    this.updateMenuItems();
  }

  private updateMenuItems() {
    this.disableMenuItems = [];
    if (!this.canCopyFileInGrid()) {
      this.disableMenuItems = [...this.disableMenuItems, 'copy'];
    }
    if (!this.canPasteFileInGrid()) {
      this.disableMenuItems = [...this.disableMenuItems, 'paste'];
    }
    if (!this.canOpenFileInEditor()) {
      this.disableMenuItems = [...this.disableMenuItems, 'edit'];
    }
  }

  private createObjet(
    file: FileItem,
    folderPath: string | undefined,
    createUniqueName: boolean
  ): FileItem {
    return {
      alias: this.fileManagerService.getAlias(this.fileManagerService?.selectedNode?.id),
      itemName: file.itemName,
      path: file.path,
      itemNameCopy: createUniqueName
        ? FilesManager.createUniqueFileNameGridForCopy(
            file.itemName,
            this.filesInFolder,
            2,
            this.translate.instant(_('admins.resources.copy'))
          )
        : file.itemName,
      itemPathCopy: folderPath,
      size: file.size,
      shouldReplaceExistingItem: false,
    };
  }

  private pasteFilesInFolder(folder: FileDescription) {
    const filesState: FilesState = {
      existingFiles: [],
      goodFiles: [],
      sameFiles: [],
    };
    const filesAdded: FileItem[] = [];
    let existingFile: FileDescription | undefined;
    const folderPath = this.fileManagerService.createFolderPath(folder);
    this.resourcesService
      .onExpandingFolder(
        this.fileManagerService.getAlias(this.fileManagerService?.selectedNode?.id),
        folderPath
      )
      .subscribe((items) => {
        this.filesCopied.forEach((file) => {
          existingFile = items.find((elem) => elem.name === file.itemName);
          if (existingFile) {
            existingFile.pathFile = file.path;
            filesState.existingFiles.push(existingFile);
            filesState.sameFiles.push(existingFile);
          } else {
            filesAdded.push(this.createObjet(file, folderPath, false));
          }
        });
        if (filesState.sameFiles.length > 0) {
          this.displayDialogReplaceFile.emit({
            files: filesState,
            isMovingItems: false,
            isPasteItems: true,
            sourceParentNode: this.fileManagerService.selectedNode,
            destinationParentNode: this.fileManagerService.selectedNode,
            data: this.filesInFolder,
            nodeMoved: folder,
          });
        }
        if (filesAdded.length > 0) {
          this.copingFilesAddInGrid(filesAdded, false);
        }
      });
  }

  private copingFilesAddInGrid(filesToPast: FileItem[], isAddInGrid: boolean) {
    this.resourcesService.onCopingFiles(filesToPast).subscribe((result) => {
      const filesCopied: FileItem[] = filesToPast.filter(
        (o1) => !result.some((o2: any) => o1.itemName === o2.itemName)
      );
      if (isAddInGrid) {
        // Add In grid :
        const newFilesInGrid: FileDescription[] = [];
        filesCopied.forEach((file) => {
          if (file.itemNameCopy === undefined) {
            throw new Error('file itemNameCopy is undefined');
          }
          if (file.size === undefined) {
            throw new Error('file size is undefined');
          }
          newFilesInGrid.push({
            name: file.itemNameCopy,
            type: FileItemType.file,
            lastWriteTime: new Date(Date.now()).toISOString(),
            extension: FilesUtils.extractFileExt(file.itemName, true),
            length: file.size,
          });
        });
        newFilesInGrid.forEach((file) => {
          this.createTemporaryRowGrid(
            file,
            this.fileManagerService.getIndexFirstFileInGrid(this.filesInFolder),
            false
          );
        });
      }
      if (result.length > 0) {
        this.fileManagerService.afterErrorCopyingFiles(
          true,
          result.map((r: any) => r.itemName)
        );
      }
      if (filesCopied.length > 0) {
        this.fileManagerService.createNotification(
          'success',
          this.translate.instant(_('admins.resources.filesPasted'), {
            numberFiles: filesCopied.length,
            filesName: filesCopied.map((f) => f.itemName).join(', '),
          }),
          false,
          4000
        );
      }
    });
  }
}
