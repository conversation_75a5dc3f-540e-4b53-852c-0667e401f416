import { waitForAsync } from '@angular/core/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxTreeObjectsComponent } from './tree-objects.component';
import { MockService } from 'ng-mocks';
import { LegacyTxObjectsService } from '../../services/structure/services/objects.service';
import { LegacyTxTreeObjectsService } from './tree-objects.service';
import { LegacyTxObjectTypeService } from '../../services/structure/services/object-type.service';
import { ContextMenuAllModule, TreeViewAllModule } from '@syncfusion/ej2-angular-navigations';

describe('TxTreeObjectsComponent', () => {
  let component: TxTreeObjectsComponent;
  let fixture: ComponentFixture<TxTreeObjectsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxTreeObjectsComponent],
      imports: [TreeViewAllModule, ContextMenuAllModule],
      providers: [
        {
          provide: LegacyTxObjectsService,
          useValue: MockService(LegacyTxObjectsService),
        },
        {
          provide: LegacyTxTreeObjectsService,
          useValue: MockService(LegacyTxTreeObjectsService),
        },
        {
          provide: LegacyTxObjectTypeService,
          useValue: MockService(LegacyTxObjectTypeService),
        },
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxTreeObjectsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
