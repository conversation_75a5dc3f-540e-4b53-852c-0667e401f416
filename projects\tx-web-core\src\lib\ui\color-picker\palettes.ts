export const DEFAULT_PALETTE: string[] = [
  '#816361', // <PERSON> (500)
  '#F3311B', // Red (500)
  '#FD8F05', // Orange (500)
  '#FFC107', // Yellow (500)
  '#3EB55B', // Green (500)
  '#00CEDE', // <PERSON>an (500)
  '#0C92EB', // Blue (500)
  '#A6358A', // Purple (500)
  '#E91E63', // Pink (500)

  '#D3D3D3', // <PERSON> (100)
  '#FFC9CD', // Red (100)
  '#FFDDB1', // Orange (100)
  '#FFECB3', // Yellow (100)
  '#C6E8CD', // Green (100)
  '#AEF1F2', // Cyan (100)
  '#B9DCF8', // Blue (100)
  '#E6C0DA', // Purple (100)
  '#F8BBD0', // Pink (100)

  '#B9B4B4', // <PERSON> (200)
  '#F29490', // Red (200)
  '#FFC77E', // Orange (200)
  '#FFE082', // Yellow (200)
  '#A2DAAD', // Green (200)
  '#75E9EB', // Cyan (200)
  '#8DC7F4', // Blue (200)
  '#D696C3', // Purple (200)
  '#F48FB1', // Pink (200)

  '#A39493', // Brown (300)
  '#E86965', // Red (300)
  '#FEB04C', // Orange (300)
  '#FFD54F', // Yellow (300)
  '#7BCC8C', // Green (300)
  '#1DDEE5', // Cyan (300)
  '#5FB2F0', // Blue (300)
  '#C36DAB', // Purple (300)
  '#F06292', // Pink (300)

  '#927B79', // Brown (400)
  '#F0453D', // Red (400)
  '#FE9F26', // Orange (400)
  '#FFCA28', // Yellow (400)
  '#5DC173', // Green (400)
  '#00D7E1', // Cyan (400)
  '#3BA1EE', // Blue (400)
  '#B54F99', // Purple (400)
  '#EC407A', // Pink (400)

  '#745958', // Brown (600)
  '#E5241D', // Red (600)
  '#F98306', // Orange (600)
  '#FFB300', // Yellow (600)
  '#35A651', // Green (600)
  '#00BDCA', // Cyan (600)
  '#0684DD', // Blue (600)
  '#9A3084', // Purple (600)
  '#D81B60', // Pink (600)

  '#634D4C', // Brown (700)
  '#D31518', // Red (700)
  '#F27307', // Orange (700)
  '#FFA000', // Yellow (700)
  '#2A9446', // Green (700)
  '#00A7B0', // Cyan (700)
  '#0072CB', // Blue (700)
  '#8A297C', // Purple (700)
  '#C2185B', // Pink (700)

  '#534142', // Brown (800)
  '#C7060F', // Red (800)
  '#EC6408', // Orange (800)
  '#FF8F00', // Yellow (800)
  '#20833B', // Green (800)
  '#009398', // Cyan (800)
  '#0062B9', // Blue (800)
  '#7A2474', // Purple (800)
  '#AD1457', // Pink (800)

  '#423435', // Brown (900)
  '#B90000', // Red (900)
  '#E24709', // Orange (900)
  '#FF6F00', // Yellow (900)
  '#0C6327', // Green (900)
  '#006F6B', // Cyan (900)
  '#00449A', // Blue (900)
  '#5F1C64', // Purple (900)
  '#880E4F', // Pink (900)

  '#000000', // Black(900)
  '#262626', // Black (800)
  '#434343', // Black (700)
  '#555555', // Black (600)
  '#7B7B7B', // Black (500)
  '#9D9D9D', // Black (400)
  '#C4C4C4', // Black (300)
  '#D9D9D9', // Black (200)
  '#FFFFFF', // White

  '', // None
];

export const DEFAULT_PALETTE_ORDER: string[] = [
  '#FD8F05', // Orange (500)
  '#816361', // Brown (500)
  '#0C92EB', // Blue (500)
  '#F3311B', // Red (500)
  '#3EB55B', // Green (500)
  '#FFC107', // Yellow (500)
  '#A6358A', // Purple (500)
  '#00CEDE', // Cyan (500)
  '#E91E63', // Pink (500)
];

export const LIGHT_PALETTE: string[] = [
  '#B9B4B4', // Brown (200)
  '#F29490', // Red (200)
  '#FFC77E', // Orange (200)
  '#FFE082', // Yellow (200)
  '#A2DAAD', // Green (200)
  '#75E9EB', // Cyan (200)
  '#8DC7F4', // Blue (200)
  '#D696C3', // Purple (200)
  '#F48FB1', // Pink (200)

  '#D3D3D3', // Brown (100)
  '#FFC9CD', // Red (100)
  '#FFDDB1', // Orange (100)
  '#FFECB3', // Yellow (100)
  '#C6E8CD', // Green (100)
  '#AEF1F2', // Cyan (100)
  '#B9DCF8', // Blue (100)
  '#E6C0DA', // Purple (100)
  '#F8BBD0', // Pink (100)

  '#A39493', // Brown (300)
  '#E86965', // Red (300)
  '#FEB04C', // Orange (300)
  '#FFD54F', // Yellow (300)
  '#7BCC8C', // Green (300)
  '#1DDEE5', // Cyan (300)
  '#5FB2F0', // Blue (300)
  '#C36DAB', // Purple (300)
  '#F06292', // Pink (300)

  '#927B79', // Brown (400)
  '#F0453D', // Red (400)
  '#FE9F26', // Orange (400)
  '#FFCA28', // Yellow (400)
  '#5DC173', // Green (400)
  '#00D7E1', // Cyan (400)
  '#3BA1EE', // Blue (400)
  '#B54F99', // Purple (400)
  '#EC407A', // Pink (400)

  '#816361', // Brown (500)
  '#F3311B', // Red (500)
  '#FD8F05', // Orange (500)
  '#FFC107', // Yellow (500)
  '#3EB55B', // Green (500)
  '#00CEDE', // Cyan (500)
  '#0C92EB', // Blue (500)
  '#A6358A', // Purple (500)
  '#E91E63', // Pink (500)

  '#745958', // Brown (600)
  '#E5241D', // Red (600)
  '#F98306', // Orange (600)
  '#FFB300', // Yellow (600)
  '#35A651', // Green (600)
  '#00BDCA', // Cyan (600)
  '#0684DD', // Blue (600)
  '#9A3084', // Purple (600)
  '#D81B60', // Pink (600)

  '#634D4C', // Brown (700)
  '#D31518', // Red (700)
  '#F27307', // Orange (700)
  '#FFA000', // Yellow (700)
  '#2A9446', // Green (700)
  '#00A7B0', // Cyan (700)
  '#0072CB', // Blue (700)
  '#8A297C', // Purple (700)
  '#C2185B', // Pink (700)

  '#534142', // Brown (800)
  '#C7060F', // Red (800)
  '#EC6408', // Orange (800)
  '#FF8F00', // Yellow (800)
  '#20833B', // Green (800)
  '#009398', // Cyan (800)
  '#0062B9', // Blue (800)
  '#7A2474', // Purple (800)
  '#AD1457', // Pink (800)

  '#423435', // Brown (900)
  '#B90000', // Red (900)
  '#E24709', // Orange (900)
  '#FF6F00', // Yellow (900)
  '#0C6327', // Green (900)
  '#006F6B', // Cyan (900)
  '#00449A', // Blue (900)
  '#5F1C64', // Purple (900)
  '#880E4F', // Pink (900)

  '#000000', // Black(900)
  '#262626', // Black (800)
  '#434343', // Black (700)
  '#555555', // Black (600)
  '#7B7B7B', // Black (500)
  '#9D9D9D', // Black (400)
  '#C4C4C4', // Black (300)
  '#D9D9D9', // Black (200)
  '#FFFFFF', // White

  '', // None
];

export const LIGHT_PALETTE_ORDER: string[] = [
  '#F29490', // Red (200)
  '#FFE082', // Yellow (200)
  '#A2DAAD', // Green (200)
  '#FFC77E', // Orange (200)
  '#8DC7F4', // Blue (200)
  '#B9B4B4', // Brown (200)
  '#D696C3', // Purple (200)
  '#75E9EB', // Cyan (200)
  '#F48FB1', // Pink (200)
];

export const getRandomColor = (usedColors: string[], palette = DEFAULT_PALETTE_ORDER): string => {
  usedColors = usedColors.map((color) => color.toLowerCase()); // convert to LowerCase to compare string
  const nextColor = palette.find((color) => !usedColors.includes(color.toLowerCase()));
  if (nextColor) {
    return nextColor;
  } else {
    // if all colors are already used, build a mix color
    const availableColors = DEFAULT_PALETTE.slice(0, -1);
    let mixColor;
    for (let i = 0; i < 20; i++) {
      mixColor = getMixColor(availableColors);
      if (!usedColors.includes(mixColor.toLowerCase())) {
        break;
      }
    }

    return mixColor ?? '#000000';
  }
};

export const getRandomLightColor = (usedColors: string[]): string => {
  return getRandomColor(usedColors, LIGHT_PALETTE_ORDER);
};

const getMixColor = (availableColors: string[]): string => {
  const color1 = availableColors[Math.floor(Math.random() * availableColors.length)];
  const color2 = availableColors[Math.floor(Math.random() * availableColors.length)];

  return mixHexColors(color1, color2, 0.5);
};

// Mixin 2 colors using a weight: number between 0 & 1, with 1 correspond to 100% of color 1
const mixHexColors = (hexColor1: string, hexColor2: string, weight: number): string => {
  const rgb1 = hexToRgb(hexColor1);
  const rgb2 = hexToRgb(hexColor2);

  if (!rgb1 || !rgb2) {
    return '#000000'; // return black color in case of error
  }

  const w1 = Math.min(1, Math.max(0, weight)); // Ensure weight is between 0 and 1
  const w2 = 1 - w1;

  const mixedR = Math.round(rgb1.r * w1 + rgb2.r * w2);
  const mixedG = Math.round(rgb1.g * w1 + rgb2.g * w2);
  const mixedB = Math.round(rgb1.b * w1 + rgb2.b * w2);

  return rgbToHex({ r: mixedR, g: mixedG, b: mixedB });
};

// Transform a hexadecimal color to RGB color
const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null;
};

// Transform a RGB color to hexadecimal color
const rgbToHex = (rgb: { r: number; g: number; b: number }): string => {
  return '#' + componentToHex(rgb.r) + componentToHex(rgb.g) + componentToHex(rgb.b);
};

const componentToHex = (c: number): string => {
  const hex = c.toString(16);
  return hex.length == 1 ? '0' + hex : hex;
};
