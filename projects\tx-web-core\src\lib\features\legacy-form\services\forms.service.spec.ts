import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed, waitForAsync } from '@angular/core/testing';
import { ConfigServiceMock, ObjectTypeMockService } from '../../../ui/testing';
import { TxConfigService } from '../../../data-access/config';
import { LegacyTxAttributesService } from './structure/services/attributes.service';
import { LegacyTxObjectTypeService } from './structure/services/object-type.service';
import { TxFormConfiguration } from '../models/formConfiguration/businessClass/form-configuration';
import { TxEditionMode } from '../models/formConfiguration/businessClass/form-enum';
import { TxFormConfigFiller } from '../models/formConfiguration/executionClass/form-config-filler';
import { TxObjectConfiguration } from '../models/object-configuration';
import { LegacyTxFormsService } from './forms.service';
import { MockService } from 'ng-mocks';
import { TxFormSettings } from '../models/formConfiguration/businessClass/form-settings';
import { of } from 'rxjs';

describe('FormsService', () => {
  let service: LegacyTxFormsService;
  let objectTypeService: LegacyTxObjectTypeService;
  let attributesService: LegacyTxAttributesService;
  let configService: TxConfigService;
  let http: HttpTestingController;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        { provide: LegacyTxObjectTypeService, useClass: ObjectTypeMockService },
        {
          provide: LegacyTxAttributesService,
          useValue: MockService(LegacyTxAttributesService, {
            listObjectTypeAttributes: () => of(),
          }),
        },
        {
          provide: TxFormConfigFiller,
          useValue: MockService(TxFormConfigFiller),
        },
        { provide: TxConfigService, useClass: ConfigServiceMock },
      ],
    });
    service = TestBed.inject(LegacyTxFormsService);
    objectTypeService = TestBed.inject(LegacyTxObjectTypeService);
    attributesService = TestBed.inject(LegacyTxAttributesService);
    configService = TestBed.inject(TxConfigService);
    http = TestBed.inject(HttpTestingController);
  }));

  it('should be create', () => {
    expect(service).toBeTruthy();
  });

  it('should create a formConfig and add it into FormConfigs dictionnary', () => {
    let form: TxFormConfiguration;
    service
      .loadConfig(1, '', new Map<string, string>(), undefined, TxEditionMode.read, 1, [])
      .subscribe((res: TxObjectConfiguration) => {
        form = res.steps[0].formConfig;
        expect(service.formConfigs.has('form_0_1')).toBeTruthy();
        expect(service.formConfigs.get('form_0_1')).toEqual(form);
      });
  });

  it('should generate a same form only once', () => {
    const spyAddTab = jest.spyOn(service.formConfigFiller, 'addTabs');
    service
      .loadConfig(1, '', new Map<string, string>(), undefined, TxEditionMode.read, 1, [])
      .subscribe(() => {
        expect(spyAddTab).toHaveBeenCalledTimes(1);
      });
    service
      .loadConfig(1, '', new Map<string, string>(), undefined, TxEditionMode.read, 1, [])
      .subscribe(() => {
        expect(spyAddTab).toHaveBeenCalledTimes(1);
      });
  });
});
