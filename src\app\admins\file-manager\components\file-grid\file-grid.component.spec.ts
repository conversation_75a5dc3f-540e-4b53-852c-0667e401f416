import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { FileGridComponent } from './file-grid.component';
import { FileManagerService } from '../../services/file-manager.service';
import { FileDescription, FileItemType, FileTree } from '../../models/file-models';
import { FileManagerComponent } from '../file-manager.component';
import { ResourcesService } from '../../services/resources.service';
import { of } from 'rxjs';
import { GridComponent } from '@syncfusion/ej2-angular-grids';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  FileManagerComponentMock,
  FileManagerServiceMock,
  ResourcesServiceMock,
} from '../../tests/file-manager.mock';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { FileTreeComponent } from '../file-tree/file-tree.component';
import { CUSTOM_ELEMENTS_SCHEMA, Component } from '@angular/core';
import { By } from '@angular/platform-browser';
import { FileManagerTestHelper } from '../../tests/file-manager.helper';
import { MockComponent } from 'ng-mocks';
import {
  DataBaseRights,
  TxContextMenuComponent,
  TxGridComponent,
} from '@bassetti-group/tx-web-core';
import { LoaderComponent } from 'src/app/shared/components/loader/loader.component';

let treeDataMock: FileTree[];
let filesInFolderMock: FileDescription[];
let selectedNodeMock: any;
let filesUploadingMock;
@Component({
  selector: 'app-host-component',
  template: `<app-file-grid
    [filesInFolder]="files"
    (displayDialog)="dialogDisplayafterDelete($event)"
    (handleFileInputFromGrid)="handleFileInput($event)"
    (moveFile)="moveFilesInTreeManager($event)"
    (indicatorHiddenGrid)="hideIndicator($event)"
    (refreshTreeFromGrid)="refreshTreeInFileManager()"></app-file-grid>`,
})
class TestHostComponent {
  files = [];
  handleFileInput($event: any) {
    return;
  }
  hideIndicator() {}
  dialogDisplayafterDelete(event: any) {
    return;
  }
  moveFilesInTreeManager($event: any) {
    return;
  }
  refreshTreeInFileManager() {}
}

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'tx-grid',
  template: ``,
  providers: [
    {
      provide: GridComponent,
      useClass: GridMockComponent,
    },
  ],
})
export class GridMockComponent {
  getSelectedRows() {
    return [];
  }
  getSelectedRowIndexes() {
    return [];
  }
  getRowInfo() {
    return;
  }
  selectRows() {
    return;
  }
  startEdit() {
    return;
  }
  dataSource = {
    selection: {
      clear: () => {},
      isSelected: () => false,
    },
  };
  disableEditing() {}
}

describe('FileGridComponent', () => {
  let component: FileGridComponent;
  let fileManagerComponent: FileManagerComponent;
  let hostComponent: TestHostComponent;
  let hostFixture: ComponentFixture<TestHostComponent>;
  let fileManagerService: FileManagerService;
  let resourcesService: ResourcesService;

  const TRANSLATIONS = {
    en: {},
    fr: {},
  };
  beforeEach(waitForAsync(() => {
    /*
      There is a problem with GridComponent, so we add our own GridMockComponent
      and use CUSTOM_ELEMENTS_SCHEMA to avoid problem with sub-elements e-columns etc.
      and mock the grid cause a problem with contextMenu, so we mock it too
      without this, the tests failed...
    */
    TestBed.configureTestingModule({
      declarations: [
        FileGridComponent,
        TestHostComponent,
        GridMockComponent,
        MockComponent(TxContextMenuComponent),
        MockComponent(LoaderComponent),
      ],
      imports: [
        FontAwesomeTestingModule,
        MatTooltipModule,
        TranslateTestingModule.withTranslations(TRANSLATIONS),
      ],
      providers: [
        { provide: FileManagerComponent, useClass: FileManagerComponentMock },
        { provide: FileManagerService, useClass: FileManagerServiceMock },
        { provide: ResourcesService, useClass: ResourcesServiceMock },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    fileManagerComponent = TestBed.inject(FileManagerComponent);
    fileManagerService = TestBed.inject(FileManagerService);
    resourcesService = TestBed.inject(ResourcesService);
  }));

  beforeEach(() => {
    hostFixture = TestBed.createComponent(TestHostComponent);
    hostComponent = hostFixture.componentInstance;
    component = hostFixture.debugElement.children[0].componentInstance;
    hostFixture.detectChanges();
    fileManagerComponent.fileTreeComponent = {
      refreshTree: jest.fn(),
    } as unknown as FileTreeComponent;
  });

  beforeEach(() => {
    const testHelperForGrid = new FileManagerTestHelper();
    const testDataForGrid = testHelperForGrid.generateTestData();
    treeDataMock = testDataForGrid.treeDataMock;
    filesInFolderMock = testDataForGrid.filesInFolderMock;
    filesUploadingMock = testDataForGrid.filesUploadingMock;
    selectedNodeMock = testDataForGrid.selectedNodeMock;

    fileManagerService.treeData = JSON.parse(JSON.stringify(treeDataMock));
    component.filesInFolder = JSON.parse(JSON.stringify(filesInFolderMock));
    fileManagerService.selectedNode = JSON.parse(JSON.stringify(selectedNodeMock));
    fileManagerService.filesUploading = JSON.parse(JSON.stringify(filesUploadingMock));

    component.isGridUpdatable = true;
    component.isLangLoading = false;
    hostFixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('On Cell Drag Stop', () => {
    let eventMock: any;

    beforeEach(() => {
      eventMock = {};
      component.isLangLoading = false;
    });

    it('should call hideIndicator', () => {
      const spyHideIndicator = jest.spyOn(hostComponent, 'hideIndicator');
      component.onCellDragStop(eventMock);
      expect(spyHideIndicator).toBeCalled();
    });
  });
  describe('On Cell Editing', () => {
    let cellEventMock: any;
    let selectedNodeTestMock: FileTree;

    beforeEach(() => {
      selectedNodeTestMock = { ...selectedNodeMock };
      selectedNodeTestMock.resource.right = DataBaseRights.DbrStructure;
      fileManagerService.selectedNode = selectedNodeTestMock;
      cellEventMock = { rowData: { name: 'testName' }, cancel: false };
    });

    it('should call "createNotification" with dbrNone', () => {
      if (fileManagerService.selectedNode) {
        fileManagerService.selectedNode.resource.right = DataBaseRights.DbrNone;
      }
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onCellEditing(cellEventMock);
      expect(spyCreateNotification).toBeCalled();
    });

    it('should call "createNotification" with dbrRead', () => {
      if (fileManagerService.selectedNode) {
        fileManagerService.selectedNode.resource.right = DataBaseRights.DbrRead;
      }
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onCellEditing(cellEventMock);
      expect(spyCreateNotification).toBeCalled();
    });

    it('should not assign true to args.cancel with dbrStructure', () => {
      if (fileManagerService.selectedNode) {
        fileManagerService.selectedNode.resource.right = DataBaseRights.DbrStructure;
      }
      expect(cellEventMock.cancel).toBe(false);
      component.onCellEditing(cellEventMock);
      expect(cellEventMock.cancel).toBe(false);
    });

    it('should call "createNotification" with dbrStructure', () => {
      if (fileManagerService.selectedNode) {
        fileManagerService.selectedNode.resource.right = DataBaseRights.DbrStructure;
      }
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onCellEditing(cellEventMock);
      expect(spyCreateNotification).not.toBeCalled();
    });

    it('should not assign true to args.cancel with dbrWrite', () => {
      if (fileManagerService.selectedNode) {
        fileManagerService.selectedNode.resource.right = DataBaseRights.DbrWrite;
      }
      expect(cellEventMock.cancel).toBe(false);
      component.onCellEditing(cellEventMock);
      expect(cellEventMock.cancel).toBe(false);
    });

    it('should call "createNotification" with dbrWrite', () => {
      if (fileManagerService.selectedNode) {
        fileManagerService.selectedNode.resource.right = DataBaseRights.DbrWrite;
      }
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onCellEditing(cellEventMock);
      expect(spyCreateNotification).not.toBeCalled();
    });
  });

  describe('On Action Complete', () => {
    let saveEventMock: any;

    beforeEach(() => {
      saveEventMock = { requestType: 'save', action: 'edit' };
      component.onCellEdited = jest.fn();
      component.onRefreshingGrid = jest.fn();
    });

    it('should call "onCellEdited" with "requestType=save" and "action=edit"', () => {
      saveEventMock.requestType = 'save';
      saveEventMock.action = 'edit';
      const spyOnCellEdited = jest.spyOn(component, 'onCellEdited');
      component.onActionComplete(saveEventMock);
      expect(spyOnCellEdited).toBeCalledWith(saveEventMock);
    });
  });

  describe('On Cell Edited', () => {
    let cellEventMock: any;

    beforeEach(() => {
      cellEventMock = {
        data: {
          name: 'New.JPG',
          type: 'File',
          lastWriteTime: '2021-11-02T14:22:18.9564791Z',
          extension: '.JPG',
          length: 75775,
        },
        previousData: {
          name: 'Old.JPG',
          type: 'File',
          lastWriteTime: '2021-11-02T14:22:18.9564791Z',
          extension: '.JPG',
          length: 75775,
        },
        cancel: false,
      };

      component.filesInFolder.find = jest
        .fn()
        .mockReturnValue({
          name: 'Old.JPG',
          type: 'File',
          lastWriteTime: '2021-11-02T14:22:18.9564791Z',
          extension: '.JPG',
          length: 75775,
        });
      component.getNameWithoutExtensionInGrid = jest.fn().mockReturnValue('');
      fileManagerService.createNotification = jest.fn();
      fileManagerService.getNameWithExtension = jest.fn().mockReturnValue('');
      fileManagerComponent.getFilesInFolder = jest.fn().mockReturnValue(of(''));
    });

    it('should assign true to args.cancel with newName="" and File', () => {
      component.getNameWithoutExtensionInGrid = jest.fn().mockReturnValue('');
      component.onCellEdited(cellEventMock);
      expect(cellEventMock.cancel).toBe(true);
    });

    it('should call createNotification with newName="" and File', () => {
      component.getNameWithoutExtensionInGrid = jest.fn().mockReturnValue('');
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onCellEdited(cellEventMock);
      expect(spyCreateNotification).toBeCalledWith(
        'information',
        'admins.resources.fileNameNotEmpty',
        false,
        5000
      );
    });

    it('should assign true to args.cancel with newName="" and Directory', () => {
      component.filesInFolder.find = jest.fn().mockReturnValue({
        name: 'Old.JPG',
        type: 'Directory',
        lastWriteTime: '2021-11-02T14:22:18.9564791Z',
        extension: '.JPG',
        length: 75775,
      });
      component.getNameWithoutExtensionInGrid = jest.fn().mockReturnValue('');
      component.onCellEdited(cellEventMock);
      expect(cellEventMock.cancel).toBe(true);
    });

    it('should call createNotification with newName="" and Directory', () => {
      component.filesInFolder.find = jest.fn().mockReturnValue({
        name: 'Old.JPG',
        type: 'Directory',
        lastWriteTime: '2021-11-02T14:22:18.9564791Z',
        extension: '.JPG',
        length: 75775,
      });
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onCellEdited(cellEventMock);
      expect(spyCreateNotification).toBeCalledWith(
        'information',
        'admins.resources.folderNameNotEmpty',
        false,
        5000
      );
    });
  });

  describe('On Refreshing Grid', () => {
    let filesUploadingTestMock;

    beforeEach(() => {
      document.body.style.cursor = 'auto';
      fileManagerService.selectedNode = {
        id: '1',
        name: 'CRs',
        isFolder: true,
        isTemp: false,
        hasChildren: true,
        expanded: false,
        resource: {
          alias: 'CRs',
          isFolder: true,
          right: DataBaseRights.DbrStructure,
          originalRight: DataBaseRights.DbrStructure,
          lastModification: '2021-11-02T13:50:47.2091990Z',
        },
      };
      filesUploadingTestMock = {
        1: [
          {
            name: 'filesUploading1.jpg',
            type: 'File',
            lastWriteTime: '2021-11-02T14:22:18.9564791Z',
            extension: '.jpg',
            length: 75775,
          },
          {
            name: 'filesUploading2.txt',
            type: 'File',
            lastWriteTime: '2021-11-02T14:22:18.9564791Z',
            extension: '.txt',
            length: 75775,
          },
        ],
        2: [
          {
            name: 'filesUploading3.JPG',
            type: 'File',
            lastWriteTime: '2021-11-02T14:22:18.9564791Z',
            extension: '.JPG',
            length: 75775,
          },
        ],
      };
      fileManagerService.filesUploading = filesUploadingTestMock;
      component.createTemporaryRowGrid = jest.fn();
      fileManagerService.getIndexFirstFileInGrid = jest.fn().mockReturnValue(1);
    });

    it('should assign a new value to the document cursor', () => {
      component.onRefreshingGrid();
      expect(document.body.style.cursor).toBe('unset');
    });

    it('should call createTemporaryRowGrid', () => {
      const spyCreateTemporaryRowGrid = jest.spyOn(component, 'createTemporaryRowGrid');
      component.onRefreshingGrid();
      expect(spyCreateTemporaryRowGrid.mock.calls).toEqual([
        [
          {
            extension: '.jpg',
            lastWriteTime: '2021-11-02T14:22:18.9564791Z',
            length: 75775,
            name: 'filesUploading1.jpg',
            type: 'File',
          },
          1,
        ],
        [
          {
            extension: '.txt',
            lastWriteTime: '2021-11-02T14:22:18.9564791Z',
            length: 75775,
            name: 'filesUploading2.txt',
            type: 'File',
          },
          1,
        ],
      ]);
      expect(spyCreateTemporaryRowGrid).toBeCalledTimes(2);
    });
  });
  describe('On Row Selecting', () => {
    let rowSelectingEventMock: any;

    beforeEach(() => {
      rowSelectingEventMock = {
        data: [{ isTemp: true }, { isTemp: true }],
        cancel: false,
      };
    });

    it('should assign true to event.cancel with array of target cells with isTemp=true', () => {
      component.onRowSelecting(rowSelectingEventMock);
      expect(rowSelectingEventMock.cancel).toBe(true);
    });

    it('should assign true to event.cancel with only one target cell with isTemp=true', () => {
      rowSelectingEventMock.data = { isTemp: true };
      component.onRowSelecting(rowSelectingEventMock);
      expect(rowSelectingEventMock.cancel).toBe(true);
    });

    it('should not assign true to event.cancel with array of target cells with isTemp=false', () => {
      rowSelectingEventMock.data = [{ isTemp: false }, { isTemp: false }];
      component.onRowSelecting(rowSelectingEventMock);
      expect(rowSelectingEventMock.cancel).toBe(false);
    });

    it('should not assign true to event.cancel with only one target cell with isTemp=false', () => {
      rowSelectingEventMock.data = { isTemp: false };
      component.onRowSelecting(rowSelectingEventMock);
      expect(rowSelectingEventMock.cancel).toBe(false);
    });
  });

  describe('Rename With Name In Grid', () => {
    let dataMock: any;

    beforeEach(() => {
      dataMock = {
        name: 'nameMock',
        rowGrid: { name: 'nameRowMock' },
      };
    });

    it('should assign a new value to rowGrid.name', () => {
      component.renameWithNameInGrid(dataMock);
      expect(dataMock.rowGrid.name).toBe(dataMock.name);
    });
  });
  describe('Sort Grid', () => {
    let filesMock: FileDescription[];
    let filesInputMock: FileDescription[];
    let filesOutputMock: FileDescription[];

    beforeEach(() => {
      filesMock = [
        {
          name: 'New folder',
          type: 'Directory',
          lastWriteTime: '2023-05-15T08:37:40.349Z',
          extension: '',
          length: -1,
        },
      ];
      filesInputMock = [filesMock[0]];
      filesOutputMock = [filesMock[0]];
      fileManagerService.getRight = jest.fn().mockReturnValue(DataBaseRights.DbrStructure);
    });

    it('should sort the input files', () => {
      fileManagerService.sortGrid(filesInputMock);
      expect(filesInputMock).toStrictEqual(filesOutputMock);
    });
  });
  describe('On Cell Drag (Grid to tree)', () => {
    let eventMock: any;
    let targetMock: HTMLElement;
    let targetChildMock: HTMLElement;

    beforeEach(() => {
      targetMock = document.createElement('div');
      targetMock.setAttribute('id', 'fileTree');
      targetMock.classList.add('e-list-item');
      targetChildMock = document.createElement('div');
      targetChildMock.classList.add('tree-id');
      targetChildMock.classList.add('e-fullrow');
      targetChildMock.textContent = 'nodeContentMock';
      targetMock.appendChild(targetChildMock);
      eventMock = { target: targetMock, cancel: false };

      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrNone } });
    });

    it('should call _findNode', () => {
      const spyFindNode = jest.spyOn(fileManagerService, 'findNode');
      component.onCellDrag(eventMock);
      expect(spyFindNode).toBeCalledWith('nodeContentMock', fileManagerService.treeData);
    });

    it('should add class "border-accent-dashed" to targetChildMock with nodeElement', () => {
      component.onCellDrag(eventMock);
      expect(targetChildMock.classList.contains('border-accent-dashed')).toBe(true);
    });

    it('should assign "true" to event.cancel with dbrNone', () => {
      component.onCellDrag(eventMock);
      expect(eventMock.cancel).toBe(true);
    });

    it('should not assign "true" to event.cancel with dbrStructure', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrStructure } });
      component.onCellDrag(eventMock);
      expect(eventMock.cancel).toBe(false);
    });
  });

  describe('On Cell Drag (Grid to grid)', () => {
    let eventMock: any;
    let targetMock: HTMLElement;

    beforeEach(() => {
      targetMock = document.createElement('div');
      targetMock.setAttribute('id', 'filesGrid');
      targetMock.classList.add('e-row');
      eventMock = { target: targetMock, cancel: false };

      if (fileManagerService.selectedNode) {
        fileManagerService.selectedNode.resource.right = DataBaseRights.DbrStructure;
      }
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrNone } });
    });

    it('should assign "true" to event.cancel with dbrNone', () => {
      if (fileManagerService.selectedNode) {
        fileManagerService.selectedNode.resource.right = DataBaseRights.DbrNone;
      }
      component.onCellDrag(eventMock);
      expect(eventMock.cancel).toBe(true);
    });

    it('should add class "border-accent-dashed" to targetMock', () => {
      if (fileManagerService.selectedNode) {
        fileManagerService.selectedNode.resource.right = DataBaseRights.DbrNone;
      }
      component.onCellDrag(eventMock);
      expect(targetMock.classList.contains('border-accent-dashed')).toBe(true);
    });
  });
  describe('On Cell Drag Start', () => {
    let rowDragEventMock: any;

    beforeEach(() => {
      rowDragEventMock = { rows: '1', cancel: false };
      fileManagerService.selectedNode = { ...selectedNodeMock };
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
    });

    it('should not assign true to event.cancel with dbrStructure', () => {
      component.onCellDragStart(rowDragEventMock);
      expect(rowDragEventMock.cancel).toBe(false);
    });

    it('should not assign true to event.cancel with !event.rows', () => {
      rowDragEventMock.rows = undefined;
      if (fileManagerService.selectedNode) {
        fileManagerService.selectedNode.resource.right = DataBaseRights.DbrWrite;
      }
      component.onCellDragStart(rowDragEventMock);
      expect(rowDragEventMock.cancel).toBe(false);
    });
  });
  describe('Move Files In Grid', () => {
    let sourceParentNodeMock: FileTree;
    let destinationNodeMock: FileTree;
    let filesMovedMock: FileDescription[];

    beforeEach(() => {
      sourceParentNodeMock = {
        id: '1',
        name: 'CRs',
        isFolder: true,
        isTemp: false,
        hasChildren: true,
        expanded: false,
        resource: {
          alias: 'CRs',
          isFolder: true,
          right: DataBaseRights.DbrStructure,
          originalRight: DataBaseRights.DbrStructure,
          lastModification: '2023-05-15T07:49:37.0000000Z',
        },
      };

      destinationNodeMock = {
        id: '2',
        name: 'Core Folder',
        isFolder: true,
        isTemp: false,
        hasChildren: true,
        expanded: false,
        resource: {
          alias: 'CRs',
          isFolder: true,
          right: DataBaseRights.DbrStructure,
          originalRight: DataBaseRights.DbrStructure,
          lastModification: '2023-05-15T13:50:47.0000000Z',
        },
      };

      filesMovedMock = [
        {
          name: 're.JPG',
          type: 'File',
          lastWriteTime: '2021-11-02T14:22:18.9564791Z',
          extension: '.JPG',
          length: 75775,
        },
      ];

      component.controlFilesDescToDrag = jest.fn().mockReturnValue('1');
      fileManagerService.getAlias = jest.fn().mockReturnValue('CRs');
      fileManagerService.findPath = jest.fn().mockReturnValue('Documents');
    });

    it('should call moveFilesInTree', () => {
      const spyMoveFilesInTree = jest.spyOn(hostComponent, 'moveFilesInTreeManager');
      component.moveFilesInGrid(sourceParentNodeMock, destinationNodeMock, filesMovedMock);
      expect(spyMoveFilesInTree).toBeCalledWith({
        filesControlled: '1',
        sourceParentNode: sourceParentNodeMock,
        destinationNode: destinationNodeMock,
        nodes: [],
      });
    });

    it('should call onExpandingFolder', () => {
      const spyOnExpandingFolder = jest.spyOn(resourcesService, 'onExpandingFolder');
      component.moveFilesInGrid(sourceParentNodeMock, destinationNodeMock, filesMovedMock);
      expect(spyOnExpandingFolder).toBeCalledWith('CRs', 'Documents');
    });
  });

  describe('On Cell Double Click', () => {
    let recordDoubleClickEventMock: any;
    let selectedNodeMockClone: any;

    beforeEach(() => {
      component.isLangLoading = false;
      selectedNodeMockClone = JSON.parse(JSON.stringify(selectedNodeMock));
      recordDoubleClickEventMock = { rowData: { type: FileItemType.directory, name: 'Models' } };
      fileManagerService.selectedNode = JSON.parse(JSON.stringify(selectedNodeMock));
      fileManagerComponent.selectNode = jest.fn();
      fileManagerService.treeData = treeDataMock;
      fileManagerComponent.getFilesInFolder = jest.fn(() => {
        if (fileManagerService.selectedNode !== undefined) {
          fileManagerService.selectedNode.children = [{ name: 'Models', id: '1' } as FileTree];
        }
        return of([]);
      });
    });

    it('should not call "getFilesInFolder" with selectedNode.children', () => {
      const spyGetFilesInFolder = jest.spyOn(fileManagerComponent, 'getFilesInFolder');
      component.onCellDoubleClick(recordDoubleClickEventMock);
      expect(spyGetFilesInFolder).toBeCalledTimes(1);
    });

    it('should call "getFilesInFolder" with !selectedNode.children', () => {
      if (fileManagerService.selectedNode) {
        fileManagerService.selectedNode.children = undefined;
      }
      selectedNodeMockClone.children = [{ name: 'Models', id: '1' } as FileTree];
      const spyGetFilesInFolder = jest.spyOn(fileManagerComponent, 'getFilesInFolder');
      component.onCellDoubleClick(recordDoubleClickEventMock);
      expect(spyGetFilesInFolder).toBeCalledWith(selectedNodeMockClone);
    });
  });

  describe('File Drop Grid', () => {
    let eventMock: any;

    beforeEach(() => {
      eventMock = { dataTransfer: { files: ['1'] } };
      fileManagerComponent.handleFileInput = jest.fn();
    });

    it('should call "handleFileInput"', () => {
      const spyHandleFileInput = jest.spyOn(hostComponent, 'handleFileInput');
      component.fileDropGrid(eventMock);
      expect(spyHandleFileInput).toBeCalledWith({ files: ['1'] });
    });
  });
  describe('Get Add Tooltip', () => {
    beforeEach(() => {
      fileManagerService.selectedNode = { ...selectedNodeMock };
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
    });

    it('should return "admins.resources.noFolderSelected" with !filesGrid', () => {
      component.filesGrid = undefined;
      expect(component.getAddTooltip()).toBe('admins.resources.noFolderSelected');
    });

    it('should return "admins.resources.noFolderSelected" with !selectedNode', () => {
      component.filesGrid = undefined;
      expect(component.getAddTooltip()).toBe('admins.resources.noFolderSelected');
    });

    it('should return "" with filesGrid and selectedNode and dbrStructure', () => {
      expect(component.getAddTooltip()).toBe('');
    });

    it('should return "admins.resources.fileAddAndModifNotAllow" with filesGrid and selectedNode and dbrNone', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      expect(component.getAddTooltip()).toBe('admins.resources.fileAddAndModifNotAllow');
    });

    it('should return "admins.resources.fileAddAndModifNotAllow" with filesGrid and selectedNode and dbrRead', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      expect(component.getAddTooltip()).toBe('admins.resources.fileAddAndModifNotAllow');
    });
  });

  describe('Get Create Folder Tooltip', () => {
    beforeEach(() => {
      fileManagerService.selectedNode = { ...selectedNodeMock };
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
    });

    it('should return "admins.resources.noFolderSelected" with !filesGrid', () => {
      component.filesGrid = undefined;
      expect(component.getCreateFolderTooltip()).toBe('admins.resources.noFolderSelected');
    });

    it('should return "admins.resources.noFolderSelected" with !selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      expect(component.getCreateFolderTooltip()).toBe('admins.resources.noFolderSelected');
    });

    it('should return "" with filesGrid and selectedNode and dbrStructure', () => {
      expect(component.getCreateFolderTooltip()).toBe('');
    });

    it('should return "" with filesGrid and selectedNode and dbrNone', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      expect(component.getCreateFolderTooltip()).toBe('admins.resources.fileAddAndModifNotAllow');
    });

    it('should return "" with filesGrid and selectedNode and dbrRead', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      expect(component.getCreateFolderTooltip()).toBe('admins.resources.fileAddAndModifNotAllow');
    });

    it('should return "" with filesGrid and selectedNode and dbrWrite', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrWrite;
      expect(component.getCreateFolderTooltip()).toBe('admins.resources.fileAddAndModifNotAllow');
    });
  });

  describe('Get Delete Tooltip', () => {
    beforeEach(() => {
      fileManagerService.selectedNode = { ...selectedNodeMock };
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      (component.filesGrid as TxGridComponent<any>).selectRows = jest.fn().mockReturnValue(['1']);
    });

    it('should return "admins.resources.noFileSelected" with !filesGrid', () => {
      component.filesGrid = undefined;
      expect(component.getDeleteTooltip()).toBe('admins.resources.noFileSelected');
    });

    it('should return "admins.resources.noFileSelected" with !selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      expect(component.getDeleteTooltip()).toBe('admins.resources.noFileSelected');
    });

    it('should return "admins.resources.noFileSelected" with selectedNode and filesGrid and dbrStructure and "filesGrid.getSelectedRecords().length!=0"', () => {
      (component.filesGrid as TxGridComponent<any>).selectRows = jest.fn().mockReturnValue([]);
      expect(component.getDeleteTooltip()).toBe('admins.resources.noFileSelected');
    });

    it('should return "admins.resources.fileAddAndModifNotAllow" with selectedNode and filesGrid and dbrWrite', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrWrite;
      expect(component.getDeleteTooltip()).toBe('admins.resources.fileAddAndModifNotAllow');
    });

    it('should return "admins.resources.fileAddAndModifNotAllow" with selectedNode and filesGrid and dbrNone', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      expect(component.getDeleteTooltip()).toBe('admins.resources.fileAddAndModifNotAllow');
    });

    it('should return "admins.resources.fileAddAndModifNotAllow" with selectedNode and filesGrid and dbrRead', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      expect(component.getDeleteTooltip()).toBe('admins.resources.fileAddAndModifNotAllow');
    });

    it('should return "admins.resources.fileAddAndModifNotAllow" with selectedNode and filesGrid and dbrRead', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      expect(component.getDeleteTooltip()).toBe('admins.resources.fileAddAndModifNotAllow');
    });
  });

  describe('Get Download Tooltip', () => {
    beforeEach(() => {
      fileManagerService.selectedNode = { ...selectedNodeMock };
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue([{ type: FileItemType.file }]);
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue([{ type: FileItemType.file }]);
    });

    it('should return "admins.resources.noItemSelected" with !filesGrid', () => {
      component.filesGrid = undefined;
      expect(component.getDownloadTooltip()).toBe('admins.resources.noItemSelected');
    });

    it('should return "admins.resources.noItemSelected" with !selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      expect(component.getDownloadTooltip()).toBe('admins.resources.noItemSelected');
    });

    it('should return "admins.resources.noItemSelected" with selectedNode and filesGrid and dbrStructure and "filesGrid.getSelectedRecords.length=0"', () => {
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest.fn().mockReturnValue([]);
      expect(component.getDownloadTooltip()).toBe('admins.resources.noItemSelected');
    });

    it('should return "admins.resources.fileDownloadNotAllow" with dbrNone', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      expect(component.getDownloadTooltip()).toBe('admins.resources.fileDownloadNotAllow');
    });

    it('should return "" with dbrStructure and File', () => {
      expect(component.getDownloadTooltip()).toBe('');
    });

    it('should return "admins.resources.foldersSelected" with dbrStructure and 2 or more Directories', () => {
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue([{ type: FileItemType.directory }]);
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue([{ type: FileItemType.directory }, { type: FileItemType.directory }]);
      expect(component.getDownloadTooltip()).toBe('admins.resources.foldersSelected');
    });

    it('should return "admins.resources.foldersFilesSelected" with dbrStructure and files & Directories', () => {
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue([{ type: FileItemType.directory }]);
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue([{ type: FileItemType.directory }, { type: FileItemType.file }]);
      expect(component.getDownloadTooltip()).toBe('admins.resources.foldersFilesSelected');
    });
  });

  describe('Get Rename Tooltip', () => {
    beforeEach(() => {
      fileManagerService.selectedNode = { ...selectedNodeMock };
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue(['1']);
    });

    it('should return "admins.resources.noFileSelected" with !filesGrid', () => {
      component.filesGrid = undefined;
      expect(component.getRenameTooltip()).toBe('admins.resources.noFileSelected');
    });

    it('should return "admins.resources.noFileSelected" with !selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      expect(component.getRenameTooltip()).toBe('admins.resources.noFileSelected');
    });

    it('should return "admins.resources.noFileSelected" with selectedNode and filesGrid and dbrStructure and "filesGrid.getSelectedRecords.length=0"', () => {
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest.fn().mockReturnValue([]);
      expect(component.getRenameTooltip()).toBe('admins.resources.noFileSelected');
    });

    it('should return "admins.resources.fileAddAndModifNotAllow" with dbrNone', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      expect(component.getRenameTooltip()).toBe('admins.resources.fileAddAndModifNotAllow');
    });

    it('should return "admins.resources.fileAddAndModifNotAllow" with dbrWrite', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrWrite;
      expect(component.getRenameTooltip()).toBe('admins.resources.fileAddAndModifNotAllow');
    });

    it('should return "admins.resources.fileAddAndModifNotAllow" with dbrRead', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      expect(component.getRenameTooltip()).toBe('admins.resources.fileAddAndModifNotAllow');
    });

    it('should return "" with dbrStructure', () => {
      expect(component.getRenameTooltip()).toBe('');
    });
  });

  describe('Get Open History Tooltip', () => {
    beforeEach(() => {
      fileManagerService.selectedNode = { ...selectedNodeMock };
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue(['1']);
    });

    it('should return "admins.resources.noFileSelected" with !filesGrid', () => {
      component.filesGrid = undefined;
      expect(component.getOpenHistoryTooltip()).toBe('admins.resources.noFileSelected');
    });

    it('should return "admins.resources.noFileSelected" with !selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      expect(component.getOpenHistoryTooltip()).toBe('admins.resources.noFileSelected');
    });

    it('should return "admins.resources.noFileSelected" with selectedNode and filesGrid and dbrStructure and "filesGrid.getSelectedRecords.length=0"', () => {
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest.fn().mockReturnValue([]);
      expect(component.getOpenHistoryTooltip()).toBe('admins.resources.noFileSelected');
    });

    it('should return "" with selectedNode and filesGrid and dbrNone and "filesGrid.getSelectedRecords.length!=0"', () => {
      expect(component.getOpenHistoryTooltip()).toBe('');
    });
  });

  describe('Can Open History In Grid', () => {
    beforeEach(() => {
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue(['1']);
    });

    it('should return "false" with !selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      expect(component.canOpenHistoryInGrid()).toBe(false);
    });

    it('should return "false" with !filesGrid', () => {
      component.filesGrid = undefined;
      expect(component.canOpenHistoryInGrid()).toBe(false);
    });

    it('should return "false" with "filesGrid.getSelectedRecords.length!=1"', () => {
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest.fn().mockReturnValue([]);
      expect(component.canOpenHistoryInGrid()).toBe(false);
    });

    it('should return "true" with "filesGrid.getSelectedRecords.length=1"', () => {
      expect(component.canOpenHistoryInGrid()).toBe(true);
    });
  });

  describe('Can Rename File In Grid', () => {
    beforeEach(() => {
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue(['1']);
    });

    it('should return "false" with !selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      expect(component.canRenameFileInGrid()).toBe(false);
    });

    it('should return "false" with !filesGrid', () => {
      component.filesGrid = undefined;
      expect(component.canRenameFileInGrid()).toBe(false);
    });

    it('should return "false" with "filesGrid.getSelectedRecords.length!=1"', () => {
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest.fn().mockReturnValue([]);
      expect(component.canRenameFileInGrid()).toBe(false);
    });

    it('should return "false" with "filesGrid.getSelectedRecords.length=1" and dbrNone', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      expect(component.canRenameFileInGrid()).toBe(false);
    });

    it('should return "false" with "filesGrid.getSelectedRecords.length=1" and dbrRead', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      expect(component.canRenameFileInGrid()).toBe(false);
    });

    it('should return "false" with "filesGrid.getSelectedRecords.length=1" and dbrWrite', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrWrite;
      expect(component.canRenameFileInGrid()).toBe(false);
    });

    it('should return "true" with "filesGrid.getSelectedRecords.length=1" and dbrStructure', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      expect(component.canRenameFileInGrid()).toBe(true);
    });
  });

  describe('Can Delete File In Grid', () => {
    beforeEach(() => {
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue(['1']);
    });

    it('should return "false" with !selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      expect(component.canDeleteFileInGrid()).toBe(false);
    });

    it('should return "false" with !filesGrid', () => {
      component.filesGrid = undefined;
      expect(component.canDeleteFileInGrid()).toBe(false);
    });

    it('should return "false" with "filesGrid.getSelectedRecords.length!=1"', () => {
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest.fn().mockReturnValue([]);
      expect(component.canDeleteFileInGrid()).toBe(false);
    });

    it('should return "false" with "filesGrid.getSelectedRecords.length=1" and dbrNone', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      expect(component.canDeleteFileInGrid()).toBe(false);
    });

    it('should return "false" with "filesGrid.getSelectedRecords.length=1" and dbrRead', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      expect(component.canDeleteFileInGrid()).toBe(false);
    });

    it('should return "false" with "filesGrid.getSelectedRecords.length=1" and dbrWrite', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrWrite;
      expect(component.canDeleteFileInGrid()).toBe(false);
    });

    it('should return "true" with "filesGrid.getSelectedRecords.length=1" and dbrStructure', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      expect(component.canDeleteFileInGrid()).toBe(true);
    });
  });
  describe('Can Create Folder In Grid', () => {
    beforeEach(() => {
      fileManagerService.selectedNode = { ...selectedNodeMock };
    });

    it('should return "false" with !selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      expect(component.canCreateFolderInGrid()).toBe(false);
    });

    it('should return "false" with !filesGrid', () => {
      component.filesGrid = undefined;
      expect(component.canCreateFolderInGrid()).toBe(false);
    });

    it('should return "true" with selectedNode and filesGrid and dbrStructure', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      expect(component.canCreateFolderInGrid()).toBe(true);
    });

    it('should return "false" with selectedNode and filesGrid and dbrNone', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      expect(component.canCreateFolderInGrid()).toBe(false);
    });

    it('should return "false" with selectedNode and filesGrid and dbrRead', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      expect(component.canCreateFolderInGrid()).toBe(false);
    });

    it('should return "false" with selectedNode and filesGrid and dbrWrite', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrWrite;
      expect(component.canCreateFolderInGrid()).toBe(false);
    });
  });
  describe('Can Download File In Grid', () => {
    beforeEach(() => {
      fileManagerService.selectedNode = { ...selectedNodeMock };
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue([{ type: FileItemType.file }]);
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
    });

    it('should return "false" with !filesGrid', () => {
      component.filesGrid = undefined;
      expect(component.canDownloadFileInGrid()).toBe(false);
    });

    it('should return "false" with filesGrid.getSelectedRecords.length=0', () => {
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest.fn().mockReturnValue([]);
      expect(component.canDownloadFileInGrid()).toBe(false);
    });

    it('should return "false" with selectedNode and dbrNone', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      expect(component.canDownloadFileInGrid()).toBe(false);
    });

    it('should return "true" with File', () => {
      expect(component.canDownloadFileInGrid()).toBe(true);
    });

    it('should return "false" with Directory', () => {
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue([{ type: FileItemType.directory }]);
      expect(component.canDownloadFileInGrid()).toBe(true);
    });
  });

  describe('Delete File In Grid', () => {
    let filesMock: any;
    let dialogDeleteFilesMock: any;

    beforeEach(() => {
      fileManagerService.getAlias = jest.fn().mockReturnValue('CRs');
      fileManagerService.findPath = jest.fn().mockReturnValue('Documents');
      filesMock = [{ name: 'nameMock1', type: FileItemType.directory }];
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue(filesMock);
      dialogDeleteFilesMock = {
        files: filesMock,
        parentNode: fileManagerService.selectedNode,
        alias: 'CRs',
        path: 'Documents',
        isDeletingFromTree: false,
      };
    });

    it('should call "show" with Folder', () => {
      const spyShow = jest.spyOn(hostComponent, 'dialogDisplayafterDelete');
      component.deleteFileInGrid();
      expect(spyShow).toBeCalledWith({
        message: 'admins.resources.folderDelete',
        annotation: undefined,
        object: dialogDeleteFilesMock,
      });
    });

    it('should call "show" with File', () => {
      filesMock = [{ name: 'nameMock1', type: FileItemType.file }];
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue(filesMock);
      dialogDeleteFilesMock.files = filesMock;
      const spyShow = jest.spyOn(hostComponent, 'dialogDisplayafterDelete');
      component.deleteFileInGrid();
      expect(spyShow).toBeCalledWith({
        message: 'admins.resources.fileDelete',
        annotation: undefined,
        object: dialogDeleteFilesMock,
      });
    });

    it('should call "show" with Items', () => {
      filesMock = [
        { name: 'nameMock1', type: FileItemType.file },
        { name: 'nameMock2', type: FileItemType.file },
      ];
      (component.filesGrid as TxGridComponent<any>).getSelectedRows = jest
        .fn()
        .mockReturnValue(filesMock);
      dialogDeleteFilesMock.files = filesMock;
      const spyShow = jest.spyOn(hostComponent, 'dialogDisplayafterDelete');
      component.deleteFileInGrid();
      expect(spyShow).toBeCalledWith({
        message: 'admins.resources.itemsDelete',
        annotation: undefined,
        object: dialogDeleteFilesMock,
      });
    });
  });
  describe('Control Files Desc To Drag', () => {
    let argsFilesMock: any;
    let argsSelectedNodeMock: any;
    let argsFilesInFolder: any;
    let returnMock: any;

    beforeEach(() => {
      argsFilesMock = [{ name: 'nameMock1', resource: { lastModification: '1', size: 100 } }];
      argsSelectedNodeMock = { resource: { right: DataBaseRights.DbrStructure } };
      argsFilesInFolder = [{ name: 'nameMock1' }, { name: 'nameMock2' }];

      returnMock = {
        existingFiles: [{ name: 'nameMock1' }],
        sameFiles: [{ name: 'nameMock1', resource: { lastModification: '1', size: 100 } }],
        goodFiles: [],
      };

      component.filesInFolder = [
        {
          name: 'nameMock1',
          type: 'File',
          lastWriteTime: '2021-11-02T14:22:18.9564791Z',
          extension: '.JPG',
          length: 75775,
        },
        {
          name: 'otherName',
          type: 'File',
          lastWriteTime: '2021-11-02T14:22:18.9564791Z',
          extension: '.JPG',
          length: 75775,
        },
      ];

      fileManagerService.createNotification = jest.fn();
    });

    it('should call createNotification with dbrNone', () => {
      argsSelectedNodeMock.resource.right = DataBaseRights.DbrNone;
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.controlFilesDescToDrag(argsFilesMock, argsSelectedNodeMock, argsFilesInFolder);
      expect(spyCreateNotification).toBeCalledWith(
        'error',
        'admins.resources.fileAddAndModifNotAllow',
        false,
        8000
      );
    });

    it('should not call createNotification with dbrRead', () => {
      argsSelectedNodeMock.resource.right = DataBaseRights.DbrRead;
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.controlFilesDescToDrag(argsFilesMock, argsSelectedNodeMock, argsFilesInFolder);
      expect(spyCreateNotification).toBeCalledWith(
        'error',
        'admins.resources.fileAddAndModifNotAllow',
        false,
        8000
      );
    });

    it('should not call createNotification with dbrWrite', () => {
      argsSelectedNodeMock.resource.right = DataBaseRights.DbrWrite;
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.controlFilesDescToDrag(argsFilesMock, argsSelectedNodeMock, argsFilesInFolder);
      expect(spyCreateNotification).toBeCalledWith(
        'error',
        'admins.resources.fileAddAndModifNotAllow',
        false,
        8000
      );
    });

    it('should not call createNotification with dbrStructure', () => {
      argsSelectedNodeMock.resource.right = DataBaseRights.DbrStructure;
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.controlFilesDescToDrag(argsFilesMock, argsSelectedNodeMock, argsFilesInFolder);
      expect(spyCreateNotification).not.toBeCalled();
    });

    it('should return existingFiles and sameFiles with common files in filesInFolder', () => {
      expect(
        component.controlFilesDescToDrag(argsFilesMock, argsSelectedNodeMock, argsFilesInFolder)
      ).toStrictEqual(returnMock);
    });

    it('should return goodFiles with none common files in filesInFolder', () => {
      argsFilesInFolder = [{ name: 'nameMock2' }];
      expect(
        component.controlFilesDescToDrag(argsFilesMock, argsSelectedNodeMock, argsFilesInFolder)
      ).toStrictEqual({
        existingFiles: [],
        goodFiles: [{ name: 'nameMock1', resource: { lastModification: '1', size: 100 } }],
        sameFiles: [],
      });
    });
  });
  describe('Do Add Files In Grid', () => {
    let argsMock: any;

    beforeEach(() => {
      argsMock = {
        filesToAdd: [{ id: '1', file: 'file1' }],
        filesToReplace: [{ id: '2', file: 'file2' }],
        node: { id: 1 },
      };
      component.createTemporaryRowGrid = jest.fn();
      fileManagerService.getIndexFirstFileInGrid = jest.fn().mockReturnValue(1);
      fileManagerService.getAlias = jest.fn().mockReturnValue('CRs');
      fileManagerService.findPath = jest.fn().mockReturnValue('Documents');
    });

    it('should call _getIndexFirstFileInGrid', () => {
      const spyGetIndexFirstFileInGrid = jest.spyOn(fileManagerService, 'getIndexFirstFileInGrid');
      component.doAddFilesInGrid(argsMock);
      expect(spyGetIndexFirstFileInGrid).toBeCalledWith(component.filesInFolder);
    });

    it('should call createNotification with nbTotalFiles > 0', () => {
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.doAddFilesInGrid(argsMock);
      expect(spyCreateNotification).toBeCalledWith(
        'loading',
        'admins.resources.uploadingFiles',
        false,
        0,
        0
      );
    });

    it('should not call createNotification with nbTotalFiles = 0', () => {
      argsMock = { filesToAdd: [], filesToReplace: [], node: { id: 1 } };
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.doAddFilesInGrid(argsMock);
      expect(spyCreateNotification).not.toBeCalled();
    });

    it('should call createTemporaryRowGrid 2 times with filesToAdd and filesToReplace', () => {
      const spyCreateTemporaryRowGrid = jest.spyOn(component, 'createTemporaryRowGrid');
      component.doAddFilesInGrid(argsMock);
      expect(spyCreateTemporaryRowGrid).toBeCalledTimes(2);
      expect(spyCreateTemporaryRowGrid.mock.calls).toEqual([
        [{ id: '1', file: 'file1' }, 1],
        [{ id: '2', file: 'file2' }, 1],
      ]);
    });

    it('should call onUploadingFile with filesToAdd', () => {
      const spyOnUploadingFile = jest.spyOn(resourcesService, 'onUploadingFile');
      component.doAddFilesInGrid(argsMock);
      expect(spyOnUploadingFile).toBeCalledTimes(1);
      expect(spyOnUploadingFile).toBeCalledWith('CRs', 'file1', 'Documents');
    });

    it('should call onReplacingFile with filesToReplace', () => {
      const spyOnReplacingFile = jest.spyOn(resourcesService, 'onReplacingFile');
      component.doAddFilesInGrid(argsMock);
      expect(spyOnReplacingFile).toBeCalledTimes(1);
      expect(spyOnReplacingFile).toBeCalledWith('CRs', 'file2', 'Documents');
    });
  });
  describe('Row Bound', () => {
    let argsMock: any;

    beforeEach(() => {
      argsMock = { data: { isTemp: true }, row: undefined };
      argsMock.row = document.createElement('div');
    });

    it('should add a class to argsMock.row with isTemp=true', () => {
      component.rowBound(argsMock);
      expect((argsMock.row as HTMLDivElement).classList.contains('fm-row-isTemp')).toBe(true);
    });

    it('should not add a class to argsMock.row with isTemp=false', () => {
      argsMock.data.isTemp = false;
      component.rowBound(argsMock);
      expect((argsMock.row as HTMLDivElement).classList.contains('fm-row-isTemp')).toBe(false);
    });
  });
  describe('Create Temporary Row Grid', () => {
    let argsFileMock: FileDescription;
    let argsIndexFirstFileInGridMock: number;

    beforeEach(() => {
      argsFileMock = {
        isTemp: true,
        name: 'nameFile',
        type: FileItemType.file,
        lastWriteTime: '',
        extension: '',
        length: -1,
      };
      argsIndexFirstFileInGridMock = 1;

      component.filesInFolder.findIndex = jest.fn().mockReturnValue(0);
    });

    it('should delete filesInFolder[0] element and add argsFileMock at filesInFolder[0]', () => {
      const firstFilesInFolder = component.filesInFolder[0];
      expect(component.filesInFolder.some((e) => e === firstFilesInFolder)).toBe(true);
      component.createTemporaryRowGrid(argsFileMock, argsIndexFirstFileInGridMock);
      expect(component.filesInFolder[0]).toBe(argsFileMock);
      expect(component.filesInFolder.some((e) => e === firstFilesInFolder)).toBe(false);
    });

    it('should add argsFileMock at filesInFolder[1] with index=-1 and File', () => {
      component.filesInFolder.findIndex = jest.fn().mockReturnValue(-1);

      const firstFilesInFolder = component.filesInFolder[1];
      expect(component.filesInFolder.some((e) => e === firstFilesInFolder)).toBe(true);
      component.createTemporaryRowGrid(argsFileMock, argsIndexFirstFileInGridMock);
      expect(component.filesInFolder[1]).toBe(argsFileMock);
      expect(component.filesInFolder.some((e) => e === firstFilesInFolder)).toBe(true);
    });

    it('should add argsFileMock at filesInFolder[0] with index=-1 and Directory', () => {
      component.filesInFolder.findIndex = jest.fn().mockReturnValue(-1);
      argsFileMock.type = FileItemType.directory;

      const firstFilesInFolder = component.filesInFolder[1];
      expect(component.filesInFolder.some((e) => e === firstFilesInFolder)).toBe(true);
      component.createTemporaryRowGrid(argsFileMock, argsIndexFirstFileInGridMock);
      expect(component.filesInFolder[0]).toBe(argsFileMock);
      expect(component.filesInFolder.some((e) => e === firstFilesInFolder)).toBe(true);
    });
  });

  describe('Remplace Temporary Row Grid', () => {
    let argsFileMock: any;
    let argsNode: any;
    let argsUploaded: any;

    beforeEach(() => {
      argsFileMock = { name: '1.JPG' };
      argsNode = { id: '1' };
      argsUploaded = true;
      (fileManagerService.selectedNode as FileTree).id = '1';

      component.filesInFolder = JSON.parse(JSON.stringify(filesInFolderMock));
      component.filesInFolder.findIndex = jest.fn().mockReturnValue(0);
    });

    it('should assign "false" to child.isTemp with child.name=file.name with uploaded', () => {
      component.replaceTemporaryRowGrid(argsFileMock, argsNode, argsUploaded);
      expect(component.filesInFolder[2].isTemp).toBe(false);
    });

    it('should splice component.filesInFolder[0] with !uploaded', () => {
      argsUploaded = false;
      component.replaceTemporaryRowGrid(argsFileMock, argsNode, argsUploaded);
      expect(component.filesInFolder[0] === filesInFolderMock[0]).toBe(false);
    });
  });
  describe('Replace Temporary Node', () => {
    let argsFileMock: any;
    let argsSelectedNode: any;
    let argsUploaded: any;

    let originalNodeMock: any;

    beforeEach(() => {
      argsFileMock = { name: 'nameMock' };
      argsSelectedNode = { id: '1' };
      argsUploaded = true;
      originalNodeMock = {
        isFolder: true,
        children: [
          { name: 'nameMock', isTemp: true },
          { name: 'nameMock2', isTemp: true },
        ],
      };

      fileManagerComponent.fileTreeComponent = {
        refreshTree: jest.fn(),
      } as unknown as FileTreeComponent;
      fileManagerService.findNode = jest.fn().mockReturnValue(originalNodeMock);
    });

    it('should assign "false" to originalNode.children.isTemp with same name and uploaded', () => {
      component.replaceTemporaryNode(argsFileMock, argsSelectedNode, argsUploaded);
      expect(originalNodeMock.children[0].isTemp).toBe(false);
    });

    it('should not assign "false" to originalNode.children.isTemp with different name with uploaded', () => {
      argsFileMock.name = 'nameFileMock';
      component.replaceTemporaryNode(argsFileMock, argsSelectedNode, argsUploaded);
      expect(originalNodeMock.children[0].isTemp).toBe(true);
    });

    it('should filter originalNodeMock.children with !uploaded', () => {
      argsUploaded = false;
      component.replaceTemporaryNode(argsFileMock, argsSelectedNode, argsUploaded);
      expect(originalNodeMock.children).toStrictEqual([{ isTemp: true, name: 'nameMock2' }]);
    });
  });
  describe('_Set File After Success Moving Grid', () => {
    let argsDestinationFolderMock: any;
    let argsFileMock: any;
    let argsNodeMovedMock: any;
    let selectedNodeMockClone: any;

    beforeEach(() => {
      selectedNodeMockClone = fileManagerService.selectedNode;
      argsDestinationFolderMock = selectedNodeMockClone;
      argsFileMock = { name: 'nameFileMock', type: FileItemType.directory };
      argsNodeMovedMock = {};

      component.filesInFolder = JSON.parse(JSON.stringify(filesInFolderMock));
      component.filesInFolder.findIndex = jest.fn().mockReturnValue(1);
      fileManagerService.getIndexFirstFileInGrid = jest.fn().mockReturnValue(0);
    });

    it('should assign argsFileMock in filesInFolder[1] with "selectedNode=destinationFolder"', () => {
      component.setFileAfterSuccessMovingGrid(
        argsDestinationFolderMock,
        argsFileMock,
        argsNodeMovedMock
      );
      expect(component.filesInFolder[1]).toBe(argsFileMock);
    });

    it('should assign argsFileMock in filesInFolder[1] with "selectedNode=parentNodeMoved"', () => {
      component.setFileAfterSuccessMovingGrid(
        argsDestinationFolderMock,
        argsFileMock,
        argsNodeMovedMock
      );
      expect(component.filesInFolder[1]).toBe(argsFileMock);
    });

    it('should assign argsFileMock in filesInFolder[0] with File', () => {
      component.filesInFolder.findIndex = jest.fn().mockReturnValue(-1);
      fileManagerService.getIndexFirstFileInGrid = jest.fn().mockReturnValue(0);
      argsFileMock = { name: 'nameFileMock', type: FileItemType.file };
      component.setFileAfterSuccessMovingGrid(
        argsDestinationFolderMock,
        argsFileMock,
        argsNodeMovedMock
      );
      expect(component.filesInFolder[0]).toBe(argsFileMock);
    });
  });
  describe('Create Folder test "create-folder-button" ', () => {
    beforeEach(() => {
      fileManagerService.selectedNode = { ...selectedNodeMock };

      fileManagerService.updateNotification = jest.fn();
      fileManagerService.getAlias = jest.fn().mockReturnValue('CRs');
      fileManagerService.findPath = jest.fn().mockReturnValue('Documents');
    });

    it('should call "createFolder"', () => {
      const spyCreateFolder = jest.spyOn(component, 'createFolder');
      hostFixture.debugElement
        .query(By.css('#fm-create-folder-button'))
        .triggerEventHandler('click', null);
      expect(spyCreateFolder).toBeCalledTimes(1);
      expect(spyCreateFolder).toBeCalled();
    });
  });

  describe('On Cell Drag Stop Grid To Grid || Tree', () => {
    let eventMock: any;
    let eventTarget: Element;
    let eventTargetAncestor: Element;
    beforeEach(() => {
      eventTarget = document.createElement('div');
      eventTarget.classList.add('tree-id');
      eventTarget.textContent = '0';

      eventTargetAncestor = document.createElement('div');
      eventTargetAncestor.classList.add('e-text-content');
      eventTargetAncestor.setAttribute('id', 'fileTree');
      eventTargetAncestor.appendChild(eventTarget);
      component.isGridUpdatable = true;

      eventMock = {
        data: filesInFolderMock,
        target: eventTarget,
        dropIndex: 0,
        cancel: false,
      };

      fileManagerService.findPath = jest
        .fn()
        .mockReturnValueOnce('Documents1')
        .mockReturnValueOnce('Documents2');
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrStructure } });
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      fileManagerService.getAlias = jest.fn().mockReturnValue('CRs');
      fileManagerService.createNotification = jest.fn();
      fileManagerComponent.getFilesInFolder = jest.fn(() => {
        (fileManagerService.selectedNode as FileTree).children = [];
        return of([]);
      });
      component.moveFilesInGrid = jest.fn();
    });

    //On Cell Drag Stop Grid To Grid
    it('should call "createNotification" with dbrNone', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrNone } });
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onCellDragStop(eventMock);
      expect(spyCreateNotification).toBeCalledWith(
        'error',
        'admins.resources.noRightsToMoveItem',
        false,
        8000
      );
    });

    it('should assign "true" to event.cancel with filesMoved.length === 1 && filesMoved[0] === destinationCellFolder', () => {
      eventMock.data = [component.filesInFolder[0]];
      component.onCellDragStop(eventMock);
      expect(eventMock.cancel).toBe(true);
    });

    it('should not call "getFilesInFolder" with parentNode.children', () => {
      const spyGetFilesInFolder = jest.spyOn(fileManagerComponent, 'getFilesInFolder');
      component.onCellDragStop(eventMock);
      expect(spyGetFilesInFolder).not.toBeCalled();
    });

    it('should call "getFilesInFolder" with !parentNode.children', () => {
      (fileManagerService.selectedNode as FileTree).children = undefined;
      const spyGetFilesInFolder = jest.spyOn(fileManagerComponent, 'getFilesInFolder');
      component.onCellDragStopGridToGrid(eventMock);
      expect(spyGetFilesInFolder).toBeCalledWith(fileManagerService.selectedNode);
    });

    //_On Cell Drag Stop Grid To Tree  tests
    it('should call "createNotification" with aliasNodeMoved !== aliasFolder and filesMoved.length > 1', () => {
      fileManagerService.getAlias = jest
        .fn()
        .mockReturnValueOnce('CRs')
        .mockReturnValueOnce('Others');
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onCellDragStop(eventMock);
      expect(spyCreateNotification).toBeCalledWith(
        'information',
        'admins.resources.itemsNotMovedAlias',
        false,
        8000
      );
    });

    it('should call "createNotification" with aliasNodeMoved !== aliasFolder and filesMoved.length === 1 && filesMoved[0].type === FileItemType.directory', () => {
      eventMock.data = [{ ...filesInFolderMock[0] }];
      fileManagerService.getAlias = jest
        .fn()
        .mockReturnValueOnce('CRs')
        .mockReturnValueOnce('Others');
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onCellDragStop(eventMock);
      expect(spyCreateNotification).toBeCalledWith(
        'information',
        'admins.resources.folderNotMovedAlias',
        false,
        8000
      );
    });

    it('should call "createNotification" with aliasNodeMoved !== aliasFolder and filesMoved.length === 1 && filesMoved[0].type === FileItemType.directory', () => {
      eventMock.data = [{ ...filesInFolderMock[2] }];
      fileManagerService.getAlias = jest
        .fn()
        .mockReturnValueOnce('CRs')
        .mockReturnValueOnce('Others');
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onCellDragStop(eventMock);
      expect(spyCreateNotification).toBeCalledWith(
        'information',
        'admins.resources.fileNotMovedAlias',
        false,
        8000
      );
    });

    it('should assign "true" to event.cancel with pathDestinationFolder === pathParentNodeMoved', () => {
      fileManagerService.findPath = jest.fn().mockReturnValue('Documents');
      component.onCellDragStop(eventMock);
      expect(eventMock.cancel).toBe(true);
    });

    it('should assign "true" to event.cancel with pathNodeMoved === pathDestinationFolder', () => {
      eventMock.data = [{ name: 'nameFileMock' }];
      fileManagerService.findPath = jest
        .fn()
        .mockReturnValueOnce('Documents1\\nameFileMock')
        .mockReturnValueOnce('Documents1');
      component.onCellDragStop(eventMock);
      expect(eventMock.cancel).toBe(true);
    });

    it('should call "createNotification" with pathNodeMoved === pathDestinationFolder', () => {
      eventMock.data = [{ name: 'nameFileMock' }];
      fileManagerService.findPath = jest
        .fn()
        .mockReturnValueOnce('Documents1\\nameFileMock')
        .mockReturnValueOnce('Documents1');
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');

      component.onCellDragStop(eventMock);
      expect(spyCreateNotification).toBeCalledWith(
        'information',
        'admins.resources.folderNotMovedInItselfOrChildren',
        false,
        8000
      );
    });

    it('should call "createNotification" with pathDestinationFolder.startsWith(pathNodeMoved)', () => {
      eventMock.data = [{ name: 'nameFileMock' }];
      fileManagerService.findPath = jest
        .fn()
        .mockReturnValueOnce('Documents1\\nameFileMock\\fileChildMock')
        .mockReturnValueOnce('Documents1');
      component.onCellDragStop(eventMock);
      expect(eventMock.cancel).toBe(true);
    });

    it('should call "moveFilesInGrid" with pathDestinationFolder.startsWith(pathNodeMoved)', () => {
      component.onCellDragStop(eventMock);
      const spyMoveFilesInGrid = jest.spyOn(component, 'moveFilesInGrid');
      expect(spyMoveFilesInGrid).toBeCalledWith(
        fileManagerService.selectedNode,
        { resource: { right: DataBaseRights.DbrStructure } },
        eventMock.data
      );
    });

    it('should not call "moveFilesInGrid" with !isGridUpdatable', () => {
      component.isGridUpdatable = false;
      component.onCellDragStop(eventMock);
      const spyMoveFilesInGrid = jest.spyOn(component, 'moveFilesInGrid');
      expect(spyMoveFilesInGrid).not.toBeCalled();
    });

    it('should assign "true" to event.cancel with dbrNone', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrNone } });
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;

      component.onCellDragStop(eventMock);
      expect(eventMock.cancel).toBe(true);
    });

    it('should call "createNotification" with dbrNone', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrNone } });
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;

      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onCellDragStop(eventMock);
      expect(spyCreateNotification).toBeCalledWith(
        'error',
        'admins.resources.noRightsToMoveItem',
        false,
        8000
      );
    });
  });
});
