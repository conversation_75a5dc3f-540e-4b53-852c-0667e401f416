import { A11yModule } from '@angular/cdk/a11y';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  ContentChild,
  ContentChildren,
  ElementRef,
  EventEmitter,
  HostListener,
  Inject,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Optional,
  Output,
  QueryList,
  Renderer2,
  SimpleChange,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { MatBadgeModule } from '@angular/material/badge';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatPaginator, MatPaginatorIntl, MatPaginatorModule } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import {
  MatColumnDef,
  MatTable,
  MatTableDataSource,
  MatTableModule,
} from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ConceptType, TxObjectTypeType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import {
  LocaleService,
  TxEscapeHtmlPipe,
  TxHighlightSearchPipe,
} from '@bassetti-group/tx-web-core/src/lib/utilities';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { intersection, isEmpty, memoizeWith, uniqWith } from 'ramda';
import { Observable, of } from 'rxjs';
import { PaginatedTableDataSource } from './data-source/paginated-table-data-source';
import { PAGINATION_END_POINT, PaginationEndpointFn } from './data-source/pagination';
import { INITIAL_SORT_TOKEN, Sort } from './data-source/sort.util';
import { TxCellPrefixTemplate } from './directives/cell-prefix.directive';
import { TxGridColumnTemplate } from './directives/grid-column-template.directive';
import { TxGridGroupTemplate } from './directives/grid-group.directive';
import { TxGridToolbarTemplate } from './directives/grid-header.directive';
import {
  GridFilter,
  TxGridFilterMenuComponent,
} from './grid-filter-menu/grid-filter-menu.component';
import { TxGridDataType, TxGridEditType, TxGroupByValue } from './grid.const';
import { GridDirective } from './grid.directive';
import {
  FilterOperators,
  FilterValues,
  TxDragOption,
  TxGridColumn,
  TxGridDoubleClickArgs,
  TxGridFilterOnData,
  TxGridRowSelectArgs,
  compareTxGridColumn,
} from './grid.interface';
import { getNestedProperty } from './grid.utils';
import { InputSearchComponent, InputSearchEventInfo } from './input-search/input-search.component';
import { CustomPaginatorService } from './paginator/custom-paginator.service';
import { FetchNestedProperty } from './pipes/fetch-nested-property.pipe';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { FormsModule } from '@angular/forms';
import { GridCellClass } from './pipes/grid-cell-class';

type RenderedColumn<T> = keyof T | 'tree-control' | 'select' | 'drag-drop' | 'delete';
@Component({
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatCheckboxModule,
    DragDropModule,
    MatSortModule,
    FontAwesomeModule,
    TranslateModule,
    TxGridFilterMenuComponent,
    MatPaginatorModule,
    InputSearchComponent,
    MatTooltipModule,
    A11yModule,
    MatBadgeModule,
    MatSelectModule,
    FetchNestedProperty,
    MatAutocompleteModule,
    FormsModule,
    GridCellClass,
    TxEscapeHtmlPipe,
    TxHighlightSearchPipe,
  ],
  providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorService }],
  selector: 'tx-grid',
  templateUrl: './grid.component.html',
  styleUrls: ['./grid.component.scss'],
})
export class TxGridComponent<T extends {}>
  extends GridDirective<T>
  implements OnChanges, OnInit, AfterViewInit, OnDestroy
{
  @Input() set groupByField(value: keyof T) {
    this._groupByField = value;
    this.groupDataByField(value);
  }
  /**
   * This input should never use when data input is used.
   */
  @Input() set parentDataSource(value: PaginatedTableDataSource<T> | undefined) {
    if (value !== undefined) {
      this.dataSource = value;
    }
  }
  /**
   * Input property representing the data source for the grid.
   */
  @Input() set data(value: Observable<T[]> | T[]) {
    if (!!value && !this.dataSource && !this.paginationEndPoint) {
      const data = value instanceof Observable ? value : of(value);
      this.dataSource = new PaginatedTableDataSource<T>(
        data,
        this.primaryKey,
        undefined,
        undefined,
        this.pageSize
      );
      this.disableCheckSelectionForEmptyData();
      this.memoIsAllSelected = memoizeWith((nbSelected) => nbSelected + '', this.isAllSelected);
    } else if (!!value && this.dataSource) {
      if (!this.dataSource.primaryKey && !!this.primaryKey) {
        this.dataSource.primaryKey = this.primaryKey;
      }
      this.dataSource.addData(value);
      this.disableCheckSelectionForEmptyData();
      this.memoIsAllSelected = memoizeWith((nbSelected) => nbSelected + '', this.isAllSelected);
    }

    if (this.groupByField) {
      this.groupDataByField(this.groupByField);
    }

    this.applyInitialSort(this.dataSource, this.renderedColumns, this.initSortDirection);
  }
  /**
   * Input property to control whether row drag-and-drop functionality is enabled.
   */
  @Input() allowRowDragAndDrop = false;
  @Input() filterColumns: TxGridColumn<T>[] | undefined;
  @Input({ required: true }) primaryKey: undefined | keyof T;
  /**
   * Input property to customize the placeholder text in the column filter input.
   * Default value is 'Search'.
   */
  @Input() inputPlaceholder?: string;
  @Input() inputValue: string = '';

  /**
   * Input property to enable or disable row selection in the grid.
   * If set to true, users can select rows in the grid.
   */
  @Input() isRowSelectable = false;

  @Input() enableMultiSelect: boolean = false;
  /**
   * Input property to resize columns in the grid.
   * If set to true, users can resize columns in the grid.
   */
  @Input() enableColumnResize: boolean = false;

  /**
   * Input property to enable or disable the Search filtering feature in the grid.
   */
  @Input() enableSearching = false;
  /**
   * Input property to enable or disable the Column filtering feature in the grid.
   */
  @Input() enableFiltering: boolean = false;
  /**
   * Input property to allow double click edit feature in the grid.
   */
  @Input() allowEditing: boolean = false;

  @Input() allowEditOnDblClick: boolean = false;
  @Input() allowCellEditOnDblClick: boolean = false;
  @Input() allowAutoCellEditing: boolean = false;
  /**
   * Input property to disable editing cell in click other cell in same row.
   */
  @Input() disableEditOnCellClick: boolean = false;
  @Input() allowAutoRowSelect: boolean = false;

  @Input() filterOptions: {
    column: string;
    options: { value: ConceptType; text: string }[];
    hideFilterType: boolean;
    filterType: string;
    settings?: {
      types: TxObjectTypeType[];
      onlyVisible: boolean;
    };
  }[] = [];

  /**
   * Input to set new filters.
   * The existing filters will be replaced.
   */
  @Input() newFilters?: GridFilter[];

  /**
   * Input property to display a message when there are no rows to display.
   * syncFusion.grid.EmptyRecord by default.
   */
  @Input() emptyRecordMessage: string = _('txWebCore.syncFusion.grid.EmptyRecord');

  @Input() enablePagination = false;

  @Input() rightClickSection = false;

  @Input() dragOption: TxDragOption = {};

  @Input() droppedTargetContainer?: any;

  @Input() pageSize: number = 100;
  /**
   * Search input IsFiltered
   */
  @Input() searchInputIsFiltered: boolean = false;

  @Input() disableBindSelectReset: boolean = false;
  @Input() disableSearchDataFilter: boolean = false;
  @Input() set disableInitialSort(value: boolean) {
    if (this.dataSource) {
      this.dataSource.disableInitialSort = value;
    }
  }

  @Input() disableSelectionHandling = false;

  @Input() displayedColumns: RenderedColumn<T>[] = [];
  @Input() highlightRowsPK: T[keyof T][] = [];

  @Input() autoGrowColumns: boolean = false;
  @Input() skipResetDblClick: boolean = false;

  /**
   * Output event emitter triggered when a row is selected in the grid.
   * Emits the selected row data.
   */
  @Output() rowSelected: EventEmitter<TxGridRowSelectArgs<T>> = new EventEmitter();

  /**
   * Output event emitter triggered when a row is deselected in the grid.
   * Emits the deselected row data.
   */
  @Output() rowDeselected: EventEmitter<TxGridRowSelectArgs<T>> = new EventEmitter();

  /**
   * Triggers when data source is populated in the Grid.
   */
  @Output() dataBound: EventEmitter<void> = new EventEmitter();
  @Output() actionBegin: EventEmitter<object> = new EventEmitter();
  @Output() rowDrop: EventEmitter<object> = new EventEmitter();
  @Output() actionComplete: EventEmitter<object> = new EventEmitter();
  @Output() updateCountCaption: EventEmitter<object> = new EventEmitter();
  @Output() deleteFilter: EventEmitter<string> = new EventEmitter();
  @Output() deleteAllFilters: EventEmitter<void> = new EventEmitter();
  @Output() searchInputChange: EventEmitter<InputSearchEventInfo> = new EventEmitter();
  @Output() searchClear: EventEmitter<void> = new EventEmitter();
  @Output() filterChange: EventEmitter<GridFilter | string> = new EventEmitter();
  @Output() rowDoubleClick: EventEmitter<TxGridDoubleClickArgs<T>> = new EventEmitter();
  @Output() rowDragStart: EventEmitter<T | T[]> = new EventEmitter();
  @Output() rowDrag: EventEmitter<T> = new EventEmitter();
  @Output() ready: EventEmitter<TxGridComponent<T>> = new EventEmitter();
  @Output() rowKeyUp: EventEmitter<Event> = new EventEmitter();
  @Output() columnResize: EventEmitter<Event> = new EventEmitter();

  @ViewChild('table', { static: true }) table!: MatTable<T> & { _elementRef: ElementRef };
  @ViewChild(TxGridFilterMenuComponent) gridFilter?: TxGridFilterMenuComponent;
  @ViewChild(InputSearchComponent) gridSearch?: InputSearchComponent;
  @ViewChild(MatSort) set sort(value: MatSort | undefined) {
    if (value) {
      if (this.dataSource) this.dataSource.matSort = value;
    }
  }
  @ViewChild(MatPaginator) set paginator(value: MatPaginator | undefined) {
    if (value) {
      this.dataSource.matPaginator = value;
    }
  }
  get columnDefinitions() {
    return this._columnDefinitions;
  }
  @ContentChildren(TxGridColumnTemplate) set columnDefinitions(
    value: QueryList<TxGridColumnTemplate> | undefined
  ) {
    if (value !== undefined) {
      this._columnDefinitions = value;
      if (this._memorizeColumnTemplate !== null) {
        this._memorizeColumnTemplate = this.updateMemorizeColumnTemplate(this._columnDefinitions);
      }
    }
  }
  private _columnDefinitions: QueryList<TxGridColumnTemplate> | undefined;
  headerTest!: TxGridToolbarTemplate;
  @ContentChild(TxGridToolbarTemplate) headerTemplate!: TxGridToolbarTemplate;
  @ContentChild(TxGridGroupTemplate) groupTemplate!: TxGridGroupTemplate;
  @ContentChild(TxCellPrefixTemplate) cellPrefixTemplate!: TxCellPrefixTemplate;
  filterModule: boolean = true;
  @ContentChild(MatColumnDef) columnDef: MatColumnDef | undefined;
  memoIsAllSelected: (nbSelected: number) => boolean = (nbSelected: number) => false;
  public get element(): HTMLElement {
    return this.elementRef.nativeElement;
  }
  public get groupByField(): keyof T | undefined {
    return this._groupByField;
  }
  public get searchableKeys(): (keyof T)[] {
    return this._searchableKeys;
  }
  public getSelectedRows(): (T | number | string)[] {
    return this.dataSource?.selection.selected ?? [];
  }

  public dragDisabled = true;
  public filterValues: FilterValues<T> = <FilterValues<T>>{};
  public filterOperators: FilterOperators<T> = <FilterOperators<T>>{};
  public checkFieldMuted: undefined | keyof T;
  public selectedRowPKValue: T[keyof T] | undefined;
  public localeDateFormat: string = '';
  public DataType = TxGridDataType;
  public searchById: number | undefined;
  public filterValue: FilterValues<T> | null = null;
  public filterOpt: FilterOperators<T> | null = null;
  public editingElement?: T & { isEditActive?: boolean };
  public cellEditColumn?: keyof T;
  public dragElement?: T;
  public searchValue: string = '';
  public searchedById?: number;
  renderedColumns: (keyof T | 'tree-control' | 'select' | 'drag-drop' | 'delete')[] = [];

  private _selectedRow?: T & { isEditActive?: boolean };
  private _isClickInRow: boolean = false;
  private _selectedRowsIndex: number[] = [];
  private _searchableKeys: (keyof T)[] = [];
  private _isResizePressed = false;
  private _currentResizeIndex!: number;
  private _startX!: number;
  private _startWidth!: number;
  private _groupByField?: keyof T;
  private resizableMousemove!: () => void;
  private resizableMouseup!: () => void;
  public readonly TxGridEditType = TxGridEditType;

  private _memorizeColumnTemplate: { [key: string]: { [key: string]: TemplateRef<any> } } | null =
    null;
  public get columnTemplates(): {
    [key: string]: { [key: string]: TemplateRef<any> };
  } {
    if (this._memorizeColumnTemplate !== null) {
      return this._memorizeColumnTemplate;
    }
    if (this.columnDefinitions !== undefined) {
      this._memorizeColumnTemplate = this.updateMemorizeColumnTemplate(this.columnDefinitions);
    } else {
      this._memorizeColumnTemplate = { columns: {}, headers: {} };
    }
    return this._memorizeColumnTemplate;
  }

  constructor(
    private readonly localeService: LocaleService,
    private readonly renderer: Renderer2,
    private readonly elementRef: ElementRef,
    @Optional() public dataSource: PaginatedTableDataSource<T>,
    @Optional()
    @Inject(PAGINATION_END_POINT)
    private readonly paginationEndPoint: PaginationEndpointFn<T>,
    @Optional()
    @Inject(INITIAL_SORT_TOKEN)
    initialSort: Sort<T>
  ) {
    super();
    if (!dataSource && paginationEndPoint) {
      this.dataSource = new PaginatedTableDataSource<T>(
        paginationEndPoint,
        undefined,
        initialSort,
        undefined,
        this.pageSize
      );
      this.memoIsAllSelected = memoizeWith((nbSelected) => nbSelected + '', this.isAllSelected);
    }
  }

  ngOnInit(): void {
    this.localeDateFormat = this.localeService.getDateFormat();
    this.ready.emit(this);
    if (this.enableMultiSelect && !this.renderedColumns.includes('select')) {
      this.renderedColumns = this.insertSelectColumn(this.renderedColumns);
    }
    if (this.groupByField !== undefined) {
      this.groupDataByField(this.groupByField);
    }
    this.filterColumns = this.filterColumns ?? this.columns;
    this.applyInitialSort(this.dataSource, this.displayedColumns);
    this.rowHeight = typeof this.rowHeight === 'number' ? this.rowHeight + 'px' : this.rowHeight;
  }

  ngAfterContentInit(): void {
    if (this.columnDef) {
      this.table?.addColumnDef(this.columnDef);
    }
  }
  ngOnDestroy(): void {
    this.resetEdit();
  }
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.onDataBind();
    }, 0);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.columns?.currentValue) {
      this.columns = uniqWith(compareTxGridColumn, this.columns).map((column) => {
        return column.isSearchable === undefined ? { ...column, isSearchable: true } : column;
      });
      this.renderedColumns = [
        this.displayedColumns.length === 0
          ? this.columns
              .filter((column) => column.visible === undefined || column.visible)
              .map((column) => column.field)
          : this.displayedColumns,
      ].flat();
      this.checkFieldMuted = this.columns.find((column) => column.checkMuted)?.field;
      if (this.enableMultiSelect && this.dataSource) {
        this.disableCheckSelectionForEmptyData();
      }

      if (this.allowRowDragAndDrop) {
        this.renderedColumns = ['drag-drop', ...this.renderedColumns];
      }
      this._searchableKeys = (changes.columns.currentValue as TxGridColumn<T>[])
        .filter((column) => column.isSearchable)
        .map((column) => column.field);
    }
    if (
      changes.displayedColumns?.currentValue &&
      this.allowRowDragAndDrop &&
      this.renderedColumns[0] !== 'drag-drop'
    ) {
      this.renderedColumns = ['drag-drop', ...this.renderedColumns];
    }
    if (
      changes.allowRowDragAndDrop?.currentValue === true &&
      this.renderedColumns[0] !== 'drag-drop'
    ) {
      this.renderedColumns = ['drag-drop', ...this.renderedColumns];
    }
    if (changes.pageSize?.currentValue !== changes.pageSize?.previousValue && this.dataSource) {
      this.dataSource.pageSize = changes.pageSize?.currentValue;
    }
    if (changes.primaryKey?.currentValue && this.dataSource) {
      this.dataSource.primaryKey = changes.primaryKey?.currentValue;
    }
    this.multiSelectChange(changes.enableMultiSelect);
  }

  removeGroups(dataSource: T[]): T[] {
    return dataSource.filter((data: T & { isTxGroup?: boolean }) => !data.isTxGroup);
  }
  isGroup(index: number, item: any): boolean {
    return item.isTxGroup;
  }

  groupHeaderClick(row: any) {
    if (row.expanded) {
      this.dataSource.groupRowValue = this.dataSource.groupRowValue.filter(
        (item) => item !== row[TxGroupByValue]
      );
    } else {
      this.dataSource.groupRowValue.push(row[TxGroupByValue]);
    }
    row.expanded = !row.expanded;
    const filterData: GridFilter = {
      column: {
        field: TxGroupByValue,
      },
      operator: 'equal',
      value: this.dataSource.groupRowValue,
    };
    this.dataSource.filterBy(filterData, true);
  }

  onDataBind() {
    if (
      this.primaryKey &&
      !this.dataSource?.dataList?.find(
        (row: any) => row[this.primaryKey ?? ''] === this.selectedRowPKValue
      ) &&
      !this.disableBindSelectReset
    ) {
      this._selectedRow = undefined;
      this.dataSource?.selection.clear();
      this.selectedRowPKValue = undefined;
    }
    this.dataBound.emit();
    this.updateCountCaption.emit();
  }
  onDragStart(row: T) {
    this.dragDisabled = false;
    this.dragElement = row;
    if (this.dragOption.multiple && this.dataSource.getSelectedElements().length) {
      if (!this.dataSource.selection.isSelected(row)) {
        this.dataSource.select(row);
      }
      this.rowDragStart.emit(this.getSelectedRows() as T[]);
    } else {
      this.rowDragStart.emit(row);
    }
  }
  onDragStopped() {
    this.dragDisabled = true;
    this.dragElement = undefined;
  }

  /**
   * Event handler for dropping a table row during drag-and-drop.
   * Rearranges the data array based on the drop event.
   *
   * @param event - Drag-and-drop event containing information about the drop.
   * @memberof TxGridComponent
   */
  dropTableRow(event: CdkDragDrop<MatTableDataSource<any>>): void {
    this.dragDisabled = true;
    this.dragElement = undefined;

    let tableArray = this.dataSource.dataList;
    const previousIndex = tableArray?.findIndex((d) => d === event.item.data);
    this.rowDrop.emit({
      dropIndex: event.currentIndex,
      fromIndex: previousIndex,
      data: this.dataSource.getSelectedElements().length
        ? this.dataSource.getSelectedElements()
        : [tableArray[previousIndex]],
      target: event.item.element.nativeElement,
    });
    if (!this.dragOption.blockTransformation) {
      const droppedData = this.dataSource.dataList;
      moveItemInArray(droppedData, event.previousIndex, event.currentIndex);
      this.dataSource.addData(droppedData);
    }
  }

  onRowSelected(
    row: T & { isEditActive?: boolean },
    event: Event | undefined,
    rowIndex: number
  ): void {
    if (this.disableSelectionHandling) {
      this.rowSelected.emit({
        data: [row],
        event: event,
        rowIndex: rowIndex,
      });
      return;
    }
    this._isClickInRow = true;
    this.dataSource.selection.clear();
    if (this.primaryKey === undefined) throw new Error('Please define a primary key columnsData');
    if (this.selectedRowPKValue === row[this.primaryKey]) {
      this.selectedRowPKValue = undefined;
      this.rowDeselected.emit({
        data: [row],
        event: event,
        rowIndex: rowIndex,
      });
    } else {
      if ((this.allowEditOnDblClick || this.allowCellEditOnDblClick) && row.isEditActive) return;
      this.selectedRowPKValue = row[this.primaryKey];
      let selected = this.dataSource.dataList.find(
        (row: any) => row[this.primaryKey ?? ''] == this.selectedRowPKValue
      );
      if (selected) this.dataSource.select(selected);
      this.rowSelected.emit({
        data: [row],
        event: event,
        rowIndex: rowIndex,
      });
    }
  }

  selectRowByPK(primaryKeyData: T[keyof T]): void {
    this.dataSource.selection.clear();
    const rowIndex = this.dataSource.dataList.findIndex(
      (row: T) => row[this.primaryKey as keyof T] == primaryKeyData
    );
    if (rowIndex >= 0) {
      this.selectedRowPKValue = this.dataSource.dataList[rowIndex][this.primaryKey as keyof T];
      this.dataSource.select(this.dataSource.dataList[rowIndex]);
    }
  }

  trackByTable(index: number, row: T): T[keyof T] | T {
    return this.primaryKey ? row[this.primaryKey] : row;
  }

  selectRowsByPK(primaryKeyData: T[keyof T]) {
    this.selectedRowPKValue = primaryKeyData;
    let selected = this.dataSource.dataList.find(
      (row: any) => row[this.primaryKey ?? ''] == this.selectedRowPKValue
    );
    if (selected) this.dataSource.select(selected);
  }

  isAllSelected(nbSelected: number): boolean {
    return (
      nbSelected ===
      this.dataSource.dataList.filter((row: T & { isTxGroup?: boolean }) => !row.isTxGroup).length
    );
  }
  checkBoxSelect(row: T, event: Event, rowIndex: number): void {
    this.dataSource.selection.toggle(row);
    if (this.dataSource.selection.isSelected(row)) {
      this.selectedRowPKValue = this.primaryKey ? row[this.primaryKey] : undefined;
      this._selectedRowsIndex = [...this._selectedRowsIndex, rowIndex];
      this.rowSelected.emit({
        data: [row],
        event: event,
        rowIndex: rowIndex,
        rowIndexes: this._selectedRowsIndex,
      });
    } else {
      this.selectedRowPKValue = undefined;
      this._selectedRowsIndex = this._selectedRowsIndex.filter((value) => value !== rowIndex);
      this.rowDeselected.emit({
        data: [row],
        event: event,
        rowIndex: rowIndex,
        rowIndexes: this._selectedRowsIndex,
      });
    }
  }

  masterToggle(event: Event): void {
    if (this.isAllSelected(this.dataSource.getSelectedElements().length)) {
      this.clearSelection();
      this._selectedRowsIndex = [];
      this.rowDeselected.emit({
        data: this.dataSource.getSelectedElements() as T[],
        event: event,
        rowIndex: 0,
        rowIndexes: this._selectedRowsIndex,
      });
    } else {
      this._selectedRowsIndex = this.dataSource.dataList.map(
        (row: T & { isTxGroup?: boolean }, index) => {
          if (!row.isTxGroup) this.dataSource.selection.select(row);
          return index;
        }
      );
      this.rowSelected.emit({
        data: this.dataSource.getSelectedElements() as T[],
        event: event,
        rowIndex: 0,
        rowIndexes: this._selectedRowsIndex,
      });
    }
  }
  clearSelection() {
    this.dataSource.selection.clear();
  }

  getRowIndexByPrimaryKey(value: T[keyof T] | undefined): number {
    const primaryKey = this.primaryKey;
    if (primaryKey !== undefined) {
      return this.dataSource.dataList.findIndex((row) => row[primaryKey] === value);
    } else {
      return -1;
    }
  }
  enableEditing(row: T, event: Event, rowIndex: number) {
    const element = event.target as HTMLElement;
    element.closest('.mat-mdc-cell')?.classList.remove('warning-cell');
    const rowEle = element.closest('mat-row');
    if (event.type === 'keyup' && rowEle) {
      rowEle.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    this._selectedRow = row;
    this._selectedRow.isEditActive = true;
    this.editingElement = { ...this._selectedRow };
    if (!this.skipResetDblClick) {
      this.dataSource.selection.clear();
    }

    this.selectedRowPKValue = undefined;
    this.rowDeselected.emit({
      data: [row],
      event: event,
      rowIndex: rowIndex,
    });
  }
  disableEditing() {
    if (!this.editingElement) return;
    if (this._selectedRow) this._selectedRow.isEditActive = false;
    let emitData = {
      requestType: 'save',
      previousData: { ...this._selectedRow },
      data: this.editingElement,
      action: 'edit',
    };
    delete emitData.previousData.isEditActive;
    this.actionComplete.emit(emitData);
    this.editingElement = undefined;
  }

  resetEdit() {
    if (!this.editingElement) return;
    if (this._selectedRow) this._selectedRow.isEditActive = false;
    this.editingElement = undefined;
  }
  disableCellEditing(event: Event) {
    if (this._selectedRow) this._selectedRow.isEditActive = false;
    if (this.editingElement && this.primaryKey && this.cellEditColumn) {
      let emitData = {
        requestType: 'save',
        columnName: this.cellEditColumn,
        value: getNestedProperty(this.editingElement, this.cellEditColumn as string) as string,
        previousValue: getNestedProperty(
          this._selectedRow as T,
          this.cellEditColumn as string
        ) as string,
        rowData: { ...this._selectedRow } as T & { isEditActive?: boolean },
      };
      delete emitData.rowData.isEditActive;
      this.actionCellComplete.emit(emitData);
      if ((event as KeyboardEvent).key !== 'Enter') {
        this.selectedRowPKValue = undefined;
      } else {
        this.selectedRowPKValue = this.editingElement[this.primaryKey];
      }
      this.editingElement = undefined;
    }
  }

  cancelCellEditing() {
    if (this._selectedRow) delete this._selectedRow.isEditActive;
    this.editingElement = undefined;
  }

  onBlur() {
    if (this.editingElement) {
      this.disableEditing();

      if (!this._isClickInRow) {
        this.dataSource.selection.clear();
        this.selectedRowPKValue = undefined;
      }
      this._isClickInRow = false;
    }
  }
  getSelectedRowIndex() {
    return this.getRowIndexByPrimaryKey(this.selectedRowPKValue);
  }

  selectRows(indexes: number[]): void {
    if (this.isRowSelectable && this.primaryKey) {
      this.selectedRowPKValue = this.dataSource.dataList[indexes[0]][this.primaryKey];
    }
    this.clearSelection();
    indexes = Array.from(new Set(indexes));
    if (isEmpty(this.dataSource.dataList)) {
      return;
    }
    indexes.forEach((index) => this.dataSource.select(this.dataSource.dataList[index]));
  }
  getColumnByField(field: string): TxGridColumn<T> | undefined {
    return this.columns.find((column) => column.field === field);
  }

  filterBy(event: GridFilter | string, forGroup?: boolean): void {
    const filter = typeof event === 'string' ? this.getSearchFilterValue(event) : event;
    if (this.groupByField) {
      this.dataSource.removeFilteredColsByField(TxGroupByValue, true);
    }
    if (!this.disableSearchDataFilter) {
      this.dataSource.filterBy(filter, forGroup);
    }
    this.filterChange.emit(event);
  }

  searchItem(inputSearchEventInfo: InputSearchEventInfo): void {
    this.searchInputChange.emit(inputSearchEventInfo);
    this.searchValue = inputSearchEventInfo.inputSearch.nativeElement.value;
  }

  removeSearchItem(): void {
    this.searchClear.emit();
    this.searchValue = '';
  }

  onDeleteFilter(field: string): void {
    if (this.groupByField) {
      this.dataSource.removeFilteredColsByField(TxGroupByValue, true);
    }
    this.dataSource.removeFilteredColsByField(field);
  }
  onDeleteAllFilters(): void {
    this.dataSource.filterBy(null);
  }
  onCellInput(event: Event, field: string) {
    const inputElement = event.target as HTMLInputElement;
    this.editElement(inputElement.value, field);
  }
  onCellChange(value: string, field: string) {
    this.editElement(value, field);
  }
  startEdit(): void {
    this._selectedRow = this.dataSource.dataList.find(
      (row: any) =>
        row[this.primaryKey ?? ''] ==
        (this.getSelectedRows().length && this.primaryKey
          ? (this.getSelectedRows() as T[])[0][this.primaryKey]
          : null)
    ) as T;
    this._selectedRow.isEditActive = true;
    this.editingElement = { ...this._selectedRow };
    this.dataSource.selection.clear();
  }

  handleDoubleClick(row: T, event: Event, rowIndex: number) {
    this.rowDoubleClick.emit({ rowData: row, rowIndex, event });
    if (this.allowEditOnDblClick) {
      this.enableEditing(row, event, rowIndex);
    }
  }
  handleCellEdit(
    element: T & { isEditActive?: boolean },
    event: Event,
    rowIndex: number,
    column?: TxGridColumn<T>
  ) {
    this.enableEditing(element, event, rowIndex);
    this.cellEditColumn = column?.field;
    this.cellEdit.emit({
      type: 'edit',
      columnName: column?.field as string,
      rowData: element,
      event,
      rowIndex,
    });
  }

  onCellDblClick(
    element: T & { isEditActive?: boolean },
    event: Event,
    rowIndex: number,
    column?: TxGridColumn<T>
  ) {
    if (this.allowCellEditOnDblClick && column?.editable) {
      this.handleCellEdit(element, event, rowIndex, column);
    }
  }
  toggleEditCheckBox(event: any) {
    if (this.editingElement && this.cellEditColumn) {
      this.editingElement[this.cellEditColumn] = event?.checked;
    }
  }
  changeSelection(event: any) {
    if (this.editingElement && this.cellEditColumn) {
      this.editingElement[this.cellEditColumn] = event?.value;
      this.disableCellEditing(event);
    }
  }

  handleContextmenu(row: { [key: string]: any }, event: Event, rowIndex: number) {
    if (this.rightClickSection && this.enableMultiSelect) {
      event.preventDefault();
      this.clearSelection();
      this._selectedRowsIndex = [];
      if (!this.dataSource.selection.isSelected(row as T)) this._selectedRowsIndex.push(rowIndex);
      this.dataSource.select(row as T);
    }
  }

  searchByTextSelect(input: string, domSearch?: boolean, disableEvent?: boolean) {
    if (!this.primaryKey) {
      throw new Error('Primary Key Column is not defined');
    }
    const lowerCaseInput = input.toLowerCase();
    let result: any;
    if (domSearch) {
      const gridData = this.dataSource.parseData();
      result = this.dataSource.searchByDOM(
        gridData,
        lowerCaseInput,
        this.selectedRowPKValue,
        this.element
      );
      this.selectedRowPKValue = result.selectedPK;
      if (result.selected && !disableEvent) {
        this.rowSelected.emit({
          data: [result.selected],
          event: new Event('Search'),
          rowIndex: result.rowIndex,
        });
      }
    } else {
      result = this.dataSource.searchByData(
        lowerCaseInput,
        this.selectedRowPKValue,
        this._searchableKeys
      );
      this.selectedRowPKValue = result.selectedPK;
      if (result.selected && !disableEvent) {
        this.rowSelected.emit({
          data: [result.selected],
          event: new Event('Search'),
          rowIndex: result.rowIndex,
        });
      }
    }
    setTimeout(() => {
      const nextRow = this.element.querySelector(`mat-row[data-id="${result.selectedPK}"]`);
      nextRow?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
      });
    }, 0);
    return result;
  }

  onResizeColumn(event: any, index: number) {
    this._currentResizeIndex = index;
    this._isResizePressed = true;
    this._startX = event.pageX;
    this._startWidth = this.extractWidth(this.columns[index]);
    event.preventDefault();
    this.mouseMove(index);
  }

  mouseMove(index: number) {
    this.resizableMousemove = this.renderer.listen('document', 'mousemove', (event) => {
      if (this._isResizePressed && event.buttons) {
        const dx = event.pageX - this._startX;
        const width = this._startWidth + dx;
        if (this._currentResizeIndex === index && width > 50) {
          this.setColumnWidthChanges(index, width);
          this.columnResize.emit(event);
        }
      }
    });
    this.resizableMouseup = this.renderer.listen('document', 'mouseup', (event) => {
      if (this._isResizePressed) {
        this._isResizePressed = false;
        this._currentResizeIndex = -1;
        this.resizableMousemove();
        this.resizableMouseup();
      }
    });
  }

  setColumnWidthChanges(index: number, width: number) {
    const orgWidth = this.extractWidth(this.columns[index]);
    const dx = width - orgWidth;

    // Find the next visible column
    let nextIndex = index + 1;
    while (
      nextIndex < this.columns.length &&
      !(this.columns[nextIndex].visible || this.columns[nextIndex].visible == undefined)
    ) {
      nextIndex++;
    }

    // Check if a visible column was found also
    if (dx !== 0 && nextIndex < this.columns.length) {
      const newWidth = this.extractWidth(this.columns[nextIndex]) - dx;
      if (this.autoGrowColumns) {
        this.columns[index].width = width + 'px';
      } else if (newWidth > 50) {
        this.columns[index].width = width + 'px';
        this.columns[nextIndex].width = newWidth + 'px';
      }
    }
  }

  extractWidth(column: TxGridColumn<T>) {
    let columnCells = this.table._elementRef.nativeElement.getElementsByClassName(
      'mat-column-' + (column.field as string)
    );
    if (columnCells.length > 0) {
      return (columnCells[0] as any).offsetWidth;
    }
  }
  @HostListener('document:mouseup', ['$event'])
  onMouseUp(event: PointerEvent) {
    this.dragDisabled = true;
    this.dragElement = undefined;
  }

  onRowClick(row: T & { isEditActive?: boolean }, event: Event, rowIndex: number) {
    event.stopPropagation();
    this.enableMultiSelect
      ? this.checkBoxSelect(row, event, rowIndex)
      : this.isRowSelectable && this.onRowSelected(row, event, rowIndex);
    if (this.allowCellEditOnDblClick) return;
    if (
      this._selectedRow &&
      this._selectedRow[this.primaryKey as keyof T] !== row[this.primaryKey as keyof T]
    )
      this.onBlur();
  }

  onTableClick(event: Event) {
    event.stopPropagation();
    if (this.allowCellEditOnDblClick) this.onCellBlur(event);
    else this.onBlur();
  }
  setNestedProperty(obj: T, path: string, value: string) {
    path = path.replace(/\[(\w+)\]/g, '.$1');
    path = path.replace(/^\./, '');
    const keys = path.split('.');

    keys.forEach((key, index) => {
      if (index === keys.length - 1) {
        obj[key as keyof T] = value as T[keyof T];
      } else {
        obj = obj[key as keyof T] as T;
      }
    });
  }
  onRowKeyUp(event: KeyboardEvent) {
    if (this.allowAutoRowSelect) {
      const idSelectedRow =
        this.selectedRowPKValue ??
        (this.primaryKey && this._selectedRow ? this._selectedRow[this.primaryKey] : undefined);

      const { rowIndex, nextRow, selectionList } = this.dataSource.prepareToSelection(
        this.dataSource.dataList,
        idSelectedRow
      );

      switch (event.key) {
        case 'Enter':
          this.onRowEnterKey(event, selectionList, nextRow, rowIndex);
          break;
        case 'Tab':
          this.onRowTabKey(event, selectionList, idSelectedRow, rowIndex);
          break;
      }

      this.rowKeyUp.emit(event);
    }
  }

  hideColumns(columnFields: Array<keyof T>) {
    columnFields.forEach((field) => {
      const index = this.renderedColumns.indexOf(field);
      if (index !== -1) this.renderedColumns.splice(index, 1);
    });
  }
  showColumns(columnFields: Array<keyof T>) {
    this.renderedColumns = Array.from(new Set(columnFields));
  }
  getRowClasses(row: T): string {
    const isSelectedRow =
      (!this.enableMultiSelect &&
        this.isRowSelectable &&
        this.primaryKey &&
        this.selectedRowPKValue === row[this.primaryKey]) ||
      (this.rightClickSection &&
        this.enableMultiSelect &&
        this.dataSource.selection.isSelected(row)) ||
      (this.primaryKey && this.highlightRowsPK.includes(row[this.primaryKey]));
    const isRowTemp = this.checkFieldMuted && row[this.checkFieldMuted];

    const isPlaceholderHidden = this.dragOption?.hidePlaceholder;

    const isDisplayHidden =
      this.dragOption?.multiple && !this.dragDisabled && this.dataSource.selection.isSelected(row);

    let rowClass = '';
    if (isSelectedRow) {
      rowClass = 'table-row-selected';
    } else if (this.isRowSelectable) {
      rowClass = 'table-row-hover';
    }

    return [
      rowClass,
      isRowTemp ? 'fm-row-isTemp' : '',
      isPlaceholderHidden ? 'hide-placeholder' : '',
      isDisplayHidden ? 'display-hide' : '',
    ]
      .filter(Boolean)
      .join(' ');
  }
  getColumnStyle(column: any) {
    if (!column.width) {
      return {};
    }
    return {
      flex: `0 0 ${column.width}`,
      'flex-grow': this.autoGrowColumns && !column.autoGrowDisable && !column.resize ? 1 : 0,
    };
  }
  handlerCellClick(
    event: Event,
    column?: TxGridColumn<T>,
    element?: T & { isEditActive?: boolean }
  ): void {
    if (this.allowCellEditOnDblClick) {
      this.onCellBlur(event, column, element);
    }
  }

  private onCellBlur(
    event: Event,
    column?: TxGridColumn<T>,
    element?: T & { isEditActive?: boolean }
  ) {
    if (
      this.disableEditOnCellClick &&
      column?.field === this.cellEditColumn &&
      this.editingElement &&
      element &&
      element[this.primaryKey as keyof T] === this.editingElement[this.primaryKey as keyof T]
    ) {
      return;
    }

    if (
      !this.disableEditOnCellClick &&
      this.editingElement &&
      element &&
      element[this.primaryKey as keyof T] === this.editingElement[this.primaryKey as keyof T]
    ) {
      return;
    }

    this.disableCellEditing(event);

    if (!this._isClickInRow && !this.skipResetDblClick) {
      this.dataSource.selection.clear();
      this.selectedRowPKValue = undefined;
    }
    this._isClickInRow = false;
  }

  private disableCheckSelectionForEmptyData() {
    this.displayedColumns = this.displayedColumns.filter((column) => column !== 'select');
    if (this.dataSource.dataList.length > 0 && this.enableMultiSelect) {
      this.displayedColumns = ['select', ...new Set(this.displayedColumns)];
    }
  }

  private groupDataByField(groupByField: keyof T) {
    if (this.dataSource?.dataList?.length > 0) {
      this.dataSource.removeFilteredColsByField(TxGroupByValue, true);
      this.dataSource.groupBy = groupByField;
      this.dataSource.updateData(this.dataSource.dataList); //_endPointChange not working when value change from undefine after refresh
    } else if (this.dataSource !== null) {
      this.dataSource.groupBy = groupByField;
    }
  }

  private getSearchFilterValue(event: string): TxGridFilterOnData<T> {
    const eventValue = parseInt(event);
    const searchableFields = this.appropriateSearchableFields(this._searchableKeys);
    return !isNaN(eventValue)
      ? {
          fields: searchableFields,
          conditions: this.toEqualConditions(searchableFields),
          value: event,
        }
      : { fields: this._searchableKeys, value: event };
  }
  private toEqualConditions(searchableKeys: (keyof T)[]): FilterOperators<T> | undefined {
    return searchableKeys.reduce((conditions, field) => {
      const definedConditions = conditions ?? ({} as FilterOperators<T>);
      return { ...definedConditions, [field]: 'equal' };
    }, undefined as FilterOperators<T> | undefined);
  }

  private appropriateSearchableFields(searchableFields: (keyof T)[]): (keyof T)[] {
    const numberTypeField = this.columns
      .filter((column) => column.type === 'number')
      .map((column) => column.field);
    const updatedSearchableFields = intersection(numberTypeField, searchableFields);
    return updatedSearchableFields;
  }
  private applyInitialSort(
    dataSource: PaginatedTableDataSource<T>,
    renderedColumns: RenderedColumn<T>[],
    direction: 'asc' | 'desc' = 'asc'
  ): void {
    if (this.columns && this.renderedColumns.length === 0) {
      renderedColumns = [
        this.displayedColumns.length === 0
          ? this.columns
              .filter((column) => column.visible === undefined || column.visible)
              .map((column) => column.field)
          : this.displayedColumns,
      ].flat();
    }
    const column = (renderedColumns as (keyof T)[]).filter(
      (column) =>
        column !== 'select' ||
        column !== 'drag-drop' ||
        column !== 'tree-control' ||
        column !== 'delete'
    )[0];
    if (dataSource.dataList.length > 0 && !dataSource.isSortApplied() && column !== undefined) {
      dataSource.sortBy({
        active: column,
        direction: direction,
      });
    }
  }

  private multiSelectChange(change: SimpleChange): void {
    if (change?.firstChange === true) {
      return;
    }
    const index = this.renderedColumns.indexOf('select');
    if (change?.currentValue === true && index === -1) {
      this.renderedColumns = this.insertSelectColumn(this.renderedColumns);
    } else if (change?.currentValue === false && index !== -1) {
      this.renderedColumns = this.renderedColumns.filter((columnField) => columnField !== 'select');
    }
  }

  private insertSelectColumn(renderedColumns: RenderedColumn<T>[]): RenderedColumn<T>[] {
    if (renderedColumns[0] === 'drag-drop') {
      const [dragDrop, ...columns] = renderedColumns;
      return [dragDrop, 'select', ...columns];
    }
    return ['select', ...renderedColumns];
  }
  private updateMemorizeColumnTemplate(columnDefinitions: QueryList<TxGridColumnTemplate>) {
    const columnTemplates: { [key: string]: TemplateRef<any> } = {};
    const headerTemplates: { [key: string]: TemplateRef<any> } = {};
    for (const columnDefinition of columnDefinitions.toArray()) {
      columnTemplates[columnDefinition.fieldName] = columnDefinition.columnTemplate;
      headerTemplates[columnDefinition.fieldName] = columnDefinition.headerTemplate;
    }
    return { columns: columnTemplates, headers: headerTemplates };
  }
  private onRowEnterKey(event: KeyboardEvent, selectionList: T[], nextRow: T, rowIndex: number) {
    if (this.allowAutoCellEditing && selectionList.length - 1 > rowIndex) {
      this.handleCellEdit(selectionList[rowIndex + 1], event, rowIndex + 1, {
        field: this.cellEditColumn,
      } as TxGridColumn<T>);
    }
    this.onRowSelected(nextRow, event, rowIndex + 1);
  }

  private onRowTabKey(
    event: KeyboardEvent,
    selectionList: T[],
    idSelectedRow: T[keyof T] | undefined,
    rowIndex: number
  ) {
    if (!this.allowCellEditOnDblClick && !this.allowEditOnDblClick) {
      return; // No edit allowed, exit early
    }

    const editableColumns = this.columns.filter(
      (col) => (col.visible === undefined || col.visible) && col.editable
    );
    if (editableColumns.length === 0) {
      return; // No editable columns, exit early
    }

    const nextCell = this.getNextEditableCell(event, selectionList, rowIndex, editableColumns);

    if (!nextCell) {
      event.preventDefault();
      return; // No next cell to edit, prevent default and exit
    }

    // cancel previous edition if needed
    this.onCellBlur(event, nextCell.column, nextCell.row);
    // edit new cell
    this.handleCellEdit(nextCell.row, event, nextCell.rowIndex, nextCell.column);
    if (this.primaryKey && idSelectedRow !== nextCell.row[this.primaryKey])
      this.onRowSelected(nextCell.row, event, nextCell.rowIndex);
    event.preventDefault();
  }
  private getNextEditableCell(
    event: KeyboardEvent,
    selectionList: T[],
    rowIndex: number,
    editableColumns: any[]
  ): { row: T; rowIndex: number; column: any } | null {
    let nextIndexEditableColumn = editableColumns.findIndex(
      (col) => col.field === this.cellEditColumn
    );

    if (nextIndexEditableColumn < 0) {
      // no cell in edition = edit first column of the current row
      nextIndexEditableColumn = 0;
    } else if (!event.shiftKey && nextIndexEditableColumn + 1 < editableColumns.length) {
      // edit the next column of the current row
      nextIndexEditableColumn++;
    } else if (event.shiftKey && nextIndexEditableColumn - 1 >= 0) {
      // edit the previous column of the current row
      nextIndexEditableColumn--;
    } else if (!event.shiftKey && rowIndex + 1 < selectionList.length) {
      // no more editable columns on current row = edit first column of the next row
      nextIndexEditableColumn = 0;
      rowIndex++;
    } else if (event.shiftKey && rowIndex - 1 >= 0) {
      // no more editable columns on current row = edit last column of the previous row
      nextIndexEditableColumn = editableColumns.length - 1;
      rowIndex--;
    } else {
      return null; // No next cell to edit
    }

    return {
      row: selectionList[rowIndex],
      rowIndex,
      column: editableColumns[nextIndexEditableColumn],
    };
  }

  private editElement(value: string, field: string) {
    if (this._selectedRow) {
      this.editingElement = { ...this._selectedRow };
      this.setNestedProperty(this.editingElement, field, value);
      this.editingElement.isEditActive = undefined;
    }
  }
}
