import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MOCK_TX_CONFIG_SERVICE, TxConfigService } from '../../config';
import { TxLinkTypesService } from '../link-types.service';
import { MockProvider } from 'ng-mocks';
import { MOCK_OBJECT_TYPE_ICON_SERVICE, TxObjectTypeIconService } from '../../session';

describe('Service: LinkTypes', () => {
  let service: TxLinkTypesService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        TxLinkTypesService,
        MockProvider(TxConfigService, MOCK_TX_CONFIG_SERVICE, 'useValue'),
        MockProvider(TxObjectTypeIconService, MOCK_OBJECT_TYPE_ICON_SERVICE, 'useValue'),
      ],
    });

    service = TestBed.inject(TxLinkTypesService);
    TestBed.inject(TxConfigService);
    TestBed.inject(HttpTestingController);
  });

  beforeEach(() => {});

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
