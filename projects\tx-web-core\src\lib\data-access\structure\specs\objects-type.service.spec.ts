import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import {
  TxLockingType,
  TxObjectTypeType,
  TxObjectType,
  DataBaseRights,
} from '../../../business-models';
import { TxObjectsTypeService } from '../objects-type.service';
import { TxConfigService, MOCK_TX_CONFIG_SERVICE } from '../../config';
import { MockProvider } from 'ng-mocks';
import { MOCK_OBJECT_TYPE_ICON_SERVICE, TxObjectTypeIconService } from '../../session';

describe('ObjectsTypeService', () => {
  const apiUrl = 'https://localhost:44336/';
  let service: TxObjectsTypeService;
  let http: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        Mo<PERSON><PERSON>rovider(TxConfigService, MOCK_TX_CONFIG_SERVICE, 'useValue'),
        MockProvider(TxObjectTypeIconService, MOCK_OBJECT_TYPE_ICON_SERVICE, 'useValue'),
      ],
    });
    service = TestBed.inject(TxObjectsTypeService);
    http = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    http.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Get Object Types', () => {
    let objectTypeTests: TxObjectType[];
    beforeEach(() => {
      objectTypeTests = [
        {
          id: 1,
          name: 'Contact',
          order: 1,
          type: TxObjectTypeType.Standard,
          isVisible: true,
          displayResultInTextSearch: true,
          hasDistinctName: true,
          icon: 1,
          isFolder: false,
          lockingDuration: 0,
          lockingType: TxLockingType.None,
          right: DataBaseRights.DbrStructure,
          tags: [],
        },
        {
          id: 2,
          name: 'Portal',
          order: 2,
          type: TxObjectTypeType.Portal,
          isVisible: true,
          displayResultInTextSearch: true,
          hasDistinctName: true,
          icon: 1,
          isFolder: false,
          lockingDuration: 0,
          lockingType: TxLockingType.None,
          right: DataBaseRights.DbrStructure,
          tags: [],
        },
      ];
    });

    it('should get object types for the first time', () => {
      let response: TxObjectType[] = [];
      service.listAll(false).subscribe((res: TxObjectType[]) => {
        response = res;
      });

      http.expectOne(`${apiUrl}api/Structure/objecttype`).flush(objectTypeTests);
      expect(response).toStrictEqual(objectTypeTests);
    });

    it('should get object types from previous request', () => {
      let response: TxObjectType[] = [];
      service.concepts = objectTypeTests;
      // eslint-disable-next-line @typescript-eslint/dot-notation
      service['reloadAll'] = false;
      service.listAll(false).subscribe((res: TxObjectType[]) => {
        response = res;
      });

      http.expectNone(`${apiUrl}api/Structure/objecttype`);
      expect(response).toStrictEqual(objectTypeTests);
    });

    it('should force update object types', () => {
      let response: TxObjectType[] = [];
      service.concepts = objectTypeTests;
      // eslint-disable-next-line @typescript-eslint/dot-notation
      service['reloadAll'] = false;
      service.listAll(true).subscribe((res: TxObjectType[]) => {
        response = res;
      });

      http.expectOne(`${apiUrl}api/Structure/objecttype`).flush(objectTypeTests);
      expect(response).toStrictEqual(objectTypeTests);
    });
  });
});
