import { NgModule } from '@angular/core';
import { TxColumnNameTemplateComponent } from './column-name-template/column-name-template.component';
import { TxGridColumnTemplate } from './directives/grid-column-template.directive';
import { PreventClickPropagation } from './directives/stop-click-propagation.directive';
import { TxGridFilterMenuComponent } from './grid-filter-menu/grid-filter-menu.component';
import { InputSearchComponent } from './input-search/input-search.component';
import { TxGridComponent } from './grid.component';
import { TxNotEmptyFilterPipe } from './grid-filter-menu/not-empty-filter.pipe';
import { TxGridToolbarTemplate } from './directives/grid-header.directive';
import { TxGridGroupTemplate } from './directives/grid-group.directive';
import { A11yModule } from '@angular/cdk/a11y';

@NgModule({
  imports: [
    TxColumnNameTemplateComponent,
    TxGridColumnTemplate,
    TxGridToolbarTemplate,
    TxGridGroupTemplate,
    PreventClickPropagation,
    TxGridFilterMenuComponent,
    InputSearchComponent,
    TxGridComponent,
    TxNotEmptyFilterPipe,
  ],
  exports: [
    TxColumnNameTemplateComponent,
    TxGridColumnTemplate,
    TxGridToolbarTemplate,
    TxGridGroupTemplate,
    PreventClickPropagation,
    TxGridFilterMenuComponent,
    InputSearchComponent,
    TxGridComponent,
    TxNotEmptyFilterPipe,
    A11yModule
  ],
})
export class TxGridModule {}
