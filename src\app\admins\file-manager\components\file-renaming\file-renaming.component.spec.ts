import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialogModule } from '@angular/material/dialog';
import { FileRenamingComponent } from './file-renaming.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';

describe('FileRenamingComponent', () => {
  let component: FileRenamingComponent;
  let fixture: ComponentFixture<FileRenamingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [FileRenamingComponent],
      imports: [
        MatDialogModule,
        NoopAnimationsModule,
        FontAwesomeTestingModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FileRenamingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Close', () => {
    let oldNameMock: string;
    let newNameMock: string;
    let nodeToChangeMock: any;
    let rowGridMock: any;
    let pathMock: string;
    let aliasMock: string;

    beforeEach(() => {
      oldNameMock = 'oldName';
      newNameMock = 'newName';
      nodeToChangeMock = { id: '1' };
      rowGridMock = { id: '2' };
      pathMock = 'pathMock';
      aliasMock = 'aliasMock';

      component.oldName = oldNameMock;
      component.newName = newNameMock;
      component.nodeToChange = nodeToChangeMock;
      component.selectedRowGrid = rowGridMock;
      component.path = pathMock;
      component.alias = aliasMock;
    });

    it('should call cancel.emit', () => {
      const spyEmit = jest.spyOn(component.cancel, 'emit');
      component.close();
      expect(spyEmit).toBeCalledWith({
        name: oldNameMock,
        oldName: oldNameMock,
        node: nodeToChangeMock,
        rowGrid: rowGridMock,
        path: pathMock,
        alias: aliasMock,
      });
    });
  });

  describe('Valid', () => {
    let oldNameMock: string;
    let newNameMock: string;
    let nodeToChangeMock: any;
    let rowGridMock: any;
    let pathMock: string;
    let aliasMock: string;

    beforeEach(() => {
      oldNameMock = 'oldName';
      newNameMock = 'newName';
      nodeToChangeMock = { id: '1' };
      rowGridMock = { id: '2' };
      pathMock = 'pathMock';
      aliasMock = 'aliasMock';

      component.oldName = oldNameMock;
      component.newName = newNameMock;
      component.nodeToChange = nodeToChangeMock;
      component.selectedRowGrid = rowGridMock;
      component.path = pathMock;
      component.alias = aliasMock;
    });

    it('should call confirm.emit', () => {
      const spyEmit = jest.spyOn(component.confirm, 'emit');
      component.valid();
      expect(spyEmit).toBeCalledWith({
        oldName: oldNameMock,
        newName: newNameMock,
        node: nodeToChangeMock,
        rowGrid: rowGridMock,
        path: pathMock,
        alias: aliasMock,
      });
    });
  });

  describe('Show', () => {
    let fileNameMock: string;
    let newNameMock: string;
    let pathMock: string;
    let aliasMock: string;
    let nodeMock: any;
    let selectedRowGridMock: any;

    beforeEach(() => {
      fileNameMock = 'nameMock';
      newNameMock = 'newNameMock';
      pathMock = 'DocumentsMock';
      aliasMock = 'CRs';
      nodeMock = { id: '1' };
      selectedRowGridMock = { id: '2' };
    });

    it('should assign fileNameMock to this.oldName', () => {
      component.show(fileNameMock, newNameMock, pathMock, aliasMock, nodeMock, selectedRowGridMock);
      expect(component.oldName).toBe(fileNameMock);
    });

    it('should assign newNameMock to this.newName', () => {
      component.show(fileNameMock, newNameMock, pathMock, aliasMock, nodeMock, selectedRowGridMock);
      expect(component.newName).toBe(newNameMock);
    });

    it('should assign nodeMock to this.nodeToChange', () => {
      component.show(fileNameMock, newNameMock, pathMock, aliasMock, nodeMock, selectedRowGridMock);
      expect(component.nodeToChange).toBe(nodeMock);
    });

    it('should assign pathMock to this.path', () => {
      component.show(fileNameMock, newNameMock, pathMock, aliasMock, nodeMock, selectedRowGridMock);
      expect(component.path).toBe(pathMock);
    });

    it('should assign aliasMock to this.alias', () => {
      component.show(fileNameMock, newNameMock, pathMock, aliasMock, nodeMock, selectedRowGridMock);
      expect(component.alias).toBe(aliasMock);
    });

    it('should assign aliasMock to this.selectedRowGrid', () => {
      component.show(fileNameMock, newNameMock, pathMock, aliasMock, nodeMock, selectedRowGridMock);
      expect(component.selectedRowGrid).toBe(selectedRowGridMock);
    });

    it('call dialog.open', () => {
      const spyOpen = jest.spyOn(component.dialog, 'open');
      component.show(fileNameMock, newNameMock, pathMock, aliasMock, nodeMock, selectedRowGridMock);
      expect(spyOpen).toBeCalled();
    });
  });
});
