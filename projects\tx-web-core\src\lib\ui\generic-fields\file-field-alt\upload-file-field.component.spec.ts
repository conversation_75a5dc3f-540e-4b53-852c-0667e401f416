import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxUploadFileFieldComponent } from './upload-file-field.component';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { Component } from '@angular/core';
import { HarnessLoader } from '@angular/cdk/testing';
import { TestbedHarnessEnvironment } from '@angular/cdk/testing/testbed';
import { MatChipOptionHarness } from '@angular/material/chips/testing';

@Component({
  template: `<tx-upload-file-field
    [formControl]="control"
    [idAttribute]="1"
    [required]="true"></tx-upload-file-field>`,
})
class HostUploadFileFieldComponent {
  control = new FormControl([]);
}

describe('TxFileFieldAltComponent', () => {
  let component: TxUploadFileFieldComponent;
  let fixture: ComponentFixture<TxUploadFileFieldComponent>;
  let hostComponent: HostUploadFileFieldComponent;
  let hostFixture: ComponentFixture<HostUploadFileFieldComponent>;
  let loaderTest: HarnessLoader;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [
        FontAwesomeTestingModule,
        ReactiveFormsModule,
        TxUploadFileFieldComponent,
        HttpClientTestingModule,
        NoopAnimationsModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
      ],
      declarations: [HostUploadFileFieldComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxUploadFileFieldComponent);
    component = fixture.componentInstance;
    component.control = new FormControl();
    component.idAttribute = 1;
    component.maxMoFileSize = 1;
    component.multiple = false;
    hostFixture = TestBed.createComponent(HostUploadFileFieldComponent);
    hostComponent = hostFixture.componentInstance;
    fixture.detectChanges();
    hostFixture.detectChanges();
    loaderTest = TestbedHarnessEnvironment.loader(fixture);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
    expect(hostComponent).toBeTruthy();
  });

  it('inptut is required', async () => {
    expect(hostComponent.control.errors).toEqual({ required: true });
  });

  it('should add file', () => {
    const file = new File([], 'fileTest');
    const event = { dataTransfer: { files: [file] } };
    component.onFileSelected(event);
    expect(component.control.value).toStrictEqual(['fileTest']);
  });

  it('should not add a file too big', () => {
    const file: File = {
      name: 'fileTest',
      lastModified: 1713537376118,
      webkitRelativePath: '',
      size: 1111111,
      type: 'text/plain',
    } as File;
    const event = { dataTransfer: { files: [file] } };
    component.onFileSelected(event);
    fixture.detectChanges();
    expect(component.control.value).toStrictEqual(null);
  });

  it('should not add a second file', () => {
    const file1 = new File([], 'fileTest1');
    const file2 = new File([], 'fileTest2');
    const event = { dataTransfer: { files: [file1, file2] } };
    component.onFileSelected(event);
    expect(component.control.value).toStrictEqual(['fileTest1']);
  });

  it('should remove file', async () => {
    const file = new File([], 'fileTest');
    const event = { dataTransfer: { files: [file] } };
    component.onFileSelected(event);
    fixture.detectChanges();
    const chip = await loaderTest.getHarness(MatChipOptionHarness);
    await chip.remove();
    expect(component.control.value).toStrictEqual([]);
  });
});
