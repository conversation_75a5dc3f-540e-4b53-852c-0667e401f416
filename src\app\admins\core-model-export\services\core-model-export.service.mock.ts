import { MockService } from 'ng-mocks';
import { CoreModelExportService } from './core-model-export.service';
import { of } from 'rxjs';
import { ConceptType } from '@bassetti-group/tx-web-core';

export const CORE_MODELS_EXPORT_SERVICE_MOCK = MockService(CoreModelExportService, {
  concepts$: of([
    {
      type: ConceptType.ObjectType,
      id: 25,
      name: 'TEST_WF',
      tags: ['otTESTWF_CM8974C727'],
      metaDataList: [],
      errors: [],
      explanation: 'test',
    },
  ]),
  history$: of([
    {
      version: '0.0.1',
      name: 'Core Model Test',
      explanation: 'history description',
      comment: 'comment',
      date: new Date(),
      username: '<PERSON>',
    },
  ]),
});
