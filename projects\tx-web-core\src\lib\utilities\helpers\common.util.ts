export class TxCommonUtils {
  /**
   * Test if a value is NOT null or undefined
   *
   * @param value The value to test
   */
  public static isNotNil<T>(value: T | null | undefined): value is T {
    return value != null;
  }

  /**
   * Test if a value is defined (not empty, null or undefined)
   *
   * @param value The value to test
   */
  public static isDefined<T>(value: T | null | undefined): value is T {
    return (
      value !== '' &&
      value !== null &&
      value !== undefined &&
      (!Array.isArray(value) || (Array.isArray(value) && value.length > 0))
    );
  }
  public static isValueDefined<T>(value: T | null | undefined): value is T {
    return value !== '' && value !== null && value !== undefined;
  }

  /**
   * Return the value if it is NOT null or undefined, else return the default value
   *
   * @param value The value to test
   * @param value The default value
   */
  public static getValue(value: any, defaultValue: any): any {
    return this.isNotNil(value) ? value : defaultValue;
  }

  /**
   * Return a deep copy, preserving Type of objects
   *
   * @param value The value to test
   * @param value The default value
   */
  public static deepCopy<T>(source: T): T {
    const deepCopyStep = (element: any): any => {
      if (Array.isArray(element)) {
        return element.map((item) => deepCopyStep(item));
      }
      if (element instanceof Date) {
        return new Date(element.getTime());
      }
      if (element && typeof element === 'object') {
        return Object.getOwnPropertyNames(element).reduce((o, prop) => {
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          Object.defineProperty(o, prop, Object.getOwnPropertyDescriptor(element, prop)!);
          o[prop] = deepCopyStep((element as { [key: string]: any })[prop]);
          return o;
        }, Object.create(Object.getPrototypeOf(element)));
      }
      return element;
    };

    return deepCopyStep(source) as T;
  }
}
