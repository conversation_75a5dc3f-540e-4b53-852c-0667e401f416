import { Component, Input, OnInit } from '@angular/core';
import { LegacyTxDataString } from '../../../services/structure/models/data';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { TxAttributeField } from '../../../models/formConfiguration/businessClass/attribute-field';
import { TxTextInputObjectFieldComponent } from '../text-input-object-field/text-input-object-field.component';

@Component({
  selector: 'tx-short-string-object-field',
  templateUrl: '../text-input-object-field/text-input-object-field.component.html',
  styleUrls: [
    './short-string-object-field.component.scss',
    '../text-input-object-field/text-input-object-field.component.scss',
  ],
})
export class TxShortStringObjectFieldComponent
  extends TxTextInputObjectFieldComponent
  implements OnInit
{
  @Input() declare field: TxAttributeField;
  @Input() declare data: LegacyTxDataString;

  constructor(public attributeService: LegacyTxAttributesService) {
    super(attributeService);
    this.inputType = 'text';
    this.inTextArea = false;
    if (!this.numberOfLine) {
      this.numberOfLine = 2;
    }
    this.textAreaHeight = this.numberOfLine * 14; // fontSize + 1
  }

  initValue() {
    if (this.data) {
      this.value = this.data.value;
    }
  }

  getData(): LegacyTxDataString {
    return new LegacyTxDataString(this.idObject, this.idAttribute, this.control.value);
  }
}
