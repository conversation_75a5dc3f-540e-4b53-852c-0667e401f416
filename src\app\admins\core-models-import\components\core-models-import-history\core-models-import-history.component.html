<app-core-models-history
  class="core-model-import-history__table"
  [columns]="columns"
  [history]="history"
  (customizeCell)="customizeCell($event)"
  ><ng-template [appOverrideColumn]="CoreModelsImportHistoryFieldEnum.View" let-data>
    <div class="core-model-import-history__view-details view-details" (click)="showDetails(data)">
      <fa-icon [icon]="['fal', 'eye']" size="lg"> </fa-icon>
    </div>
  </ng-template>
  <ng-template [appOverrideColumn]="CoreModelsImportHistoryFieldEnum.Status" let-data>
    <div
      class="status-cell"
      [ngClass]="
        data[CoreModelsImportHistoryFieldEnum.Status] === 'Success' ? 'is-ok' : 'is-in-error'
      ">
      <p class="core-model-import-history__status">
        {{ 'generic.' + data[CoreModelsImportHistoryFieldEnum.Status] | translate }}
      </p>
    </div>
  </ng-template>

  <ng-template #optionalTableBarInput>
    <div class="tx-slide-toggle">
      <tx-slide-toggle class="tx-slide-toggle__toggle" [formControl]="filterLatestControl">
        <span class="tx-slide-toggle__toggle-label">
          {{ 'generic.showLatestImports' | translate }}
        </span>
      </tx-slide-toggle>
    </div>
  </ng-template>
</app-core-models-history>
<ng-template #detailTemplate>
  <div class="core-model-import-history__detailed-concepts">
    <ng-container *ngIf="selectedConcepts">
      <app-tested-core-model-concepts
        [flatConcepts]="flatConcepts"
        *ngIf="selectedConcepts | isTestedConcepts; else completedImport"
        [showOnlyErrors]="showOnlyErrors"
        (filterOnConceptsInConflicts)="filterOnConceptsInConflicts($event)">
      </app-tested-core-model-concepts>
    </ng-container>
    <ng-template #completedImport>
      <app-imported-core-model-concepts [flatConcepts]="flatConcepts">
      </app-imported-core-model-concepts>
    </ng-template>
  </div>
</ng-template>
