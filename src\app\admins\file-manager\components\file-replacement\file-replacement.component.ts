import { Component, EventEmitter, Output, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {
  FileDescription,
  FilesManager,
  FileTree,
  FileGridSelection,
  FilesState,
} from '../../models/file-models';
import { FileManagerService } from '../../services/file-manager.service';

@Component({
  selector: 'app-file-replacement',
  templateUrl: './file-replacement.component.html',
  styleUrls: ['./file-replacement.component.scss'],
})
export class FileReplacementComponent {
  @Output() confirm = new EventEmitter();

  @ViewChild('dialogContent') private dialogContent?: TemplateRef<any>;

  public useDetailedChoice = false;
  public totalFiles = 0;
  public files: FilesState;
  public selectedNode: FileTree | undefined;
  public filesToUpload: FileGridSelection[] = [];
  public allFTUSelected = false;
  public filesInDestination: FileGridSelection[] = [];
  public allFIDSelected = false;

  public sourceParentNode: FileTree | undefined;
  public destinationParentNode: FileTree | undefined;

  public isMovingItems = false;
  public isPasteItems = false;

  public filesDescInDestination: FileDescription[] = [];

  constructor(public dialog: MatDialog, private fileManagerService: FileManagerService) {
    this.files = { goodFiles: [], sameFiles: [], existingFiles: [] };
  }

  someFTUSelect(): boolean {
    return this.filesToUpload.filter((f) => f.selected).length > 0 && !this.allFTUSelected;
  }
  setAllFTU(selected: boolean) {
    this.allFTUSelected = selected;
    this.filesToUpload.forEach((t) => (t.selected = selected));
  }
  updateAllFTUSelect() {
    this.allFTUSelected = this.filesToUpload.every((t) => t.selected);
  }

  someFIDSelect(): boolean {
    return this.filesInDestination.filter((f) => f.selected).length > 0 && !this.allFIDSelected;
  }
  setAllFID(selected: boolean) {
    this.allFIDSelected = selected;
    this.filesInDestination.forEach((t) => (t.selected = selected));
  }
  updateAllFIDSelect() {
    this.allFIDSelected = this.filesInDestination.every((t) => t.selected);
  }

  formatFileSizeInReplacement(size: number): number {
    return this.fileManagerService.formatFileSize(size);
  }
  replaceAllFiles() {
    this.onConfirm();
  }

  ignoreAllFiles() {
    this.files.sameFiles = [];
    this.onConfirm();
  }

  switchToDetailedChoice() {
    this.useDetailedChoice = true;
  }

  public validDetailedChoice() {
    const selectedFTU = this.filesToUpload.filter((file) => file.selected).map((f) => f.file);
    const selectedFID = this.filesInDestination.filter((file) => file.selected).map((f) => f.file);
    let isFTU: boolean;
    let isFID: boolean;

    const files = Object.assign([], this.files.sameFiles);
    this.files.sameFiles = [];

    files.forEach((file: FileDescription) => {
      isFTU = selectedFTU.some((ftu) => ftu.name === file.name);
      isFID = selectedFID.some((fid) => fid.name === file.name);

      if (isFTU && isFID) {
        // check files selected in both columns => add increment, add it to goodFiles and remove it from sameFiles
        const newName = FilesManager.createUniqueFileNameGrid(
          file.name,
          this.filesDescInDestination,
          2
        );

        file.newName = newName;
        if (file.file) {
          const newFile = new File([file.file], newName, {
            lastModified: file.file.lastModified,
            type: file.file.type,
          });
          file.file = newFile;
        }

        this.files.goodFiles.push(file);
      } else if (isFTU) {
        // file to replace => add to sameFiles
        this.files.sameFiles?.push(file);
      }
      // keep file in destination => remove from sameFiles
    });

    this.onConfirm();
  }

  public onConfirm(): void {
    this.reset();
    this.confirm.emit({
      filesToAdd: this.files.goodFiles,
      filesToReplace: this.files.sameFiles,
      node: this.selectedNode,
      sourceParentNode: this.sourceParentNode,
      destinationParentNode: this.destinationParentNode,
      isMovingItems: this.isMovingItems,
      isPasteItems: this.isPasteItems,
    });
    this.dialog.closeAll();
  }

  public reset(): void {
    this.useDetailedChoice = false;
    this.allFTUSelected = false;
    this.allFIDSelected = false;
  }

  public show(
    files: FilesState,
    isMovingItems: boolean,
    isPasteItems: boolean,
    sourceParentNode: FileTree,
    destinationParentNode: FileTree,
    filesInDestination: FileDescription[],
    selectedNode?: FileTree
  ) {
    this.files = files;
    this.isMovingItems = isMovingItems;
    this.isPasteItems = isPasteItems;
    this.filesToUpload = this.files.sameFiles.map((f) => ({ selected: false, file: f }));
    this.filesInDestination = this.files.existingFiles.map((f) => ({ selected: false, file: f }));

    if (sourceParentNode) {
      this.sourceParentNode = sourceParentNode;
    }
    if (destinationParentNode) {
      this.destinationParentNode = destinationParentNode;
    }
    this.filesDescInDestination = filesInDestination;
    this.totalFiles = files.sameFiles.length + files.goodFiles.length;
    this.selectedNode = selectedNode;
    if (this.dialogContent) {
      this.dialog.open(this.dialogContent, { disableClose: true, panelClass: 'customDialog' });
    }
  }

  isFolderReplacement(type: string): boolean {
    return this.fileManagerService.isFolder(type);
  }
}
