enum LegacyTxLinkAttributeFilteringType {
  Undefined = 'laftUndefined',
  None = 'laftNone',
  Parent = 'laftParent',
  Or = 'laftOr',
  And = 'laftAnd',
  AndButNotEmpty = 'laftAndButNotEmpty',
}

export class LegacyTxLinkType {
  public id: number;
  public idDestinationObjectType: number;
  public idSourceObjectType: number;
  public isAssociative: boolean;
  public idFilteringObject: number;
  public multiplicity: boolean;
  public multiplicityInv: boolean;
  public isStrongFiltered: boolean;
  public isStrongFilteredInv: boolean;
  public isTransposed: boolean;
  public isTransposeInv: boolean;
  public filteringType: LegacyTxLinkAttributeFilteringType;
  public filteringTypeInv: LegacyTxLinkAttributeFilteringType;
  public name: string;
  public tags: string[];

  constructor(linkType: LegacyTxLinkType) {
    this.id = linkType.id;
    this.idDestinationObjectType = linkType.idDestinationObjectType;
    this.idSourceObjectType = linkType.idSourceObjectType;
    this.isAssociative = linkType.isAssociative;
    this.idFilteringObject = linkType.idFilteringObject;
    this.multiplicity = linkType.multiplicity;
    this.multiplicityInv = linkType.multiplicityInv;
    this.isStrongFiltered = linkType.isStrongFiltered;
    this.isStrongFilteredInv = linkType.isStrongFilteredInv;
    this.isTransposed = linkType.isTransposed;
    this.isTransposeInv = linkType.isTransposeInv;
    this.filteringType = linkType.filteringType;
    this.filteringTypeInv = linkType.filteringTypeInv;
    this.name = linkType.name;
    this.tags = linkType.tags;
  }
}
