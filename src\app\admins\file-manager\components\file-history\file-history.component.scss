.panel-header {
  fa-icon {
    margin-right: 1rem;
    font-size: 1.75rem;
    vertical-align: bottom;
  }
}

.panel-content {
  min-height: 65px;
  overflow: auto;

  .panel-section-subtitle {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    padding: 0px 2rem 0px 2rem;
  }

  .fm-panel-content {
    margin-bottom: 1rem;

    .fm-panel-title {
      padding: 0px 2rem 0px 2rem;
      margin-top: 1rem;
      font-size: 0.75rem;
      margin-bottom: 0.5rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .fm-panel-section-body {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .fm-panel-body-content {
        flex: 1;
        display: flex;
        margin-left: 2rem;
        padding: 0.5rem;
        line-height: 1.25rem;
      }
    }

    .fm-panel-body-buttons {
      padding: 0px 2rem 0px 2rem;
      cursor: pointer;

      fa-icon {
        font-size: 1.125rem;
        padding-left: 0.5rem;
      }
    }
  }

  fa-icon {
    padding-right: 0.5rem;
  }
}
