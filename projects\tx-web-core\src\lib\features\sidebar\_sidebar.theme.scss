@use '@angular/material' as mat;

@mixin sidebar-theme($theme) {
  $foreground: map-get($theme, foreground);
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  .sideNav-treeview-container {
    .mat-tree-node {
      margin: 0;
      padding: 0px;
    }
    .tree-node {
      padding: 2px 0px;
      &:hover {
        background-color: transparent;
      }
    }
    .node-select {
      left: 0px;
      position: relative;
      background-color: mat.m2-get-color-from-palette($foreground, grey10);
      &:hover {
        background-color: mat.m2-get-color-from-palette($foreground, grey10);
      }
    }
    .node-select::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      border-left: 4px solid mat.m2-get-color-from-palette($accent);
    }

    .node-select > .tree-content {
      color: mat.m2-get-color-from-palette($foreground, base);
    }
  }
}
