<div class="form-container">
  <div
    *ngIf="showBarAndButton"
    class="panel-header"
    [ngClass]="{ 'background-grey5': inRightPane }">
    <fa-icon *ngIf="formTitleIcon" class="header-icon" [icon]="formTitleIcon"></fa-icon>
    <div class="panel-title h1-title">
      <span>{{ formTitle }}</span>
    </div>
    <button
      mat-flat-button
      *ngIf="!inRightPane && !readMode"
      mat-flat-button
      class="form-button-submit button-header"
      color="accent"
      type="submit"
      (click)="onSubmit()"
      [disabled]="formDisabled">
      Save
    </button>
  </div>
  <!-- <mat-divider class="panel-divider"></mat-divider> -->
  <form
    *ngIf="form"
    [formGroup]="form"
    [ngClass]="{
      'tx-read-form-panel': inRightPane && readMode,
      'tx-write-form-panel': inRightPane && !readMode
    }"
    class="tx-form">
    <!-- tab components -->
    <mat-tab-group
      [selectedIndex]="indexTabToFocusFirst"
      (animationDone)="onAnimationDone()"
      (selectedTabChange)="onSelectedTabChange($event.index)"
      dynamicHeight
      class="form-tab-group">
      <mat-tab *ngFor="let tab of tabs; let index = index">
        <ng-template mat-tab-label>
          <div
            [matTooltip]="tab.tooltip"
            matTooltipClass="mat-tooltip-multiline"
            matTooltipShowDelay="500"
            matTooltipPosition="above">
            {{ tab.attribute.name }}
          </div>
          <span
            *ngIf="errorNumber[index]"
            [matTooltip]="
              errorNumber[index] > 1
                ? errorNumber[index] + ' errors in this tab'
                : errorNumber[index] + ' error in this tab'
            "
            class="error-number"
            >{{ errorNumber[index] }}</span
          >
        </ng-template>
        <div class="form-tab">
          <tx-object-tab-field
            [tab]="tab"
            [readMode]="readMode"
            [form]="form"
            [index]="index"
            [formConfigTag]="formConfigTag"
            [idObject]="idObject"
            (loadEvent)="loadEvent.emit($event)"
            (displayPaneEvent)="showObjectPanel($event)"
            #tabField></tx-object-tab-field>
        </div>
      </mat-tab>
    </mat-tab-group>
    <!-- actions buttons -->

    <tx-right-pane
      #rightPane
      [templateContent]="activeTemplate"
      (hide)="rightPaneHidden()"
      [hideOnClickout]="true"
      [backgroundDarkened]="false"
      [width]="'400px'"></tx-right-pane>
    <!-- user form -->
    <ng-template #templateExtendLinkField>
      <tx-extended-chip-field
        #extendedChipField
        [label]="labelExtendedChipPanel"
        [chips]="chipsExtendedChipPanel"></tx-extended-chip-field>
    </ng-template>
    <ng-template #templateEmpty> </ng-template>
  </form>

  <!-- <mat-divider *ngIf="inRightPane && !readMode"></mat-divider> -->
  <div *ngIf="inRightPane && !readMode" class="form-button-area">
    <button
      mat-flat-button
      class="form-button-submit"
      color="accent"
      type="submit"
      (click)="onSubmit()"
      [disabled]="form && !form.valid">
      Save
    </button>
  </div>
</div>
