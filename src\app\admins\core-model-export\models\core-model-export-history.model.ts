import { FormControl } from '@angular/forms';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { CoreModelsHistory } from '../../core-model-common';

export type CoreModelExportHistory = CoreModelsHistory;
export interface CoreModelExportHistoryForm {
  version: FormControl<string>;
  name: FormControl<string>;
  explanation: FormControl<string>;
  comment: FormControl<string>;
}

export enum VersionEnum {
  Major = 'major',
  Minor = 'minor',
  Release = 'release',
}
const VERSION_ENUM_LABEL_TRANSLATE = [_('generic.major'), _('generic.minor'), _('generic.release')];
export const VERSION_ENUM_LABEL = Object.values(VersionEnum).map((value) => ({
  label: _(`generic.${value}`),
  value,
}));
export enum CoreModelExportHistoryFieldEnum {
  Version = 'version',
  Name = 'name',
  Date = 'date',
  Username = 'username',
  Explanation = 'explanation',
  Comment = 'comment',
}
