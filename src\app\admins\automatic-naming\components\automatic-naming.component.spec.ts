import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AutomaticNamingComponent } from './automatic-naming.component';
import {
  AttributeSetsServiceMock,
  AttributesServiceMock,
  ObjectsServiceMock,
  ObjectsTypeServiceMock,
  SessionServiceMock,
  ToastServiceMock,
  TxAutomaticNamingServiceMock,
} from 'src/app/app.testing.mock';
import { SessionService } from 'src/app/core/services/session/session.service';
import { TxAutomaticNamingService } from '../services/automatic-naming.service';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { TreeViewAllModule } from '@syncfusion/ej2-angular-navigations';
import { MockComponent } from 'ng-mocks';
import { BreadcrumdComponent } from 'src/app/shared/components/breadcrumd/breadcrumd.component';
import {
  DataBaseRights,
  NoRecordComponent,
  ToastService,
  TxAttributesService,
  TxDialogService,
  TxLockingType,
  TxObjectTypeType,
  TxObjectsService,
  TxObjectsTypeService,
} from '@bassetti-group/tx-web-core';
import { ConfigurationsFrameworkComponent } from 'src/app/shared/components/configurations-framework/configurations-framework.component';
import { Configuration } from 'src/app/shared/components/configurations-framework/models/configurations-framework-model';
import { NamingSettings, NamingSettingsType } from '../models/naming-settings';
import { AttributeSetsService } from 'src/app/core/services/structure/attribute-sets.service';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { DialogConfirmServiceMock } from 'src/app/shared/tests/shared-testing-mock';
import { LoaderComponent } from 'src/app/shared/components/loader/loader.component';

describe('AutomaticNamingComponent', () => {
  let component: AutomaticNamingComponent;
  let fixture: ComponentFixture<AutomaticNamingComponent>;
  let otService: TxObjectsTypeService;
  let attributesService: TxAttributesService;
  let automaticNamingService: TxAutomaticNamingService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        AutomaticNamingComponent,
        MockComponent(BreadcrumdComponent),
        MockComponent(NoRecordComponent),
        MockComponent(ConfigurationsFrameworkComponent),
        MockComponent(LoaderComponent),
      ],
      imports: [
        NoopAnimationsModule,
        MatDividerModule,
        FontAwesomeTestingModule,
        MatTooltipModule,
        MatFormFieldModule,
        MatInputModule,
        MatProgressBarModule,
        MatSlideToggleModule,
        MatListModule,
        TreeViewAllModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
      ],
      providers: [
        { provide: TxObjectsTypeService, useClass: ObjectsTypeServiceMock },
        { provide: AttributeSetsService, useClass: AttributeSetsServiceMock },
        { provide: TxAttributesService, useClass: AttributesServiceMock },
        { provide: SessionService, useClass: SessionServiceMock },
        { provide: TxObjectsService, useClass: ObjectsServiceMock },
        { provide: ToastService, useClass: ToastServiceMock },
        { provide: TxAutomaticNamingService, useClass: TxAutomaticNamingServiceMock },
        { provide: TxDialogService, useClass: DialogConfirmServiceMock },
      ],
    }).compileComponents();
    otService = TestBed.inject(TxObjectsTypeService);
    attributesService = TestBed.inject(TxAttributesService);
    automaticNamingService = TestBed.inject(TxAutomaticNamingService);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AutomaticNamingComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Initial state', () => {
    it('Should get automatic naming configurations', () => {
      const spyGet = jest.spyOn(automaticNamingService, 'getConfigurations');
      fixture.detectChanges();
      expect(spyGet).toBeCalled();
    });

    it('should initialize attributes', () => {
      const spyGet = jest.spyOn(attributesService, 'listAll');
      fixture.detectChanges();
      expect(spyGet).toBeCalled();
    });

    it('should initialize object types', () => {
      const spyGet = jest.spyOn(otService, 'listAll');
      fixture.detectChanges();
      expect(spyGet).toBeCalled();
    });
  });

  describe('Add configurations', () => {
    beforeEach(() => {
      const unexistingConfigsConcept = {
        id: 0,
        name: '',
        tag: ConfigurationsFrameworkComponent.unexistingConfigTag,
        objectTypeTag: ConfigurationsFrameworkComponent.unexistingConfigObjectTypeTag,
        data: {},
        imagePath: './assets/img/iconshock/folder.svg',
        modelApplications: [],
      };
      fixture.detectChanges();
      component.isConfigsLoaded = true;
      component.configurations = [unexistingConfigsConcept];
      fixture.detectChanges();
      component.addConfigForm.setValue({
        id: 0,
        name: 'New Config',
        tag: 'newConfigTestTag',
        objectTypeId: 1,
      });
    });

    it('Should add config to concept list', () => {
      jest.spyOn(otService, 'getByID').mockReturnValue({
        id: 1,
        name: 'test',
        tags: ['tag'],
        icon: 0,
        isFolder: false,
        type: TxObjectTypeType.Associativity,
        hasDistinctName: false,
        isVisible: false,
        lockingType: TxLockingType.Auto,
        lockingDuration: 0,
        displayResultInTextSearch: false,
        right: DataBaseRights.DbrNone,
        order: 0,
      });
      component.onAfterAddConfiguration();
      expect(component.configurations.some((c) => c.name === 'New Config'));
    });

    it('Should call addConfiguration from Service', () => {
      const spyAddConfig = jest.spyOn(automaticNamingService, 'addConfiguration');
      jest.spyOn(otService, 'getByID').mockReturnValue({
        id: 1,
        name: 'test',
        tags: ['tag'],
        icon: 0,
        isFolder: false,
        type: TxObjectTypeType.Associativity,
        hasDistinctName: false,
        isVisible: false,
        lockingType: TxLockingType.Auto,
        lockingDuration: 0,
        displayResultInTextSearch: false,
        right: DataBaseRights.DbrNone,
        order: 0,
      });
      component.onAfterAddConfiguration();
      expect(spyAddConfig).toBeCalled();
    });
  });

  describe('Duplicate configuration', () => {
    let config1: Configuration;
    beforeEach(() => {
      const unexistingConfigsConcept = {
        id: 0,
        name: '',
        tag: ConfigurationsFrameworkComponent.unexistingConfigTag,
        objectTypeTag: ConfigurationsFrameworkComponent.unexistingConfigObjectTypeTag,
        data: {},
        imagePath: './assets/img/iconshock/folder.svg',
        modelApplications: [],
      };
      fixture.detectChanges();
      component.isConfigsLoaded = true;
      config1 = {
        id: 1,
        name: 'TestConfig',
        idOT: 1,
        tag: 'tagTest',
        objectTypeTag: 'otTag',
        data: null,
      };
      component.configurations = [config1, unexistingConfigsConcept];
      component.addConfigForm.setValue({
        id: 1,
        name: 'New testConfig',
        tag: 'tagTest',
        objectTypeId: 1,
      });
      fixture.detectChanges();
    });

    it('Should call addConfiguration from Service', () => {
      const spyAddConfig = jest.spyOn(automaticNamingService, 'addConfiguration');
      jest.spyOn(otService, 'getByID').mockReturnValue({
        id: 1,
        name: 'test',
        tags: ['tag'],
        icon: 0,
        isFolder: false,
        type: TxObjectTypeType.Associativity,
        hasDistinctName: false,
        isVisible: false,
        lockingType: TxLockingType.Auto,
        lockingDuration: 0,
        displayResultInTextSearch: false,
        right: DataBaseRights.DbrNone,
        order: 0,
      });
      component.onAfterDuplicateConfiguration({
        ...config1,
        name: 'duplication test',
        tag: 'duplicateTagTest',
      });
      expect(spyAddConfig).toBeCalled();
    });
  });

  describe('Edit configuration', () => {
    beforeEach(() => {
      const unexistingConfigsConcept = {
        id: 0,
        name: '',
        tag: ConfigurationsFrameworkComponent.unexistingConfigTag,
        objectTypeTag: ConfigurationsFrameworkComponent.unexistingConfigObjectTypeTag,
        data: {},
        imagePath: './assets/img/iconshock/folder.svg',
        modelApplications: [],
      };
      fixture.detectChanges();
      component.isConfigsLoaded = true;
      const config1: Configuration = {
        id: 1,
        name: 'TestConfig',
        idOT: 1,
        tag: 'tagTest',
        objectTypeTag: 'otTag',
        data: null,
      };
      component.configurations = [config1, unexistingConfigsConcept];
      component.addConfigForm.setValue({
        id: 1,
        name: 'New testConfig',
        tag: 'tagTest',
        objectTypeId: 1,
      });
      fixture.detectChanges();
    });

    it('Edit the config in the concept list', () => {
      component.onAfterEditConfig();
      expect(component.configurations.find((c) => c.tag === 'tagTest')?.name).toBe(
        'New testConfig'
      );
    });

    it('Should call addConfiguration from Service', () => {
      const spyEditConfig = jest.spyOn(automaticNamingService, 'setConfiguration');
      component.onAfterEditConfig();
      expect(spyEditConfig).toBeCalled();
    });
  });

  describe('Delete configuration', () => {
    let config1: Configuration;
    beforeEach(() => {
      const unexistingConfigsConcept = {
        id: 0,
        name: '',
        tag: ConfigurationsFrameworkComponent.unexistingConfigTag,
        objectTypeTag: ConfigurationsFrameworkComponent.unexistingConfigObjectTypeTag,
        data: {},
        imagePath: './assets/img/iconshock/folder.svg',
        modelApplications: [],
      };
      fixture.detectChanges();
      component.isConfigsLoaded = true;
      config1 = {
        id: 1,
        name: 'TestConfig',
        idOT: 1,
        tag: 'tagTest',
        objectTypeTag: 'otTag',
        data: null,
      };
      component.configurations = [config1, unexistingConfigsConcept];
      fixture.detectChanges();
    });

    it('Should call addConfiguration from Service', () => {
      const spyDeleteConfig = jest.spyOn(automaticNamingService, 'deleteConfiguration');
      jest.spyOn(otService, 'getByID').mockReturnValue({
        id: 1,
        name: 'test',
        tags: ['tag'],
        icon: 0,
        isFolder: false,
        type: TxObjectTypeType.Associativity,
        hasDistinctName: false,
        isVisible: false,
        lockingType: TxLockingType.Auto,
        lockingDuration: 0,
        displayResultInTextSearch: false,
        right: DataBaseRights.DbrNone,
        order: 0,
      });
      component.onDeleteConfiguration(config1);
      expect(spyDeleteConfig).toBeCalled();
    });
  });

  describe('Add SubConcept', () => {
    let subConcept: NamingSettings;
    beforeEach(() => {
      fixture.detectChanges();
      component.isConfigsLoaded = true;
      const config1: Configuration = {
        id: 1,
        name: 'TestConfig',
        idOT: 1,
        tag: 'tagTest',
        objectTypeTag: 'otTag',
        data: null,
      };
      config1.subConcept = { name: 'test', elements: [] };
      component.configurations = [config1];
      component.configurationSelected = config1;
      subConcept = {
        icon: 'calendar',
        id: 1,
        name: NamingSettingsType.date,
        type: NamingSettingsType.date,
        sDateFormat: 'dd/mm',
      };
      component.addNamingSubConceptForm.patchValue({
        id: null,
        type: NamingSettingsType.date,
        dateFormat: 'dd/mm',
      });
    });

    it('should add concept to config selected', () => {
      component.onAfterAddElementSubconcept();
      fixture.detectChanges();
      expect(component.configurationSelected?.subConcept?.elements?.[0]).toEqual(subConcept);
    });

    it('should call edit config function from service', () => {
      const spyEditConfig = jest.spyOn(automaticNamingService, 'setConfiguration');
      component.onAfterAddElementSubconcept();
      expect(spyEditConfig).toHaveBeenCalled();
    });
  });

  describe('Edit SubConcept', () => {
    let subConcept: NamingSettings;
    let subConceptTest: NamingSettings;
    beforeEach(() => {
      fixture.detectChanges();
      component.isConfigsLoaded = true;
      const config1: Configuration = {
        id: 1,
        name: 'TestConfig',
        idOT: 1,
        tag: 'tagTest',
        objectTypeTag: 'otTag',
        data: null,
      };
      component.configurations = [config1];
      component.configurationSelected = config1;
      subConcept = {
        icon: 'calendar',
        id: 1,
        name: NamingSettingsType.date,
        type: NamingSettingsType.date,
        sDateFormat: 'dd/mm',
      };
      subConceptTest = {
        icon: 'calendar',
        id: 1,
        name: NamingSettingsType.date,
        type: NamingSettingsType.date,
        sDateFormat: 'yy/dd/mm',
      };
      config1.subConcept = { name: 'test', elements: [subConcept] };
      component.subConceptEdited = subConcept;
      component.addNamingSubConceptForm.patchValue({
        id: null,
        type: NamingSettingsType.date,
        dateFormat: 'yy/dd/mm',
      });
      fixture.detectChanges();
    });

    it('should edit subconcept in config selected', () => {
      component.onAfterEditSubConcept();
      expect(component.configurationSelected?.subConcept?.elements?.[0]).toEqual(subConceptTest);
    });

    it('should call edit config function from service', () => {
      const spyEditConfig = jest.spyOn(automaticNamingService, 'setConfiguration');
      component.onAfterEditSubConcept();
      expect(spyEditConfig).toHaveBeenCalled();
    });
  });

  describe('Delete SubConcept', () => {
    let subConcept: NamingSettings;
    beforeEach(() => {
      fixture.detectChanges();
      component.isConfigsLoaded = true;
      const config1: Configuration = {
        id: 1,
        name: 'TestConfig',
        idOT: 1,
        tag: 'tagTest',
        objectTypeTag: 'otTag',
        data: null,
      };
      component.configurations = [config1];
      component.configurationSelected = config1;
      subConcept = {
        icon: 'calendar',
        id: 1,
        name: NamingSettingsType.date,
        type: NamingSettingsType.date,
        sDateFormat: 'dd/mm',
      };
      config1.subConcept = { name: 'test', elements: [subConcept] };
    });

    it('Should delete SubConcept from configuration selected', () => {
      component.onDeleteSubConcept(subConcept);
      expect(component.configurationSelected?.subConcept?.elements?.length).toBe(0);
    });

    it('should call edit config function from service', () => {
      const spyEditConfig = jest.spyOn(automaticNamingService, 'setConfiguration');
      component.onDeleteSubConcept(subConcept);
      expect(spyEditConfig).toHaveBeenCalled();
    });
  });
});
