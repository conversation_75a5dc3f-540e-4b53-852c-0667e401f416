import { isEmpty } from 'ramda';
import {
  FlatTestedCoreModelConcept,
  TestedCoreModelConcept,
} from '../models/tested-core-model-concept.model';
import { ImportCoreModelConcepts } from '../models/import-core-model-concept.model';
import { TableUtils } from 'src/app/core/utils/table.utils';
import {
  FlatImportedCoreModelConcept,
  ImportedCoreModelConcept,
} from '../models/imported-core-model-concept.model';
import { FlatImportCoreModelConcepts } from '../models/flat-import-core-model-concepts.model';
import {
  CoreModelImportHistory,
  CoreModelsImportHistoryStatus,
  FailedImportHistory,
} from '../models/import-history.model';

export const isTestedConcepts = (
  concepts: ImportCoreModelConcepts
): concepts is TestedCoreModelConcept[] =>
  !isEmpty(concepts) && (concepts[0] as TestedCoreModelConcept).conflicts !== undefined;

export const toFlatImportCoreModelConcepts = (
  concepts: ImportCoreModelConcepts
): FlatImportCoreModelConcepts => {
  if (isTestedConcepts(concepts)) {
    return TableUtils.toFlatData<TestedCoreModelConcept, FlatTestedCoreModelConcept>(concepts);
  }
  return TableUtils.toFlatData<ImportedCoreModelConcept, FlatImportedCoreModelConcept>(concepts);
};

export const isFailedConcepts = (
  concepts: CoreModelImportHistory
): concepts is FailedImportHistory => statusIsFailed(concepts.status);

export const statusIsFailed = (status: CoreModelsImportHistoryStatus) => status === 'Failed';
