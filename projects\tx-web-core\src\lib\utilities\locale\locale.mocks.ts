import { MatDateFormats } from '@angular/material/core';
import { of } from 'rxjs';

export class LocaleServiceMock {
  $localeChanged = of('en');

  setDateFormat() {
    return;
  }
  getDateFormat(): string {
    return 'dd/MM/yyyy';
  }
  getCustomDateFormats(): MatDateFormats {
    return {
      parse: {
        dateInput: 'input',
      },
      display: {
        dateInput: 'YYYY-MM-DD',
        monthYearLabel: 'MMM YYYY',
        dateA11yLabel: 'LL',
        monthYearA11yLabel: 'MMMM YYYY',
      },
    };
  }
  setLocale() {
    return;
  }
  getLocale() {
    return 'en';
  }
}
