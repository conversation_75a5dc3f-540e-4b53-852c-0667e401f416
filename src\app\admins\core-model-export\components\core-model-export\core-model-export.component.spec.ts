import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CoreModelExportComponent } from './core-model-export.component';
import { MockComponent, MockPipe, MockProvider } from 'ng-mocks';
import { BreadcrumdComponent } from 'src/app/shared/components/breadcrumd/breadcrumd.component';
import { CoreModelExportService } from '../../services/core-model-export.service';
import { CORE_MODELS_EXPORT_SERVICE_MOCK } from '../../services/core-model-export.service.mock';
import { NbErrorsPipeMock } from 'src/app/shared/pipes/nb-errors.pipe.mock';
import { NbErrorsPipe } from 'src/app/shared/pipes/nb-errors.pipe';
import { rightPaneServiceMock } from 'src/app/core/right-pane/right-pane.service.mock';
import { RightPaneService } from 'src/app/core/right-pane/right-pane.service';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { TRANSLATIONS_MOCK } from 'src/app/core/translation/translations.mock';
import { CoreModelExportHistoryComponent } from '../core-model-export-history/core-model-export-history.component';
import { AsyncPipe } from '@angular/common';
import { MatTab, MatTabGroup, MatTabsModule } from '@angular/material/tabs';
import { MatDivider } from '@angular/material/divider';
import { CoreModelsConceptsComponent } from '../core-models-concepts/core-models-concepts.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('CoreModelsExportComponent', () => {
  let component: CoreModelExportComponent;
  let fixture: ComponentFixture<CoreModelExportComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        CoreModelExportComponent,
        MockComponent(BreadcrumdComponent),
        MockComponent(CoreModelsConceptsComponent),
        MockComponent(CoreModelExportHistoryComponent),
        MockPipe(AsyncPipe),
        MockComponent(MatProgressSpinner),
        MockComponent(MatDivider),
      ],
      imports: [
        NoopAnimationsModule,
        MatTabsModule,
        TranslateTestingModule.withTranslations('en', TRANSLATIONS_MOCK),
      ],
      providers: [
        MockProvider(NbErrorsPipe, NbErrorsPipeMock, 'useClass'),
        MockProvider(CoreModelExportService, CORE_MODELS_EXPORT_SERVICE_MOCK, 'useValue'),
        MockProvider(RightPaneService, rightPaneServiceMock, 'useValue'),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CoreModelExportComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
