import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CoreModelsImportComponent } from './core-models-import.component';
import { MockComponent, MockProvider } from 'ng-mocks';
import { CoreModelsImportService } from './services/core-models-import.service';
import { DemoButtonPanelsComponent } from 'src/app/shared/components/demo-button-panels/demo-button-panels.component';
import { FileInformationComponent } from 'src/app/shared/components/file-information/file-information.component';
import { CheckComponent } from 'src/app/shared/components/check/check.component';
import { BreadcrumdComponent } from 'src/app/shared/components/breadcrumd/breadcrumd.component';
import { ToastService } from '@bassetti-group/tx-web-core';
import { TRANSLATIONS_MOCK } from 'src/app/core/translation/translations.mock';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CORE_MODELS_IMPORT_SERVICE_MOCK } from './services/core-models-import.service.mock';
import { ToastServiceMock } from 'src/app/app.testing.mock';

describe('CoreModelsImportComponent', () => {
  let component: CoreModelsImportComponent;
  let fixture: ComponentFixture<CoreModelsImportComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        CoreModelsImportComponent,
        MockComponent(DemoButtonPanelsComponent),
        MockComponent(BreadcrumdComponent),
        MockComponent(FileInformationComponent),
        MockComponent(CheckComponent),
      ],
      imports: [TranslateTestingModule.withTranslations(TRANSLATIONS_MOCK)],
      providers: [
        MockProvider(CoreModelsImportService, CORE_MODELS_IMPORT_SERVICE_MOCK, 'useValue'),
        MockProvider(ToastService, ToastServiceMock, 'useClass'),
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();
    fixture = TestBed.createComponent(CoreModelsImportComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
