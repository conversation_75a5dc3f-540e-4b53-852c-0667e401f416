import type { Meta, StoryObj } from '@storybook/angular';
import { TxColorPickerComponent } from './tx-color-picker.component';
import { FormControl } from '@angular/forms';
import { ColorFormats } from './enums/formats';

const meta: Meta<TxColorPickerComponent> = {
  component: TxColorPickerComponent,
  title: 'TxColorPickerComponent',
  tags: ['autodocs'],
};
export default meta;
type Story = StoryObj<TxColorPickerComponent>;

export const Primary: Story = {
  args: {
    colorFormControl: new FormControl<string>('#fff'),
    formats: [ColorFormats.HEX, ColorFormats.RGBA],
  },
};
