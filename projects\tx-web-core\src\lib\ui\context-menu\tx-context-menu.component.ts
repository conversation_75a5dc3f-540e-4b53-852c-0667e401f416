import { CdkMenuModule } from '@angular/cdk/menu';
import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DoCheck,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { MatMenuModule, MatMenuTrigger } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { take, timer } from 'rxjs';
import { BeforeOpenCloseMenuEventArgs } from './before-open-close-menu-event-args.model';
import { TxContextMenuEventArgs } from './tx-context-menu-event-args.model';
import { TxContextMenuItem } from './tx-context-menu-item.model';

@Component({
  selector: 'tx-context-menu',
  standalone: true,
  imports: [CommonModule, MatMenuModule, CdkMenuModule, TranslateModule],
  templateUrl: './tx-context-menu.component.html',
  styleUrls: ['./tx-context-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TxContextMenuComponent implements DoCheck {
  /**
   * Query selector for the trigger element that opens the context menu.
   */
  @Input() trigger = '';
  /**
   * An array of context menu items to display in the menu.
   */
  @Input() items: TxContextMenuItem[] = [];
  /**
   * An array of context menu items id to disable.
   */
  @Input() disableItems: string[] = [];
  /**
   * Event emitted before the context menu is opened.
   */
  @Output() beforeOpen = new EventEmitter<BeforeOpenCloseMenuEventArgs>();
  /**
   * Event emitted when a context menu item is selected.
   */
  // eslint-disable-next-line @angular-eslint/no-output-native
  @Output() select = new EventEmitter<TxContextMenuEventArgs>();
  @Output() closed = new EventEmitter<void>();
  /**
   * Event emitted before a context menu item is rendered.
   */
  @ViewChild(MatMenuTrigger)
  contextMenuTest?: MatMenuTrigger;
  element?: HTMLElement;

  /**
   * Reference to the MatMenuTrigger element for opening the context menu.
   */
  contextMenuTestPosition = { x: '0px', y: '0px' };

  constructor(private readonly elementRef: ElementRef) {}

  /**
   * Host listener for handling clicks outside of the context menu.
   * Closes the context menu and emits an event when the menu is closed.
   */
  @HostListener('document:click', ['$event'])
  clickedOut(event: MouseEvent) {
    if (this.contextMenuTest?.menuOpen) {
      this.contextMenuTest.closeMenu();
    }
  }

  ngDoCheck(): void {
    this.detectRightClick();
  }

  /**
   * Detects a right-click event on the specified trigger element and opens a context menu.
   *
   * @memberof TxContextMenuComponent
   */
  detectRightClick() {
    if (!this.element) {
      this.element = this.elementRef.nativeElement.parentElement.querySelector(this.trigger);
      if (this.element) {
        this.element.addEventListener('contextmenu', (event) => {
          event.preventDefault();
          const emit: BeforeOpenCloseMenuEventArgs = {
            element: this.element ?? ({} as HTMLElement),
            items: [],
            event,
            top: event.clientY,
            left: event.clientX,
          };

          this.beforeOpen.emit(emit);
          if (this.contextMenuTest?.menuOpen) {
            this.contextMenuTest.closeMenu();
            timer(100)
              .pipe(take(1))
              .subscribe(() => {
                this.onContextMenu(event);
              });
          } else {
            this.onContextMenu(event);
          }
        });
        this.element.addEventListener('click', (event) => {
          this.clickedOut(event);
        });
      }
    }
  }

  /**
   * Handles the selection of a context menu item.
   *
   * @param item The selected context menu item.
   * @memberof TxContextMenuComponent
   */
  onSelect(menuData: TxContextMenuItem, event: Event) {
    const emit: TxContextMenuEventArgs = {
      element: this.element ?? ({} as HTMLElement),
      item: menuData,
      event,
    };
    this.select.emit(emit);
  }

  /**
   * Opens the context menu and emits an event when the menu is opened.
   *
   * @param event - The mouse event that triggered the context menu.
   * @param element - The trigger element for the context menu.
   * @memberof TxContextMenuComponent
   */
  onContextMenu(event: MouseEvent) {
    event.preventDefault();
    const parent = (event.target as HTMLElement).closest('.pane-container') as HTMLElement;
    if (parent) {
      this.handelRightPanePosition(parent, event);
    } else {
      this.contextMenuTestPosition.x = event.clientX + 'px';
      this.contextMenuTestPosition.y = event.clientY + 'px';
    }

    this.contextMenuTest?.menu?.focusFirstItem('mouse');
    this.contextMenuTest?.openMenu();
  }
  private handelRightPanePosition(parent: HTMLElement, event: MouseEvent) {
    const parentRect = parent?.getBoundingClientRect();

    const transform = window.getComputedStyle(parent).transform;

    let offsetX = 0;
    let offsetY = 0;

    if (transform !== 'none') {
      const matrix = new DOMMatrix(transform);
      offsetX = matrix.e;
      offsetY = matrix.f;
    }

    this.contextMenuTestPosition.x = event.clientX - parentRect?.left + offsetX + 'px';
    this.contextMenuTestPosition.y = event.clientY - parentRect?.top + offsetY + 'px';
  }
}
