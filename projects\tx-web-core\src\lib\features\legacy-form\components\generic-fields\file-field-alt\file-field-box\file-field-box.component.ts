import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { LegacyTxFile } from '../../../../services/structure/models/attribute';
import { LegacyTxDataBaseAction } from '../../../../services/structure/models/data';
import { LegacyFileUtils } from '../../../../utilities/legacy-file-utils';

@Component({
  selector: 'tx-file-field-box',
  templateUrl: './file-field-box.component.html',
  styleUrls: ['./file-field-box.component.scss'],
})
export class TxFileFieldBoxComponent implements OnInit {
  @Input() file!: LegacyTxFile;
  @Input() oldName!: string;
  @Input() icon!: string[];
  @Input() hideVisualisationToggle = true;
  @Output() removeEvent = new EventEmitter<LegacyTxFile>();

  constructor() {}

  ngOnInit() {
    if (this.file) {
      this.icon = LegacyFileUtils.getFileIcon(this.file.name);
    }
  }

  cancelUpload() {
    this.file.uploadSub?.unsubscribe();
    this.reset();
  }

  reset() {
    this.file.uploadProgress = undefined;
    this.file.uploadSub = undefined;
  }

  removeFile() {
    this.removeEvent.emit(this.file);
  }

  getFileSize() {
    if (this.file && this.file.size > 0) {
      return LegacyFileUtils.fileSizeToDisplay(this.file.size);
    }
    return null;
  }

  changeView() {
    this.file.view = !this.file.view;
    if (this.file.action === LegacyTxDataBaseAction.None) {
      this.file.action = LegacyTxDataBaseAction.Modify;
    }
  }
}
