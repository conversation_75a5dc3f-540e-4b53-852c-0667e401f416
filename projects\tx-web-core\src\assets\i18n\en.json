{"txWebCore": {"admins": {"columns": {"name": "Name", "tags": "Tags", "type": "Type", "view": "View"}, "dataModel": {"biLink": "Bi-directional link", "boolean": "Boolean", "date": "Date", "dateAndTime": "Date and time", "directLink": "Direct link", "email": "Email", "file": "File", "group": "Group", "invLink": "Inverted link", "listing": "Listing", "longText": "Long text", "range": "Range", "rangeMeanValue": "Range & Mean value", "shortText": "Short text", "singleValue": "Single value", "tab": "Tab", "table": "Table", "url": "URL"}, "languages": {"noAttributesForListings": "No Attribute of Object Types for type listings", "noAttributesForPortal": "No Attribute of Object Type for type 'Portal'", "noObjectTypeSelected": "No Object Type selected"}, "wording": {"attributes": "Attributes", "objectTypes": "Object Types"}}, "button": {"create": "Create", "delete": "Delete"}, "components": {"autoTagConcepts": "Some of the selected {{conceptType}} do not have any tag. Missing tags will be automatically created. This action cannot be undone. \n Create missing tags ?", "chipsField": {"showAllLinksChip": "Show all {{numberChips}} links"}, "colorPicker": {"advancedColors": "Advanced colors", "invalidColor": "The color is invalid.", "label": "Color", "recommendedColors": "Recommended colors"}, "datepicker": {"endDate": "End date", "invalidEndDate": "Invalid end date", "invalidStartDate": "Invalid start date", "startDate": "Start date"}, "inputNumberField": {"separatorMinMax": "to"}, "otDropdown": {"noObjectTypeAvailable": "No Object Type available"}, "paginator": {"firstPage": "First page", "itemsPerPage": "Items per page", "lastPage": "Last page", "nextPage": "Next page", "pageOf": "{{page}} of {{amountPages}} pages ({{itemsCount}} items)", "previousPage": "Previous page"}, "treeView": {"noRecordFound": "No record found"}, "uploadFileField": {"browseLabel": "browse", "dragDropFile": "Drop file here !", "dropFileLabel": "Drop file here or ", "maximumSizeMo": "Maximum size : {{maxMoFileSize}}Mo"}}, "dataType": {"1": "Boolean", "3": "Short text", "4": "Listing", "5": "Decimal", "6": "Table", "9": "Long text", "11": "Link association", "12": "Link", "13": "Group", "50": "Single Value", "51": "Range", "52": "Range Mean Value", "61": "Series", "62": "Table Value", "63": "Archived Graphic", "80": "Date", "81": "Date and Time", "100": "File", "110": "Email", "111": "Url", "121": "Link Inv", "122": "Link Bi", "123": "Tab", "-1": "Object Type", "-10": "Type Tab", "-1000": "Trash Id Object", "-101": "Series Type", "-11": "Exportation", "-12": "MD log", "-13": "Right", "-14": "File Type", "-15": "Model", "-151": "Input Output", "-16": "Model Application", "-161": "Applied Input Output", "-18": "Requirement List", "-181": "Criterion", "-19": "Choice Guide", "-2": "Tab", "-20": "Question", "-29": "Attribute Set", "-3": "Object", "-31": "Language", "-32": "Translation", "-33": "Object Type Translation", "-39": "Equivalence Set", "-4": "Attribute", "-40": "Eq object id", "-41": "Eq object SId", "-42": "Eq Attribute Id", "-43": "Eq Attribute SId", "-44": "Eq Object Attribute", "-5": "Conversion", "-55": "MD Log", "-57": "Exportation", "-6": "Unit", "-8": "Link Type", "-9": "Source"}, "errorMessage": {"fileTooBig": "File size is too big", "maxValue": "max value = ", "meanOutOfMinMax": "Mean value must be between min value and max value", "minGreaterMax": "Max value must be greater than min value", "minValue": "min value = ", "noDate": "No date specified", "required": "Required"}, "errors": {"0": {"content": "The TEEXMA® API service is not available or the configured URL is not valid. Please contact your administrator with the following details.", "header": "API service not found"}, "400": {"alreadyBoundSupportObject": {"content": "The user is already linked to support object.", "header": "Invalid action"}, "default": {"content": "A request on the TEEXMA® service is not correctly build or the parameters have changed. Please contact your administrator with the following details.", "header": "Bad request"}, "invalidDatabaseRequest": {"content": "The request to be executed on the database is not valid.", "header": "Invalid action"}, "invalidFileEncryption": {"content": "The uploaded file is not correctly encrypted.", "header": "Invalid action"}, "invalidSupportObject": {"content": "No support object has been given for the creation of user.", "header": "Invalid action"}, "pageReadingNotAllowed": {"content": "You do not have access to the requested data.", "header": "Access forbidden"}, "userCreationFailed": {"content": "An error occurred while creating the user.", "header": "Creation user error"}, "userDeletionNotAllowed": {"content": "You cannot remove this user.", "header": "Action not allowed"}, "userEditionFailed": {"content": "An error occurred while editing the user.", "header": "Edition user error"}}, "401": {"default": {"content": "The connection to TEEXMA® service is not available for some reasons. Please contact your administrator with the following details.", "header": "Service unavailable"}, "userDisconnected": {"content": "You have been disconnected from the application. Please reconnect.", "header": "Disconnected"}}, "403": {"default": {"content": "The resource you are trying to access is protected and you are not authorized to view it. Please contact your administrator with the following details.", "header": "Access forbidden"}}, "404": {"attribute": {"content": "Sorry, the Attribute you trying to access is not found in the database.", "header": "Attribute not found"}, "default": {"content": "The requested resource was not found or the request URL does not exist. Please contact your administrator with the following details.", "header": "Resource not found"}, "object": {"content": "Sorry, the Object you trying to access is not found in the database.", "header": "Object not found"}, "objectType": {"content": "Sorry, the Object Type you trying to access is not found in the database.", "header": "Object Type not found"}, "preferenceXML": {"content": "The configuration file \"Preferences.xml\" was not found in the Preferences directory.", "header": "Preferences File not found"}, "requirementList": {"content": "Sorry, the Requirement List you trying to access is not found in the database.", "header": "Requirement list not found"}, "user": {"content": "Sorry, the User you trying to access is not found in the database.", "header": "User not found"}, "userGroup": {"content": "Sorry, the User Group you trying to access is not found in the database.", "header": "User group not found"}}, "500": {"default": {"content": "The server has encountered an internal error or a timeout problem and cannot provide the requested resource. Please contact your administrator with the following details.", "header": "Internal server error"}}, "unhandled": {"content": "An unhandled error has occurred, please contact your administrator with the following details.", "header": "Unhandled error"}}, "generic": {"associatives": "Associativities", "conceptHasReservedTag": "This concept has a reserved tag for the application. It cannot be deleted.", "id": "ID:", "listings": "Listings", "max": "max", "mean": "mean", "min": "min", "modify": "Modify", "noRecordToDisplay": "No record to display", "other": "+{{count}} other", "others": "+{{count}} others", "showSelection": "Show selection", "standards": "Standards", "systems": "Systems", "tagCopy": "Tag copied", "description": "Description", "creationDate": "Creation Date"}, "gridFilters": {"button": {"addFilter": "Add a filter", "removeAllFilters": "Remove all filters"}, "chipFilterTooltip": "Filter applied : {{filteredColumn}} {{operator}} «{{filterValue}}»", "filterOn": "Filter on", "filterType": "Filter type", "filterTypes": {"contains": "contains", "endsWith": "end with", "equal": "is equal to", "greaterThan": "is greater than", "greaterThanOrEqual": "is greater or equal to", "lessThan": "is less than", "lessThanOrEqual": "is less or equal to", "notEqual": "is not equal to", "startsWith": "starts with"}, "filterValue": "Filter value", "removeFilter": "Remove filter", "selectFilterDateValue": "Select a date", "selectFilterValue": "Select a value"}, "input": {"fieldRequired": "Required", "search": "Search", "searchByNameTagId": "Search by name, tag or ID..."}, "search": {"conceptNotAccessible": "The concept is not accessible", "noSufficientRight": "No sufficient right"}, "syncFusion": {"grid": {"EmptyRecord": "Empty record", "FilterButton": "Filter", "all": "All"}}, "tooltip": {"changeFilter": "Change filter settings", "copy": "Copy", "noSufficientRight": "You do not have sufficient rights", "notAccessible": "Not accessible"}, "contextMenu": {"expand": "Expand the selected branch", "collapse": "Collapse the selected branch", "select": "Check the selected branch", "deselect": "Uncheck the selected branch"}, "window": {"cancel": "Cancel", "confirmation": "Confirm", "ok": "Ok"}}}