# TxWebCore lib
Welcome to the TxWebCore lib. Core lib for TEEXMA®, TxAdministration, etc. compatible with TxBusinessRest Server 3.0.3

## Table of Contents
- [Project Overview](#project-overview)
- [External Dependencies](#external-dependencies)
- [Code scaffolding](#code-scaffolding)
- [Build](#build)
- [Running unit tests](#running-unit-tests)
- [Documentation](#documentation)

## Project Overview

This project is a core Angular component lib for TEEXMA®, TxAdministration, etc.

## External Dependencies

This app need some external libraries to build correctly:<br>
- [Angular Material [17.3.10]](https://material.angular.io/) 
- [Angular [17.3.10]](https://angular.io/) 
- [Font Awesome [5.15.3]](https://fontawesome.com/)
- [Angular Font Awesome [0.14.0]](https://github.com/FortAwesome/angular-fontawesome)
- [ngx-translate [15.0.0]](https://github.com/ngx-translate/core)
- [ngx-translate-extract [1.0.0]](https://github.com/biesbjerg/ngx-translate-extract)
- [Syncfusion [20.4.38]](https://www.syncfusion.com/)
- [ng-mocks [^14.1.1]](https://ng-mocks.sudo.eu/) 
- [unzipit [^1.4.3]](https://github.com/greggman/unzipit)
- [ramda [^0.29.0]](https://ramdajs.com/)

## Code scaffolding

Run `ng generate component component-name --project tx-web-core` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module --project tx-web-core`.
> Note: Don't forget to add `--project tx-web-core` or else it will be added to the default project in your `angular.json` file. 

## Build

Run `build:lib` to build the project. The build artifacts will be stored in the `dist/` directory.


## Publishing

Run `release:lib` to release lib version.

## Running unit tests

Run `npm run test-lib` to execute the unit tests via [Jest](https://jestjs.io/).

When testing a new component, there are a few things that you need to do to ensure that your tests run smoothly. Here are some tips:

Use the FontAwesomeTestingModule to avoid errors related to Font Awesome icons and use the TranslateTestingModule with a dedicated mock object to avoid errors related to translations:

```typescript
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';

...
 imports: [
        FontAwesomeTestingModule,
        TranslateTestingModule.withTranslations(TRANSLATIONS),
      ...
      ]
```
### Writing unit tests
When writing unit tests, it's important to follow best practices to ensure that your tests are effective and reliable. Here are some resources that can help you:
[Angular Testing Guide](https://angular.io/guide/testing)

We also recommend following the guidelines provided by our development team, which you can find in our [documentation](https://docs.google.com/document/d/11oLHgEFLGR2GIDvzBGsmWI8dnwBz9uDJhdbKkWmGdbA/edit?usp=sharing)

## Documentation

We use StoryBook to create the documents. All documentation components are accessible via [private web site](https://bassetti-group.github.io/tx-administration)

