<ng-template #noConflicts>
  <div class="status-cell is-ok">
    <p
      class="tested-core-model-concepts__conflicts-td tested-core-model-concepts__conflicts-td--none">
      {{ 'generic.none' | translate }}
    </p>
  </div>
</ng-template>

<tx-grid
  #coreModelGrid
  class="tested-core-model-concepts__table"
  [columns]="columns"
  [filterColumns]="filterColumns"
  [primaryKey]="'id'"
  [groupByField]="TestedCoreModelConceptFieldEnum.Type"
  [data]="flatConcepts"
  [filterOptions]="gridFilterOptions"
  [enableRowTooltip]="true"
  [enableFiltering]="true"
  [enableSearching]="true"
  [isRowSelectable]="true"
  rowHeight="auto"
  (searchInputChange)="searchItem($event)">
  <tx-grid-toolbar>
    <ng-template>
      <div class="slider-wrapper">
        <tx-slide-toggle [formControl]="slideToggleControl">
          <span>
            {{ 'generic.showOnlyErrors' | translate }}
          </span>
        </tx-slide-toggle>
      </div>
    </ng-template>
  </tx-grid-toolbar>
  <tx-grid-group>
    <ng-template let-data let-searchValue="searchValue">
      <tx-concept-accordion-group
        class="tested-core-model-concepts__concept-accordion-group"
        [data]="data"
        [searchValue]="searchValue"
        [issue]="{
          field: TestedCoreModelConceptFieldEnum.Conflicts,
          label: conflictLabel
        }"></tx-concept-accordion-group>
    </ng-template>
  </tx-grid-group>

  <tx-grid-column [fieldName]="TestedCoreModelConceptFieldEnum.Name">
    <ng-template
      [appOverrideColumn]="TestedCoreModelConceptFieldEnum.Name"
      let-data
      let-searchValue="searchValue"
      let-searchById="searchById">
      <tx-concept-name-table-cell
        [searchById]="searchById"
        [id]="data[TestedCoreModelConceptFieldEnum.Id]"
        [name]="data[TestedCoreModelConceptFieldEnum.Name]"
        [searchValue]="searchValue"
        [icon]="data.icon">
      </tx-concept-name-table-cell>
    </ng-template>
  </tx-grid-column>
  <tx-grid-column [fieldName]="TestedCoreModelConceptFieldEnum.Name">
    <ng-template let-data let-searchValue="searchValue" let-searchById="searchById">
      <tx-concept-name-table-cell
        [searchById]="searchById"
        [id]="data[TestedCoreModelConceptFieldEnum.Id]"
        [name]="data[TestedCoreModelConceptFieldEnum.Name]"
        [searchValue]="searchValue"
        [icon]="data.icon">
      </tx-concept-name-table-cell>
    </ng-template>
  </tx-grid-column>
  <tx-grid-column [fieldName]="TestedCoreModelConceptFieldEnum.ObjectType">
    <ng-template let-data let-searchValue="searchValue">
      <tx-text-icon
        *ngIf="data[TestedCoreModelConceptFieldEnum.ObjectType]"
        [icon]="data.objectTypeIcon"
        [text]="data[TestedCoreModelConceptFieldEnum.ObjectType]"
        [searchValue]="searchValue"></tx-text-icon>
    </ng-template>
  </tx-grid-column>
  <tx-grid-column [fieldName]="TestedCoreModelConceptFieldEnum.ObjectType">
    <ng-template let-data let-searchValue="searchValue">
      <tx-text-icon
        *ngIf="data[TestedCoreModelConceptFieldEnum.ObjectType]"
        [icon]="data.objectTypeIcon"
        [text]="data[TestedCoreModelConceptFieldEnum.ObjectType]"
        [searchValue]="searchValue"></tx-text-icon>
    </ng-template>
  </tx-grid-column>
  <tx-grid-column [fieldName]="TestedCoreModelConceptFieldEnum.ModificationType">
    <ng-template let-data let-searchValue="searchValue">
      <span
        [innerHTML]="
          data[TestedCoreModelConceptFieldEnum.ModificationType]
            | escapeHtml
            | highlightSearch : (searchValue | escapeHtml)
        "></span>
    </ng-template>
  </tx-grid-column>

  <tx-grid-column [fieldName]="TestedCoreModelConceptFieldEnum.Tags">
    <ng-template let-data let-searchValue="searchValue">
      <!-- issue -->
      <tx-texts-to-copy
        [texts]="data[TestedCoreModelConceptFieldEnum.Tags]"
        [searchValue]="searchValue"></tx-texts-to-copy>
    </ng-template>
  </tx-grid-column>
  <tx-grid-column [fieldName]="TestedCoreModelConceptFieldEnum.Conflicts">
    <ng-template [appOverrideColumn]="TestedCoreModelConceptFieldEnum.Conflicts" let-data>
      <ng-container
        *ngIf="
          data[TestedCoreModelConceptFieldEnum.Conflicts] | formatErrors as formattedErrors;
          else noConflicts
        ">
        <div class="status-cell is-in-error">
          <p
            class="tested-core-model-concepts__conflicts-td tested-core-model-concepts__conflicts-td--conflicts"
            [innerHTML]="formattedErrors"></p>
        </div>
      </ng-container>
    </ng-template>
    <ng-template let-data #headerTemplate>
      <p class="tested-core-model-concepts__conflicts-th">
        <span>{{ conflictsColumns.headerText | translate }}</span>
        <span
          *ngIf="nbErrors && nbErrors.errors > 0"
          class="badge background-e-error tested-core-model-concepts__error-badge">
          {{ nbErrors.errors | localizedNumber }}
        </span>
      </p>
    </ng-template>
  </tx-grid-column>
</tx-grid>
<div class="tested-core-model-concepts__table-info border-grey">
  {{ flatConcepts.length }} {{ 'generic.item-s' | translate }}
</div>
