import { Component, Input } from '@angular/core';
import { TxAttributePoint } from '../../../../services/structure/models/attribute';
import { LegacyTxAttributesService } from '../../../../services/structure/services/attributes.service';
import { TxObjectFieldComponent } from '../object-field/object-field.component';

@Component({
  selector: 'tx-input-object-field',
  templateUrl: './input-object-field.component.html',
  styleUrls: ['./input-object-field.component.scss'],
})
export class TxInputObjectFieldComponent extends TxObjectFieldComponent {
  @Input() pattern!: RegExp;
  @Input() placeHolder!: string;
  @Input() minLength!: number;
  @Input() maxLength!: number;
  @Input() lowerBoundValue: any;
  @Input() upperBoundValue: any;

  public inputType = 'text';
  public withMaxLength!: boolean;
  public withBounds!: boolean;
  public hintBound!: string;

  constructor(public attributeService: LegacyTxAttributesService) {
    super(attributeService);
  }

  initPropertiesFromField() {
    super.initPropertiesFromField();

    if (this.pattern === undefined) {
      this.pattern = this.field.properties.pattern;
    }

    if (this.placeHolder === undefined) {
      this.placeHolder = this.field.properties.placeHolder;
    }

    if (this.minLength === undefined) {
      this.minLength = this.field.attribute.option.minLength;
    }

    if (this.maxLength === undefined) {
      this.maxLength = this.field.attribute.option.maxLength;
    }

    if (this.lowerBoundValue === undefined) {
      this.lowerBoundValue = (this.field.attribute as TxAttributePoint).lowerBound;
    }

    if (this.upperBoundValue === undefined) {
      this.upperBoundValue = (this.field.attribute as TxAttributePoint).upperBound;
    }

    this.withMaxLength = this.minLength !== undefined || this.maxLength !== undefined;
    this.withBounds = this.lowerBoundValue !== undefined || this.upperBoundValue !== undefined;
  }
}
