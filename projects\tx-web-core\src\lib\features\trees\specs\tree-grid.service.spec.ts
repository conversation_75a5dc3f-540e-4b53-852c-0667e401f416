/* tslint:disable:no-unused-variable */

import { TestBed } from '@angular/core/testing';
import {
  DataBaseRights,
  TxLockingType,
  TxObjectTypeType,
  TxTreeGrid,
  TxTreeGridService,
} from '@bassetti-group/tx-web-core';
import {
  RowCollapsedEventArgs,
  RowExpandedEventArgs,
  TreeGridComponent,
} from '@syncfusion/ej2-angular-treegrid';

describe('Service: TreeGrid', () => {
  let service: TxTreeGridService;
  let data: TxTreeGrid<any>[];
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [TxTreeGridService],
    });
    service = TestBed.inject(TxTreeGridService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('onNodeExpanded', () => {
    let rowExpandedEvent: RowExpandedEventArgs;
    beforeEach(() => {
      rowExpandedEvent = { data: { id: 1 } };
    });

    it('should expand object if object is found', () => {
      data = [{ id: 1, expanded: false, name: 'Contact', txObject: {} }];
      service.onNodeExpanded(rowExpandedEvent, data);
      expect(data[0].expanded).toBe(true);
    });

    it('should not expand object if object is not found', () => {
      data = [{ id: 2, expanded: false, name: 'Contact', txObject: {} }];
      service.onNodeExpanded(rowExpandedEvent, data);
      expect(data[0].expanded).toBe(false);
    });
  });

  describe('onNodeCollapsed', () => {
    let rowCollapsedEvent: RowCollapsedEventArgs;
    beforeEach(() => {
      rowCollapsedEvent = { data: { id: 1 } };
    });

    it('should collapse object if object is found', () => {
      data = [{ id: 1, expanded: true, name: 'Contact', txObject: {} }];
      service.onNodeCollapsed(rowCollapsedEvent, data);
      expect(data[0].expanded).toBe(false);
    });

    it('should not collapse object if object is not found', () => {
      data = [{ id: 2, expanded: true, name: 'Contact', txObject: {} }];
      service.onNodeCollapsed(rowCollapsedEvent, data);
      expect(data[0].expanded).toBe(true);
    });
  });

  describe('getExpandedState', () => {
    beforeEach(() => {
      data = [{ id: 1, expanded: false, name: 'Contact', txObject: {} }];
    });

    it('should return the expanded state on an object if found', () => {
      const obj = {
        id: 1,
        name: 'Contact',
        order: 1,
        type: TxObjectTypeType.Standard,
        isVisible: true,
        displayResultInTextSearch: true,
        hasDistinctName: true,
        icon: 1,
        isFolder: false,
        lockingDuration: 0,
        lockingType: TxLockingType.None,
        right: DataBaseRights.DbrStructure,
        tags: [],
      };
      expect(service.getExpandedState(obj, data)).toBe(false);
    });

    it('should return the default value which is true if object is not found', () => {
      const obj = {
        id: 2,
        name: 'User',
        order: 2,
        type: TxObjectTypeType.User,
        isVisible: true,
        displayResultInTextSearch: true,
        hasDistinctName: true,
        icon: 1,
        isFolder: false,
        lockingDuration: 0,
        lockingType: TxLockingType.None,
        right: DataBaseRights.DbrStructure,
        tags: [],
      };
      expect(service.getExpandedState(obj, data)).toBe(true);
    });
  });

  describe('onRowBound', () => {
    let rowDataBoundEventIdInteger: any;
    let rowDataBoundEventIdNonInteger: any;
    beforeEach(() => {
      rowDataBoundEventIdInteger = { data: { id: 123 }, row: undefined };
      rowDataBoundEventIdInteger.row = document.createElement('div');
      rowDataBoundEventIdNonInteger = { data: { id: 'is non integer' }, row: undefined };
      rowDataBoundEventIdNonInteger.row = document.createElement('div');
    });

    it('should add row-opacity class if id is not an integer', () => {
      service.onRowBound(rowDataBoundEventIdNonInteger);
      const isClassAdded = rowDataBoundEventIdNonInteger.row?.classList.contains('row-opacity');
      expect(isClassAdded).toBe(true);
    });

    it('should not add row-opacity class if id is an integer', () => {
      service.onRowBound(rowDataBoundEventIdInteger);
      const isClassAdded = rowDataBoundEventIdInteger.row?.classList.contains('row-opacity');
      expect(isClassAdded).toBe(false);
    });

    it('should not add row-opacity class if row is undefined', () => {
      const rowDataBoundEventRowUndefined: any = {
        data: { id: 'not an integer' },
        row: undefined,
      };
      service.onRowBound(rowDataBoundEventRowUndefined);
      const isClassAdded =
        rowDataBoundEventRowUndefined.row?.classList.contains('row-opacity') ?? false;
      expect(isClassAdded).toBe(false);
    });
  });

  describe('expandParentRows', () => {
    let rowInfo: any;
    let grid: TreeGridComponent;

    beforeEach(() => {
      grid = TestBed.createComponent(TreeGridComponent).componentInstance;
    });

    describe('parentItem exists in rowInfo', () => {
      let parentRow: HTMLTableRowElement;
      beforeEach(() => {
        rowInfo = { rowData: { parentItem: { index: 1 } } };
        parentRow = document.createElement('tr');
        grid.getRowByIndex = jest.fn().mockReturnValue(parentRow);
        grid.expandRow = jest.fn();
        grid.getRowInfo = jest
          .fn()
          .mockReturnValueOnce({
            rowIndex: 1,
            row: parentRow,
            rowData: { parentItem: { index: 2 } },
            column: {},
          })
          .mockReturnValueOnce({
            rowIndex: 1,
            row: parentRow,
            rowData: { parentItem: { index: 3 } },
            column: {},
          })
          .mockReturnValueOnce({
            rowIndex: 1,
            row: parentRow,
            rowData: { parentItem: undefined },
            column: {},
          });
      });

      it('should expand row 3 times', () => {
        const spyExpandRow = jest.spyOn(grid, 'expandRow');
        service.expandParentRows(rowInfo, grid);
        expect(spyExpandRow).toBeCalledTimes(3);
      });

      it('should call expandParentRow recursively ', () => {
        const spyExpandParentRows = jest.spyOn(service, 'expandParentRows'); //first call
        service.expandParentRows(rowInfo, grid);
        expect(spyExpandParentRows).toBeCalledTimes(4); //called as many times until parent item of row is undefined
      });
    });

    describe('parentItem does not exist in rowInfo', () => {
      beforeEach(() => {
        rowInfo = { rowData: { parentItem: undefined } };
      });

      it('should not expand row', () => {
        const spyExpandRow = jest.spyOn(grid, 'expandRow');
        service.expandParentRows(rowInfo, grid);
        expect(spyExpandRow).not.toBeCalled();
      });

      it('should stop the recursive call of this function', () => {
        const spyExpandParentRows = jest.spyOn(service, 'expandParentRows');
        service.expandParentRows(rowInfo, grid);
        expect(spyExpandParentRows).toBeCalledTimes(1); //not called for the second time
      });
    });
  });

  describe('getTreeGridFromParent', () => {
    beforeEach(() => {
      data = [
        { id: 1, idParent: undefined, name: 'Parent', txObject: {} },
        { id: 11, idParent: 1, name: 'Child 1', txObject: {} },
        { id: 111, idParent: 11, name: '1st GrandChild', txObject: {} },
        { id: 112, idParent: 11, name: '2nd GrandChild ', txObject: {} },
        { id: 12, idParent: 1, name: 'Child 2', txObject: {} },
        { id: 121, idParent: 12, name: '3rd GrandChild', txObject: {} },
        { id: 13, idParent: 1, name: 'Child 3', txObject: {} },
      ];
    });

    it('should return all child and grandchildren ', () => {
      expect(service.getTreeGridFromParent(1, data).map((t) => t.id)).toStrictEqual([
        11, 111, 112, 12, 121, 13,
      ]);
    });

    it('should return empty array if id has no child ', () => {
      expect(service.getTreeGridFromParent(13, data).map((t) => t.id)).toStrictEqual([]);
    });

    it('should return empty array if no matching id parent is found ', () => {
      expect(service.getTreeGridFromParent(99, data).map((t) => t.id)).toStrictEqual([]);
    });
  });
});
