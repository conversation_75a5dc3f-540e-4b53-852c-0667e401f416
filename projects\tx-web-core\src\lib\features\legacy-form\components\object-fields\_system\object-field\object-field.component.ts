import { Observable } from 'rxjs';
import { Component, EventEmitter, Input, Output, AfterViewInit, OnInit } from '@angular/core';
import { TxBaseFieldComponent } from '../../../generic-fields/base-field/base-field.component';
import { AbstractControl, FormControl, ValidatorFn, Validators } from '@angular/forms';
import { TxAttributeField } from '../../../../models/formConfiguration/businessClass/attribute-field';
import { LegacyTxAttributesService } from '../../../../services/structure/services/attributes.service';
import { LegacyTxAttribute } from '../../../../services/structure/models/attribute';
import { LegacyTxData } from '../../../../services/structure/models/data';
import { _Utils } from '@bassetti-group/tx-web-core/src/lib/utilities';

@Component({
  selector: 'app-object-field',
  templateUrl: './object-field.component.html',
})
export class TxObjectFieldComponent extends TxBaseFieldComponent implements AfterViewInit, OnInit {
  @Input() idObject: any;
  @Input() idAttribute = 0;
  @Input() attributeTag!: string;
  @Input() attribute!: LegacyTxAttribute;
  @Input() data!: LegacyTxData;
  @Input() field!: TxAttributeField;

  @Output() hideEvent = new EventEmitter<boolean>();
  @Output() afterInit = new EventEmitter<TxObjectFieldComponent>();
  @Output() onChange = new EventEmitter<any>();

  constructor(public attributeService: LegacyTxAttributesService) {
    super();
  }

  ngAfterViewInit(): void {
    this.afterInit.emit(this);

    if (this.control)
      this.control.valueChanges.subscribe(() => {
        this.onChange.emit(this.control.value);
      });
  }

  loadSomething(): Observable<unknown> {
    return new Observable((observer) => {
      if (!this.attribute) {
        if (this.idAttribute) {
          this.attributeService.listAttributes(this.idAttribute).subscribe((attributes) => {
            this.attribute = attributes[0];
            observer.next();
            observer.complete();
          });
          return;
        } else if (this.attributeTag) {
          this.attributeService.listAttributes(this.attributeTag).subscribe((attributes) => {
            this.attribute = attributes[0];
            observer.next();
            observer.complete();
          });
          return;
        } else if (this.field && this.field.attribute) {
          this.attribute = this.field.attribute;
        }
      }
      observer.next();
      observer.complete();
    });
  }

  initProperties() {
    super.initProperties();

    if (this.field) {
      this.initPropertiesFromField();
    }

    if (this.attribute) {
      this.initPropertiesFromAttribute();
    }

    if (!this.attribute) {
      // this.attribute = new TxAttribute({});
    }

    if (this.form && !this.getFormControl(this.id)) {
      this.form.addControl(this.id, this.control);
    }

    if (this.data) {
      this.setData(this.data);
    }
  }

  initPropertiesFromAttribute() {
    if (!this.attribute) {
      this.attribute = this.field?.attribute;
    }

    if (this.idAttribute < 1) {
      this.idAttribute = this.attribute.id;
    }

    if (!this.id && this.attribute.id) {
      this.id = this.attribute.id.toString();
    }
    if (!this.label) {
      this.label = this.attribute.name;
    }
  }

  initFormControl(name: string) {
    this.control = this.createFormControl(name, this.value, this.getValidators(), this.disabled);
  }

  getValidators(): ValidatorFn[] {
    const res = [];
    if (!this.readMode) {
      if (this.isMandatory()) {
        res.push(Validators.required);
      }
    }
    return res;
  }

  initPropertiesFromField() {
    if (!this.id && this.field.id) {
      this.id = this.field.id.toString();
    }

    if (!this.label) {
      this.label = this.field.attribute.name;
    }

    if (!this.attribute) {
      this.attribute = this.field.attribute;
    }

    if (!this.classes) {
      this.classes = this.field.classes;
    }

    if (!this.labelTooltip) {
      this.labelTooltip = this.attributeService.getHint(this.attribute.id);
      // this.labelTooltip = this.attribute.id.toString();

      // if (!environment.production){
      //   this.labelTooltip = `id : ${this.attribute.id}  ${this.labelTooltip}`;
      //   this.label = this.labelTooltip;
      // }

      if (!this.attribute.description) {
        // Todo: add new line + description on ToolTip
        // this.labelTooltip += '\n description';
      }
    }
    if (!this.control) {
      this.initFormControl(this.id);
    }
  }

  createFormControl(
    name: string,
    initValue: any,
    validators: ValidatorFn[] = [],
    disabled: boolean
  ): AbstractControl {
    const control = new FormControl({ value: initValue, disabled: disabled }, validators);
    if (this.form) {
      this.form.addControl(name, control);
    }
    return control;
  }

  getData(): LegacyTxData | undefined {
    return;
  }

  getDataToSave(): LegacyTxData | undefined {
    if (this.valueHasChanged() && this.isValid) {
      if (this.valueIsEmpty()) {
        // add a deleted data object
        return LegacyTxData.removedData(this.idObject, this.idAttribute);
      } else {
        return this.getData();
      }
    } else {
      return;
    }
  }

  isMandatory(): boolean {
    if (!_Utils.isAssigned(this.required)) {
      let res = false;
      if (this.field?.properties?.mandatory !== undefined) {
        res = this.field?.properties?.mandatory;
      }
      this.required = res;
    }
    return this.required;
  }

  clear() {
    this.control?.reset();
    this.value = '';
    this.clearControl();
  }

  clearControl() {
    this.control?.setValue('');
  }

  setData(data: any) {
    this.clear();
    this.data = data;
    this.control?.setValue(data.value);
  }

  haveToBeHidden() {
    return this.readMode && this.valueIsEmpty();
  }

  addFieldClasses(classToAdd: string) {
    this.field.classes += ' ' + classToAdd;
  }
}
