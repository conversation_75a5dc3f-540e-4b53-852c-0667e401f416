.tested-core-model-concepts {
  &__table {
    width: 100%;
    height: calc(100% - 1.5rem);
  }
  &__table-info {
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-top: 0;
    padding: 0 5px;
  }
  &__conflicts-td {
    margin: 0;
    font-weight: 600;
    &--conflicts {
      display: flex;
      flex-direction: column;
      text-align: start;
      row-gap: 0.2rem;
    }
    &--none {
      text-transform: capitalize;
      text-align: center;
    }
  }
  &__conflicts-th {
    margin: 0;
    display: grid;
    align-items: center;
    grid-template-columns: auto 1fr;
  }
  &__error-badge {
    align-self: center;
    justify-self: start;
    margin: 0.2rem;
  }
}
.slider-wrapper {
  display: flex;
  align-items: center;
}
.status-cell {
  min-width: 115% !important;
}
