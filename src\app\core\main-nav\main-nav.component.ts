import { animate, style, transition, trigger } from '@angular/animations';
import { DOCUMENT } from '@angular/common';
import { Component, ElementRef, Inject, OnInit, ViewChild } from '@angular/core';
import { MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { Event, NavigationEnd, Router } from '@angular/router';
import {
  AdministrationSettings,
  AuthenticationService,
  ConnectedUser,
  ToastService,
  ToastType,
  TxConfigService,
  TxTreeViewSelect,
} from '@bassetti-group/tx-web-core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { Subscription, timer } from 'rxjs';
import { take } from 'rxjs/operators';
import { ErrorService } from 'src/app/core/services/errors/error.service';
import { SearchService, SearchSettings } from 'src/app/core/services/search/search.service';
import { environment } from 'src/environments/environment';
import { AboutComponent } from '../about/about.component';
import { AppService } from '../services/app.service';
import { SessionService } from '../services/session/session.service';
import { RouteUtils } from '../utils/route.utils';
import { AdminTree } from './main-nav-models';
import { SearchPaneComponent, SearchType } from './search-pane/search-pane.component';

export interface Lang {
  id: number;
  name: string;
  nameBis?: string;
  noFlag?: boolean;
  code: string;
  languageUsedCode: string;
}

@Component({
  selector: 'app-main-nav',
  templateUrl: './main-nav.component.html',
  styleUrls: ['./main-nav.component.scss'],
  animations: [
    trigger('slideInOut', [
      transition(':enter', [
        style({ transform: 'translateX(-1300%)' }),
        animate('250ms 150ms ease-in', style({ transform: 'translateX(0%)' })),
      ]),
      transition(':leave', [animate('250ms ease-out', style({ transform: 'translateX(-1300%)' }))]),
    ]),
    trigger('fadeInOut', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('250ms 300ms ease-in', style({ opacity: 1 })),
      ]),
      transition(':leave', [
        style({ opacity: 1 }),
        animate('250ms ease-out', style({ opacity: 0 })),
      ]),
    ]),
    trigger('leaveTrigger', [
      transition(':leave', [style({ opacity: 1 }), animate('300ms', style({ opacity: 0 }))]),
    ]),
  ],
})
export class MainNavComponent implements OnInit {
  @ViewChild('aboutComponent') aboutComponent?: AboutComponent;
  @ViewChild('searchComponent') searchComponent?: SearchPaneComponent;
  @ViewChild('searchInput') searchInput?: ElementRef<HTMLInputElement>;
  @ViewChild(MatAutocompleteTrigger) autoComplete?: MatAutocompleteTrigger;

  public sidenavSelectedItem?: string;
  public isExpanded = true;
  public isSearchActive = false;
  public isPageLoading = true;
  public isLoaderDisplay = true;
  public languages: Lang[] = [];
  public actualTheme?: string;
  public contrastTheme?: string;
  public isDarkMode?: boolean;
  public connectedUser?: ConnectedUser;
  public actualYear?: number;
  public adminTree: AdminTree[] = RouteUtils.createMainNavAdminTree(this.router.config);
  public field: AdminTree[] = [];
  public selectedNode: string = this.adminTree[0].id;
  public searchValue = '';
  public searchSettings: SearchSettings = { words: [], searchType: SearchType.Name };
  public administrationSettings?: AdministrationSettings;
  public isNavigationToTEEXMAAllowed = false;
  hoveredOption = '';
  sessionTimeout = 0;
  timerSubscription?: Subscription;

  constructor(
    private readonly appService: AppService,
    private readonly router: Router,
    private readonly configService: TxConfigService,
    private readonly sessionService: SessionService,
    private readonly toastService: ToastService,
    private readonly authService: AuthenticationService,
    private readonly searchService: SearchService,
    private readonly errorService: ErrorService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  ngOnInit(): void {
    this.router.events.subscribe((event: Event) => {
      if (event instanceof NavigationEnd) {
        this.sidenavSelectedItem = this.getRouteFromUrl(event.url);
        this.afterDataLoad();
      }
    });
    if (!environment.production) {
      this.adminTree.push({
        id: '666',
        adminName: 'Sand box',
        adminIcon: ['fal', 'umbrella-beach'],
        adminRights: [],
        route: 'sandBox',
        isParent: true,
        tooltipTrad: 'This page allows to test new generic components not in use yet.',
        isSelected: false,
      });
    }
    this.sessionService.getSessionTimeout().subscribe(
      (timeout) => {
        this.sessionTimeout = timeout;
      },
      (error) => {},
      () => {
        this.resetTimer();
      }
    );
    this.authService.userActionOccured.subscribe(() => {
      if (this.timerSubscription) {
        this.timerSubscription.unsubscribe();
      }
      this.resetTimer();
    });
    this.sessionService.getLanguages().subscribe((langs: Lang[]) => {
      this.languages = langs;
      this.administrationSettings = this.sessionService.getAdministrationSettings();
      this.isNavigationToTEEXMAAllowed = this.sessionService.isNavigationToTEEXMAllowed();
    });
    this.sessionService.getConnectedUser().subscribe((user) => {
      if (user?.isLoaded) {
        this.connectedUser = user;
        this.filterAdminTree();
      }
    });
    this.appService.getAppLoaded().subscribe((isLoaded) => {
      this.isPageLoading = !isLoaded;
      setTimeout(() => {
        this.isLoaderDisplay = !isLoaded;
      }, 300);
    });
    const today = new Date();
    this.appService.getThemeClass().subscribe((theme) => {
      const splittedThemeName = theme.split('-');
      this.actualTheme = `${splittedThemeName[0]}-${splittedThemeName[1]}`;
      this.contrastTheme = splittedThemeName[2];
      this.isDarkMode = this.contrastTheme === 'dark';
    });
    this.actualYear = today.getFullYear();
    this.searchService.userSettings.subscribe((searchSettings: SearchSettings) => {
      this.searchSettings = searchSettings;
    });
  }

  resetTimer(endTime: number = this.sessionTimeout) {
    if (endTime === 0) {
      return;
    }

    const interval = 1000;
    const duration = endTime * 60;
    this.authService.updateSession(duration * 1000);
    this.timerSubscription = timer(0, interval)
      .pipe(take(duration))
      .subscribe(
        (value) => {},
        (err) => {},
        () => {
          this.authService.clearSession(); // make sure to clear token before in case of page reloading
          this.errorService.addError({
            error: {
              errorKey: 'USER_IS_DISCONNECTED_TIMEOUT',
            },
          });
        }
      );
  }

  getNumberUnreadNotif(): number {
    return this.toastService.getNotifications().filter((notif) => notif.isUnread).length;
  }

  getTooltipNotifications(): string {
    if (this.getNumberUnreadNotif() > 0) {
      return _('toolbar.someNotifications');
    } else {
      return _('toolbar.noNotifications');
    }
  }

  hasUnreadNotifications(): boolean {
    return this.toastService.hasUnreadNotifications();
  }

  openNotification() {
    //gives delay so that the user will still see the new notification before considering it as read
    setTimeout(() => {
      this.toastService.getNotifications().forEach((notif) => {
        notif.isUnread = false;
      });
    }, 1500);
  }

  changeTheme(theme: string) {
    this.actualTheme = theme;
    this.applyContrastAndTheme();
    this.resetSideNav();
  }

  changeContrast(contrast: string) {
    this.isDarkMode = contrast === 'dark';
    this.contrastTheme = contrast;
    this.applyContrastAndTheme();
    this.resetSideNav();
  }

  getSrcLogo(): string {
    return this.isDarkMode
      ? './assets/img/logo-bassetti-white.svg'
      : './assets/img/logo-bassetti.svg';
  }

  getTxIcon(): string {
    return this.isDarkMode
      ? './assets/img/favicon-tx-dark-mode.svg'
      : './assets/img/favicon-tx-light-mode.svg';
  }

  applyContrastAndTheme() {
    this.appService.setThemeClass(`${this.actualTheme}-${this.contrastTheme}`);
  }

  changeLanguage(lang: Lang) {
    this.sessionService.registerCulture(lang, true);
  }

  onSideNavChange(): void {
    this.isExpanded = !this.isExpanded;
    this.appService.setSideNavState(this.isExpanded);
  }

  resetSideNav(): void {
    this.isExpanded = true;
    this.appService.setSideNavState(this.isExpanded);
  }

  backToDashboard() {
    this.router.navigate(['/' + RouteUtils.DASHBOARD_PATH]);
    this.sidenavSelectedItem = RouteUtils.DASHBOARD_PATH;
  }

  redirectToAdmin(item: AdminTree) {
    if (item.route !== undefined) {
      this.router.navigate([`/${item.route}`]);
      this.sidenavSelectedItem = item.route;
    }
  }

  closeSearch() {
    this.searchComponent?.hidePane();
  }

  updateSearch(value: string) {
    this.searchValue = value;
    this.autoComplete?.closePanel();
  }

  onRefreshStructureClicked(): void {
    this.configService.cleanCachedStructure().subscribe(() => {
      const message = _('mainNav.reloadCachedStructureSuccessful');
      const type: ToastType = 'success';
      this.toastService.show({
        templateContext: { data: { state: type, message } },
        date: new Date(),
        type,
        description: message,
        isPersistent: true,
        isUnread: true,
      });

      // refresh the current url
      const currentUrl = this.router.url;
      this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
        this.router.navigate([currentUrl]);
      });
    });
  }

  onSearchIconClicked() {
    this.isSearchActive = true;
    setTimeout(() => {
      this.focusInput();
    }, 200);
  }

  onEnterInputSearch() {
    if (this.searchSettings.searchType) {
      this.searchService.search(
        { term: this.searchValue, type: this.searchSettings.searchType },
        true
      );
    }
  }

  focusInput() {
    setTimeout(() => {
      this.searchInput?.nativeElement.focus();
    }, 300); // waiting for animation otherwise the autocomplete liste can have a wrong position
  }

  focusSearch() {
    if (this.searchValue !== '') {
      this.searchComponent?.displayPane();
    }
  }

  clearInput() {
    this.isSearchActive = false;
    this.searchValue = '';
    this.closeSearch();
  }

  onAboutMenuOpened() {
    this.aboutComponent?.onOpen();
  }

  goToTEEXMA() {
    const teexmaUrl = this.sessionService.getTEEXMAUrl();
    if (teexmaUrl !== '') {
      const link = this.document.createElement('a');
      link.target = '_blank';
      link.href = teexmaUrl;
      link.click();
      link.remove();
    }
  }

  logout() {
    this.authService.logout();
  }

  filterAdminTree() {
    this.field = this.adminTree.filter((admin) => {
      if (admin.hidden) {
        return false;
      } else if (admin.adminRights === undefined || admin.adminRights?.length === 0) {
        return true;
      } else {
        return this.sessionService.hasRightsOn(
          admin.adminRights,
          (this.connectedUser as ConnectedUser).adminRights
        );
      }
    });
  }

  afterDataLoad() {
    const item = this.adminTree.find((admin) => admin.route === this.sidenavSelectedItem);
    if (item?.id && !item.isSelected) {
      this.selectedNode = item.id;
      this.selectItem(item);
    }
  }

  beforeSelectingNode(event: TxTreeViewSelect<AdminTree>) {
    if (event.nodeData.children && event.nodeData.children.length > 0) {
      event.cancel = true;
    }
  }

  afterSelectingNode(event: TxTreeViewSelect<AdminTree>) {
    const item = this.adminTree.find((admin) => admin.id === event.nodeData.id);
    if (item?.route && !item.isSelected) {
      if (item.route === RouteUtils.DASHBOARD_PATH) {
        this.backToDashboard();
      } else {
        this.redirectToAdmin(item);
      }
    }
  }
  selectItem(item: AdminTree) {
    this.adminTree = this.adminTree.map((adminItem) =>
      adminItem.id === item.id
        ? { ...adminItem, isSelected: true }
        : { ...adminItem, isSelected: false }
    );
  }

  onKeyWordSelected(searchInput: { value: string }, option: string) {
    searchInput.value = option;
    this.updateSearch(option);
  }

  removeKeyWord(index: number, event: MouseEvent) {
    this.searchService.removeKeyWord(index);
    this.autoComplete?.openPanel();
    event.stopPropagation();
  }
  private getRouteFromUrl(routeUrl: string): string {
    const lastRoutePart = routeUrl.split('/').pop();
    let firstRoutePart = lastRoutePart?.split('?').shift(); // remove url params
    if (firstRoutePart === '') {
      firstRoutePart = RouteUtils.DASHBOARD_PATH;
    }
    return firstRoutePart ?? RouteUtils.DASHBOARD_PATH;
  }
}
