<tx-chips-field
  #chipsField
  *ngIf="!readMode"
  [label]="label"
  [labelTooltip]="labelTooltip"
  [control]="control"
  [disabled]="disabled"
  [required]="required"
  [visible]="visible"
  [selectable]="selectable"
  [removable]="removable"
  [addOnBlur]="addOnBlur"
  [multiple]="multiple"
  [maxChipsMessage]="maxChipsMessage"
  [information]="information"
  [value]="value"
  [pattern]="pattern"
  [data]="data"
  [withInputText]="true"></tx-chips-field>

<div *ngIf="readMode" class="form-field read-field">
  <mat-label
    class="read-form-label form-label mat-form-label"
    [matTooltip]="labelTooltip"
    matTooltipClass="mat-label-tooltip"
    matTooltipShowDelay="500"
    matTooltipPosition="above">
    {{ label }}
  </mat-label>
  <div class="read-form-field">
    <span
      class="url-read"
      [ngClass]="{ 'clickable-url': !isDirectoryUrl(value) }"
      *ngFor="let value of control.value"
      (click)="openUrl(value)"
      >{{ value }}</span
    >
  </div>
</div>
