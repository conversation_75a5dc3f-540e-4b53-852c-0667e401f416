import { HttpTestingController, TestRequest, provideHttpClientTesting } from '@angular/common/http/testing';
import { fakeAsync, TestBed, tick, waitForAsync } from '@angular/core/testing';
import { ConfigServiceMock, createFileFromMockFile, testSections } from 'src/app/app.testing.mock';
import { TxConfigService, ToastComponent } from '@bassetti-group/tx-web-core';
import { Audit, DatabaseInformation, Section, Worksheets } from '../models/audit';
import { AuditsService } from './audits.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

describe('Service: Audits', () => {
  let apiUrl: string;
  let service: AuditsService;
  let http: HttpTestingController;
  let defaultAuditReq: TestRequest;

  beforeEach(() => {
    TestBed.configureTestingModule({
    imports: [],
    providers: [{ provide: TxConfigService, useClass: ConfigServiceMock }, AuditsService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()]
});
    service = TestBed.inject(AuditsService);
    http = TestBed.inject(HttpTestingController);
  });

  beforeEach(() => {
    apiUrl = 'https://localhost:44336';
    defaultAuditReq = http.expectOne(`${apiUrl}/api/TxAudit/0`);
  });

  afterEach(() => {
    http.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Load default audit', () => {
    beforeEach(() => {
      defaultAuditReq.flush({ idAudit: 5, sections: testSections });
    });

    it('should get id from default audit', () => {
      expect(service.idAudit).toBe(5);
    });

    it('should get sections from default audit', () => {
      expect(service.sections.length).toBe(2);
    });

    it('should update sections', waitForAsync(() => {
      service.getSections().subscribe((sections) => {
        expect(sections).toBe(testSections);
      });
    }));

    it('should update file audit information', waitForAsync(() => {
      service.getInformationAuditPoint().subscribe((info) => {
        expect(info).toStrictEqual({
          isFileUploaded: false,
          numberAuditPoints: 3,
          fileName: undefined,
        });
      });
    }));

    it('should update loading bar', waitForAsync(() => {
      service.getLoadingBar().subscribe((state) => {
        expect(state).toBe(false);
      });
    }));
  });

  describe('Get Database information', () => {
    let mockResponse: DatabaseInformation;
    beforeEach(() => {
      mockResponse = { revision: 6666, version: '4.3.0' };
    });

    it('should get revision DatabaseInformation', () => {
      let revision = 0;

      service.getInfomation().subscribe((res: DatabaseInformation) => {
        revision = res.revision;
      });
      http.expectOne(`${apiUrl}/api/TxAudit/informations`).flush(mockResponse);

      expect(revision).toBe(6666);
    });

    it('should get version DatabaseInformation', () => {
      let version = '';

      service.getInfomation().subscribe((res: DatabaseInformation) => {
        version = res.version;
      });
      http.expectOne(`${apiUrl}/api/TxAudit/informations`).flush(mockResponse);

      expect(version).toBe('4.3.0');
    });
  });

  describe('Is audit already executed', () => {
    it('should be executed if there is a toast', () => {
      service.toastProgress = {} as ToastComponent;

      expect(service.isAlreadyExecuted()).toBe(true);
    });

    it('should not be executed if there is no sections loaded', () => {
      service.toastProgress = null;
      expect(service.isAlreadyExecuted()).toBe(false);
    });

    it('should not be executed if there is no sections with a status', () => {
      service.toastProgress = null;
      service.sections = [
        {
          name: 'Migration',
          auditPoints: [
            {
              id: 0,
              explanation: '',
              name: '[4.3+] Excel models',
              status: undefined,
              result: '',
              isMultiLine: false,
              allResult: '',
              isShowAllLine: false,
            },
          ],
        },
      ];

      expect(service.isAlreadyExecuted()).toBe(false);
    });

    it('should be executed if there is some sections with a status', () => {
      service.toastProgress = null;
      service.sections = [
        {
          name: 'Migration',
          auditPoints: [
            {
              id: 0,
              explanation: '',
              name: '[4.3+] Excel models',
              status: 'ok',
              result: '',
              isMultiLine: false,
              allResult: '',
              isShowAllLine: false,
            },
          ],
        },
      ];

      expect(service.isAlreadyExecuted()).toBe(true);
    });
  });

  describe('Post file', () => {
    let file: File;
    beforeEach(() => {
      file = createFileFromMockFile({ body: 'test', mimeType: 'text/plain', name: 'TxAudit' });
    });

    it('should post a new file', () => {
      const formData = new FormData();
      formData.append('file', file, file.name);

      service.postFile(file).subscribe((res: any) => {});

      const req = http.expectOne({
        url: `${apiUrl}/api/TxAudit/auditfile`,
        method: 'POST',
      });
      req.flush('');
      expect(req.request.body).toEqual(formData);
    });

    describe('Update information after post a file', () => {
      let fileSections: Section[];
      beforeEach(() => {
        fileSections = [
          {
            name: 'M',
            auditPoints: [
              {
                id: 0,
                explanation: 'Search...',
                name: 'Excel models',
                status: '',
                result: '',
                isMultiLine: false,
                allResult: '',
                isShowAllLine: false,
              },
              {
                id: 1,
                explanation: 'Searching...',
                name: 'Old Extraction module',
                status: '',
                result: '',
                isMultiLine: false,
                allResult: '',
                isShowAllLine: false,
              },
            ],
          },
        ];
      });

      it('should set idAudit file uploaded', fakeAsync(() => {
        service.postFile(file).subscribe((res: any) => {});
        http
          .expectOne({
            url: `${apiUrl}/api/TxAudit/auditfile`,
            method: 'POST',
          })
          .flush({ idAudit: 8, sections: fileSections });

        tick();

        expect(service.idAuditClient).toBe(8);
      }));

      it('should update sections', fakeAsync(() => {
        service.postFile(file).subscribe((res: any) => {});
        http
          .expectOne({
            url: `${apiUrl}/api/TxAudit/auditfile`,
            method: 'POST',
          })
          .flush({ idAudit: 8, sections: fileSections });

        tick();

        let sections;
        service.getSections().subscribe((s) => {
          sections = s;
        });

        tick();

        expect(sections).toBe(fileSections);
      }));

      it('should update file audit information', fakeAsync(() => {
        service.postFile(file).subscribe((res: any) => {});
        http
          .expectOne({
            url: `${apiUrl}/api/TxAudit/auditfile`,
            method: 'POST',
          })
          .flush({ idAudit: 8, sections: fileSections });

        tick();

        let info;
        service.getInformationAuditPoint().subscribe((i) => {
          info = i;
        });

        tick();

        expect(info).toStrictEqual({
          isFileUploaded: true,
          numberAuditPoints: 2,
          fileName: 'TxAudit',
        });
      }));
    });
  });

  describe('Reset audit points', () => {
    let sections: Section[];
    let resetSections: Section[];
    beforeEach(() => {
      sections = [
        {
          name: 'M',
          auditPoints: [
            {
              id: 0,
              explanation: 'Search...',
              name: 'Excel models',
              status: 'Ok',
              result: 'res',
              isMultiLine: true,
              allResult: 'aRes',
              isShowAllLine: false,
            },
            {
              id: 1,
              explanation: 'Searching...',
              name: 'Old Extraction module',
              status: 'Error',
              result: 'res',
              isMultiLine: false,
              allResult: 'aRes',
              isShowAllLine: true,
            },
          ],
        },
      ];
      resetSections = [
        {
          name: 'M',
          auditPoints: [
            {
              id: 0,
              explanation: 'Search...',
              name: 'Excel models',
              status: undefined,
              result: '',
              isMultiLine: false,
              allResult: '',
              isShowAllLine: false,
            },
            {
              id: 1,
              explanation: 'Searching...',
              name: 'Old Extraction module',
              status: undefined,
              result: '',
              isMultiLine: false,
              allResult: '',
              isShowAllLine: false,
            },
          ],
        },
      ];
    });

    it('should reset results for all audit points', () => {
      service.sections = sections;
      service.updateAuditPoints();
      expect(service.sections).toStrictEqual(resetSections);
    });

    it('should update sections', fakeAsync(() => {
      service.sections = sections;
      service.updateAuditPoints();

      let sec;
      service.getSections().subscribe((s) => {
        sec = s;
      });

      tick();
      expect(sec).toStrictEqual(resetSections);
    }));
  });

  describe('Export audit results', () => {
    let sections: Section[];
    let expectedWorksheet: Worksheets;
    beforeEach(() => {
      sections = [
        {
          name: 'Migration',
          auditPoints: [
            {
              id: 0,
              name: 'Excel models',
              status: 'Ok',
              result: 'first result',
              allResult: '',
            } as Audit,
            {
              id: 1,
              name: 'Old Extraction module',
              status: 'Ok',
              result: 'one\ntwo\nthree',
              allResult: 'four\nfive',
            } as Audit,
          ],
        },
        {
          name: 'Tests',
          auditPoints: [
            {
              id: 2,
              name: 'IDK',
              status: 'Error',
              result: 'error1\nerror2',
              allResult: '',
            } as Audit,
          ],
        },
      ];
      expectedWorksheet = {
        worksheets: [
          {
            name: 'Migration',
            cells: [
              { row: 1, col: 1, data: 'Name' },
              { row: 1, col: 2, data: 'Status' },
              { row: 1, col: 3, data: 'Message' },
              { row: 2, col: 1, data: 'Excel models' },
              { row: 2, col: 2, data: 'Ok' },
              { row: 2, col: 3, data: 'first result' },
              { row: 3, col: 1, data: 'Old Extraction module' },
              { row: 3, col: 2, data: 'Ok' },
              { row: 3, col: 3, data: 'one' },
              { row: 4, col: 1, data: 'Old Extraction module' },
              { row: 4, col: 2, data: 'Ok' },
              { row: 4, col: 3, data: 'two' },
              { row: 5, col: 1, data: 'Old Extraction module' },
              { row: 5, col: 2, data: 'Ok' },
              { row: 5, col: 3, data: 'three' },
            ],
          },
          {
            name: 'Tests',
            cells: [
              { row: 1, col: 1, data: 'Name' },
              { row: 1, col: 2, data: 'Status' },
              { row: 1, col: 3, data: 'Message' },
              { row: 2, col: 1, data: 'IDK' },
              { row: 2, col: 2, data: 'Error' },
              { row: 2, col: 3, data: 'error1' },
              { row: 3, col: 1, data: 'IDK' },
              { row: 3, col: 2, data: 'Error' },
              { row: 3, col: 3, data: 'error2' },
            ],
          },
        ],
      };
    });

    it('should export results to worksheets', () => {
      service.sections = sections;
      service.exportResults().subscribe((res) => {});

      const req = http.expectOne({
        url: `${apiUrl}/api/Export/spreadsheet/xlsx`,
        method: 'POST',
      });
      expect(req.request.body).toStrictEqual(expectedWorksheet);
    });
  });

  describe('Delete audit files', () => {
    it('should delete uploaded file', () => {
      service.informationAudit.isFileUploaded = true;
      service.idAuditClient = 8;

      service.deleteAuditFiles().subscribe((res) => {});

      http.expectOne({
        url: `${apiUrl}/api/TxAudit/files/${service.idAuditClient}`,
        method: 'DELETE',
      });
    });

    it('should delete default file (from temp table)', () => {
      service.informationAudit.isFileUploaded = false;
      service.idAudit = 7;

      service.deleteAuditFiles().subscribe((res) => {});

      http.expectOne({
        url: `${apiUrl}/api/TxAudit/files/${service.idAudit}`,
        method: 'DELETE',
      });
    });
  });

  describe('Execute queries', () => {
    describe('Use idAudit file', () => {
      let sections: Section[];
      beforeEach(() => {
        sections = [{ name: 'Migration', auditPoints: [{ id: 0 } as Audit] }];
      });

      it('should execute queries for default audit', () => {
        service.informationAudit.isFileUploaded = false;
        service.idAudit = 4;
        service.sections = sections;

        service.executeQueries().subscribe((res) => {});

        http.expectOne(`${apiUrl}/api/TxAudit/queryresult/${service.idAudit}/0`);
      });

      it('should execute queries for uploaded audit file', () => {
        service.informationAudit.isFileUploaded = true;
        service.idAuditClient = 4;
        service.sections = sections;

        service.executeQueries().subscribe((res) => {});

        http.expectOne(`${apiUrl}/api/TxAudit/queryresult/${service.idAuditClient}/0`);
      });
    });

    describe('Queries responses', () => {
      beforeEach(() => {
        service.idAudit = 5;
        service.informationAudit.isFileUploaded = false;
        service.sections = [{ name: 'Migration', auditPoints: [{ id: 0, name: 'test' } as Audit] }];
      });

      it('should catch and return message error', fakeAsync(() => {
        const mockResponse = { status: 400, statusText: 'Bad Request' };
        const errorMsg = 'Invalid query.';

        service.executeQueries().subscribe();
        http
          .expectOne(`${apiUrl}/api/TxAudit/queryresult/${service.idAudit}/0`)
          .flush(errorMsg, mockResponse);

        tick();

        let sections;
        service.getSections().subscribe((s) => {
          sections = s;
        });

        tick();

        expect(sections).toStrictEqual([
          {
            name: 'Migration',
            auditPoints: [
              {
                id: 0,
                status: 'error',
                result: 'Invalid query. Please check the audit file for this point',
                name: 'test',
                isShowAllLine: false,
                isMultiLine: false,
              },
            ],
          },
        ]);
      }));

      it('should get a result without multiline', fakeAsync(() => {
        service.executeQueries().subscribe((res) => {});
        http.expectOne(`${apiUrl}/api/TxAudit/queryresult/${service.idAudit}/0`).flush({
          id: 0,
          name: 'test',
          status: 'ok',
          result: 'first result',
        } as Audit);

        tick();

        let sections;
        service.getSections().subscribe((s) => {
          sections = s;
        });

        tick();

        expect(sections).toStrictEqual([
          {
            name: 'Migration',
            auditPoints: [
              {
                id: 0,
                status: 'ok',
                result: 'first result',
                name: 'test',
                isShowAllLine: false,
                isMultiLine: false,
              },
            ],
          },
        ]);
      }));

      it('should get a result with multiline', fakeAsync(() => {
        service.executeQueries().subscribe((res) => {});
        http.expectOne(`${apiUrl}/api/TxAudit/queryresult/${service.idAudit}/0`).flush({
          id: 0,
          name: 'test',
          status: 'warning',
          result: 'first\nsecond\nthird\nfourth\nfifth',
        } as Audit);

        tick();

        let sections;
        service.getSections().subscribe((s) => {
          sections = s;
        });

        tick();

        expect(sections).toStrictEqual([
          {
            name: 'Migration',
            auditPoints: [
              {
                id: 0,
                status: 'warning',
                result: 'first\nsecond\nthird',
                allResult: 'fourth\nfifth',
                name: 'test',
                isShowAllLine: false,
                isMultiLine: true,
              },
            ],
          },
        ]);
      }));
    });
  });
});
