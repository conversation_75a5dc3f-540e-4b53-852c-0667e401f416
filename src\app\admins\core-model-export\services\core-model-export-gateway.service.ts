import { Observable } from 'rxjs';
import { CoreModelExportHistoryDTO } from '../models/core-model-export-history-object.dto';
import { CoreModelExportConceptDTO } from '../models/core-model-export-concept.dto';

export abstract class CoreModelsExportGatewayService {
  abstract loadCoreModelsExportHistoryList(): Observable<CoreModelExportHistoryDTO[]>;
  abstract exportCoreModels(history: CoreModelExportHistoryDTO): Observable<Blob>;
  abstract loadCoreModelsConceptData(): Observable<CoreModelExportConceptDTO[]>;
}
