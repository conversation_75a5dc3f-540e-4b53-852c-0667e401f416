:host {
  width: 100%;
  height: 100%;

  * {
    box-sizing: border-box;
  }

  box-sizing: border-box;
}

.filter-search-pane {
  grid-row: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-flow: row wrap;
  padding: 0.1rem 0.5rem;
  border-bottom: none;
}

.filters {
  display: flex;
  align-items: center;
  flex: 6;
  &__display-only-concepts-in-error {
    margin-inline-start: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.table-grid {
  height: 100%;
  grid-row: 2;
  width: 100%;
  display: grid;
  grid-template-rows: minmax(calc(100% - 1.625rem), 100%) auto;

  &__table {
    height: 100%;
    ::ng-deep .mat-mdc-row {
      height: auto;
    }
  }

  &__table-info {
    padding: 0.25rem 0.5rem;
    text-align: end;
    border-top: none;
  }
}

.no-label-input {
  margin-top: 0.16rem;
}

.chip-table {
  white-space: nowrap;
}

.error-cell {
  height: 100%;
  display: grid;
  place-items: center;
  padding: 0 1rem;
  min-width: 100%;
}

.ok-container {
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-weight: 600;
  text-transform: capitalize;
}

tx-texts-to-copy,
tx-concept-name-table-cell {
  max-width: 100%;
  white-space: nowrap;
}

.text-with-icon {
  display: flex;
  column-gap: 0.25rem;
  align-items: center;
  span {
    max-width: 100%;
    white-space: nowrap;
  }
}

.groupHeader {
  font-weight: 700;
  font-size: 14px;
  margin-left: 8px;
}

.background-e-error {
  margin-inline-start: 0.5rem;
}

.error-container {
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.24rem;
  align-items: start;
}