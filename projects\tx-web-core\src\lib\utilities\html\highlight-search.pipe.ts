import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  standalone: true,
  name: 'highlightSearch',
})
export class TxHighlightSearchPipe implements PipeTransform {
  transform(value: string, args: string | undefined): string {
    if (!args) {
      return value;
    }

    args = args.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&'); // escape special chars for regex

    //to make sure it is accent insensitive
    args = args
      .replace(/[OÒÓÔÕÖoòóôõö]/g, '[oòóôõö]')
      .replace(/[AÀÁÂÃÄÅaàáâãäå]/g, '[aàáâãäå]')
      .replace(/[EÈÉÊËeèéêë]/g, '[eèéêë]')
      .replace(/[CÇcç]/g, '[cç]')
      .replace(/[IÌÍÎÏiìíîï]/g, '[iìíîï]')
      .replace(/[NÑnñ]/g, '[nñ]')
      .replace(/[UÙÚÛÜuùúûü]/g, '[uùúûü]')
      .replace(/[YÝŸyýÿ]/g, '[yýÿ]');

    const re = new RegExp(args, 'gi'); // 'gi' for case insensitive
    return value.replace(re, '<mark>$&</mark>');
  }
}
