import { NgModule } from '@angular/core';
import { TxInputTextFieldComponent } from './input-text-field/input-text-field.component';
import { TxReadTextFieldComponent } from './read-text-field/read-text-field.component';
import { TxInputNumberComponent } from './input-number/input-number.component';
import { TxInputMinMaxMeanComponent } from './input-number/input-min-max-mean.component';
import { TxInputSelectComponent } from './input-select/input-select.component';
import { TxChipsFieldComponent } from './chips-field/chips-field.component';
import { TxUploadFileFieldComponent } from './file-field-alt/upload-file-field.component';
import { TxFileFieldBoxComponent } from './file-field-alt/file-field-box/file-field-box.component';
import { TxSlideToggleComponent } from './tx-slide-toggle/tx-slide-toggle.component';
import { TxDatepickerComponent } from './datepicker';
import { TxDragDropDirective } from './dragDrop.directive';
import { TxColorPickerFieldComponent } from './color-picker-field/tx-color-picker-field.component';

@NgModule({
  imports: [
    TxInputTextFieldComponent,
    TxInputNumberComponent,
    TxInputMinMaxMeanComponent,
    TxInputSelectComponent,
    TxChipsFieldComponent,
    TxUploadFileFieldComponent,
    TxFileFieldBoxComponent,
    TxReadTextFieldComponent,
    TxSlideToggleComponent,
    TxDatepickerComponent,
    TxColorPickerFieldComponent,
    TxDragDropDirective,
  ],
  exports: [
    TxInputTextFieldComponent,
    TxInputNumberComponent,
    TxInputMinMaxMeanComponent,
    TxInputSelectComponent,
    TxChipsFieldComponent,
    TxUploadFileFieldComponent,
    TxFileFieldBoxComponent,
    TxReadTextFieldComponent,
    TxSlideToggleComponent,
    TxDatepickerComponent,
    TxColorPickerFieldComponent,
    TxDragDropDirective,
  ],
})
export class TxGenericFieldsModule {}
