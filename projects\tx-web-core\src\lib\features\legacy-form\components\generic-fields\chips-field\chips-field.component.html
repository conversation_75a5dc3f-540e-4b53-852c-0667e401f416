<div
  class="form-field"
  [ngClass]="{
    'form-field-error': control && !control.valid && !disabled,
    'read-field': readMode
  }">
  <mat-form-field
    *ngIf="!readMode && withInputText"
    hideRequiredMarker
    color="accent"
    [ngClass]="{ 'form-field-no-underline': !this.multiple && this.chips.length }"
    class="input-field"
    [hintLabel]="information">
    <mat-label
      class="form-label mat-form-label"
      matTooltipClass="mat-label-tooltip mat-tooltip-multiline"
      matTooltipShowDelay="500"
      matTooltipPosition="above">
      {{ label }}
      <span
        *ngIf="required"
        [ngClass]="{ 'span-error': control && control.hasError('required') }"
        class="mat-form-label">
        *</span
      >
    </mat-label>
    <mat-chip-list #chipList aria-label="Chips" [formControl]="control" class="chip-list">
      <div *ngFor="let chip of chips" class="data-chip-container">
        <mat-chip
          [matTooltip]="chip.name"
          [matTooltipShowDelay]="500"
          matTooltipPosition="above"
          [removable]="chip.removable"
          (removed)="remove(chip)"
          class="data-chip"
          [ngClass]="{
            'full-width-chip': fullWidthChip,
            'chip-selected': chip.selected,
            clickable: !disabled
          }">
          <div class="chip-text-container">
            <span class="chip-text" [ngClass]="{ 'removable-chip': chip.removable }">{{
              chip.name
            }}</span>
            <div *ngIf="chip.removable" class="icon-container">
              <mat-icon class="chip-icon-right remove-chip-icon" matChipRemove>cancel</mat-icon>
              <!-- <fa-icon class="chip-icon chip-icon-right" [icon]="['fal', 'times']" (click)="removeFile()"></fa-icon> -->
            </div>
          </div>
        </mat-chip>
      </div>
      <input
        matInput
        #inputChip
        class="input-text"
        *ngIf="this.multiple || !this.chips.length"
        [placeholder]="placeHolder"
        [matChipInputFor]="chipList"
        [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
        [matChipInputAddOnBlur]="addOnBlur"
        (matChipInputTokenEnd)="onAdd($event)"
        autocomplete="off" />
    </mat-chip-list>
    <mat-error *ngIf="control && control.hasError('required')"><strong>Required</strong></mat-error>
  </mat-form-field>

  <div *ngIf="readMode || !withInputText">
    <fa-icon
      matSuffix
      mat-icon-button
      *ngIf="actionIcon"
      [matTooltip]="actionIconToolTip"
      [icon]="actionIcon"
      class="action-icon"
      [ngClass]="{ 'disabled-icon': disabled }"
      (click)="onActionIconClick()"></fa-icon>
    <mat-label
      class="read-form-label form-label mat-form-label"
      matTooltipClass="mat-label-tooltip"
      matTooltipShowDelay="500"
      matTooltipPosition="above">
      {{ label }}
      <span
        *ngIf="required"
        [ngClass]="{ 'span-error': control && control.hasError('required') }"
        class="mat-form-label">
        *</span
      >
    </mat-label>
    <div class="read-form-field">
      <mat-chip-list
        #chipList
        aria-label="Chips"
        [formControl]="control"
        class="chip-list"
        (change)="OnSelectionChange()">
        <div *ngFor="let chip of chips.slice(0, maxChipDisplay)" class="data-chip-container">
          <mat-chip
            #matChip="matChip"
            (click)="onChipClick(chip)"
            (keyup.Space)="onChipClick(chip)"
            [matTooltip]="chip.name"
            [matTooltipShowDelay]="500"
            matTooltipPosition="above"
            [removable]="chip.removable"
            (removed)="remove(chip)"
            class="data-chip"
            [ngClass]="{
              'full-width-chip': fullWidthChip,
              'chip-selected': chip.selected,
              clickable: !disabled
            }">
            <div *ngIf="chip.fileUrl" class="img-thumbnail-container">
              <img [src]="chip.fileUrl" class="img-thumbnail" />
            </div>
            <div *ngIf="!chip.fileUrl || !hideTitleImageThumbnail" class="chip-text-container">
              <fa-icon
                *ngIf="chip.icon"
                matSuffix
                mat-icon-button
                [icon]="chip.icon"
                class="chip-icon chip-icon-left"></fa-icon>
              <span class="chip-text" [ngClass]="{ 'removable-chip': chip.removable }">{{
                chip.name
              }}</span>
            </div>
            <div *ngIf="chip.removable" class="icon-container">
              <mat-icon class="chip-icon-right remove-chip-icon" matChipRemove>cancel</mat-icon>
              <!-- <fa-icon class="chip-icon chip-icon-right" [icon]="['fal', 'times']" (click)="removeFile()"></fa-icon> -->
            </div>
          </mat-chip>
        </div>
        <div *ngIf="displayExtenderChip" class="data-chip-container">
          <mat-chip (click)="displayRightPane()" class="data-chip clickable">
            <div class="chip-text-container">
              <span class="extender-chip-text"> show all {{ chips.length }} links</span>
            </div>
          </mat-chip>
        </div>
      </mat-chip-list>
      <mat-error *ngIf="control && control.hasError('required')" class="error"
        ><strong>Required</strong></mat-error
      >
    </div>
  </div>
</div>
