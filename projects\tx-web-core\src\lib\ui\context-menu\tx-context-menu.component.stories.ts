import type { Meta, StoryObj } from '@storybook/angular';
import { TxContextMenuComponent } from './tx-context-menu.component';

const meta: Meta<TxContextMenuComponent> = {
  component: TxContextMenuComponent,
  title: 'TxContextMenuComponent',
  tags: ['autodocs'],
  render: (args) => ({
    props: args,
    template: `
      <div>
        <button mat-button id="trigger-menu">Open Menu</button>
        <tx-context-menu 
          [trigger]="trigger"
          [items]="items"
          [disableItems]="disableItems"
        ></tx-context-menu>
      </div>
    `,
  }),
};
export default meta;
type Story = StoryObj<TxContextMenuComponent>;

export const Primary: Story = {
  args: {
    trigger: '#trigger-menu',
    items: [{label :'Element 1'},{label :'Element 2'}],
    disableItems: [],
  },
};
