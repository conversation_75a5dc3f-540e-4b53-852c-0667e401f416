import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Subscription, take } from 'rxjs';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import {
  DataBaseRights,
  TxObjectsTypeService,
  TxObjectType,
  TxObjectTypesTreeGridComponent,
  TxObjectTypeType,
} from '@bassetti-group/tx-web-core';
import { RightPaneRef } from 'src/app/core/right-pane/right-pane.ref';
import { RightPaneService } from 'src/app/core/right-pane/right-pane.service';

@Component({
  selector: 'app-data-model-object-types',
  templateUrl: './data-model-object-types.component.html',
  styleUrls: ['./data-model-object-types.component.scss'],
})
export class DataModelObjectTypesComponent {
  @ViewChild('templateAddObjectType') public templateAddObjectType: TemplateRef<any> | undefined;
  @ViewChild(TxObjectTypesTreeGridComponent) public objTreeGrid:
    | TxObjectTypesTreeGridComponent
    | undefined;

  @Input() objectTypesFilteredIds: string[] = [];

  @Output() changeSelection = new EventEmitter<TxObjectType>();

  public isLoaderActive = true;
  public isExplanationDisplayed = false;
  public objectType?: TxObjectType | null = null;
  // Creation and Edit Form :
  public isFormEditMode = false;
  public settingFormObjectType: { object: any; isEditMode: boolean } | undefined;
  public rightPaneRef?: RightPaneRef;

  protected hasDescription = true;
  protected subscription: Subscription | undefined;

  constructor(
    public otService: TxObjectsTypeService,
    public el: ElementRef,
    private rightPaneService: RightPaneService
  ) {}

  public changeObjectType(ot: TxObjectType): void {
    this.objectType = ot;
    this.changeSelection.emit(ot);
  }

  getAddOTTooltip(): string {
    return _('button.add');
  }

  getDeleteOTTooltip(): string {
    return _('button.delete');
  }

  getEditOTTooltip(): string {
    if (!this.objectType) {
      return _('admins.dataModel.editTooltipSelected');
    }
    if (!(this.objectType && this.objectType.right === DataBaseRights.DbrStructure)) {
      return _('admins.dataModel.editNotAllowed');
    }
    return _('button.edit');
  }

  getStatsOTTooltip(): string {
    return _('admins.dataModel.statistics');
  }

  // ---------------------
  // Autorizations on button

  canDeleteOT(): boolean {
    // Cannot delete OT Portal, People, Enumeration and Source :
    return (
      this.objectType !== undefined &&
      this.objectType !== null &&
      !this.otService.isPortalOT(this.objectType) &&
      !this.otService.isPeopleOT(this.objectType) &&
      !this.otService.isSourceOT(this.objectType)
    );
  }

  canAddOT(): boolean {
    return true;
  }

  canEditOT(): boolean {
    return this.objectType?.right ? this.objectType.right === DataBaseRights.DbrStructure : false;
  }

  // ---------------------
  // Action on buttons

  // Add / edit one ot :

  public addObjectType() {
    let idObjectTypeParent = null;
    let type;

    if (!this.objectType) {
      type = TxObjectTypeType.Standard;
    } else {
      type = this.objectType.type;
      idObjectTypeParent = this.objectType.idObjectTypeParent;
    }

    this.settingFormObjectType = {
      object: { id: 0, name: '', tags: [], idObjectTypeParent, type, isFolder: false },
      isEditMode: false,
    };
    this.buildNewPaneAndSetAfterClosed();
  }

  public editObjectType() {
    this.settingFormObjectType = { object: this.objectType, isEditMode: true };
    this.buildNewPaneAndSetAfterClosed();
  }

  public selectRowAfterAddOrEditOT(objectType: TxObjectType) {
    setTimeout(() => {
      // Data in treegrid are not fully load, so the object type is not recognize yet (change by another way)
      if (this.objTreeGrid) {
        this.objTreeGrid.clearSelectionInGrids();
        this.objTreeGrid.searchIdInGridsAndSelectRow(objectType.id);
      }
    }, 600);
  }

  private buildNewPaneAndSetAfterClosed() {
    this.rightPaneRef = this.rightPaneService.showNewPaneWithTemplate({
      template: this.templateAddObjectType,
    });
    this.rightPaneRef.afterClosed.pipe(take(1)).subscribe((result) => {
      if (!result.hasError && !result.cancel) {
        this.selectRowAfterAddOrEditOT(result.data);
      }
    });
  }
}
