<div
  #wholeContainer
  class="pane-modal-sreen"
  [ngClass]="{ 'darkened-background': backgroundDarkened }">
  <div #paneContainer class="pane-container background mat-elevation-z8">
    <fa-icon
      [icon]="['fal', 'times']"
      size="lg"
      class="pane-close-button"
      (click)="hidePane()"></fa-icon>
    <div class="pane-div-content">
      <ng-container *ngTemplateOutlet="templateContent"></ng-container>
    </div>
  </div>
</div>
