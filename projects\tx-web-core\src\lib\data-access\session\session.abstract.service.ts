import { BehaviorSubject, Observable, filter } from 'rxjs';
import { PreferencesSettings } from './configuration-settings.model';
import { Lang } from './lang.model';
import { ConnectedUser } from './user.models';
import { AdminRights } from './admin-rights.model';

export abstract class AbstractSessionService {
  currentLang?: Lang;
  preferences$: Observable<PreferencesSettings | undefined>;
  protected preferences = new BehaviorSubject<PreferencesSettings | undefined>(undefined);
  protected specificIcons = new BehaviorSubject<string[]>([]);
  constructor() {
    this.preferences$ = this.preferences
      .asObservable()
      .pipe(
        filter(
          (preferences: PreferencesSettings | undefined): preferences is PreferencesSettings =>
            preferences !== undefined
        )
      );
  }
  abstract getConnectedUser(): Observable<ConnectedUser>;
  abstract hasRightsOn(
    mandatoryRights: AdminRights | AdminRights[],
    userRights: AdminRights[]
  ): boolean;
  abstract load(codeLang?: string): void;
  abstract getDateAndTimeFormatPreference(): string | undefined;
  abstract getDateFormatPreference(): string | undefined;
  abstract getSpecificIconsZip(): Observable<Blob>;
}
