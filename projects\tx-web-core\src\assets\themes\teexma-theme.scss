// define 3 theme color
@use '@angular/material' as mat;
// mat-palette accepts $palette-name, main, lighter and darker variants
$primary-brown: (
  50: #e7e5e6,
  100: #c3bfc0,
  200: #9b9496,
  300: #73696b,
  400: #55494c,
  500: #37292c,
  600: #312427,
  700: #2a1f21,
  800: #23191b,
  900: #160f10,
  A100: #ff5b7c,
  A200: #ff2853,
  A400: #f40031,
  A700: #da002c,
  contrast: (
    50: #3d2e31,
    100: #3d2e31,
    200: #3d2e31,
    300: #3d2e31,
    400: #3d2e31,
    500: #ffffff,
    600: #ffffff,
    700: #ffffff,
    800: #ffffff,
    900: #ffffff,
    A100: #3d2e31,
    A200: #3d2e31,
    A400: #3d2e31,
    A700: #3d2e31,
  ),
);

$lt-accent-orange: (
  50: #feeee4,
  100: #fcd4bb,
  200: #fab78d,
  300: #f79a5f,
  400: #f6843d,
  500: #f46e1b,
  600: #f36618,
  700: #f15b14,
  800: #ef5110,
  900: #ec3f08,
  A100: #ffffff,
  A200: #ffe7e1,
  A400: #ffbeae,
  A700: #ffa995,
  contrast: (
    50: #3d2e31,
    100: #3d2e31,
    200: #3d2e31,
    300: #3d2e31,
    400: #3d2e31,
    500: #ffffff,
    600: #ffffff,
    700: #ffffff,
    800: #ffffff,
    900: #ffffff,
    A100: #3d2e31,
    A200: #3d2e31,
    A400: #3d2e31,
    A700: #3d2e31,
  ),
);

$dark-accent-orange: (
  50: #fff6eb,
  100: #ffeacc,
  200: #ffdcab,
  300: #ffcd89,
  400: #ffc36f,
  500: #ffb856,
  600: #ffb14f,
  700: #ffa845,
  800: #ffa03c,
  900: #ff912b,
  A100: #ffffff,
  A200: #ffffff,
  A400: #ffead8,
  A700: #ffdcbe,
  contrast: (
    50: #1e1e23,
    100: #1e1e23,
    200: #1e1e23,
    300: #1e1e23,
    400: #1e1e23,
    500: #1e1e23,
    600: #1e1e23,
    700: #1e1e23,
    800: #1e1e23,
    900: #1e1e23,
    A100: #1e1e23,
    A200: #1e1e23,
    A400: #1e1e23,
    A700: #1e1e23,
  ),
);

$warn-red: (
  50: #fce7e7,
  100: #f7c4c2,
  200: #f29c9a,
  300: #ed7472,
  400: #e95753,
  500: #e53935,
  600: #e23330,
  700: #de2c28,
  800: #da2422,
  900: #d31716,
  A100: #ffffff,
  A200: #ffd1d1,
  A400: #ff9f9e,
  A700: #ff8585,
  contrast: (
    50: #3d2e31,
    100: #3d2e31,
    200: #3d2e31,
    300: #3d2e31,
    400: #3d2e31,
    500: #ffffff,
    600: #ffffff,
    700: #ffffff,
    800: #ffffff,
    900: #ffffff,
    A100: #3d2e31,
    A200: #3d2e31,
    A400: #3d2e31,
    A700: #3d2e31,
  ),
);

$Application-lt-theme-primary: mat.m2-define-palette($primary-brown);
$Application-lt-theme-accent: mat.m2-define-palette($lt-accent-orange);
$Application-lt-theme-warn: mat.m2-define-palette($warn-red);

$Application-dark-theme-primary: mat.m2-define-palette($primary-brown);
$Application-dark-theme-accent: mat.m2-define-palette($dark-accent-orange);
$Application-dark-theme-warn: mat.m2-define-palette($warn-red);

$Application-theme-teexma-light: mat.m2-define-light-theme(
  (
    color: (
      primary: $Application-lt-theme-primary,
      accent: $Application-lt-theme-accent,
      warn: $Application-lt-theme-warn,
    ),
  )
);
$Application-theme-teexma-dark: mat.m2-define-dark-theme(
  (
    color: (
      primary: $Application-dark-theme-primary,
      accent: $Application-dark-theme-accent,
      warn: $Application-dark-theme-warn,
    ),
  )
);

// change foreground
$foreground-light: map-get($Application-theme-teexma-light, foreground);
$teexma-foreground-light: (
  base: mat.m2-get-color-from-palette($foreground-light, base),
  disabled: mat.m2-get-color-from-palette($foreground-light, disabled),
  disabled-button: mat.m2-get-color-from-palette($foreground-light, disabled-button),
  secondary-text: #b1abad,
  slider-off: mat.m2-get-color-from-palette($foreground-light, slider-off),
  slider-off-active: mat.m2-get-color-from-palette($foreground-light, slider-off-active),
  slider-min: mat.m2-get-color-from-palette($foreground-light, slider-min),
  elevation: mat.m2-get-color-from-palette($foreground-light, elevation),
  divider: #d8d5d6,
  dividers: #d8d5d6,
  disabled-text: #bbbbbb,
  hint-text: #3d2e31,
  icon: #3d2e31,
  icons: #3d2e31,
  text: #3d2e31,
  title: #3d2e31,
  subtitle: #3c2f2f,
  selected-text: #ffffff,
  borders: #d8d5d6,
  field-borders: #949494,
  grey5: #f5f4f4,
  grey10: #ebeaea,
  grey20: #d8d5d6,
  grey40: #b1abad,
  grey60: #8b8283,
  grey80: #64585a,
  pastel-green: #509b79,
  white-text: #ffffff,
  // text that stays white in both light & dark mode
  text2: #ffffff,
  // renommer
  url-text: #0779ff,
);

//change background
$background-light: map-get($Application-theme-teexma-light, background);
$teexma-background-light: (
  divider: mat.m2-get-color-from-palette($background-light, divider),
  dividers: mat.m2-get-color-from-palette($background-light, dividers),
  disabled: mat.m2-get-color-from-palette($background-light, disabled),
  disabled-button: mat.m2-get-color-from-palette($background-light, disabled-button),
  disabled-text: mat.m2-get-color-from-palette($background-light, disabled-text),
  elevation: mat.m2-get-color-from-palette($background-light, elevation),
  hint-text: mat.m2-get-color-from-palette($background-light, hint-text),
  unselected-chip: mat.m2-get-color-from-palette($background-light, unselected-chip),
  secondary-text: #b1abad,
  icon: mat.m2-get-color-from-palette($background-light, icon),
  icons: mat.m2-get-color-from-palette($background-light, icons),
  text: mat.m2-get-color-from-palette($background-light, text),
  slider-min: mat.m2-get-color-from-palette($background-light, slider-min),
  slider-off: mat.m2-get-color-from-palette($background-light, slider-off),
  slider-off-active: mat.m2-get-color-from-palette($background-light, slider-off-active),
  base: #ffffff,
  base-contrast: #3d2e31,
  pastel-green: #85d3af,
  pastel-yellow: #f9f0c1,
  pastel-red: #f6c8c8,
  pastel-blue: #bed1d8,
  info-background: #f9f0c1,
  selected-text: #3297fd,
  moz-selected-text: #0078d7,
  main-toolbar: mat.m2-get-color-from-palette($primary-brown, 500),
  hovered-card: #ffffff,
  card-header: #64585a,
);

$foreground-dark: map-get($Application-theme-teexma-dark, foreground);
$teexma-foreground-dark: (
  base: mat.m2-get-color-from-palette($foreground-dark, base),
  disabled: mat.m2-get-color-from-palette($foreground-dark, disabled),
  disabled-button: mat.m2-get-color-from-palette($foreground-dark, disabled-button),
  secondary-text: #b3999c,
  slider-off: mat.m2-get-color-from-palette($foreground-dark, slider-off),
  slider-off-active: mat.m2-get-color-from-palette($foreground-dark, slider-off-active),
  slider-min: mat.m2-get-color-from-palette($foreground-dark, slider-min),
  elevation: mat.m2-get-color-from-palette($foreground-dark, elevation),
  divider: #616161,
  dividers: #616161,
  disabled-text: #bbbbbb,
  hint-text: rgba(255, 255, 255, 0.8),
  icon: rgba(255, 255, 255, 0.8),
  icons: rgba(255, 255, 255, 0.8),
  text: rgba(255, 255, 255, 0.8),
  title: rgba(255, 255, 255, 0.87),
  subtitle: rgba(255, 255, 255, 0.87),
  selected-text: rgba(255, 255, 255, 0.9),
  borders: #616161,
  field-borders: #7c7c7f,
  grey5: #28282e,
  grey10: #32323a,
  grey20: #474752,
  grey40: #999eb3,
  grey60: #d5d5d8,
  grey80: #eaeaeb,
  pastel-green: #85d3af,
  white-text: rgba(255, 255, 255, 0.87),
  // text that stays white in both light & dark mode
  text2: #1e1e23,
  // renommer
  url-text: #0779ff,
);

$background-dark: map-get($Application-theme-teexma-dark, background);
$teexma-background-dark: (
  divider: mat.m2-get-color-from-palette($background-dark, divider),
  dividers: mat.m2-get-color-from-palette($background-dark, dividers),
  disabled: mat.m2-get-color-from-palette($background-dark, disabled),
  disabled-button: mat.m2-get-color-from-palette($background-dark, disabled-button),
  disabled-text: mat.m2-get-color-from-palette($background-dark, disabled-text),
  elevation: mat.m2-get-color-from-palette($background-dark, elevation),
  hint-text: mat.m2-get-color-from-palette($background-dark, hint-text),
  unselected-chip: mat.m2-get-color-from-palette($background-dark, unselected-chip),
  secondary-text: #b3999c,
  icon: mat.m2-get-color-from-palette($background-dark, icon),
  icons: mat.m2-get-color-from-palette($background-dark, icons),
  text: mat.m2-get-color-from-palette($background-dark, text),
  slider-min: mat.m2-get-color-from-palette($background-dark, slider-min),
  slider-off: mat.m2-get-color-from-palette($background-dark, slider-off),
  slider-off-active: mat.m2-get-color-from-palette($background-dark, slider-off-active),
  base: #1e1e23,
  base-contrast: #f0f0f0,
  pastel-green: #2d7e59,
  pastel-yellow: #c9ac13,
  pastel-red: #bf1f1f,
  pastel-blue: #487cb9,
  info-background: #f9f0c1cc,
  selected-text: #0865d5,
  moz-selected-text: #0078d7,
  elevation-4: #c8c8c805,
  elevation-12: #c8c8c81f,
  main-toolbar: #383840,
  hovered-card: rgba(59, 59, 65, 1),
  card-header: #474752,
);

$Application-theme-teexma-light: map-merge(
  $Application-theme-teexma-light,
  (
    foreground: $teexma-foreground-light,
    background: $teexma-background-light,
  )
);
$Application-theme-teexma-dark: map-merge(
  $Application-theme-teexma-dark,
  (
    foreground: $teexma-foreground-dark,
    background: $teexma-background-dark,
  )
);
