import { TestBed, inject } from '@angular/core/testing';
import { CustomPaginatorService } from './custom-paginator.service';
import { TranslateTestingModule } from 'ngx-translate-testing';

describe('Service: CustomPaginator', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [TranslateTestingModule.withTranslations({ en: {}, fr: {} })],
      providers: [CustomPaginatorService],
    });
  });

  it('should ...', inject([CustomPaginatorService], (service: CustomPaginatorService) => {
    expect(service).toBeTruthy();
  }));
});
