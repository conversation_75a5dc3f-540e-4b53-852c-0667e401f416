import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import {
  InputSearchComponent,
  MOCK_OBJECTS_TYPE_SERVICE,
  TxColumnNameTemplateComponent,
  TxObjectsTypeService,
  TxObjectType,
  TxTreeGrid,
  TxTreeGridService,
} from '@bassetti-group/tx-web-core';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { RowDataBoundEventArgs } from '@syncfusion/ej2-angular-grids';
import {
  RowCollapsedEventArgs,
  RowExpandedEventArgs,
  TreeGridModule,
} from '@syncfusion/ej2-angular-treegrid';
import { MockComponent, MockService } from 'ng-mocks';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { TxObjectTypesTreeGridComponent } from './object-types-tree-grid.component';
import { TxObjectTypesTreeGridService } from './object-types-tree-grid.service';

const TRANSLATIONS = {
  en: {},
  fr: {},
};

describe('ObjectTypesTreeGridComponent', () => {
  let component: TxObjectTypesTreeGridComponent;
  let fixture: ComponentFixture<TxObjectTypesTreeGridComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        MatFormFieldModule,
        MatInputModule,
        MatTooltipModule,
        MatExpansionModule,
        TreeGridModule,
        FontAwesomeTestingModule,
        TranslateTestingModule.withTranslations(TRANSLATIONS),
        TxObjectTypesTreeGridComponent,
        MockComponent(TxColumnNameTemplateComponent),
        MockComponent(InputSearchComponent),
      ],
      providers: [
        {
          provide: TxObjectsTypeService,
          useValue: MOCK_OBJECTS_TYPE_SERVICE,
        },
        {
          provide: TxObjectTypesTreeGridService,
          useValue: MockService(TxObjectTypesTreeGridService),
        },
      ],
    })
      .overrideComponent(TxObjectTypesTreeGridComponent, {
        set: {
          providers: [
            {
              provide: TxTreeGridService,
              useValue: MockService(TxTreeGridService, {
                onNodeExpanded: (event: RowExpandedEventArgs, data: TxTreeGrid<any>[]) => {
                  const obj = data.find((ot) => ot.id === (event.data as TxTreeGrid<any>).id);
                  if (obj !== undefined) {
                    obj.expanded = true;
                  }
                },
                onNodeCollapsed: (event: RowCollapsedEventArgs, data: TxTreeGrid<any>[]) => {
                  const obj = data.find((ot) => ot.id === (event.data as TxTreeGrid<any>).id);
                  if (obj) {
                    obj.expanded = false;
                  }
                },
                onRowBound: (args: RowDataBoundEventArgs) => {
                  if (!Number.isInteger((args.data as any).id)) {
                    args.row?.classList.add('row-opacity');
                  }
                },
              }),
            },
          ],
        },
      })
      .compileComponents();
  });
  beforeEach(() => {
    fixture = TestBed.createComponent(TxObjectTypesTreeGridComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });
  beforeEach(() => {
    component.isDataLoaded = true;
    component.isSearchActive = true;
    fixture.detectChanges();
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });
  describe('Get cell tooltip', () => {
    it('should return only name', () => {
      expect(
        component.getTooltip({
          id: 1,
          name: 'Contact',
          txObject: {} as TxObjectType,
        })
      ).toBe('txWebCore.generic.id 1\nContact');
    });
    it('should return concatenation of name & explanation with break line', () => {
      expect(
        component.getTooltip({
          id: 1,
          name: 'Contact',
          txObject: { explanation: 'This is explanation' } as TxObjectType,
        })
      ).toBe('txWebCore.generic.id 1\nContact\nThis is explanation');
    });
  });

  describe('Search', () => {
    describe('Actions on TreeGridObject', () => {
      let gridData: TxTreeGrid<any>[];
      beforeEach(() => {
        gridData = [
          { id: 1, expanded: false, name: 'Contact', txObject: {} },
          { id: 2, expanded: true, name: 'Building', txObject: {} },
        ];
      });
      it('should modify the TreeGridObject "expanded" property to true', () => {
        component.onNodeExpanded({ data: { id: 1 } }, gridData);
        expect(gridData[0].expanded).toBe(true);
      });
      it('should modify the TreeGridObject "expanded" property to false', () => {
        component.onNodeCollapsed({ data: { id: 2 } }, gridData);
        expect(gridData[1].expanded).toBe(false);
      });
    });
    describe('Change opacity rows', () => {
      let rowMock: HTMLTableRowElement;
      beforeEach(() => {
        rowMock = document.createElement('tr');
      });
      it('should add an opacity for description rows', () => {
        component.onRowBound({ data: { id: 2.1 }, row: rowMock });
        expect(rowMock.classList.contains('row-opacity')).toBe(true);
      });
      it('should NOT add an opacity for standard rows', () => {
        component.onRowBound({ data: { id: 2 }, row: rowMock });
        expect(rowMock.classList.contains('row-opacity')).toBe(false);
      });
    });
  });
});
