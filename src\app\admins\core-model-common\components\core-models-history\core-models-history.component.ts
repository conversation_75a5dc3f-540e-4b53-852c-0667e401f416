import {
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  ContentChildren,
  EventEmitter,
  Input,
  Output,
  QueryList,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TxColumn, TxColumnType } from 'src/app/shared/models/grid/tx-column';
import { CoreModelsHistory } from '../../models/core-models-history.model';
import { QueryCellInfoEventArgs } from '@syncfusion/ej2-angular-grids';
import { OverrideColumnDirective } from 'src/app/shared/directives/override-column.directive';
import { InputSearchEventInfo, TxGridComponent } from '@bassetti-group/tx-web-core';
import { FlatCoreModelExportConcept } from 'src/app/admins/core-model-export/models/core-model-export-concept.model';
@Component({
  selector: 'app-core-models-history',
  templateUrl: './core-models-history.component.html',
  styleUrls: ['./core-models-history.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CoreModelsHistoryComponent<T extends CoreModelsHistory> {
  @Input() history: T[] = [];
  @Output() customizeCell = new EventEmitter<QueryCellInfoEventArgs>();
  @ContentChildren(OverrideColumnDirective)
  overrideColumnsDirs?: QueryList<OverrideColumnDirective>;
  @ContentChild('optionalTableBarInput')
  optionalTableBarInputRef: TemplateRef<{ $implicit: ReadonlyArray<T> }> | undefined;

  readonly CORE_MODELS_HISTORY_DATE_FIELD = 'date';
  searchPlaceholder = _('generic.search');
  private _columns: TxColumn[] = [];
  private _coreModelsGrid?: TxGridComponent<FlatCoreModelExportConcept>;
  public inputSearchValue: string = '';
  public filterColumns: TxColumn[] = [];
  public searchById: number | undefined;
  get columns() {
    return this._columns;
  }
  @Input() set columns(value: TxColumn[] | undefined) {
    if (value !== undefined) {
      this._columns = value.map((column) => {
        return {
          ...column,
          type: this.fieldType(column.field),
          width: this.getColumnWidth(column.width),
        };
      });
      this.filterColumns = this._columns.filter((data) => data.field !== 'view');
    }
  }
  @ViewChild('coreModelGrid') set coreModelsGrid(
    value: TxGridComponent<FlatCoreModelExportConcept> | undefined
  ) {
    if (value !== undefined && this._coreModelsGrid !== value) {
      this._coreModelsGrid = value;
    }
  }
  get coreModelsGrid() {
    return this._coreModelsGrid;
  }

  onCustomizeCell(queryCellInfo: QueryCellInfoEventArgs): void {
    this.customizeCell.emit(queryCellInfo);
  }

  private fieldType(field: string): TxColumnType {
    if (field === 'date') {
      return 'date';
    }
    return 'string';
  }

  getColumnWidth(value?: string | number) {
    if (value === undefined || value === null) {
      return undefined;
    }
    return value + (!isNaN(+value) ? 'px' : '');
  }

  searchItem(inputSearchEventInfo: InputSearchEventInfo): void {
    if (this.coreModelsGrid) {
      this.inputSearchValue = inputSearchEventInfo.inputSearch.nativeElement.value;
      const event = inputSearchEventInfo.event;
      const eventCode = event instanceof KeyboardEvent ? event.code : '';
      if (event.type === 'keyup' && (eventCode === 'Enter' || eventCode === 'NumpadEnter')) {
        this.coreModelsGrid.searchByTextSelect(this.inputSearchValue);
      }
    }
  }
}
