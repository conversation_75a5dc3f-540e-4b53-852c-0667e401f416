import { CommonModule } from '@angular/common';
import { Component, Input, OnDestroy } from '@angular/core';
import {
  ControlValueAccessor,
  FormBuilder,
  FormControl,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validator,
} from '@angular/forms';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { validColorValidator } from 'ngx-colors';
import { ColorForm } from './interfaces/color-form';
import {
  ColorVariants,
  DEFAULT_PALETTE,
  TxColorPickerComponent,
} from '@bassetti-group/tx-web-core/src/lib/ui/color-picker';
import { TranslateModule } from '@ngx-translate/core';
import { map, Subject, takeUntil, tap } from 'rxjs';

@Component({
  standalone: true,
  selector: 'tx-color-picker-field',
  templateUrl: './tx-color-picker-field.component.html',
  styleUrls: [],
  imports: [
    MatFormFieldModule,
    TxColorPickerComponent,
    ReactiveFormsModule,
    MatInputModule,
    TranslateModule,
    CommonModule,
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: TxColorPickerFieldComponent,
      multi: true,
    },
    { provide: NG_VALIDATORS, useExisting: TxColorPickerFieldComponent, multi: true },
  ],
})
export class TxColorPickerFieldComponent implements OnDestroy, ControlValueAccessor, Validator {
  @Input() width = '100%';
  @Input() label = _('txWebCore.components.colorPicker.label');
  @Input() colorPalette: Array<string | ColorVariants> = DEFAULT_PALETTE;

  public form: FormGroup<ColorForm> | undefined;

  private readonly _destroying$ = new Subject<void>();
  private _onTouched?: () => void;

  constructor(private readonly fb: FormBuilder) {}

  get colorControl() {
    return this.form?.get('color') as FormControl<string>;
  }
  get inputControl() {
    return this.form?.get('input') as FormControl<string>;
  }

  writeValue(value: string | undefined) {
    if (!this.form) {
      this.form = this.fb.group({
        color: this.fb.control<string | undefined>(value),
        input: this.fb.control<string>(value ?? '', validColorValidator()),
      });

      this.inputControl?.valueChanges.subscribe((value) => {
        if (this.colorControl.valid) {
          this.colorControl.setValue(value, { emitEvent: false });
        }
      });

      this.colorControl?.valueChanges.subscribe((value) => {
        this.inputControl.setValue(value, { emitEvent: false });
      });
    } else {
      this.form.setValue({
        color: value,
        input: value ?? '',
      });
    }
  }

  setDisabledState(isDisabled: boolean): void {
    if (isDisabled) {
      this.form?.reset();
      this.form?.disable();
    } else {
      this.form?.enable();
    }
  }

  registerOnTouched(fn: () => void) {
    this._onTouched = fn;
  }

  registerOnChange(fn: () => void) {
    this.form?.valueChanges
      .pipe(
        takeUntil(this._destroying$),
        map(() => this.colorControl.value),
        tap(fn)
      )
      .subscribe();
  }

  ngOnDestroy() {
    this._destroying$.next();
    this._destroying$.complete();
  }

  validate(): ValidationErrors | null {
    return this.form?.invalid
      ? {
          invalid: true,
        }
      : null;
  }
}
