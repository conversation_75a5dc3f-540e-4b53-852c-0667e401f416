<TXUtils xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="C:/Tools/TxUtils/Resources/TXUCmd.xsd">

    <Consts>
		<Const sName="#AssetsIconsDir#" sValue="#FILEDIR#..\projects\tx-web-core\src\assets\img\icons\"/>
		<Const sName="#PngObjectIcons#" sValue="\\vfiler01\TxDev\External\TxIcons\1.0.0\"/>
		<Const sName="#SvgObjectIcons#" sValue="\\vfiler01\TxDev\External\Iconshock\1.0.0\"/>
		<Const sName="#ProjectDir#" sValue="#FileDir#..\"/>
    </Consts>
	
    <UnzipFile sFilePath="#SvgObjectIcons#svg.7z" sDestinationDir="#AssetsIconsDir#"/>
    <UnzipFile sFilePath="#PngObjectIcons#png.7z" sDestinationDir="#AssetsIconsDir#"/>
	
	<Copy sPath_Src="#FILEDIR#..\src\assets\configs\config.json" sPath_Dest="#FILEDIR#..\src\assets\configs\config.json.previous"/>
	<Copy sPath_Src="#FILEDIR#..\src\assets\configs\config.json.template" sPath_Dest="#FILEDIR#..\src\assets\configs\config.json"/>
	<Copy sPath_Src="#FILEDIR#..\src\assets\configs\config.json.previous" sPath_Dest="#FILEDIR#..\src\assets\configs\config.json"/>
	<Remove sPathes="#FILEDIR#..\src\assets\configs\config.json.previous"/>

    <Execute sPath_File="npm" sParameters="ci" bVisible="false" bUse_CreateProcess="false" sDir="#ProjectDir#" bWait="true"/>
</TXUtils>
