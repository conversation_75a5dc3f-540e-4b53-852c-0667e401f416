import { Pipe, PipeTransform } from '@angular/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TxDataType } from './tx-data.model';

@Pipe({
  standalone: true,
  name: 'dataType',
})
export class DataTypePipe implements PipeTransform {
  transform(value: unknown, ...args: unknown[]): string {
    switch (value) {
      case TxDataType.Tab:
        return _('txWebCore.admins.dataModel.tab');
      case TxDataType.Boolean:
        return _('txWebCore.admins.dataModel.boolean');
      case TxDataType.ShortText:
        return _('txWebCore.admins.dataModel.shortText');
      case TxDataType.Listing:
        return _('txWebCore.admins.dataModel.listing');
      case TxDataType.Table:
        return _('txWebCore.admins.dataModel.table');
      case TxDataType.LongText:
        return _('txWebCore.admins.dataModel.longText');
      case TxDataType.Group:
        return _('txWebCore.admins.dataModel.group');
      case TxDataType.SingleValue:
        return _('txWebCore.admins.dataModel.singleValue');
      case TxDataType.Range:
        return _('txWebCore.admins.dataModel.range');
      case TxDataType.RangeMeanValue:
        return _('txWebCore.admins.dataModel.rangeMeanValue');
      case TxDataType.Date:
        return _('txWebCore.admins.dataModel.date');
      case TxDataType.DateAndTime:
        return _('txWebCore.admins.dataModel.dateAndTime');
      case TxDataType.File:
        return _('txWebCore.admins.dataModel.file');
      case TxDataType.Email:
        return _('txWebCore.admins.dataModel.email');
      case TxDataType.Url:
        return _('txWebCore.admins.dataModel.url');
      case TxDataType.LinkDirect:
        return _('txWebCore.admins.dataModel.directLink');
      case TxDataType.LinkInv:
        return _('txWebCore.admins.dataModel.invLink');
      case TxDataType.LinkBi:
        return _('txWebCore.admins.dataModel.biLink');
    }
    return 'Unknown value';
  }
}
