const [releaseVersion, date] = process.argv.slice(2);

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'versions.json');

fs.readFile(filePath, 'utf8', (err, data) => {
    let json = data ? JSON.parse(data) : {};

    json[releaseVersion] = date

    const updatedJson = JSON.stringify(json, null, 2);

    fs.writeFile(filePath, updatedJson, 'utf8', (err) => {
        if (err) {
            console.error('Error while writing json:', err);
            process.exit(1);
        }
        console.log('The json file has been updated.');
    });
});
