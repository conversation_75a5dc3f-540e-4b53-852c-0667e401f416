<div class="fm-container-left">
  <div
    #containerTree
    *ngIf="!isLangLoading"
    class="tree-container border-grey background-grey5"
    txDragDrop
    (fileDropped)="fileDropTree($event)"
    (hideIndicator)="hideIndicator($event)"
    (showIndicator)="showIndicator($event)">
    <tx-tree-view
      cdkDropList
      #treeview="cdkDropList"
      #fileTree
      id="fileTree"
      [primaryKey]="'id'"
      [hasChildren]="'hasChildren'"
      (nodeSelected)="onNodeSelected($event)"
      (nodeExpanded)="onNodeExpanding($event)"
      (nodeDragStart)="onNodeDragStart($event)"
      (nodeDragging)="onNodeDrag($event)"
      (nodeDragStop)="onNodeDragStop($event)"
      [data]="treeData">
      <tx-tree-node>
        <ng-template #nodeTemplate let-data>
          <div [ngClass]="{ temporary: data.isTemp }">
            <fa-icon [icon]="[data.isTemp ? 'fal' : 'fas', getTreeIcon(data)]" size="lg"></fa-icon>
            <span class="tree-text">{{ data.name }}</span>
            <span class="tree-id" style="display: none">{{ data.id }}</span>
          </div>
        </ng-template>
      </tx-tree-node>
    </tx-tree-view>
    <!-- <ejs-treeview
      cdkDropList
      #treeview="cdkDropList"
      #fileTree
      id="fileTree"
      (cdkDropListDropped)="onCellDragStop($event)"
      [fields]="field"
      [animation]="treeAnimation"
      [allowEditing]="true"
      [allowDragAndDrop]="true"
      (nodeClicked)="onNodeClicked($event)"
      (nodeSelected)="onNodeSelected($event)"
      (nodeEdited)="onNodeEdited($event)"
      (nodeEditing)="onNodeEditing($event)"
      (nodeCollapsed)="onNodeCollapsed($event)"
      (nodeExpanding)="onNodeExpanding($event)"
      (nodeExpanded)="onNodeExpanded($event)"
      (nodeDragStart)="onNodeDragStart($event)"
      (nodeDragging)="onNodeDrag($event)"
      (nodeDragStop)="onNodeDragStop($event)">
      <ng-template #nodeTemplate let-data>
        <div [ngClass]="{ temporary: data.isTemp }">
          <fa-icon [icon]="[data.isTemp ? 'fal' : 'fas', getTreeIcon(data)]" size="lg"></fa-icon>
          <span class="tree-text">{{ data.name }}</span>
          <span class="tree-id" style="display: none">{{ data.id }}</span>
        </div>
      </ng-template>
    </ejs-treeview> -->
    <tx-context-menu
      trigger="#fileTree"
      [disableItems]="disableMenuItems"
      [items]="menuItems"
      (beforeOpen)="beforeOpen($event)"
      (select)="menuClick($event)"></tx-context-menu>
  </div>
</div>
