import { Observable, of, tap, map } from 'rxjs';
import { TxObject } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TxConfigService } from '@bassetti-group/tx-web-core/src/lib/data-access/config';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TxObjectTypeIconService } from '@bassetti-group/tx-web-core/src/lib/data-access/session';

@Injectable({
  providedIn: 'root',
})
export class TxObjectsService {
  protected apiUrl?: string;

  private taggedObjects?: TxObject[];

  constructor(
    protected http: HttpClient,
    protected configService: TxConfigService,
    private readonly objectTypeIconService: TxObjectTypeIconService
  ) {
    this.apiUrl = this.configService.getApiUrl();
  }

  public getObjectsFromParent(
    idOT: number,
    idParent: number,
    isRecursive: boolean = false,
    includeFolder: boolean = true
  ): Observable<{ cachedIds: number[]; objects: TxObject[] }> {
    const params = new HttpParams()
      .set('idObjectType', idOT.toString())
      .set('idParent', idParent.toString())
      .set('includeFolder', includeFolder ? 'true' : 'false')
      .set('isRecursive', isRecursive ? 'true' : 'false')
      .set('removeTrashObjects', 'true')
      .set('sortById', 'false')
      .set('sortByOrder', 'true');
    return this.http.get<{ cachedIds: number[]; objects: TxObject[] }>(
      `${this.apiUrl}api/Objects/read/childrens`,
      {
        params,
      }
    );
  }

  public getAllObjectsChildren(idObjectType: number) {
    //this is a temporary method
    return this.getObjectsFromParent(idObjectType, 0, true, false);
  }
  public getChildrenObjectsByIds(
    objectIds: number[],
    idObjectType: number
  ): Observable<TxObject[]> {
    //this is a temporary method
    return this.getObjectsFromParent(idObjectType, 0, true, false).pipe(
      map((data) => data.objects.filter((val) => objectIds.includes(val.id)))
    );
  }

  public getRootToParentIds(): Observable<number[]> {
    //this is a demo method
    return new Observable((subscriber) => {
      subscriber.next([113536, 117729]);
    });
  }

  public searchObjects(
    search: string,
    idOT?: number,
    limitChar?: number,
    includeFolder: boolean = true
  ): Observable<TxObject[]> {
    let params = new HttpParams().set('searchString', search);
    if (idOT !== undefined) {
      params = params.set('idObjectType', idOT.toString());
    }
    if (limitChar !== undefined) {
      params = params.set('limit', limitChar.toString());
    }
    return this.http
      .get<TxObject[]>(`${this.apiUrl}api/Objects/search`, {
        params,
      })
      .pipe(
        map((response) => (includeFolder ? response : response.filter((data) => !data.isFolder)))
      );
  }

  getIconPath(icon: number | string): string {
    return this.objectTypeIconService.getIconPath(icon);
  }

  public getTaggedObjects(): Observable<TxObject[]> {
    if (this.taggedObjects) {
      return of(this.taggedObjects);
    } else {
      return this.http
        .get<TxObject[]>(`${this.apiUrl}api/Objects/read/taggedobjects`)
        .pipe(tap((objects) => (this.taggedObjects = objects)));
    }
  }

  public getObjectByTag(tag: string): Observable<TxObject> {
    return new Observable((observer) => {
      this.getTaggedObjects().subscribe((objects: TxObject[]) => {
        const res = objects.find((o) => o.tags.includes(tag));
        observer.next(res);
        observer.complete();
      });
    });
  }

  public createTagPlaceholderFromId(id: number): string {
    return `#objTag${id}#`;
  }

  public isObjectsTagsMissing(jsonObject: any): boolean {
    return !!JSON.stringify(jsonObject).match(/#objTag\d+#/g);
  }

  public getIdsObjectsTagsMissing(jsonObject: any): number[] {
    const tags = JSON.stringify(jsonObject).match(/#objTag\d+#/g);
    if (tags) {
      const idObjects = tags
        .map((tag) => {
          const match = /#objTag(\d+)#/.exec(tag);
          return match ? parseInt(match[1]) : -1;
        }) // get idObj from placeholder Tag
        .filter((id) => id !== -1);
      return [...new Set(idObjects)]; // remove duplicates
    } else {
      return [];
    }
  }

  public autotag(idsObj: number[]): Observable<TxObject[]> {
    return this.http
      .post<TxObject[]>(`${this.apiUrl}api/Structure/tag/autotag?concept=Object`, idsObj)
      .pipe(
        tap((objects) => {
          if (this.taggedObjects) {
            this.taggedObjects = [...this.taggedObjects, ...objects];
          }
        })
      );
  }
}
