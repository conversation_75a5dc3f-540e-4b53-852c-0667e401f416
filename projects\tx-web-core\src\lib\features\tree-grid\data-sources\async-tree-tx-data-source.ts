import {
  BehaviorSubject,
  concatMap,
  defaultIfEmpty,
  filter,
  from,
  last,
  map,
  Observable,
  of,
  take,
} from 'rxjs';
import { PaginatedTreeGridDataSource } from './paginated-tree-grid-data-source';
import { TxTreeGrid } from '../tree-grid.models';
import {
  addLevelsToTreeSourceData,
  findNodeById,
  generateTree,
  updateNodeById,
} from '../tree-utils';
import { Page } from '@bassetti-group/tx-web-core/src/lib/features/grid';
import { omit } from 'ramda';
import { inject } from '@angular/core';
import { TxObjectsTypeService } from '@bassetti-group/tx-web-core/src/lib/data-access/structure';

export type TxLoadChildrenDataFn<T> = (
  idObjectType: number,
  id: number | string
) => Observable<T[]>;

export type CreateTreeGridElementsFn<T> = (objects: T[]) => AsyncTxTreeGrid<T>[];
export interface AsyncTxTreeGrid<T> extends TxTreeGrid<T> {
  id: number;
  children?: AsyncTxTreeGrid<T>[];
}
export class AsyncTreeGridTXDataSource<T extends {}> extends PaginatedTreeGridDataSource<
  AsyncTxTreeGrid<T>
> {
  isLoading$: Observable<boolean>;
  protected _isLoading = new BehaviorSubject<boolean>(false);
  protected readonly otService: TxObjectsTypeService;
  constructor(
    endPoint: Observable<TxTreeGrid<T>[]>,
    primaryKey: keyof TxTreeGrid<T> | undefined = undefined,
    public loadChildrenFn?: TxLoadChildrenDataFn<T>,
    public createTreeGridObjects?: CreateTreeGridElementsFn<T>,
    public idObjectType?: number
  ) {
    super(endPoint, primaryKey);
    (this.otService = inject(TxObjectsTypeService)),
      (this.isLoading$ = this._isLoading.asObservable());
    this.disableSelectionUpdate = true;
  }
  override toggleNode(node: AsyncTxTreeGrid<T> | number | string, isChildPresent?: boolean) {
    if (this.primaryKey === undefined) {
      throw new Error('primaryKey not defined');
    }
    const nodeValue = (
      isNaN(+node) && typeof node == 'object' ? node[this.primaryKey] : node
    ) as keyof AsyncTxTreeGrid<T>;
    const isExpanded: boolean = this.expandedNodes.has(nodeValue);
    const isChildLoaded: boolean = this.isChildLoaded(node as any);
    if (isExpanded) {
      this.expandedNodes.delete(nodeValue);
      super.triggerUpdatePageRequest();
      return;
    }
    this.expandedNodes.add(nodeValue);
    if (isChildLoaded || isChildPresent) {
      super.triggerUpdatePageRequest();
      return;
    }
    if (
      this.loadChildrenFn === undefined ||
      this.idObjectType === undefined ||
      this.createTreeGridObjects === undefined
    ) {
      throw new Error(
        "Can't load Teexma Tree node children.\n Please provide required params: loadChildrenFn and idObjectType "
      );
    }
    this._isLoading.next(true);
    this.loadChildrenFn(this.idObjectType, nodeValue)
      .pipe(take(1))
      .subscribe({
        next: (children) => {
          // set node to parent if necessary
          if (this.createTreeGridObjects !== undefined) {
            const treeGridObjects = this.createTreeGridObjects(children);
            const updatedPage = this.updatePage(treeGridObjects, nodeValue);
            this.triggerUpdatePageRequest(updatedPage);
          }
        },
        complete: () => {
          this._isLoading.next(false);
        },
      });
  }

  override triggerUpdatePageRequest(updatedPage?: Page<AsyncTxTreeGrid<T>>): void {
    if (updatedPage === undefined) {
      super.triggerUpdatePageRequest();
      return;
    }
    this._pageRequest.next(updatedPage);
  }

  clearExpandedNodes(): void {
    this.expandedNodes.clear();
    this.triggerUpdatePageRequest();
  }

  changeLoadingState(state: boolean) {
    this._isLoading.next(state);
  }

  isChildLoaded(node: T & { children: T[] }): boolean {
    return false;
  }
  processNodeIdsSequentially(nodeIds: number[]): Observable<number | undefined> {
    if (
      this.loadChildrenFn === undefined ||
      this.idObjectType === undefined ||
      this.createTreeGridObjects === undefined
    ) {
      throw new Error(
        "Can't load Teexma Tree node children.\n Please provide required params: loadChildrenFn and idObjectType "
      );
    }

    const selectedNodeId = nodeIds.pop();

    if (nodeIds.length === 0) {
      // If there are no nodes to process, emit the selectedNodeId immediately.
      return of(selectedNodeId);
    }

    return from(nodeIds).pipe(
      concatMap((nodeId, index) =>
        this.loadChildrenFn!(this.idObjectType!, nodeId).pipe(
          take(1),
          map((children) => {
            if (!this.createTreeGridObjects) return;
            const treeGridObjects = this.createTreeGridObjects(children);
            const updatedPage = this.updatePage(treeGridObjects, nodeId);
            this.expandedNodes.add(nodeId);
            this.triggerUpdatePageRequest(updatedPage);
            if (index === nodeIds.length - 1) {
              return selectedNodeId;
            }
          })
        )
      ),
      filter((result) => result !== undefined),
      last(),
      defaultIfEmpty(selectedNodeId)
    );
  }

  updatePage(childObjects: AsyncTxTreeGrid<T>[], id: number | string): Page<AsyncTxTreeGrid<T>> {
    const currentObject = findNodeById<AsyncTxTreeGrid<T>>(
      this._pageRequest?.value?.data ?? [],
      id,
      this.primaryKey
    );
    if (currentObject === null) {
      throw new Error(`Node with id ${id} not found in the current page data.`);
    }
    const currentObjectParent = currentObject[this.parentMapping as keyof TxTreeGrid<T>];
    const dataTree = generateTree(
      [
        omit([this.parentMapping as keyof TxTreeGrid<T>, 'children'], {
          ...currentObject,
        }) as TxTreeGrid<T>,
        ...childObjects,
      ],
      this.idMapping as keyof TxTreeGrid<T>,
      this.parentMapping as keyof TxTreeGrid<T>
    );
    if (dataTree.length !== 1) {
      throw new Error(
        `Unexpected tree structure after generation. Expected 1 root node, but got ${dataTree.length}.`
      );
    }
    dataTree[0][this.parentMapping as keyof TxTreeGrid<T>] = currentObjectParent as never;
    const updateTreeData = updateNodeById<AsyncTxTreeGrid<T>>(
      this._pageRequest?.value?.data ?? [],
      dataTree[0],
      this.primaryKey as keyof AsyncTxTreeGrid<T>
    );
    if (!updateTreeData.success) {
      throw new Error('Data has not updated successfully');
    }
    this.updateData(updateTreeData.updatedData);
    const updateTreeDataWithLevel = addLevelsToTreeSourceData(
      updateTreeData.updatedData,
      this.childMapping
    ) as AsyncTxTreeGrid<T>[];
    return this.createPage(0, updateTreeDataWithLevel.length, updateTreeDataWithLevel);
  }
}
