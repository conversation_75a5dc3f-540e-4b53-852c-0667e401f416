import { DataModelComponent } from './data-model.component';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { DataModelObjectTypesComponent } from './data-model-object-types/data-model-object-types.component';
import { MockComponent } from 'ng-mocks';
import { BreadcrumdComponent } from 'src/app/shared/components/breadcrumd/breadcrumd.component';
import { RouterTestingModule } from '@angular/router/testing';
import {
  AttributeTreeGridComponent,
  DataBaseRights,
  TxAttribute,
  TxAttributeSelectionEvent,
  TxDataType,
  TxLockingType,
  TxNumericalFloatFormat,
  TxObjectType,
  TxObjectTypeType,
} from '@bassetti-group/tx-web-core';
import { AppService } from 'src/app/core/services/app.service';
import { AppServiceMock } from 'src/app/app.testing.mock';

const TRANSLATIONS = {
  en: {},
  fr: {},
};

const initialObjectTypeMock: TxObjectType = {
  id: 1,
  name: 'Contact',
  order: 0,
  icon: 0,
  isFolder: false,
  type: TxObjectTypeType.Standard,
  hasDistinctName: false,
  isVisible: false,
  tags: [],
  lockingType: TxLockingType.Auto,
  lockingDuration: 0,
  displayResultInTextSearch: true,
  right: DataBaseRights.DbrStructure,
  description: '',
  explanation: '',
  idObjectTypeParent: 0,
  options: undefined,
};

const objectTypeMock: TxObjectType = {
  id: 2,
  name: 'User',
  order: 0,
  icon: 0,
  isFolder: false,
  type: TxObjectTypeType.Standard,
  hasDistinctName: false,
  isVisible: false,
  tags: [],
  lockingType: TxLockingType.Auto,
  lockingDuration: 0,
  displayResultInTextSearch: true,
  right: DataBaseRights.DbrStructure,
  description: '',
  explanation: '',
  idObjectTypeParent: 0,
  options: undefined,
};

const initialAttributeMock: TxAttribute = {
  id: 0,
  name: 'Description',
  tags: [],
  order: 0,
  isTrackable: true,
  isList: false,
  precision: 0,
  isLBInclusive: true,
  isUBInclusive: true,
  isDisplayedInMainUnit: true,
  isInherited: false,
  dataType: TxDataType.ShortText,
  floatFormat: TxNumericalFloatFormat.Number,
  digits: 1,
  idObjectType: 0,
  isTableDisplayed: false,
  isTransposed: false,
  isSeriesNameDisplayed: false,
  isIndexesDisplayed: false,
  color: '',
  isUnderlined: false,
  linkDisplayMode: 0,
  idAttributeParent: 0,
  options: undefined,
};

const attributeMock: TxAttribute = {
  id: 0,
  name: 'Description',
  tags: [],
  order: 0,
  isTrackable: true,
  isList: false,
  precision: 0,
  isLBInclusive: true,
  isUBInclusive: true,
  isDisplayedInMainUnit: true,
  isInherited: false,
  dataType: TxDataType.ShortText,
  floatFormat: TxNumericalFloatFormat.Number,
  digits: 1,
  idObjectType: 0,
  isTableDisplayed: false,
  isTransposed: false,
  isSeriesNameDisplayed: false,
  isIndexesDisplayed: false,
  color: '',
  isUnderlined: false,
  linkDisplayMode: 0,
  idAttributeParent: 0,
  options: undefined,
};

describe('DataModelComponent', () => {
  let component: DataModelComponent;
  let fixture: ComponentFixture<DataModelComponent>;
  let appService: AppService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        DataModelComponent,
        BreadcrumdComponent,
        MockComponent(DataModelObjectTypesComponent),
        MockComponent(AttributeTreeGridComponent),
      ],
      imports: [
        FontAwesomeTestingModule,
        MatTooltipModule,
        RouterTestingModule,
        TranslateTestingModule.withTranslations(TRANSLATIONS),
      ],
      providers: [{ provide: AppService, useClass: AppServiceMock }],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DataModelComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  beforeEach(() => {
    component.objectType = { ...initialObjectTypeMock };
    component.attribute = { ...initialAttributeMock };
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Change Object Type', () => {
    it('should assign a new object type to component', () => {
      component.changeObjectType(objectTypeMock);
      expect(component.objectType).toStrictEqual(objectTypeMock);
      expect(component.attribute).toStrictEqual(undefined);
    });
  });

  describe('Change Attribute', () => {
    it('should assign a new object type to component', () => {
      component.changeAttribute({ data: { txObject: attributeMock } } as TxAttributeSelectionEvent);
      expect(component.attribute).toStrictEqual(attributeMock);
    });
  });

  describe('Get Explanation', () => {
    it('should assign true to isExplanationDisplayed', () => {
      component.getExplanation('dataModel', 'expDataModel', false);
      expect(component.isExplanationDisplayed).toBe(true);
    });
  });
});
