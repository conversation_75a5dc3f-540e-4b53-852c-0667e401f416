import { ElementRef, Injectable } from '@angular/core';
import { GridComponent, RowInfo } from '@syncfusion/ej2-angular-grids';
import { TreeGridComponent } from '@syncfusion/ej2-angular-treegrid';
import {
  GridFilter,
  BaseGridFilterValue,
  GridFilterValue,
} from './grid-filter-menu/grid-filter-menu.component';
import { TxCommonUtils } from '@bassetti-group/tx-web-core/src/lib/utilities';
import { TxObjectType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TxGridComponent } from './grid.component';
import { TxGridObject } from './grid-object.model';
import { TxGridFilterType } from './grid-filter-menu/grid-filter-menu.model';

@Injectable()
export class TxGridService {
  searchId: number | undefined;
  private _markedIndex: number = 0;
  private _highlightedText?: string;

  resetSearch(): number | undefined {
    this.searchId = undefined;
    return this.searchId;
  }

  /**
   * handles the public search functionality,first search by id,and if no id is found, it continues searching by text and selects the next matching row.
   * @param inputSearch The input element for searching.
   * @param grid The grid or tree grid component where the search is performed.
   * @param contentElement The element used to retrieve a NodeList of 'mark' elements (e.g. highlighting) within the content.
   * @param event The keyboard event triggering the search.
   * @param isRowSelected A parameter that decides whether to select the row when found (default is false).
   * @returns if the search is done by id, it returns the ID; otherwise, it returns -1.
   */
  public search(
    inputSearch: ElementRef<HTMLInputElement> | undefined,
    grid: GridComponent | TreeGridComponent | TxGridComponent<any> | undefined,
    contentElement: ElementRef,
    event: KeyboardEvent | InputEvent,
    enableSelection: boolean = true,
    isHighlightable: boolean = true,
    domSearch?: boolean
  ): number | undefined {
    if (!inputSearch) {
      throw new Error(
        'No Input provided for the method "search" of GridService. Please provide an input!'
      );
    }
    if (!grid) {
      throw new Error(
        'No Grid provided for the method "search" of GridService. Please provide a grid!'
      );
    }
    this.resetSearch();
    const eventCode = (event as KeyboardEvent).code;
    if (event.type === 'keyup' && (eventCode === 'Enter' || eventCode === 'NumpadEnter')) {
      const filterValue = inputSearch.nativeElement.value.trim().toLowerCase();
      // if value id a number = search by ID
      if (!isNaN(Number(filterValue))) {
        this.searchByID(Number(filterValue), inputSearch, grid, contentElement, enableSelection);
      }
      // if an ID is found, stop the search and return the ID
      if (this.searchId !== undefined) {
        return this.searchId;
      }
      // else, continue the search by text
      this.searchByText(
        inputSearch,
        grid,
        contentElement,
        enableSelection,
        isHighlightable,
        domSearch
      );
    }
    return this.searchId;
  }

  /**
   * for searching and selecting rows based on their Ids.
   * @param filterValue The ID to search for.
   * @param inputSearch The input element used for searching.
   * @param  grid The grid or tree grid component where the search is performed.
   * @param contentElement The element used to retrieve a NodeList of 'mark' elements (e.g. highlighting) within the content.
   * @param isRowSelected A parameter that decides whether to select the row when found (default is false).
   * @returns If the record is found, it returns the ID; otherwise, it returns -1.
   */
  searchByID(
    filterValue: number,
    inputSearch: ElementRef,
    grid: GridComponent | TreeGridComponent | TxGridComponent<any>,
    contentElement: ElementRef,
    isRowSelected: boolean = false
  ): number | undefined {
    let index: number | undefined = -1;
    if (grid instanceof GridComponent || grid instanceof TxGridComponent) {
      index = grid.getRowIndexByPrimaryKey(filterValue);
    } else {
      const filteredRow = grid
        .getDataRows()
        .map((eleRow) => grid.getRowInfo(eleRow))
        .find((row) => (row.rowData as TxGridObject).id === filterValue);
      index = filteredRow?.rowIndex;
    }

    if (index !== undefined && index > -1) {
      this.searchId = filterValue;
      if (!(grid instanceof TxGridComponent))
        this.focusInputSearch(inputSearch, this.selectRowByIndex(index, grid, isRowSelected));
    } else {
      this.searchId = undefined;
    }

    return this.searchId;
  }

  searchByText(
    inputSearch: ElementRef,
    grid: GridComponent | TreeGridComponent | TxGridComponent<any>,
    contentElement: ElementRef,
    enableSelection: boolean,
    isHighlightable: boolean,
    domSearch?: boolean
  ): void {
    if (grid instanceof TxGridComponent) {
      this.handleTxGridComponentSearch(
        inputSearch,
        grid,
        contentElement,
        enableSelection,
        isHighlightable,
        domSearch
      );
    } else {
      this.handleOtherComponentsSearch(inputSearch, grid, contentElement, enableSelection);
    }
  }

  /**
   * managing the selection of rows depending on the isRowSelected parameter, it either selects rows or visually marks a row as selected without changing the grid's row selection state.
   * @param mark The 'mark' element to use as a reference for selecting the row.
   * @param inputSearch The input element used for searching.
   * @param  grid The grid or tree grid component where the search is performed.
   * @param isRowSelected A parameter to know whether to select the row or only mark it as selected.
   */
  selectRow(
    mark: Node | null,
    inputSearch: ElementRef,
    grid: GridComponent | TreeGridComponent,
    isRowSelected: boolean
  ): void {
    if (mark !== null) {
      let row = grid.getRowInfo(mark);
      if (row.column) {
        row = this.doSelectRow(mark, grid, isRowSelected);
      }
      this.focusInputSearch(inputSearch, row);
    }
  }

  //see description of the function in tree grid service
  expandParentRows(rowInfo: RowInfo, grid: TreeGridComponent) {
    // Throw an exception to alert developers to use treeGridService instead of gridService.
    throw new Error('Use treeGridService instead of gridService for expanding parent rows.');
  }

  /**
   * Selects a row in the grid or tree grid component by its index.
   * @param index The index of the row to be selected.
   * @param  grid The grid or tree grid component where the row is selected.
   * @param isRowSelected A parameter to know whether to select the row or only mark it as selected.
   * @returns The selected row.
   */
  selectRowByIndex(
    index: number,
    grid: TreeGridComponent | GridComponent,
    isRowSelected: boolean
  ): RowInfo {
    return this.doSelectRow(grid.getRowByIndex(index), grid, isRowSelected);
  }

  filter(filter: GridFilter, gridComponent: GridComponent | undefined): void {
    if (gridComponent && this.canFilter(filter)) {
      const filterValue = this.getFormattedFilterValue(filter);
      this.applyFilter(filter, filterValue, gridComponent);
    } else if (filter.column) {
      gridComponent?.removeFilteredColsByField(filter.column.field);
    }
  }

  /* Private Methods */

  /**
   * responsible for selecting a row in a grid or tree grid component based on the provided mark.
   * @param mark The 'mark' element to use as a reference for selecting the row.
   * @param  grid The grid or tree grid component where the row is selected.
   * @param isRowSelected A parameter to know whether to select the row or only mark it as selected.
   * @returns The selected row.
   */
  private doSelectRow(
    mark: Node,
    grid: TreeGridComponent | GridComponent,
    isRowSelected: boolean
  ): RowInfo {
    const row = grid.getRowInfo(mark);
    if (grid instanceof TreeGridComponent) {
      this.expandParentRows(row, grid);
    }

    if (isRowSelected) {
      grid.clearSelection();
      if (row.rowIndex !== undefined) {
        grid.selectRow(row.rowIndex);
      }
    } else {
      mark?.parentElement?.firstElementChild?.classList.add('selected');
    }
    return row;
  }

  /**
   * checks row selection status based on the isRowSelected parameter
   * @param mark The 'mark' element to check for selection.
   * @param  grid The grid or tree grid component where the search is performed.
   * @param  isRowSelected A parameter to know if the row is selected in the grid or just visually marked as selected.
   * @returns  True if the 'mark' element is selected or if a row containing the 'mark' is selected; otherwise, false.
   */
  private isMarkSelected(
    mark: Node | null,
    grid: GridComponent | TreeGridComponent,
    isRowSelected: boolean
  ): boolean {
    if (isRowSelected && mark !== null) {
      return this.isMarkSelectedInGrid(mark, grid);
    } else {
      return mark?.parentElement?.firstElementChild?.classList.contains('selected') ?? false;
    }
  }

  /**
   * checks whether a specific row, associated with a given mark element, is selected within a grid or tree grid component.
   * @param mark The 'mark' element to check for selection.
   * @param  grid The grid or tree grid component where the search is performed.
   * @returns True if the 'mark' element is selected in grid
   */
  private isMarkSelectedInGrid(mark: Node, grid: TreeGridComponent | GridComponent): boolean {
    const indexOfSelectedRow = this.getSelectedRowIndex(grid);
    if (indexOfSelectedRow > -1 && grid.getRowInfo(mark).column) {
      const rowI = grid.getRowInfo(mark);
      if (rowI.rowIndex === indexOfSelectedRow) {
        return true;
      }
    }
    return false;
  }

  /**
   * to retrieve the index of the currently selected row within the grid.
   * @param  grid The grid or tree grid component where the search is performed.
   * @returns The index of the currently selected row within the grid.
   */
  private getSelectedRowIndex(grid: TreeGridComponent | GridComponent): number {
    const selections = grid.getSelectedRowIndexes();
    if (selections.length > 0) {
      return selections[0];
    } else {
      return -1;
    }
  }

  /**
   * to put a focus on the input search
   * @param  inputSearch The input element used for searching.
   * @param  row Row which contains information about a specific row you want to scroll to.
   */
  private focusInputSearch(inputSearch: ElementRef, row: RowInfo) {
    setTimeout(() => {
      inputSearch.nativeElement.focus();
      setTimeout(() => row.row?.scrollIntoView({ behavior: 'smooth', block: 'center' }), 200);
    }, 100);
  }

  private getFormattedFilterValue(filter: GridFilter): BaseGridFilterValue {
    if (filter?.column?.filterOptions && filter.column.filterType !== undefined) {
      return this.filterValueWithOptions(filter);
    }
    return filter.value as BaseGridFilterValue;
  }

  private filterValueWithOptions(filter: GridFilter): BaseGridFilterValue {
    if (filter?.column?.filterType === TxGridFilterType.ObjectType) {
      return (filter.value as TxObjectType[]).map((option) => option.name);
    } else {
      const options =
        filter.column?.filterOptions.filter((o: { value: string }) =>
          (filter.value as string[]).includes(o.value)
        ) ?? [];
      return options.map((option: { text: string; value: string }) => option.value);
    }
  }

  private applyFilter(
    filter: GridFilter,
    filterValue: BaseGridFilterValue | null,
    gridComponent: GridComponent | undefined
  ) {
    if (filter.column) {
      let predicate: string;
      if (Array.isArray(filter.value)) {
        gridComponent?.removeFilteredColsByField(filter?.column.field);
        predicate = 'or';
        if (filter.value.length === 1 && filter.value[0] === '') {
          // "All" option selected
          filter.operator = filter.operator === 'equal' ? 'notEqual' : 'equal';
          filterValue = null;
        }
      } else {
        predicate = 'and';
      }
      gridComponent?.filterByColumn(
        filter.column.field,
        filter.operator,
        filterValue,
        predicate,
        false,
        true
      );
    }
  }

  private canFilter(filterConfig: GridFilter): boolean {
    return TxCommonUtils.isDefined<GridFilterValue>(filterConfig.value);
  }
  /**
   * for searching and selecting rows based on the text input.
   * @param inputSearch The input element used for searching.
   * @param  grid The grid or tree grid component where the search is performed.
   * @param contentElement The element used to retrieve a NodeList of 'mark' elements (e.g. highlighting) within the content.
   * @param isRowSelected A parameter that decides whether to select the row when found (default is false).
   */
  private handleTxGridComponentSearch(
    inputSearch: ElementRef,
    grid: TxGridComponent<any>,
    contentElement: ElementRef,
    enableSelection: boolean,
    isHighlightable: boolean,
    domSearch?: boolean
  ): void {
    if (enableSelection) grid.searchByTextSelect(inputSearch.nativeElement.value, domSearch);

    if (isHighlightable) {
      if (inputSearch.nativeElement.value !== this._highlightedText) {
        this._highlightedText = inputSearch.nativeElement.value;
        this._markedIndex = 0;
      }

      const marks: NodeList = contentElement.nativeElement.querySelectorAll('mark');
      marks.forEach((mark, i) => {
        if (marks[this._markedIndex]?.parentElement?.classList.contains('groupHeader')) {
          this._markedIndex++;
          return;
        }

        (mark as Element)?.classList.remove('selected');
        if (i === this._markedIndex) {
          (mark as Element)?.classList.add('selected');
          (mark as Element).scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest',
          });
        }
      });

      this._markedIndex = this._markedIndex < marks.length ? this._markedIndex + 1 : 0;
    }
  }

  private handleOtherComponentsSearch(
    inputSearch: ElementRef,
    grid: GridComponent | TreeGridComponent,
    contentElement: ElementRef,
    isRowSelected: boolean
  ): void {
    const marks: NodeList = contentElement.nativeElement.querySelectorAll('mark');
    let mark: Node | null;
    let found: boolean;

    for (let index = marks.length - 1; index >= 0; index--) {
      mark = marks.item(index);
      found = this.isMarkSelected(mark, grid, isRowSelected);

      if (found) {
        this.deselectPreviousMark(marks, index, isRowSelected);
        this.selectNextMark(marks, index, inputSearch, grid, isRowSelected);
        break;
      }

      if (index === 0) {
        this.selectRow(mark, inputSearch, grid, isRowSelected);
      }
    }
  }

  private deselectPreviousMark(marks: NodeList, index: number, isRowSelected: boolean): void {
    if (isRowSelected === false) {
      marks.item(index)?.parentElement?.firstElementChild?.classList.remove('selected');
    }
  }

  private selectNextMark(
    marks: NodeList,
    index: number,
    inputSearch: ElementRef,
    grid: GridComponent | TreeGridComponent,
    isRowSelected: boolean
  ): void {
    if (marks.length === index + 1) {
      this.selectRow(marks.item(0), inputSearch, grid, isRowSelected);
    } else {
      this.selectRow(marks.item(index + 1), inputSearch, grid, isRowSelected);
    }
  }
}
