import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FileTreeComponent } from './file-tree.component';
import { ResourcesService } from '../../services/resources.service';
import { FileManagerService } from '../../services/file-manager.service';
import { FileManagerComponent } from '../file-manager.component';
import { FileDescription, FileItemType, FileTree } from '../../models/file-models';
import { of } from 'rxjs';
import { ToastMockComponent } from 'src/app/app.testing.mock';
import { FileManagerComponentMock } from 'src/app/admins/file-manager/tests/file-manager.mock';
import { TreeViewComponent, TreeViewModule } from '@syncfusion/ej2-angular-navigations';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { Component } from '@angular/core';
import { FileManagerServiceMock, ResourcesServiceMock } from '../../tests/file-manager.mock';
import { FileManagerTestHelper } from '../../tests/file-manager.helper';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { MockComponent, MockDirective } from 'ng-mocks';
import { TxContextMenuComponent, DataBaseRights } from '@bassetti-group/tx-web-core';
import { CdkDropList } from '@angular/cdk/drag-drop';

let treeDataMock: FileTree[];
let filesInFolderMock: FileDescription[];
let selectedNodeMock: any;
describe('FileTreeComponent', () => {
  let component: FileTreeComponent;
  let fileManagerComponent: FileManagerComponent;
  let hostComponent: TestHostComponent;
  let hostFixture: ComponentFixture<TestHostComponent>;
  let fileManagerService: FileManagerService;
  let resourcesService: ResourcesService;

  @Component({
    selector: 'app-host-component',
    template: `<app-file-tree
      (displayDialogReplaceFile)="displayDialogReplaceFile($event)"
      (displayDialog)="dialogDisplayafterDelete($event)"
      (createFolderFromTree)="createFolder()"
      (addNewFolderFromTree)="addFile()"
      (getFilesInFolderFromTree)="getFilesInFolderInTree($event)"
      (openHistoryFromTree)="openHistory($event)"
      (updateFilesInFolder)="updateFilesInFolder($event)"></app-file-tree>`,
  })
  class TestHostComponent {
    dialogDisplayafterDelete(event: any) {
      return undefined;
    }
    displayDialogReplaceFile(event: any) {
      return undefined;
    }
    createFolder() {}
    addFile() {}
    getFilesInFolderInTree($event: any) {
      return undefined;
    }
    openHistory($event: any) {
      return undefined;
    }
    updateFilesInFolder($event: any) {
      return undefined;
    }
  }
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        MockComponent(TxContextMenuComponent),
        FileTreeComponent,
        TestHostComponent,
        MockComponent(TxContextMenuComponent),
      ],
      imports: [
        MatProgressBarModule,
        MatDividerModule,
        TreeViewModule,
        FontAwesomeTestingModule,
        MatTooltipModule,
        NoopAnimationsModule,
        MockDirective(CdkDropList),
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
      ],
      providers: [
        { provide: FileManagerComponent, useClass: FileManagerComponentMock },
        { provide: FileManagerService, useClass: FileManagerServiceMock },
        { provide: ResourcesService, useClass: ResourcesServiceMock },
      ],
    }).compileComponents();

    fileManagerComponent = TestBed.inject(FileManagerComponent);
    fileManagerService = TestBed.inject(FileManagerService);
    resourcesService = TestBed.inject(ResourcesService);
  });

  beforeEach(() => {
    hostFixture = TestBed.createComponent(TestHostComponent);
    hostComponent = hostFixture.componentInstance;
    component = hostFixture.debugElement.children[0].componentInstance;
    hostFixture.detectChanges();
  });

  beforeEach(() => {
    const testHelperForTree = new FileManagerTestHelper();
    const testDataForTree = testHelperForTree.generateTestData();
    treeDataMock = testDataForTree.treeDataMock;
    filesInFolderMock = testDataForTree.filesInFolderMock;
    selectedNodeMock = testDataForTree.selectedNodeMock;
    component.treeData = JSON.parse(JSON.stringify(treeDataMock));
    component.filesInFolder = JSON.parse(JSON.stringify(filesInFolderMock));
    fileManagerService.selectedNode = JSON.parse(JSON.stringify(selectedNodeMock));
    component.isGridUpdatable = true;
    component.isLangLoading = false;
    hostFixture.detectChanges();
    (component.fileTree as TreeViewComponent).refresh = jest.fn();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('On Node Editing', () => {
    let nodeEventMock: any;

    beforeEach(() => {
      nodeEventMock = {
        node: { parentNode: { parentNode: { nodeName: 'LI' } } },
        nodeData: { id: 0, text: 'testName' },
        cancel: false,
      };
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrRead } });
    });

    it('should call "_findNode" with nodeName and nodeData', () => {
      const spyFindNode = jest.spyOn(fileManagerService, 'findNode');
      component.onNodeEditing(nodeEventMock);
      expect(spyFindNode).toBeCalledWith(nodeEventMock.nodeData.id, component.treeData);
    });

    it('should not assign false to args.cancel with nodeName and nodeData and dbrWrite', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrWrite } });
      component.onNodeEditing(nodeEventMock);
      expect(nodeEventMock.cancel).toBe(false);
    });

    it('should not assign false to args.cancel with nodeName and nodeData and dbrRead', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrStructure } });
      component.onNodeEditing(nodeEventMock);
      expect(nodeEventMock.cancel).toBe(false);
    });

    it('should assign true to args.cancel with nodeName and nodeData and dbrRead', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrRead } });
      component.onNodeEditing(nodeEventMock);
      expect(nodeEventMock.cancel).toBe(true);
    });

    it('should call "createNotification" with nodeName and nodeData and dbrRead', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrRead } });
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onNodeEditing(nodeEventMock);
      expect(nodeEventMock.cancel).toBe(true);
      expect(spyCreateNotification).toBeCalled();
    });

    it('should assign true to args.cancel with nodeName and nodeData and dbrNone', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrNone } });
      component.onNodeEditing(nodeEventMock);
      expect(nodeEventMock.cancel).toBe(true);
    });

    it('should call "createNotification" with nodeName and nodeData and dbrNone', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrNone } });
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onNodeEditing(nodeEventMock);
      expect(nodeEventMock.cancel).toBe(true);
      expect(spyCreateNotification).toBeCalled();
    });

    it('should cancel the event with no nodeName', () => {
      nodeEventMock.node.parentNode.parentNode.nodeName = '';

      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onNodeEditing(nodeEventMock);
      expect(nodeEventMock.cancel).toBe(true);
      expect(spyCreateNotification).toBeCalledWith(
        'information',
        'admins.resources.rootFolderNotRenamed',
        false,
        5000
      );
    });

    it('should not cancel the event with no nodeData', () => {
      nodeEventMock.nodeData = undefined;

      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onNodeEditing(nodeEventMock);
      expect(nodeEventMock.cancel).toBe(false);
      expect(spyCreateNotification).not.toBeCalled();
    });
  });

  describe('Open History In Tree', () => {
    let argsMock: any;
    beforeEach(() => {
      fileManagerService.findPath = jest.fn().mockReturnValue('');
      fileManagerService.removeLastElementPathIfFolder = jest.fn().mockReturnValue('');
      fileManagerComponent.openHistory = jest.fn();
      fileManagerService.getAlias = jest.fn().mockReturnValue('CRs');
      argsMock = { alias: 'CRs', fileName: 'Models', path: '' };
    });

    it('should call "openHistory"', () => {
      const spyOpenHistory = jest.spyOn(hostComponent, 'openHistory');
      component.openHistoryInTree();
      expect(spyOpenHistory).toBeCalledTimes(1);
    });

    it('should call "_findPath"', () => {
      const spyFindPath = jest.spyOn(fileManagerService, 'findPath');
      component.openHistoryInTree();
      expect(spyFindPath).toBeCalledTimes(1);
      expect(spyFindPath).toBeCalledWith(selectedNodeMock);
    });

    it('should call "removeLastElementPathIfFolder"', () => {
      const spyRemoveLastElementPathIfFolder = jest.spyOn(
        fileManagerService,
        'removeLastElementPathIfFolder'
      );
      component.openHistoryInTree();
      expect(spyRemoveLastElementPathIfFolder).toBeCalledTimes(1);
      expect(spyRemoveLastElementPathIfFolder).toBeCalledWith('', true);
    });

    it('should call "openHistory" with a fileName', () => {
      const treeData = treeDataMock[0];
      const children = treeData.children;
      const firstChild = children ? children[0] : undefined;
      fileManagerService.selectedNode = firstChild;
      const spyOpenHistory = jest.spyOn(hostComponent, 'openHistory');
      component.openHistoryInTree();
      expect(spyOpenHistory).toBeCalledTimes(1);
      expect(spyOpenHistory).toBeCalledWith(argsMock);
    });

    it('should not call "openHistory" with no selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      const spyRemoveLastElementPathIfFolder = jest.spyOn(
        fileManagerService,
        'removeLastElementPathIfFolder'
      );
      component.openHistoryInTree();
      expect(spyRemoveLastElementPathIfFolder).not.toBeCalled();
    });
  });

  describe('On Node Edited', () => {
    let nodeEventMock: any;

    beforeEach(() => {
      nodeEventMock = { nodeData: { id: 0 }, newText: 'name', oldText: 'oldName', cancel: false };
      fileManagerService.findNode = jest.fn().mockReturnValue({ id: 5, name: 'name' });
      fileManagerComponent.selectNode = jest.fn();
    });

    it('should assign true to args.cancel with no value in newText', () => {
      nodeEventMock.newText = '';
      component.onNodeEdited(nodeEventMock);
      expect(nodeEventMock.cancel).toBe(true);
    });

    it('should call "createNotification" with no value in newText', () => {
      nodeEventMock.newText = '';
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onNodeEdited(nodeEventMock);
      expect(spyCreateNotification).toBeCalledWith(
        'information',
        'admins.resources.folderNameNotEmpty',
        false,
        5000
      );
    });

    it('should call "_findNode" with newText !== oldText', () => {
      nodeEventMock.newText = 'name';
      nodeEventMock.oldText = 'oldName';
      const spyFindNode = jest.spyOn(fileManagerService, 'findNode');
      component.onNodeEdited(nodeEventMock);
      expect(spyFindNode).toBeCalledWith(nodeEventMock.nodeData.id, component.treeData);
    });

    it('should assign a new value to selecteNodes with !component.selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      component.onNodeEdited(nodeEventMock);
      expect((component.fileTree as TreeViewComponent).selectedNodes).toStrictEqual([5]);
    });
  });
  describe('On Node Selected', () => {
    let nodeSelectEventMock: any;

    beforeEach(() => {
      component.isGridUpdatable = true;
      fileManagerService.findNode = jest.fn().mockReturnValue({ id: 1 });
      fileManagerService.getRight = jest.fn().mockReturnValue(DataBaseRights.DbrStructure);
      fileManagerService.getAlias = jest.fn().mockReturnValue('CRs');
      fileManagerService.findPath = jest.fn().mockReturnValue('');
      fileManagerService.sortGrid = jest.fn();
      nodeSelectEventMock = {
        nodeData: { id: 1 },
      };
    });

    it('should call _findNode', () => {
      const spyFindNode = jest.spyOn(fileManagerService, 'findNode');
      component.onNodeSelected(nodeSelectEventMock);
      expect(spyFindNode).toBeCalledWith(1, component.treeData);
    });

    it('should not call onExpandingFolder with no isGridUpdatable and no selectedNode', () => {
      component.isGridUpdatable = false;
      fileManagerService.selectedNode = undefined;
      const spyOnExpandingFolder = jest.spyOn(resourcesService, 'onExpandingFolder');
      component.onNodeSelected(nodeSelectEventMock);
      expect(spyOnExpandingFolder).not.toBeCalled();
    });
  });
  describe('On Node Expanding', () => {
    let nodeExpandEventMock: any;

    beforeEach(() => {
      nodeExpandEventMock = { nodeData: { id: 1 }, node: document.createElement('div') };
      (nodeExpandEventMock.node as Element).getAttribute = jest.fn().mockReturnValue([1]);
      fileManagerService.findNode = jest.fn().mockReturnValue({ expanded: false });
      component._expandNode = jest.fn();
      component.refreshTree = jest.fn();
      component.isLangLoading = false;
    });

    it('should call _expandNode', () => {
      const spyExpandNode = jest.spyOn(component, '_expandNode');
      component.onNodeExpanding(nodeExpandEventMock);
      expect(spyExpandNode).toBeCalled();
    });
  });

  describe('Expand Node', () => {
    let idNodeMock: any;
    beforeEach(() => {
      idNodeMock = 1;
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ hasChildren: true, children: undefined });
      fileManagerComponent.getFilesInFolder = jest.fn().mockReturnValue(of(''));
    });

    it('should call _findNode', () => {
      const spyFindNode = jest.spyOn(fileManagerService, 'findNode');
      component._expandNode(idNodeMock);
      expect(spyFindNode).toBeCalled();
    });

    it('should call getFilesInFolder', () => {
      const spyGetFilesInFolder = jest.spyOn(fileManagerComponent, 'getFilesInFolder');
      component._expandNode(idNodeMock);
      expect(spyGetFilesInFolder).toBeCalled();
    });

    it('should call getFilesInFolder', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ hasChildren: undefined, children: undefined });
      const spyGetFilesInFolder = jest.spyOn(fileManagerComponent, 'getFilesInFolder');
      component._expandNode(idNodeMock);
      expect(spyGetFilesInFolder).not.toBeCalled();
    });

    it('should not call getFilesInFolder', () => {
      fileManagerService.findNode = jest.fn().mockReturnValue({ hasChildren: true, children: '1' });
      const spyGetFilesInFolder = jest.spyOn(fileManagerComponent, 'getFilesInFolder');
      component._expandNode(idNodeMock);
      expect(spyGetFilesInFolder).toBeCalledTimes(1);
    });
  });

  describe('On Node Expanded', () => {
    let nodeExpandEventMock: any;
    beforeEach(() => {
      nodeExpandEventMock = { nodeData: { id: 1 } };
      fileManagerService.findNode = jest.fn().mockReturnValue({ expanded: true });
      component.refreshTree = jest.fn();
      component.isLangLoading = false;
    });

    it('should call _findNode', () => {
      const spyFindNode = jest.spyOn(fileManagerService, 'findNode');
      component.onNodeExpanded(nodeExpandEventMock);
      expect(spyFindNode).toBeCalled();
    });
  });

  describe('On Node Drag Start', () => {
    let dragAndDropEventMock: any;
    beforeEach(() => {
      dragAndDropEventMock = { draggedNodeData: { id: 1 }, cancel: false };
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrStructure } });
    });

    it('should call _findNode', () => {
      const spyFindNode = jest.spyOn(fileManagerService, 'findNode');
      component.onNodeDragStart(dragAndDropEventMock);
      expect(spyFindNode).toBeCalled();
    });

    it('should not call _findNode', () => {
      dragAndDropEventMock = { draggedNodeData: undefined };
      const spyFindNode = jest.spyOn(fileManagerService, 'findNode');
      component.onNodeDragStart(dragAndDropEventMock);
      expect(spyFindNode).not.toBeCalled();
    });

    it('should not assign false to args.cancel with dbrStructure', () => {
      component.onNodeDragStart(dragAndDropEventMock);
      expect(dragAndDropEventMock.cancel).toBe(false);
    });

    it('should assign true to args.cancel with dbrRead', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrRead } });
      component.onNodeDragStart(dragAndDropEventMock);
      expect(dragAndDropEventMock.cancel).toBe(true);
    });

    it('should assign true to args.cancel with dbrWrite', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrWrite } });
      component.onNodeDragStart(dragAndDropEventMock);
      expect(dragAndDropEventMock.cancel).toBe(true);
    });

    it('should assign true to args.cancel with dbrNone', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrNone } });
      component.onNodeDragStart(dragAndDropEventMock);
      expect(dragAndDropEventMock.cancel).toBe(true);
    });
  });

  describe('On Node Drag', () => {
    let dragAndDropEventMock: any;
    let docFragmentMock: DocumentFragment;
    let nodeMock1: HTMLDivElement;
    let nodeMock2: HTMLDivElement;

    beforeEach(() => {
      docFragmentMock = document.createDocumentFragment();
      nodeMock1 = document.createElement('div');
      nodeMock2 = document.createElement('div');
      docFragmentMock.appendChild(nodeMock1);
      docFragmentMock.appendChild(nodeMock2);
      dragAndDropEventMock = {
        draggedNodeData: { id: 1 },
        cancel: false,
        position: 'Inside',
        dropIndicator: '',
        droppedNode: docFragmentMock,
      };

      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrStructure } });
    });

    it('should call _findNode', () => {
      const spyFindNode = jest.spyOn(fileManagerService, 'findNode');
      component.onNodeDrag(dragAndDropEventMock);
      expect(spyFindNode).toBeCalled();
    });

    it('should not call _findNode with draggedNodeData=undefined', () => {
      dragAndDropEventMock = { draggedNodeData: undefined };
      const spyFindNode = jest.spyOn(fileManagerService, 'findNode');
      component.onNodeDrag(dragAndDropEventMock);
      expect(spyFindNode).not.toBeCalled();
    });

    it('should not assign event.cancel=true with dbrStructure', () => {
      component.onNodeDrag(dragAndDropEventMock);
      expect(dragAndDropEventMock.cancel).toBe(false);
    });

    it('should assign event.cancel=true with dbrNone', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrNone } });
      component.onNodeDrag(dragAndDropEventMock);
      expect(dragAndDropEventMock.cancel).toBe(true);
    });

    it('should assign event.cancel=true with dbrRead', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrRead } });
      component.onNodeDrag(dragAndDropEventMock);
      expect(dragAndDropEventMock.cancel).toBe(true);
    });

    it('should assign event.cancel=true with dbrWrite', () => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrWrite } });
      component.onNodeDrag(dragAndDropEventMock);
      expect(dragAndDropEventMock.cancel).toBe(true);
    });

    it('should add class "border-accent-dashed" to "droppedNode.children.item(1).classList"', () => {
      component.onNodeDrag(dragAndDropEventMock);
      expect(
        dragAndDropEventMock.droppedNode.children.item(1).classList.contains('border-accent-dashed')
      ).toBe(true);
    });

    it('should assign "e-no-drop" to event.dropIndicator with event.droppedNodeData=null', () => {
      dragAndDropEventMock.droppedNodeData = null;
      component.onNodeDrag(dragAndDropEventMock);
      expect(dragAndDropEventMock.dropIndicator).toBe('e-no-drop');
    });

    it('should assign "e-no-drop" to event.dropIndicator with event.droppedNodeData=null', () => {
      dragAndDropEventMock.position = null;
      component.onNodeDrag(dragAndDropEventMock);
      expect(dragAndDropEventMock.dropIndicator).toBe('e-no-drop');
    });
  });

  describe('On Node Drag Stop', () => {
    let dragAndDropEventMock: any;

    beforeEach(() => {
      dragAndDropEventMock = {
        draggedNodeData: { id: 1 },
        droppedNodeData: { id: 1 },
        cancel: false,
        position: 'Inside',
        dropIndicator: '',
      };
      component.isGridUpdatable = true;
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValueOnce({ id: 1 })
        .mockReturnValueOnce({ id: 2 });
      fileManagerService.getAlias = jest.fn().mockReturnValue('CRs');
      fileManagerService.findParentNode = jest.fn().mockReturnValue({ id: 3 });
      fileManagerService.createNotification = jest.fn();
      fileManagerService.findPath = jest
        .fn()
        .mockReturnValueOnce('')
        .mockReturnValueOnce('Documents');
      fileManagerService.removeLastElementPathIfFolder = jest.fn().mockReturnValue('Documents');
      component.controlFilesTreeToDrag = jest.fn().mockReturnValue(['1']);
      component.moveFilesInTree = jest.fn();
    });

    it('should call createNotification with different alias', () => {
      fileManagerService.getAlias = jest
        .fn()
        .mockReturnValueOnce('CRs')
        .mockReturnValueOnce('Documents');
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onNodeDragStop(dragAndDropEventMock);
      expect(spyCreateNotification).toBeCalledWith(
        'information',
        'admins.resources.folderNotMovedAlias',
        false,
        8000
      );
    });

    it('should not call createNotification with same aliases', () => {
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.onNodeDragStop(dragAndDropEventMock);
      expect(spyCreateNotification).not.toBeCalledWith(
        'information',
        'admins.resources.folderNotMovedAlias',
        false,
        8000
      );
    });

    it('should call onExpandingFolder with different path', () => {
      const spyOnExpandingFolder = jest.spyOn(resourcesService, 'onExpandingFolder');
      component.onNodeDragStop(dragAndDropEventMock);
      expect(spyOnExpandingFolder).toBeCalledWith('CRs', undefined);
    });

    it('should call controlFilesTreeToDrag with different path', () => {
      const spyControlFilesTreeToDrag = jest.spyOn(component, 'controlFilesTreeToDrag');
      component.onNodeDragStop(dragAndDropEventMock);
      expect(spyControlFilesTreeToDrag).toBeCalledWith([{ id: 1 }], { id: 2 }, [
        {
          extension: '',
          lastWriteTime: '2021-11-02T13:50:47.2091990Z',
          length: -1,
          name: 'Models',
          type: 'Directory',
        },
        {
          extension: '.jpg',
          lastWriteTime: '2021-09-10T13:10:31.6050849Z',
          length: 75775,
          name: 'test.jpg',
          type: 'File',
        },
      ]);
    });

    it('should call moveFilesInTree with different path', () => {
      const spyMoveFilesInTree = jest.spyOn(component, 'moveFilesInTree');
      component.onNodeDragStop(dragAndDropEventMock);
      expect(spyMoveFilesInTree).toBeCalledWith(['1'], { id: 3 }, { id: 2 }, [{ id: 1 }]);
    });

    it('should not call onExpandingFolder with same path', () => {
      fileManagerService.findPath = jest.fn().mockReturnValue('');
      fileManagerService.removeLastElementPathIfFolder = jest.fn().mockReturnValue('');
      const spyOnExpandingFolder = jest.spyOn(resourcesService, 'onExpandingFolder');
      component.onNodeDragStop(dragAndDropEventMock);
      expect(spyOnExpandingFolder).not.toBeCalled();
    });

    it('should not call onExpandingFolder with isGridUpdatable=false', () => {
      component.isGridUpdatable = false;
      const spyOnExpandingFolder = jest.spyOn(resourcesService, 'onExpandingFolder');
      component.onNodeDragStop(dragAndDropEventMock);
      expect(spyOnExpandingFolder).not.toBeCalled();
    });
  });

  describe('Rename With Name In Tree', () => {
    let dataMock: any;

    beforeEach(() => {
      dataMock = {
        name: 'nameMock',
        node: { id: '1', resource: { alias: 'CRs' } },
      };
      (component.fileTree as TreeViewComponent).updateNode = jest.fn();
    });

    it('should call updateNode', () => {
      const spyUpdateNode = jest.spyOn(component.fileTree as TreeViewComponent, 'updateNode');
      component.renameWithNameInTree(dataMock);
      expect(spyUpdateNode).toBeCalledWith(dataMock.node.id, dataMock.name);
    });

    it('should assign a new value to node.resource.alias', () => {
      component.renameWithNameInTree(dataMock);
      expect(dataMock.node.resource.alias).toBe(dataMock.name);
    });
  });
  describe('On Node Collapsed', () => {
    let nodeExpandEventMock: any;
    beforeEach(() => {
      fileManagerService.findNode = jest
        .fn()
        .mockReturnValueOnce({ expanded: true, children: undefined })
        .mockReturnValueOnce(undefined);
      nodeExpandEventMock = { nodeData: { id: 1 } };
      (component.fileTree as TreeViewComponent).refresh = jest.fn();
    });

    it('should call refresh with node', () => {
      const spyRefresh = jest.spyOn(component, 'refreshTree').mockImplementation(jest.fn());
      component.onNodeCollapsed(nodeExpandEventMock);
      expect(spyRefresh).toBeCalled();
    });

    it('should call _findNode with selectedNode', () => {
      const idSelectedNode = fileManagerService.selectedNode?.id;
      fileManagerService.selectedNode = { ...selectedNodeMock };

      const spyFindNode = jest.spyOn(fileManagerService, 'findNode');
      component.onNodeCollapsed(nodeExpandEventMock);
      expect(spyFindNode).toBeCalledTimes(2);
      expect(spyFindNode.mock.calls).toEqual([
        [nodeExpandEventMock.nodeData.id, component.treeData],
        [idSelectedNode, component.treeData],
      ]);
    });

    it('should assign "undefined" to selectedNode with _findNode=undefined', () => {
      component.onNodeCollapsed(nodeExpandEventMock);
      expect(fileManagerService.selectedNode).toBe(undefined);
    });
  });

  describe('File Drop Tree', () => {
    let eventMock: any;
    let targetMock: HTMLElement;

    beforeEach(() => {
      targetMock = document.createElement('div');
      eventMock = {
        target: targetMock,
        dataTransfer: { files: ['1'] },
      };

      component.isGridUpdatable = true;
      component.dragExternalFile.type = 1;
      fileManagerService.selectedNode = { ...selectedNodeMock };

      (component.fileTree as TreeViewComponent).getNode = jest.fn().mockReturnValue({ id: '1' });
      fileManagerService.findNode = jest.fn().mockReturnValue({ id: '2' });
      fileManagerService.findParentNode = jest.fn().mockReturnValue({ id: '3' });
      fileManagerComponent.getFilesInFolder = jest.fn().mockReturnValue(of(''));
      fileManagerService.getAlias = jest.fn().mockReturnValue('CRs');
      fileManagerService.findPath = jest.fn().mockReturnValue('Documents');
      fileManagerComponent.controlFilesToUpload = jest.fn().mockReturnValue({ id: '4' });
      fileManagerService.sortGrid = jest.fn();
      fileManagerComponent.addFilesInGrid = jest.fn();
      fileManagerComponent.getFilesInFolderInTree = jest.fn();
    });

    it('should call controlFilesToUpload', () => {
      const spyControlFilesToUpload = jest.spyOn(hostComponent, 'getFilesInFolderInTree');
      component.fileDropTree(eventMock);
      expect(spyControlFilesToUpload).toBeCalledWith({ files: ['1'], treeNode: { id: '2' } });
    });

    it('should assign a new value to fileTree.selectedNodes', () => {
      (component.fileTree as TreeViewComponent).selectedNodes = [];
      component.fileDropTree(eventMock);
      expect((component.fileTree as TreeViewComponent).selectedNodes).toStrictEqual(['2']);
    });
  });
  describe('Get Tree Icon', () => {
    let dataMock: any;

    beforeEach(() => {
      dataMock = { isFolder: true, expanded: true };
    });

    it('should return "file" with !data.isFolder', () => {
      dataMock.isFolder = false;
      expect(component.getTreeIcon(dataMock)).toBe('file');
    });

    it('should return "folder-open" with data.isFolder and data.expanded', () => {
      expect(component.getTreeIcon(dataMock)).toBe('folder-open');
    });

    it('should return "folder-open" with data.isFolder and !data.expanded', () => {
      dataMock.expanded = false;
      expect(component.getTreeIcon(dataMock)).toBe('folder');
    });
  });

  describe('Can Delete File In Tree', () => {
    beforeEach(() => {
      fileManagerService.selectedNode = { ...selectedNodeMock };
      fileManagerService.findParentNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrStructure } });
    });

    it('should return "false" with !selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      expect(component.canDeleteFileInTree()).toBe(false);
    });

    it('should return "true" with parentNode and dbrStructure', () => {
      expect(component.canDeleteFileInTree()).toBe(true);
    });

    it('should return "false" with parentNode and dbrNone', () => {
      fileManagerService.findParentNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrNone } });
      expect(component.canDeleteFileInTree()).toBe(false);
    });

    it('should return "false" with parentNode and dbrRead', () => {
      fileManagerService.findParentNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrRead } });
      expect(component.canDeleteFileInTree()).toBe(false);
    });

    it('should return "false" with parentNode and dbrWrite', () => {
      fileManagerService.findParentNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrWrite } });
      expect(component.canDeleteFileInTree()).toBe(false);
    });
  });

  describe('Can Add File In Tree Or Grid', () => {
    beforeEach(() => {
      fileManagerService.findParentNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrRead }, isFolder: true });
    });

    it('should return "false" with !selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      expect(component.canAddFileInTreeOrGrid()).toBe(false);
    });

    it('should return "true" with selectedNode and isFolder and dbrStructure', () => {
      fileManagerService.selectedNode = { ...selectedNodeMock };
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      (fileManagerService.selectedNode as FileTree).isFolder = true;
      expect(component.canAddFileInTreeOrGrid()).toBe(true);
    });

    it('should return "false" with selectedNode and !isFolder and dbrRead', () => {
      (fileManagerService.selectedNode as FileTree).isFolder = false;
      expect(component.canAddFileInTreeOrGrid()).toBe(false);
    });
  });

  describe('Can Create Folder In Tree', () => {
    beforeEach(() => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
    });

    it('should return "false" with !selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      expect(component.canCreateFolderInTree()).toBe(false);
    });

    it('should return "true" with selectedNode and dbrStructure', () => {
      expect(component.canCreateFolderInTree()).toBe(true);
    });

    it('should return "false" with selectedNode and dbrNone', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      expect(component.canCreateFolderInTree()).toBe(false);
    });

    it('should return "false" with selectedNode and dbrRead', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      expect(component.canCreateFolderInTree()).toBe(false);
    });

    it('should return "false" with selectedNode and dbrWrite', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrWrite;
      expect(component.canCreateFolderInTree()).toBe(false);
    });
  });

  describe('Can Rename File In Tree', () => {
    beforeEach(() => {
      fileManagerService.findParentNode = jest
        .fn()
        .mockReturnValue({ resource: { right: DataBaseRights.DbrRead }, isFolder: true });
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
    });

    it('should return "false" with !selectedNode', () => {
      fileManagerService.selectedNode = undefined;
      expect(component.canRenameFileInTree()).toBe(false);
    });

    it('should return "false" with selectedNode and !this._findParentNode', () => {
      fileManagerService.findParentNode = jest.fn().mockReturnValue(undefined);
      expect(component.canRenameFileInTree()).toBe(false);
    });

    it('should return "true" with selectedNode and this._findParentNode and dbrStructure', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      expect(component.canRenameFileInTree()).toBe(true);
    });

    it('should return "true" with selectedNode and this._findParentNode and dbrWrite', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrWrite;
      expect(component.canRenameFileInTree()).toBe(true);
    });

    it('should return "false" with selectedNode and this._findParentNode and dbrNone', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      expect(component.canRenameFileInTree()).toBe(false);
    });

    it('should return "false" with selectedNode and this._findParentNode and dbrRead', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      expect(component.canRenameFileInTree()).toBe(false);
    });
  });

  describe('Delete From Tree', () => {
    let dataMock: any;

    beforeEach(() => {
      dataMock = {
        file: { id: '1', name: 'testMock1', expanded: false },
        parentNode: { id: '2', name: 'testMock2', expanded: false },
      };

      fileManagerService.getAlias = jest.fn().mockReturnValue('CRs');
      fileManagerService.removeLastElementPathIfFolder = jest.fn().mockReturnValue('Documents');
      fileManagerService.findPath = jest.fn().mockReturnValue('');
      fileManagerService.findParentNode = jest.fn().mockReturnValue(treeDataMock[0]);
      component.onNodeSelected = jest.fn();
    });

    it('should call "createNotification"', () => {
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.deleteFromTree(dataMock);
      expect(spyCreateNotification).toBeCalledWith(
        'loading',
        'admins.resources.itemsDeletingNoProgression',
        false,
        0
      );
    });

    it('should call "onDeletingFile"', () => {
      const spyOnDeletingFile = jest.spyOn(resourcesService, 'onDeletingFile');
      component.deleteFromTree(dataMock);
      expect(spyOnDeletingFile.mock.calls).toEqual([['CRs', 'testMock1', 'Documents']]);
    });
  });

  describe('Delete File In Tree', () => {
    let dialogDeleteFilesMock: any;

    beforeEach(() => {
      fileManagerService.selectedNode = { ...selectedNodeMock };
      (fileManagerService.selectedNode as FileTree).isFolder = true;
      (fileManagerService.selectedNode as FileTree).name = 'testNameMock';

      dialogDeleteFilesMock = {
        files: fileManagerService.selectedNode,
        isDeletingFromTree: true,
        parentNode: undefined,
      };
    });

    it('should call "show" with Folder', () => {
      const spyShow = jest.spyOn(hostComponent, 'dialogDisplayafterDelete');
      component.deleteFileInTree();
      expect(spyShow).toBeCalledWith({
        message: 'admins.resources.folderDelete',
        annotation: null,
        object: dialogDeleteFilesMock,
      });
    });

    it('should call "show" with File', () => {
      (fileManagerService.selectedNode as FileTree).isFolder = false;
      const spyShow = jest.spyOn(hostComponent, 'dialogDisplayafterDelete');
      component.deleteFileInTree();
      expect(spyShow).toBeCalledWith({
        message: 'admins.resources.fileDelete',
        annotation: null,
        object: dialogDeleteFilesMock,
      });
    });
  });
  describe('Move Files In Tree', () => {
    let argsFilesMock: any;
    let argsSourceParentNodeMock: any;
    let argsDestinationParentNodeMock: any;
    let argsNodeMoved: any;

    beforeEach(() => {
      argsFilesMock = { goodFiles: [], sameFiles: ['1', '2'], existingFiles: [] };
      argsSourceParentNodeMock = undefined;
      argsDestinationParentNodeMock = { id: '1' };
      argsNodeMoved = undefined;

      fileManagerService.getAlias = jest.fn().mockReturnValue('CRs');
      fileManagerService.findPath = jest.fn().mockReturnValue('Documents');
      component._expandNode = jest.fn();
      component.doMoveFilesInTree = jest.fn();
    });

    it('should call "onExpandingFolder" with length > 0', () => {
      const spyOnExpandingFolder = jest.spyOn(resourcesService, 'onExpandingFolder');
      component.moveFilesInTree(
        argsFilesMock,
        argsSourceParentNodeMock,
        argsDestinationParentNodeMock,
        argsNodeMoved
      );
      expect(spyOnExpandingFolder).toBeCalledWith('CRs', 'Documents');
    });

    it('should call dialogComponent.show with length > 0', () => {
      const spyShow = jest.spyOn(hostComponent, 'displayDialogReplaceFile');
      component.moveFilesInTree(
        argsFilesMock,
        argsSourceParentNodeMock,
        argsDestinationParentNodeMock,
        argsNodeMoved
      );
      expect(spyShow).toBeCalledWith({
        files: argsFilesMock,
        filesInFolder: component.filesInFolder,
        annotation: true,
        sourceParentNode: argsSourceParentNodeMock,
        destinationParentNode: argsDestinationParentNodeMock,
        data: [
          {
            extension: '',
            lastWriteTime: '2021-11-02T13:50:47.2091990Z',
            length: -1,
            name: 'Models',
            type: FileItemType.directory,
          },
          {
            extension: '.jpg',
            lastWriteTime: '2021-09-10T13:10:31.6050849Z',
            length: 75775,
            name: 'test.jpg',
            type: FileItemType.file,
          },
        ],
        nodeMoved: argsNodeMoved,
      });
    });

    it('should call doMoveFilesInTree with length = 0', () => {
      argsFilesMock = { goodFiles: [], sameFiles: [], existingFiles: [] };
      const spydoMoveFilesInTree = jest.spyOn(component, 'doMoveFilesInTree');
      component.moveFilesInTree(
        argsFilesMock,
        argsSourceParentNodeMock,
        argsDestinationParentNodeMock,
        argsNodeMoved
      );
      expect(spydoMoveFilesInTree).toBeCalledWith({
        filesToAdd: argsFilesMock.goodFiles,
        filesToReplace: [],
        sourceParentNode: argsSourceParentNodeMock,
        destinationParentNode: argsDestinationParentNodeMock,
        node: argsNodeMoved,
      });
    });
  });

  describe('Do Move Files In Tree', () => {
    let argsFilesMock: any;
    let mockToast: ToastMockComponent;

    beforeEach(() => {
      argsFilesMock = {
        filesToAdd: [{ name: 'nameAddMock', newName: 'newNameAddMock' }],
        filesToReplace: [{ name: 'nameReplaceMock', newName: 'newNameReplaceMock' }],
        sourceParentNode: { children: [] },
        destinationParentNode: { id: '1' },
        node: { isFolder: true },
      };
      mockToast = {
        data: {
          isUnread: true,
          isPersistent: true,
          type: 'success',
          title: 'title',
          description: 'description',
        },
      };
      component.moveFile = jest.fn();
      fileManagerService.findPath = jest
        .fn()
        .mockReturnValueOnce('Documents1')
        .mockReturnValueOnce('Documents2')
        .mockReturnValue('Documents3');
      fileManagerService.getAlias = jest.fn().mockReturnValue('CRs');
      fileManagerService.createNotification = jest.fn().mockReturnValue(mockToast);
      fileManagerComponent.setFileAfterSuccessMoving = jest.fn(argsFilesMock);
      fileManagerComponent.setChildren = jest.fn();
    });

    it('should call "_findPath" first time', () => {
      const spyFindPath = jest.spyOn(fileManagerService, 'findPath');
      component.doMoveFilesInTree(argsFilesMock);
      expect(spyFindPath).toBeCalledWith(argsFilesMock.destinationParentNode);
      expect(spyFindPath).toHaveReturnedWith('Documents1');
    });

    it('should call "_findPath" second time', () => {
      const spyFindPath = jest.spyOn(fileManagerService, 'findPath');
      component.doMoveFilesInTree(argsFilesMock);
      expect(spyFindPath).toBeCalledWith(argsFilesMock.sourceParentNode);
      expect(spyFindPath).toHaveReturnedWith('Documents2');
    });

    it('should not call "_findPath" third time with !args.node', () => {
      argsFilesMock.node = undefined;
      const spyFindPath = jest.spyOn(fileManagerService, 'findPath');
      component.doMoveFilesInTree(argsFilesMock);
      expect(spyFindPath).not.toHaveReturnedWith('Documents3');
    });

    it('should call "_getAlias"', () => {
      const spyGetAlias = jest.spyOn(fileManagerService, 'getAlias');
      component.doMoveFilesInTree(argsFilesMock);
      expect(spyGetAlias).toBeCalledWith(argsFilesMock.destinationParentNode.id);
      expect(spyGetAlias).toHaveReturnedWith('CRs');
    });

    it('should call "createNotification" with nbTotalFiles > 0', () => {
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.doMoveFilesInTree(argsFilesMock);
      expect(spyCreateNotification).toBeCalledWith(
        'loading',
        'admins.resources.movingFiles',
        false,
        0,
        0
      );
    });
  });

  describe('_Set File After Success Moving Tree', () => {
    let argsDestinationFolderMock: any;
    let argsFileMock: any;
    let argsParentNodeMovedMock: any;
    let argsNodeMovedMock: any;

    beforeEach(() => {
      argsDestinationFolderMock = { hasChildren: false, children: ['1'] };
      argsFileMock = {
        name: 'nameFileMock',
        newName: 'newNameFileMock',
        type: FileItemType.directory,
      };
      argsParentNodeMovedMock = { children: [{ name: 'nameFileMock' }] };
      argsNodeMovedMock = fileManagerService.selectedNode;
      component.refreshTree = jest.fn();
      component.createNode = jest.fn();
      component.filesInFolder.findIndex = jest.fn().mockReturnValue(0);
    });

    it('should assign "true" to destinationFolder.hasChildren', () => {
      component.setFileAfterSuccessMovingTree(
        argsDestinationFolderMock,
        argsFileMock,
        argsParentNodeMovedMock,
        argsNodeMovedMock
      );
      expect(argsDestinationFolderMock.hasChildren).toBe(true);
    });

    it('should assign file.name to file.newName', () => {
      component.setFileAfterSuccessMovingTree(
        argsDestinationFolderMock,
        argsFileMock,
        argsParentNodeMovedMock,
        argsNodeMovedMock
      );
      expect(argsFileMock.newName).toBe(argsFileMock.name);
    });

    it('should call "createNode" with Directory && destinationFolder.children', () => {
      const spyCreateNode = jest.spyOn(component, 'createNode');
      component.setFileAfterSuccessMovingTree(
        argsDestinationFolderMock,
        argsFileMock,
        argsParentNodeMovedMock,
        argsNodeMovedMock
      );
      expect(spyCreateNode).toBeCalledWith(argsFileMock, argsDestinationFolderMock, true);
    });

    it('should assign undefined to selectedNode with Directory && destinationFolder.children && nodeMoved=selectedNode', () => {
      component.setFileAfterSuccessMovingTree(
        argsDestinationFolderMock,
        argsFileMock,
        argsParentNodeMovedMock,
        argsNodeMovedMock
      );
      expect(fileManagerService.selectedNode).toBe(undefined);
    });

    it('should assign undefined to selectedNode with Directory && destinationFolder.children && nodeMoved=selectedNode', () => {
      component.setFileAfterSuccessMovingTree(
        argsDestinationFolderMock,
        argsFileMock,
        argsParentNodeMovedMock,
        argsNodeMovedMock
      );
      expect((component.fileTree as TreeViewComponent).selectedNodes).toStrictEqual([]);
    });
  });
  describe('Create Node', () => {
    let argsFileMock: any;
    let argsSelectedNode: any;
    let argsIsFolder: boolean;

    let originalNodeMock: any;
    let childNodeMock: any;

    beforeEach(() => {
      argsFileMock = { name: 'nameFileMock' };
      argsSelectedNode = { id: '1' };
      argsIsFolder = true;

      childNodeMock = { name: 'nameChildMock' };
      originalNodeMock = { id: '1', children: [{ name: 'nameChildOriginMock' }] };

      fileManagerService.formatTreeData = jest.fn().mockReturnValue(childNodeMock);
      component.refreshTree = jest.fn();
      fileManagerService.findNode = jest.fn().mockReturnValue(originalNodeMock);
      fileManagerService.getRight = jest.fn().mockReturnValue('CRs');
    });

    it('should concat childNodeMock to originalNodeMock.children at position 1', () => {
      component.createNode(argsFileMock, argsSelectedNode, argsIsFolder);
      expect(originalNodeMock.children[1]).toBe(childNodeMock);
    });

    it('should concat childNodeMock to originalNodeMock.children at position 0 with originalNodeMock.children=[]', () => {
      originalNodeMock.children = [];
      component.createNode(argsFileMock, argsSelectedNode, argsIsFolder);
      expect(originalNodeMock.children[0]).toBe(childNodeMock);
    });

    it('should call refresh', () => {
      const spyRefresh = jest.spyOn(component, 'refreshTree');
      component.createNode(argsFileMock, argsSelectedNode, argsIsFolder);
      expect(spyRefresh).toBeCalled();
    });
  });
  describe('Menu click', () => {
    let argsMock: any;

    beforeEach(() => {
      component.filesInFolder = { ...filesInFolderMock };
      argsMock = { item: { label: 'button.delete' } };
      component.deleteFileInTree = jest.fn();
      fileManagerComponent.createFolder = jest.fn();
      fileManagerComponent.addFile = jest.fn();
      component.openHistoryInTree = jest.fn();
      (component.fileTree as TreeViewComponent).beginEdit = jest.fn();
    });

    it('should call deleteFileInTree with "button.delete"', () => {
      const spyDeleteFileInTree = jest.spyOn(component, 'deleteFileInTree');
      component.menuClick(argsMock);
      expect(spyDeleteFileInTree).toBeCalled();
    });

    it('should call assign "[]" to filesInFolder with "button.delete"', () => {
      const spyOpenHistoryInTree = jest.spyOn(hostComponent, 'updateFilesInFolder');
      component.menuClick(argsMock);
      expect(spyOpenHistoryInTree).toBeCalledWith([]);
    });

    it('should call beginEdit with "admins.resources.rename"', () => {
      argsMock = { item: { label: 'admins.resources.rename' } };
      const spyBeginEdit = jest.spyOn(component.fileTree as TreeViewComponent, 'beginEdit');
      component.menuClick(argsMock);
      expect(spyBeginEdit).toBeCalledWith((fileManagerService.selectedNode as FileTree).id);
    });

    it('should call addFile with "button.add"', () => {
      argsMock = { item: { label: 'button.add' } };
      const spyAddFile = jest.spyOn(hostComponent, 'addFile');
      component.menuClick(argsMock);
      expect(spyAddFile).toBeCalled();
    });

    it('should call createFolder with "admins.resources.newFolder"', () => {
      argsMock = { item: { label: 'admins.resources.newFolder' } };
      const spyCreateFolder = jest.spyOn(hostComponent, 'createFolder');
      component.menuClick(argsMock);
      expect(spyCreateFolder).toBeCalled();
    });

    it('should call openHistoryInTree with "admins.resources.history"', () => {
      argsMock = { item: { label: 'admins.resources.history' } };
      const spyOpenHistoryInTree = jest.spyOn(component, 'openHistoryInTree');
      component.menuClick(argsMock);
      expect(spyOpenHistoryInTree).toBeCalled();
    });
  });

  describe('Control Files Tree To Drag', () => {
    let argsFilesMock: any;
    let argsSelectedNodeMock: any;
    let argsFilesInFolder: any;
    let returnMock: any;

    beforeEach(() => {
      argsFilesMock = [{ name: 'nameMock1', resource: { lastModification: '1', size: 100 } }];
      argsSelectedNodeMock = { resource: { right: DataBaseRights.DbrStructure } };
      argsFilesInFolder = [{ name: 'nameMock1' }, { name: 'nameMock2' }];

      returnMock = {
        existingFiles: [{ name: 'nameMock1' }],
        sameFiles: [
          {
            name: 'nameMock1',
            type: FileItemType.directory,
            lastWriteTime: argsFilesMock[0].resource.lastModification,
            extension: '',
            length: 100,
          },
        ],
        goodFiles: [],
      };

      component.filesInFolder = [
        {
          name: 'nameMock1',
          type: 'File',
          lastWriteTime: '2021-11-02T14:22:18.9564791Z',
          extension: '.JPG',
          length: 75775,
        },
        {
          name: 'otherName',
          type: 'File',
          lastWriteTime: '2021-11-02T14:22:18.9564791Z',
          extension: '.JPG',
          length: 75775,
        },
      ];

      fileManagerService.createNotification = jest.fn();
    });

    it('should call createNotification with dbrNone', () => {
      argsSelectedNodeMock.resource.right = DataBaseRights.DbrNone;
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.controlFilesTreeToDrag(argsFilesMock, argsSelectedNodeMock, argsFilesInFolder);
      expect(spyCreateNotification).toBeCalledWith(
        'error',
        'admins.resources.fileAddAndModifNotAllow',
        false,
        8000
      );
    });

    it('should not call createNotification with dbrRead', () => {
      argsSelectedNodeMock.resource.right = DataBaseRights.DbrRead;
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.controlFilesTreeToDrag(argsFilesMock, argsSelectedNodeMock, argsFilesInFolder);
      expect(spyCreateNotification).toBeCalledWith(
        'error',
        'admins.resources.fileAddAndModifNotAllow',
        false,
        8000
      );
    });

    it('should not call createNotification with dbrWrite', () => {
      argsSelectedNodeMock.resource.right = DataBaseRights.DbrWrite;
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.controlFilesTreeToDrag(argsFilesMock, argsSelectedNodeMock, argsFilesInFolder);
      expect(spyCreateNotification).toBeCalledWith(
        'error',
        'admins.resources.fileAddAndModifNotAllow',
        false,
        8000
      );
    });

    it('should not call createNotification with dbrStructure', () => {
      argsSelectedNodeMock.resource.right = DataBaseRights.DbrStructure;
      const spyCreateNotification = jest.spyOn(fileManagerService, 'createNotification');
      component.controlFilesTreeToDrag(argsFilesMock, argsSelectedNodeMock, argsFilesInFolder);
      expect(spyCreateNotification).not.toBeCalled();
    });

    it('should return existingFiles and sameFiles with common files in filesInFolder', () => {
      expect(
        component.controlFilesTreeToDrag(argsFilesMock, argsSelectedNodeMock, argsFilesInFolder)
      ).toStrictEqual(returnMock);
    });

    it('should return goodFiles with none common files in filesInFolder', () => {
      argsFilesInFolder = [{ name: 'nameMock2' }];
      expect(
        component.controlFilesTreeToDrag(argsFilesMock, argsSelectedNodeMock, argsFilesInFolder)
      ).toStrictEqual({
        existingFiles: [],
        goodFiles: [
          {
            extension: '',
            lastWriteTime: '1',
            length: 100,
            name: 'nameMock1',
            type: FileItemType.directory,
          },
        ],
        sameFiles: [],
      });
    });
  });
});
