import { NgModule } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { DataModelObjectTypesComponent } from './components/data-model-object-types/data-model-object-types.component';
import { ObjectTypeFormComponent } from './components/data-model-object-types/object-type-form/object-type-form.component';
import { DataModelComponent } from './components/data-model.component';
import { DataModelRoutingModule } from './data-model-routing.module';
import { DataModelExportComponent } from './components/data-model-export/data-model-export.component';

@NgModule({
  declarations: [
    DataModelComponent,
    DataModelObjectTypesComponent,
    ObjectTypeFormComponent,
    DataModelExportComponent,
  ],
  imports: [SharedModule, DataModelRoutingModule],
  providers: [],
})
export class DataModelModule {
  getComponent() {
    return DataModelComponent;
  }
}
