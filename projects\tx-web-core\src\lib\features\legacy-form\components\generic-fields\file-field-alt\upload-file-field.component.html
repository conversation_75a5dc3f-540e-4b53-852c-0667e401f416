<div class="main-div-file-uploader">
  <div
    class="uploader-dragzone accent-border"
    [ngClass]="{ 'dropzone-hovered': isDropzoneHovered }"
    LegacyTxDragDrop
    (fileDropped)="onFileSelected($event)"
    (drop)="isDropzoneHovered = false"
    (dragover)="containsFiles($event)"
    (dragleave)="onDragLeave($event)">
    <div class="uploader-drag">
      <span class="accent uploader-file">
        <fa-icon [icon]="['fal', 'file-upload']" size="lg"></fa-icon>
      </span>
      <div class="uploader-file-message">Drop file here !</div>
    </div>
  </div>
  <input
    type="file"
    class="file-input"
    #fileUpload
    [accept]="requiredFileType"
    [multiple]="multiple"
    (change)="onFileSelected($event)" />

  <div class="files-container" (dragover)="containsFiles($event)" id="{{ label }}ChipListContainer">
    <mat-chip-list
      [ngClass]="{
        'multiple-file-chip-list': multiple,
        'scrollable-multiple-file-chip-list': multiple && isScrollable()
      }"
      (wheel)="onWheel($event)">
      <div *ngIf="multiple || filesShown.length < 1" class="file-uploader">
        <fa-icon [icon]="['fal', 'file-upload']" class="form-icon"></fa-icon>

        <!-- <button mat-mini-fab color="primary" class="upload-btn"
            (click)="fileUpload.click()">
              <mat-icon>attach_file</mat-icon>
              <fa-icon [matTooltip]="'Upload a file'" matTooltipShowDelay="500" matTooltipPosition="above" [icon]="['far', 'folder-tree']" aria-hidden="true"></fa-icon>
    
          </button> -->
        <div>
          <div class="file-uploader-text">
            <span>Drop file here, or </span>
            <span class="url-read clickable-url" (click)="fileUpload.click()">browse</span>
          </div>
          <div class="file-uploader-size-text field-form-hint">
            <span>Maximum size : {{ maxMoFileSize }}Mo</span>
          </div>
        </div>
      </div>
      <div
        *ngFor="let file of files"
        [ngClass]="{ 'head-colum-file-field-box': isHeadColumnFile(file) }">
        <tx-file-field-box
          *ngIf="file.action < 3"
          [file]="file"
          (removeEvent)="removeFile($event)"
          [hideVisualisationToggle]="hideVisualisationToggle"></tx-file-field-box>
      </div>
    </mat-chip-list>
  </div>
</div>
