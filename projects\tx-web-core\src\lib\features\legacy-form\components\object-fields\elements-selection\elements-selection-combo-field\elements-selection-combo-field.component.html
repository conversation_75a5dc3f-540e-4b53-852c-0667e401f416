<div
  #mainDiv
  class="e-float-input e-input-group tx-main-esc"
  [style]="'width:' + width + 'px;'"
  [matTooltip]="labelTooltip"
  matTooltipClass="mat-tooltip-multiline"
  matTooltipShowDelay="500"
  matTooltipPosition="above"
  (blur)="focusOut(input)"
  tabindex="1">
  <!-- Focus and FocusOut Event binding Floating input label -->
  <input
    type="text"
    #input
    [required]="required"
    (focus)="onFocusInput($event.target)"
    class="tx-input-esc"
    (blur)="focusOut($event.target)"
    readonly />
  <span class="e-float-line"></span>
  <label class="e-float-text">{{ label }}</label>
  <ng-container *ngIf="isRemoveSpanVisible">
    <span
      #spanRemove
      class="e-input-group-icon e-icons e-cross-close tx-span-cross-close"
      title="Remove selected elements"
      (click)="onRemovingSelection()"></span>
  </ng-container>
  <span
    #spanSpin
    class="e-input-group-icon e-spin-down"
    (mouseup)="onMouseUp($event.target)"
    (mousedown)="onMouseDown($event.target)"
    (click)="onSpinClicked()"></span>
</div>

<!-- popup of the dropdown list -->
<tx-escf-popup
  #popup
  [attachedTo]="input"
  [spinAttached]="spanSpin"
  [elements]="elements"
  [multiple]="selection === 'Multiple'"
  [selectedElements]="selectedElements"
  [valueKey]="valueKey"
  [textKey]="textKey"
  [idObjectType]="idObjectType"
  [height]="height"
  (elementChecked)="onElementChecked($event)"
  (elementSelected)="onElementSelected($event)"></tx-escf-popup>
