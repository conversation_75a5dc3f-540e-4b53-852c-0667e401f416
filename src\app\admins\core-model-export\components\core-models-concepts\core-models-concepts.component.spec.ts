import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MockComponent, MockProvider } from 'ng-mocks';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { CoreModelsConceptsComponent } from './core-models-concepts.component';
import { MatFormField } from '@angular/material/form-field';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { GridComponent } from '@syncfusion/ej2-angular-grids';
import {
  GRID_SERVICE_MOCK,
  TxGridFilterMenuComponent,
  TxGridService,
} from '@bassetti-group/tx-web-core';
const TRANSLATIONS = {
  en: {},
  fr: {},
};

describe('CoreModelsExportComponent', () => {
  let component: CoreModelsConceptsComponent;
  let fixture: ComponentFixture<CoreModelsConceptsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        CoreModelsConceptsComponent,
        MockComponent(TxGridFilterMenuComponent),
        MockComponent(MatFormField),
        MockComponent(FaIconComponent),
        MockComponent(GridComponent),
      ],
      imports: [TranslateTestingModule.withTranslations(TRANSLATIONS)],
      providers: [MockProvider(TxGridService, GRID_SERVICE_MOCK, 'useValue')],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CoreModelsConceptsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
