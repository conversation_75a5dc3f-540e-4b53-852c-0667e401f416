.object-type-edition-global-container {
  margin-top: 16px;
  margin-left: 32px;
  margin-right: 32px;
}

.h2-section-subtitle {
  margin-top: 16px;

  display: inline-flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
}

.form-sub-section {
  display: flex;
  flex-direction: row;

  .form-div-form-field {
    display: inline-block;
    width: calc(50% - 32px);
    margin-right: 32px;
    vertical-align: top;
    margin-bottom: 4px;
  }

  .form-div-form-field-right {
    display: inline-block;
    width: calc(50% - 32px);
    margin-left: 32px;
    vertical-align: top;
    margin-bottom: 4px;
  }

  mat-form-field {
    width: calc(100%);
  }
  mat-slide-toggle {
    padding-top: 5px;
  }
}

.form-section-icon {
  .form-ot-icons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .form-ot-icon-group {
      display: flex;
      flex-direction: column;
      text-align: center;
      padding: 8px;
      min-width: 25px;

      .form-ot-icon {
        height: 16px;
        width: 16px;
      }
    }
  }

  .otgrid-accordion-icon {
    margin-right: 16px;
  }

  .otgrid-accordion-counter {
    border: none;
    border-radius: 1em;
    padding: 3px;
    font-size: 12px;
    text-align: center;
    vertical-align: middle;
    margin-left: 16px;
    width: 25px;
  }
}

.treegrids-parent {
  height: 350px;
  display: flex;
  justify-content: space-between;

  .treegrid-objecttype-attributes {
    height: 100%;
    width: 45%;

    .treegrid-associativity {
      overflow: auto;
      height: 100%;
      display: flex;
    }
  }
}

.disabletreegrid {
  pointer-events: none;
  opacity: 0.4;
}
.wrapper {
  cursor: not-allowed;
}
