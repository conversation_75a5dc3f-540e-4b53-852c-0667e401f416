import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxShortStringObjectFieldComponent } from './short-string-object-field.component';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { AttributesMockService } from '../../../testing.mock';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatChipsModule } from '@angular/material/chips';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MockComponent } from 'ng-mocks';
import { TxInputTextFieldComponent } from '../../generic-fields/input-text-field/input-text-field.component';
import { TxReadTextFieldComponent } from '../../generic-fields/read-text-field/read-text-field.component';

describe('TxShortStringObjectFieldComponent', () => {
  let component: TxShortStringObjectFieldComponent;
  let fixture: ComponentFixture<TxShortStringObjectFieldComponent>;
  let element;
  let attributesService: LegacyTxAttributesService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        TxShortStringObjectFieldComponent,
        MockComponent(TxInputTextFieldComponent),
        MockComponent(TxReadTextFieldComponent),
      ],
      providers: [{ provide: LegacyTxAttributesService, useClass: AttributesMockService }],
      imports: [
        MatChipsModule,
        MatTooltipModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatInputModule,
        BrowserAnimationsModule,
      ],
    }).compileComponents();
    attributesService = TestBed.inject(LegacyTxAttributesService);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxShortStringObjectFieldComponent);
    component = fixture.componentInstance;
    element = fixture.nativeElement;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
