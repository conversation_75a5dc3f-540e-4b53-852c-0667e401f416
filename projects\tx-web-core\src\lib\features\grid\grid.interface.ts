import { TxGridDataType, TxGridEditType, TxGridFilterValueType } from './grid.const';

/**
 * Interface representing a column configuration for the TxGridComponent.
 */
export interface TxGridColumn<T> {
  /**
   * Field name to bind to the column. It corresponds to the property in the data source.
   */
  field: keyof T;

  /**
   * Optional: Type of the column data. (e.g., 'text', 'number', 'date', etc.)
   */
  type?: TxGridDataType;

  /**
   * Header text to be displayed in the column header.
   */
  headerText: string;

  /**
   * Optional: Indicates whether a tooltip is available for the column header.
   */
  headerTooltip?: string;

  /**
   * Optional: Text alignment for the column content. (e.g., 'left', 'center', 'right')
   */
  textAlign?: 'left' | 'center' | 'right';

  /**
   * Optional: Flag indicating whether the column is editable.
   */
  editable?: boolean;

  /**
   * Optional: Flag indicating the column edit type.
   */
  editType?: TxGridEditType;

  /**
   * Optional: Flag indicating the column edit options for dropdown.
   */
  editOption?: TxEditCellSettings;

  /**
   * Optional: Flag indicating whether the column supports sorting.
   */
  sorting?: boolean;

  /**
   * Optional: Flag indicating whether the column is visible.
   */
  visible?: boolean;

  /**
   * Optional: Width of the column.
   */
  width?: string;

  /**
   * Optional: Flag indicating whether the column is resize.
   */
  resize?: boolean;

  isSearchable?: boolean;

  checkMuted?: boolean;
  autoGrowDisable?: boolean;
}

export function compareTxGridColumn<T>(a: TxGridColumn<T>, b: TxGridColumn<T>): boolean {
  return a.field === b.field;
}
export interface TxGridFilterConditions {
  [condition: string]: (value: TxGridFilterValueType, filteredValue: string) => boolean;
}

export interface TxGridFilterOption {
  label: string;
  value: string;
}

export interface TxGridFilterByColumn<T> {
  conditions: FilterOperators<T> | null;
  values: FilterValues<T> | null;
}
export interface TxGridFilterOnData<T> {
  fields: (keyof T)[];
  conditions?: FilterOperators<T>;
  value: string;
}
/**
 * Interface representing arguments for row selection in the TxGridComponent.
 */
export interface TxGridRowSelectArgs<T> {
  /**
   * Data associated with the selected row(s).
   */
  data: T[];

  /**
   * Optional: Event triggering the row selection.
   */
  event?: Event;

  /**
   * Optional: Index of the selected row.
   */
  rowIndex?: number;

  /**
   * Optional: Indexes of the selected rows.
   */
  rowIndexes?: number[];
}
export interface TxRowDragEventArgs {
  /** Defines the selected row's element. */
  rows?: Element[];
  /** Defines the target element from which drag starts. */
  target?: Element;
  data?: Object[];
  /** Defines the drag element from index. */
  fromIndex?: number;
  /** Defines the target element from index. */
  dropIndex?: number;
}

/**
 * Interface for a class SelectionSettings
 */
export interface TxGridSelectionSettings {
  /**
   * If 'persistSelection' set to true, then the Grid selection is persisted on all operations.
   * For persisting selection in the Grid, any one of the column should be enabled as a primary key.
   *
   * @default false
   */
  persistSelection?: boolean;
}

/**
 * Interface defining filter values for the TxGridComponent.
 */
export type FilterValues<Type> = {
  [Property in keyof Type]: Type[Property] | null;
};

/**
 * Interface defining filter operators for the TxGridComponent.
 */
export type FilterOperators<Type> = {
  [Property in keyof Type]: TxGridFilterOperators | null;
};

/**
 * Type defining available filter operators for the TxGridComponent.
 */
export type TxGridFilterOperators =
  | 'is-empty'
  | 'is-not-empty'
  | 'equal'
  | 'notEqual'
  | 'startsWith'
  | 'endsWith'
  | 'contains'
  | 'greaterThan'
  | 'greaterThanOrEqual'
  | 'lessThan'
  | 'lessThanOrEqual';

/**
 * Interface representing a value-label pair.
 */
export interface ValueLabel<T> {
  /** The value associated with the label. */
  value: T;
  /** The label corresponding to the value. */
  label: string;
}

/**
 * Interface representing information about a row.
 */
export interface RowInfo {
  /**
   * Returns the particular cell element.
   */
  cell?: Element;

  /**
   * Returns the index of the particular cell.
   */
  cellIndex?: number;

  /**
   * Returns the particular row element.
   */
  row?: Element;

  /**
   * Returns the index of the particular row.
   */
  rowIndex?: number;

  /**
   * Returns the data associated with the particular row.
   */
  rowData?: Object;

  /**
   * Returns information about the particular column.
   */
  column?: Object;
}

export interface TxGridDoubleClickArgs<T> {
  rowData: T;
  rowIndex: number;
  type?: string;
  columnName?: string;
  event: Event
}
export interface TxGridChecked<T> {
  objects: T[];
  checked: boolean;
}

export interface TxDragOption {
  blockTransformation?: boolean;
  hidePlaceholder?: boolean;
  multiple?: boolean;
}

export interface TxSaveEventArgs<T> {
  columnName?: keyof T;
  value?: string;
  rowData?: T;
  previousValue?: string;
}
export interface TxAddEventArgs {
  /** Defines the request type. */
  requestType?: string;
  /** Define the form element */
  form?: HTMLFormElement;
  /** Defines the record objects.
   *
   */
  data?: Object;
  /** Defines the event name. */
  type?: string;
  /** Defines the previous data. */
  previousData?: Object;
  /** Defines the added row. */
  row?: Object;
  /** Added row index */
  index?: number;
  /**
   * Defines the record objects.
   */
  rowData?: Object;
  /** Defines the target for dialog */
  target?: HTMLElement;
}
export interface TxEditCellSettings {
  params: TxEditCellSettingsParams;
}

export interface TxEditCellSettingsParams {
  popupHeight?: string;
  dataSource: TxEditCellSettingsFields[];
  fields: TxEditCellSettingsFields;
}

export interface TxEditCellSettingsFields {
  text: string;
  value: string;
}

export interface Invalid <T=string> {
  value: boolean;
  field: T | string | T[] | string[];
};