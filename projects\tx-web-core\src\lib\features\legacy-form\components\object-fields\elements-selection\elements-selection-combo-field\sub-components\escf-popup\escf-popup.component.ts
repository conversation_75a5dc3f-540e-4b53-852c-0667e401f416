import {
  TxEscfFilteredElementsComponent,
  TxListCheckEventArgs,
} from './../escf-filtered-elements/escf-filtered-elements.component';
import { LegacyTxEscfTreeComponent } from './../escf-tree/escf-tree.component';
import { animate, state, style, transition, trigger } from '@angular/animations';
import {
  Component,
  ElementRef,
  OnInit,
  Output,
  ViewChild,
  EventEmitter,
  Input,
  AfterViewInit,
  HostListener,
  OnChanges,
  SimpleChanges,
  Renderer2,
  OnDestroy,
} from '@angular/core';
import { SelectEventArgs, TabComponent, TabItem } from '@syncfusion/ej2-angular-navigations';
import { map } from 'rxjs/operators';
import { TxObjectCheckEventArgs } from '../../../../../../trees/tree-objects/tree-objects.component';
import { LegacyTxObjectTypeService } from '../../../../../../services/structure/services/object-type.service';
import { LegacyTxObjectsService } from '../../../../../../services/structure/services/objects.service';
import { _LegacyStringUtils } from '../../../../../../utilities/legacy-string-utils';

const TAB_ALL = 'all';
const TAB_MY_FAVORITE = 'myFavorites';
const TAB_MY_SELECTION = 'mySelection';

@Component({
  selector: 'tx-escf-popup',
  templateUrl: './escf-popup.component.html',
  styleUrls: ['./escf-popup.component.scss'],
  animations: [
    trigger('openClose', [
      // ...
      state(
        'open',
        style({
          display: 'block',
          height: '97px',
          opacity: 1,
        })
      ),
      state(
        'closed',
        style({
          height: '1px',
          opacity: 0,
          visibility: 'hidden',
        })
      ),
      transition('open => closed', [animate('0.5s')]),
      transition('closed => open', [animate('0.25s')]),
    ]),
  ],
})
export class TxEscfPopupComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  @Input() attachedTo!: HTMLElement;
  @Input() spinAttached!: HTMLElement;
  @Input() multiple!: boolean;
  @Input() elements!: any[];
  @Input() selectedElements!: any[];
  @Input() valueKey!: string;
  @Input() textKey!: string;
  @Input() idObjectType!: number;
  @Input() height = 300;
  @Input() displayFavorites = false;

  @Output() elementChecked = new EventEmitter<TxObjectCheckEventArgs>();
  @Output() elementSelected = new EventEmitter<TxObjectCheckEventArgs>();

  @ViewChild('popup') popup!: ElementRef;
  @ViewChild('spanSearch', { static: false }) spanSearch!: ElementRef;
  @ViewChild('inputSearch', { static: false }) inputSearch!: ElementRef;
  @ViewChild('treeElements') treeElements!: LegacyTxEscfTreeComponent;
  @ViewChild('filteredList') filteredList!: TxEscfFilteredElementsComponent;
  @ViewChild('selectionList') selectionList!: TxEscfFilteredElementsComponent;
  @ViewChild('tabs') tabs!: TabComponent;
  @ViewChild('mySelectionTab') mySelectionTab!: TabItem;

  displaySelection!: boolean;
  isOpen = false;
  isRealyOpen = false;
  isRemoveSpanVisible = false;
  inputFocused = false;
  private wasInside = false;
  treeVisible = true;
  filteredListVisible = false;
  selectedElementsValues: any[] = [];
  allTabSelected = true;
  favoritesTabSelected = false;
  selectionTabSelected = false;
  objectTypeName!: string;
  groupByKey = 'groupBy';

  // Mapping Tab items Header property
  public headerText = [
    { text: 'All', id: TAB_ALL },
    { text: 'My Favorites', id: TAB_MY_FAVORITE },
    { text: 'My Selection', id: TAB_MY_SELECTION },
  ];

  constructor(
    private renderer: Renderer2,
    private objectService: LegacyTxObjectsService,
    public objectTypeService: LegacyTxObjectTypeService
  ) {}

  private focusMainLabel() {
    this.attachedTo.parentElement?.classList.add('e-input-focus');
  }

  private unFocusMainLabel() {
    this.attachedTo.parentElement?.classList.remove('e-input-focus');
  }

  private isMainLabelFocused(): boolean {
    return this.attachedTo.parentElement?.classList.contains('e-input-focus') as boolean;
  }

  ngOnInit() {
    if (this.displayFavorites) {
      this.headerText.splice(1, 1);
    }

    this.displaySelection = this.multiple;
  }

  ngAfterViewInit() {
    window.addEventListener('scroll', this.updatePopupPosition.bind(this), true);

    this.setElementsValues();
    this.objectTypeName = this.objectTypeService.getName(this.idObjectType);
    this.renderer.appendChild(document.body, this.popup.nativeElement);
  }

  ngOnChanges(changes: SimpleChanges) {
    // if (changes.selectedElements) {
    //   this.selectedElements = changes.selectedElements.currentValue;
    //   if (this.tabs) {
    //     this.tabs.items[this.tabs.items.length - 1].header.text = `My Selection (${this.selectedElements.length})`;
    //   }
    // }
  }

  ngOnDestroy() {
    this.renderer.removeChild(document.body, this.popup.nativeElement);
  }

  setElementsValues() {
    if (this.selectedElements) {
      this.selectedElementsValues = this.selectedElements.map((e) => e[this.valueKey]);
    }
  }

  updatePopupPosition() {
    if (!this.attachedTo || !this.isOpen) {
      return;
    }
    const { x, y } = this.attachedTo.getBoundingClientRect();

    this.popup.nativeElement.style.top = y + 31 + 'px';
    this.popup.nativeElement.style.left = x + 'px';
  }

  toggle() {
    this.isOpen = !this.isOpen;

    if (this.isOpen) {
      this.treeElements.load();
      this.updatePopupPosition();
      this.focusMainLabel();
      setTimeout(() => {
        this.inputSearch.nativeElement.focus();
        this.focusMainLabel();
        this.isRealyOpen = true;

        if (
          this.selectedElements &&
          this.selectedElements.length < 1 &&
          this.selectionTabSelected
        ) {
          this.tabs.select(0);
        }
      }, 100);
    } else {
      this.close();
    }
  }

  close() {
    this.isOpen = false;
    this.inputSearch.nativeElement.value = '';
    this.isRealyOpen = false;
    this.treeVisible = true;
    this.filteredListVisible = false;
  }

  removeSelection() {
    this.updateSelectedElements(this.selectedElements);
    this.treeElements.treeObject.treeview.uncheckAll();
    this.treeElements.treeObject.treeview.selectedNodes = [];

    if (this.selectionList) {
      this.selectionList.elements = [];
    }
  }

  // @HostListener('document:click')
  // clickInside() {
  //   this.wasInside = true;
  // }

  // @HostListener('document:click', ['$event'])
  // clickout(args: any) {
  //   if (args.target === this.attachedTo) {
  //     return;
  //   }

  //   if (!this.wasInside && this.isOpen && this.isRealyOpen) {
  //       this.unFocusMainLabel();
  //       this.toggle();
  //       this.spinAttached.classList.remove('e-spin-up');
  //       this.spinAttached.classList.add('e-spin-down');
  //   }
  //   this.wasInside = false;
  // }

  // Focus Event function for input component
  focusIn(target: HTMLElement): void {
    target.parentElement!.classList.add('e-input-focus');
  }

  // FocusOut Event function for input component
  focusOut(target: HTMLElement): void {
    target.parentElement!.classList.remove('e-input-focus');
  }

  // filter input events
  onInputSearchFocusIn(target: HTMLElement) {
    this.focusIn(target);
    this.focusMainLabel();

    // update the icon color
    this.spanSearch.nativeElement.style.color = 'black';
    this.inputFocused = true;
  }

  onInputSearchFocusOut(target: HTMLElement) {
    this.focusOut(target);

    // update the icon color
    this.spanSearch.nativeElement.style.color = 'rgba(0, 0, 0, 0.54)';
    this.inputFocused = false;
  }

  onInputSearchKeyUp(event: any) {
    this.onInputSearchChange(event.target.value);
    this.isRemoveSpanVisible = this.inputSearch.nativeElement.value !== '';
  }

  onRemoveInputSearchValue() {
    this.inputSearch.nativeElement.value = '';
    this.onInputSearchChange();
    this.isRemoveSpanVisible = false;
    this.inputSearch.nativeElement.focus();
  }

  onInputSearchChange(value: string = '') {
    if (this.allTabSelected) {
      if (value === '' || value === undefined) {
        this.treeVisible = true;
        this.filteredListVisible = false;
        this.setElementsValues();
      } else {
        this.treeVisible = false;
        this.filteredListVisible = true;
        // search in objects

        this.objectService
          .searchObjects(value, undefined, undefined, this.idObjectType, true, false, 0)
          .pipe(
            map((elements) =>
              elements
                .filter((e) => !e.isFolder)
                .map((e: any) => {
                  e[this.groupByKey] = this.objectTypeName;
                  return e;
                })
            )
          )
          .subscribe((objects) => {
            this.filteredList.reloadElements(objects, this.selectedElements);
          });
      }
    } else if (this.favoritesTabSelected) {
    } else if (this.selectionTabSelected) {
      if (value === '' || value === undefined) {
        this.selectionList.reloadElements(this.selectedElements, this.selectedElements);
      } else {
        const selectedElementsFiltered = this.selectedElements.filter((s) => {
          return _LegacyStringUtils.inStr(s[this.textKey].toLowerCase(), value.toLowerCase());
        });

        this.selectionList.reloadElements(selectedElementsFiltered, selectedElementsFiltered);
      }
    }
  }

  onElementChecked(args: TxObjectCheckEventArgs) {
    this.elementChecked.emit(args);
  }

  onElementSelected(args: TxObjectCheckEventArgs) {
    if (this.multiple) {
      return;
    }

    this.elementSelected.emit(args);
  }

  onFilteredElementChecked(args: TxListCheckEventArgs) {
    this.elementChecked.emit({ checked: args.checked, txObject: args.element });
  }

  onFilteredElementSelected(args: TxListCheckEventArgs) {
    this.elementChecked.emit({ checked: args.checked, txObject: args.element });
  }

  onTabSelected(args: SelectEventArgs) {
    this.allTabSelected = args.selectedIndex === 0;

    if (this.displayFavorites) {
      this.favoritesTabSelected = args.selectedIndex === 1;
      this.selectionTabSelected = args.selectedIndex === 2;
    } else {
      this.selectionTabSelected = args.selectedIndex === 1;
    }

    if (args.previousIndex === args.selectedIndex) {
      return;
    }

    if (this.selectionTabSelected) {
      if (this.selectionList) {
        this.selectionList.reloadElements(this.selectedElements, this.selectedElements);
      } else {
        setTimeout(() => {
          this.selectionList.reloadElements(this.selectedElements, this.selectedElements);
        }, 300);
      }
    } else if (this.allTabSelected) {
      this.setElementsValues();
    }
  }

  onAnimationDone() {}

  onselectedTabChange(index: number) {}

  onTabCreated(args: Event) {
    // hide tabs
    if (!this.multiple && !this.displayFavorites) {
      const element = this.tabs.element.getElementsByClassName('e-tab-header')[0] as HTMLElement;
      element.style.display = 'none';
    }
  }

  updateSelectedElements(selectedElements: any[]) {
    this.selectedElements = selectedElements;

    if (this.tabs) {
      this.tabs.element.getElementsByClassName('e-tab-text')[
        this.tabs.items.length - 1
      ].innerHTML = `My Selection (${this.selectedElements.length})`;
    }
  }
}
