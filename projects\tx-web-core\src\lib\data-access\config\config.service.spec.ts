// @ts-strict-ignore
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { TxConfigService } from './config.service';
import { TX_ENVIRONMENT_TOKEN } from '../../utilities/environment';

describe('Service: Config', () => {
  let service: TxConfigService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        {
          provide: TX_ENVIRONMENT_TOKEN,
          useValue: {
            production: false,
            apiUrl: 'https://localhost:44336/',
            authUrl: 'https://auth.teexma.local',
          },
        },
      ],
    });
    service = TestBed.inject(TxConfigService);

    service.config = {
      authenticationRestUrl: 'https://example.com/authentication',
      teexmaUrl: 'https://example.com/',
      businessRestUrl: 'https://example.com/api',
    };
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Formatted URL', () => {
    it('should call "getUrlFormated" when getting api url', () => {
      const spyFormattedURL = jest.spyOn(service, 'getUrlFormated').mockImplementation(jest.fn());
      service.getApiUrl();
      expect(spyFormattedURL).toHaveBeenCalledWith(service.config.businessRestUrl);
    });

    it('should call "getUrlFormated" when getting auth url', () => {
      const spyFormattedURL = jest.spyOn(service, 'getUrlFormated').mockImplementation(jest.fn());
      service.getAuthUrl();
      expect(spyFormattedURL).toHaveBeenCalledWith(service.config.authenticationRestUrl);
    });
  });

  describe('Correct URLs', () => {
    beforeAll(() => {
      // @ts-ignore
      delete window.location;
      // @ts-ignore
      window.location = new URL('https://example.com');
    });

    it('should get API url', () => {
      expect(service.getApiUrl()).toBe('https://example.com/api/');
    });

    it('should get Auth url', () => {
      expect(service.getAuthUrl()).toBe('https://example.com/authentication/');
    });
  });
});
