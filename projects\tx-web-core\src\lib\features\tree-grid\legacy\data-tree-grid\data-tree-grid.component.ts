import { Component, ElementRef, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { RowDataBoundEventArgs, SelectionSettingsModel } from '@syncfusion/ej2-angular-grids';
import {
  RowCollapsedEventArgs,
  RowExpandedEventArgs,
  TreeGridComponent,
} from '@syncfusion/ej2-angular-treegrid';
import {  TxTreeGrid } from '../../tree-grid.models'
import { Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import {
  InputSearchEventInfo,
} from '@bassetti-group/tx-web-core/src/lib/features/grid';
import { TxTreeGridService } from '../../tree-grid.service';

@Component({
  standalone: true,
  selector: 'tx-data-tree-grid',
  templateUrl: './data-tree-grid.component.html',
  styleUrls: ['./data-tree-grid.component.scss'],
  providers: [TxTreeGridService],
})
export class TxDataTreeGridComponent<T extends { explanation?: string } | {}>
  implements OnDestroy, OnInit
{
  @ViewChild('treeGrid') public treeGrid?: TreeGridComponent;

  public data: TxTreeGrid<T>[] = [];
  public selectionSettings?: SelectionSettingsModel;
  public searchById: number | undefined;
  public idComponent?: number;
  public inputSearchValue = '';

  protected subscription?: Subscription;

  constructor(
    public el: ElementRef,
    public translate: TranslateService,
    public treeGridService: TxTreeGridService
  ) {}

  ngOnInit(): void {
    this.idComponent = this.treeGridService.getIdComponent();
  }
  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  getTooltip(data: TxTreeGrid<T>): string {
    return this.hasExplanation(data.txObject)
      ? `${this.translate.instant(_('txWebCore.generic.id'))} ${data.id}\n${data.name}\n${
          data.txObject.explanation
        }`
      : `${this.translate.instant(_('txWebCore.generic.id'))} ${data.id}\n${data.name}`;
  }

  removeSearch(): void {
    this.searchById = undefined;
    this.inputSearchValue = '';
  }

  search(inputSearchEventInfo: InputSearchEventInfo): void {
    this.inputSearchValue = inputSearchEventInfo.inputSearch.nativeElement.value;
    if (this.treeGrid) {
      this.searchById = this.treeGridService.search(
        inputSearchEventInfo.inputSearch,
        this.treeGrid,
        this.el,
        inputSearchEventInfo.event,
        true
      );
    }
  }

  clearSelectionInGrids() {
    this.treeGrid?.clearSelection();
  }

  searchIdInGridsAndSelectRow(id: number): boolean {
    if (this.treeGrid) {
      const index = this.selectRowById(id);
      if (index) {
        return index >= 0;
      }
    }
    return false;
  }

  selectRowById(id: number): number | undefined {
    const index = this.treeGrid
      ?.getVisibleRecords()
      .findIndex((el) => (el as TxTreeGrid<T>).id === id);

    if (this.treeGrid && index && index > -1) {
      this.searchById = id;
      this.treeGridService.selectRowByIndex(index, this.treeGrid, true);
      return index;
    }
    return undefined;
  }

  onNodeExpanded(event: RowExpandedEventArgs, data: TxTreeGrid<T>[]) {
    this.treeGridService.onNodeExpanded(event, data);
  }

  onNodeCollapsed(event: RowCollapsedEventArgs, data: TxTreeGrid<T>[]) {
    this.treeGridService.onNodeCollapsed(event, data);
  }

  onRowBound(args: RowDataBoundEventArgs) {
    this.treeGridService.onRowBound(args);
  }

  protected getExpandedState(obj: any, objGrid: TxTreeGrid<T>[]): boolean {
    return this.treeGridService.getExpandedState(obj, objGrid);
  }
  private hasExplanation(
    txObject: { explanation?: string } | {}
  ): txObject is { explanation?: string } {
    return (txObject as { explanation?: string }).explanation !== undefined;
  }
}
