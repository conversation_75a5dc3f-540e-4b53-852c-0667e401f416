import { CommonModule } from '@angular/common';
import { Component, Input, OnDestroy } from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormControl,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validator,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { Subject, startWith, takeUntil } from 'rxjs';
@Component({
  selector: 'tx-input-select',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, MatSelectModule, MatFormFieldModule],
  templateUrl: './input-select.component.html',
  styleUrls: ['./input-select.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: TxInputSelectComponent,
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: TxInputSelectComponent,
      multi: true,
    },
  ],
})
export class TxInputSelectComponent<T extends { name: string; id: number }>
  implements ControlValueAccessor, OnDestroy, Validator
{
  @Input() options: T[] = [];
  @Input() required: boolean = false;

  disabled: boolean = false;
  control: FormControl | undefined;
  private _destroying$ = new Subject<void>();
  private _onTouched: () => void = () => {};
  onValidationChange: () => void = () => {};

  writeValue(value: number): void {
    if (this.control) {
      this.control.setValue(value);
    } else {
      this.control = new FormControl(value);
    }
  }

  registerOnChange(fn: any): void {
    this.control?.valueChanges
      .pipe(startWith(this.control.value), takeUntil(this._destroying$))
      .subscribe(fn);
  }

  registerOnTouched(fn: () => void): void {
    this._onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    isDisabled ? this.control?.disable() : this.control?.enable();
    this.disabled = isDisabled;
  }

  validate(control: AbstractControl<any, any>): ValidationErrors | null {
    const requiredError = this.required ? Validators.required(control) : null;
    this.control?.setErrors(requiredError);
    return requiredError;
  }

  registerOnValidatorChange?(fn: () => void): void {
    this.onValidationChange = fn;
  }

  ngOnDestroy(): void {
    this._destroying$.next();
  }
}
