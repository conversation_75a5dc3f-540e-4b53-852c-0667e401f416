import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChild,
  ContentChildren,
  Input,
  OnInit,
  QueryList,
  ViewChild,
  Type,
  Provider,
  EventEmitter,
  Output,
  Optional,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import {
  PaginatedTableDataSource,
  TxCellPrefixTemplate,
  TxGridColumn,
  TxGridColumnTemplate,
  TxGridComponent,
  TxGridRowSelectArgs,
  TxGridToolbarTemplate,
  GridDirective,
  TxGridDoubleClickArgs,
  InputSearchEventInfo,
  GridFilter,
} from '@bassetti-group/tx-web-core/src/lib/features/grid';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { Observable, of } from 'rxjs';
import { PaginatedTreeGridDataSource } from './data-sources/paginated-tree-grid-data-source';
import { TreeCellStylePipe } from './pipes/tree-cell-style.pipe';
import { DEFAULT_CHILD_ID, TxTreeExpandState, TxTreeGridChildren } from './tree-grid.interface';
import { TxTreeGridService } from './tree-grid.service';
import { CellPrefixStylePipe } from './pipes/cell-prefix-style.pipe';
import { flattenTree } from './tree-utils';

type TreeGridDataSourceProvider<T extends TxTreeGridChildren<T>> = Provider[];
const createTreeDataSourceFactoryProvider =
  <T extends {}>(dataSource: Type<PaginatedTreeGridDataSource<T & TxTreeGridChildren<T>>>) =>
  () =>
    new dataSource(of([]));
/**
 * Help to create appropriate TreeDataSource Provider
 * @param dataSource Type of Tree data source to provide
 * @returns Instance of Tree data source
 */
export const provideTreeGridDataSource = <T extends {}>(
  dataSource: Type<PaginatedTreeGridDataSource<T & TxTreeGridChildren<T>>>
): TreeGridDataSourceProvider<T> => [
  {
    provide: dataSource,
    useFactory: createTreeDataSourceFactoryProvider(dataSource),
  },
  {
    provide: PaginatedTreeGridDataSource,
    useExisting: dataSource,
  },
  {
    provide: PaginatedTableDataSource,
    useExisting: PaginatedTreeGridDataSource,
  },
];
export const defaultTreeGridDataSource = <T extends {}>(): TreeGridDataSourceProvider<T> => [
  TxTreeGridService,
];
@Component({
  standalone: true,
  imports: [
    CommonModule,
    TxGridComponent,
    FontAwesomeModule,
    MatTableModule,
    MatButtonModule,
    TxCellPrefixTemplate,
    MatProgressSpinnerModule,
    TreeCellStylePipe,
    CellPrefixStylePipe,
  ],
  selector: 'tx-tree-grid',
  templateUrl: './tree-grid.component.html',
  styleUrls: ['./tree-grid.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TxTreeGridComponent<T extends TxTreeGridChildren<T>>
  extends GridDirective<T>
  implements OnInit, AfterViewInit, OnChanges
{
  @ViewChild(TxGridComponent) gridComponent!: TxGridComponent<T>;
  displayedColumns: (keyof T | 'tree-control' | 'select' | 'drag-drop')[] = [];
  tableDataSource: PaginatedTreeGridDataSource<T> | undefined;
  @Input() set childMapping(value: keyof TxTreeGridChildren<T>) {
    this.dataSource.childMapping = value;
  }
  @Input() primaryKey: keyof T = DEFAULT_CHILD_ID as keyof T;
  @Input() set idMapping(value: keyof T) {
    if (this.dataSource) {
      this.dataSource.idMapping = value;
    }
  }
  @Input() set parentMapping(value: keyof T) {
    if (this.dataSource) {
      this.dataSource.parentMapping = value;
    }
  }
  @Input() set buildTree(value: boolean) {
    if (this.dataSource) {
      this.dataSource.buildTree = value;
    }
  }

  @Input() set data(value: Observable<T[]> | T[]) {
    if (this.primaryKey) {
      this.dataSource.primaryKey = this.primaryKey;
    }
    if (value !== undefined) {
      this.dataSource.addData(value);
    }
  }
  @Input({ required: true }) columns!: TxGridColumn<T>[];
  @Input() set expandState(value: TxTreeExpandState) {
    this.dataSource.expandState = value;
    this.dataSource.handleExpandCollapseNode(value);
  }
  @Input() treeColumnIndex: number = 0;
  @Input() showSpinner: boolean = false;
  @Input() allowCellEditOnDblClick?: boolean;
  @Input() disableEditOnCellClick?: boolean;
  @Input() enableNativeTreeLook?: boolean;
  @Input() allowAutoRowSelect!: boolean;
  @Input() disableColumnShift?: boolean;
  @Input() enableColumnResize: boolean = false;
  @Input() autoGrowColumns: boolean = false;
  @Input() set autoReloadCollapse(value: boolean) {
    this.dataSource.autoReloadCollapse = value;
  }
  @Input() inputPlaceholder?: string;
  @Input() inputValue: string = '';
  @Input() enableSearching = false;
  @Input() searchInputIsFiltered: boolean = false;
  @Input() disableSelectionHandling = false;

  @Output() nodeExpanding: EventEmitter<TxGridRowSelectArgs<T>> = new EventEmitter();
  @Output() ready: EventEmitter<void> = new EventEmitter();
  @Output() rowSelected: EventEmitter<TxGridRowSelectArgs<T>> = new EventEmitter();
  @Output() rowDeselected: EventEmitter<TxGridRowSelectArgs<T>> = new EventEmitter();
  @Output() rowDoubleClick: EventEmitter<TxGridDoubleClickArgs<T>> = new EventEmitter();
  @Output() actionCellComplete: EventEmitter<object> = new EventEmitter();
  @Output() searchInputChange: EventEmitter<InputSearchEventInfo> = new EventEmitter();
  @Output() searchClear: EventEmitter<void> = new EventEmitter();
  @Output() filterChange: EventEmitter<GridFilter | string> = new EventEmitter();
  @Output() dataBound: EventEmitter<void> = new EventEmitter();

  @ContentChildren(TxGridColumnTemplate)
  columnDefinitions: QueryList<TxGridColumnTemplate> | undefined;
  @ContentChild(TxGridToolbarTemplate) headerTemplate: TxGridToolbarTemplate | undefined;
  public get childMapping() {
    return this.dataSource.childMapping;
  }
  constructor(
    @Optional() public dataSource: PaginatedTreeGridDataSource<T>,
    private readonly cdRef: ChangeDetectorRef
  ) {
    super();
    if (this.dataSource === null) {
      this.dataSource = new PaginatedTreeGridDataSource<T>(
        of([]),
        undefined,
        undefined,
        undefined,
        100
      );
      this.tableDataSource = this.dataSource;
    }
  }
  ngOnInit(): void {
    this.ready.emit();
    this.displayedColumns = [
      ...this.columns
        .filter((column) => column.visible === undefined || column.visible)
        .map((column) => column.field),
    ];

    if (!this.disableColumnShift) {
      this.displayedColumns.splice(this.treeColumnIndex, 0, 'tree-control');
    }
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.columns) {
      this.displayedColumns = [
        ...this.columns
          .filter((column) => column.visible === undefined || column.visible)
          .map((column) => column.field),
      ];
    }
  }
  ngAfterViewInit(): void {
    if (this.columnDefinitions) {
      this.gridComponent.columnDefinitions = this.columnDefinitions;
    }
    if (this.headerTemplate) {
      this.gridComponent.headerTest = this.headerTemplate;
    }
    this.detectChanges();
  }

  treeToggle(node: T, event: any) {
    event?.stopPropagation();
    this.dataSource.toggleNode(node);
    if (this.dataSource.isExpanded(node)) {
      this.nodeExpanding.emit({
        data: [node],
        event: event,
      });
    }
  }

  onRowSelected(event: TxGridRowSelectArgs<T>) {
    let emittedData: T | undefined;
    if (Array.isArray(event.data)) {
      emittedData = event.data.length === 1 ? event.data[0] : undefined;
    } else {
      emittedData = event.data;
    }
    if (
      (event.event as KeyboardEvent)?.code === 'Enter' &&
      emittedData !== undefined &&
      (emittedData as any).level !== undefined &&
      !this.dataSource.expandedNodes.has(emittedData[this.primaryKey])
    ) {
      this.dataSource.toggleNode(emittedData);
    }
    this.rowSelected.emit(event);
  }

  detectChanges() {
    this.cdRef.detectChanges();
  }
  hideColumns(columnFields: Array<keyof T>) {
    this.gridComponent.hideColumns(columnFields);
    this.detectChanges();
  }
  showColumns(columnFields: Array<keyof T>) {
    if (!this.disableColumnShift) {
      columnFields.splice(this.treeColumnIndex, 0, 'tree-control' as keyof T);
    }
    this.gridComponent.showColumns(columnFields);
    this.detectChanges();
  }
  searchByTextSelect(
    event: KeyboardEvent,
    input: string,
    domSearch?: boolean,
    disableEvent?: boolean
  ) {
    if (event.code === 'Enter' || event.code === 'NumpadEnter') {
      return this.gridComponent.searchByTextSelect(input, domSearch, disableEvent);
    }
  }

  onCellEdit(event: TxGridDoubleClickArgs<T>) {
    const flattenDataList = flattenTree(this.dataSource.dataList);
    const parentNodes = this.getParentNodes(event.rowData, flattenDataList);
    this.dataSource.expandNodes(parentNodes);
    this.dataSource.triggerUpdatePageRequest();

    this.cellEdit.emit(event);
  }

  private getParentNodes(rowData: T, nodeList: T[], parents: T[] = []): T[] {
    if (this.dataSource.parentMapping && this.dataSource.idMapping) {
      const idParent = rowData[this.dataSource.parentMapping];
      if (idParent) {
        const parentNode = nodeList.find((row: any) => row[this.dataSource.idMapping] === idParent);
        if (parentNode) {
          parents = [...parents, parentNode];
          return this.getParentNodes(parentNode, nodeList, parents);
        }
      }
    }

    return parents;
  }
}
