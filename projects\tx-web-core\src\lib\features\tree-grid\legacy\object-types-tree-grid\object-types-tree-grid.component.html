<div class="dm-treegrid" *ngIf="isDataLoaded">
  <div class="dm-toolbar border-grey">
    <div class="dm-buttons">
      <ng-content select="[toolbar-buttons]"></ng-content>
    </div>
    <span [matTooltip]="'txWebCore.input.search' | translate"
      ><button
        mat-button
        (click)="isSearchActive = !isSearchActive; updateSearch(''); removeSearch()"
        [ngClass]="isSearchActive ? 'background-grey20' : ''">
        <fa-icon [icon]="['fal', 'search']" size="lg"></fa-icon></button
    ></span>
  </div>

  <div *ngIf="isSearchActive" class="no-label-input border-grey">
    <tx-input-search
      [focusInput]="true"
      [fieldWidth]="'100%'"
      [placeholderValue]="'txWebCore.input.searchByNameTagId' | translate"
      class="wide-input-search-title"
      (searchChanged)="search($event)"
      (searchOrFilterCleared)="removeSearch()"></tx-input-search>
  </div>

  <div class="otgrid-accordion">
    <ejs-treegrid
      #treeGrid
      height="100%"
      [dataSource]="data"
      idMapping="id"
      parentIdMapping="idParent"
      expandStateMapping="expanded"
      [treeColumnIndex]="0"
      (collapsed)="onNodeCollapsed($event, data)"
      (expanded)="onNodeExpanded($event, data)"
      (rowSelected)="changeObjectType($event)"
      (rowDataBound)="onRowBound($event)">
      <e-columns>
        <e-column
          field="name"
          [headerText]="'txWebCore.admins.wording.objectTypes' | translate"
          width="400">
          <ng-template #template let-data>
            <tx-column-name-template
              [hasMainParentObject]="true"
              [data]="data"
              [dataLength]="getNumberOTFormType(data.id)"
              [searchId]="searchById"
              [inputSearchValue]="inputSearchValue"></tx-column-name-template>
          </ng-template>
        </e-column>
        <e-column
          field="tags"
          [headerText]="'txWebCore.admins.columns.tags' | translate"
          width="200">
          <ng-template #template let-data>
            <ng-container *ngIf="data.tags.length > 0">
              <div *ngFor="let tag of data.tags" class="grid-tags-container">
                <span
                  [matTooltip]="tag"
                  class="grid-text-with-icon"
                  [innerHTML]="
                    tag | escapeHtml | highlightSearch : (inputSearchValue | escapeHtml)
                  "></span>
                <fa-icon
                  [matTooltip]="'txWebCore.tooltip.copy' | translate"
                  [icon]="['fal', 'copy']"
                  size="lg"
                  class="color-grey40 grid-icon-action"
                  [cdkCopyToClipboard]="tag"
                  (click)="showToast('information', 'txWebCore.generic.tagCopy', false, '', 4000)"
                  TxClickableElement></fa-icon>
              </div>
            </ng-container>
          </ng-template>
        </e-column>
      </e-columns>
    </ejs-treegrid>
  </div>
</div>
