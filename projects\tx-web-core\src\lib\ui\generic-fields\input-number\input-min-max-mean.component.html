<ng-container *ngIf="form" class="input-min-max-mean">
  <div class="input-min-max-mean__label" *ngIf="displayFormLabel">
    <span
      [matTooltip]="labelTooltip ?? label | translate"
      matTooltipClass="mat-label-tooltip mat-tooltip-multiline"
      matTooltipShowDelay="500"
      matTooltipPosition="above"
      >{{ label | translate }}</span
    >
  </div>
  <div
    class="input-min-max-mean__controls"
    [ngClass]="{ 'input-min-max-mean__controls--error': form.invalid }">
    <ng-container [ngSwitch]="fieldsType">
      <ng-container *ngSwitchCase="InputNumberFieldsType.MinMax">
        <ng-container [ngTemplateOutlet]="minMaxInput"> </ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="InputNumberFieldsType.MinMaxMean">
        <ng-container [ngTemplateOutlet]="minMaxInput"> </ng-container>
        <span class="input-min-max-mean__separator--number"> / </span>
        <tx-input-number
          class="input-min-max-mean__control input-min-max-mean__control--mean"
          [required]="required"
          [attr.disabled]="disabled"
          [label]="meanLabel"
          [formControl]="meanControl"
          [lowerBound]="lowerBound"
          [lowerBoundIncluded]="lowerBoundIncluded"
          [upperBound]="upperBound"
          [upperBoundIncluded]="upperBoundIncluded"
          (focusEvent)="changeFocus($event)">
        </tx-input-number>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="units.length > 0">
      <span class="input-min-max-mean__separator input-min-max-mean__separator--unit"> </span>
      <span class="input-min-max-mean__separator" *ngIf="units.length <= 1; else unitsList">{{
        units[0].name
      }}</span>
      <ng-template #unitsList>
        <tx-input-select
          *ngIf="units.length > 1"
          class="input-min-max-mean__control input-min-max-mean__control--unit"
          [required]="required"
          [attr.disabled]="disabled"
          [options]="units"
          [formControl]="unitControl"></tx-input-select>
      </ng-template>
    </ng-container>
  </div>
  <div>
    <mat-hint
      class="input-min-max-mean__hint"
      *ngIf="(lowerBound || upperBound) && focused"
      align="start"
      >{{ hintBound }}</mat-hint
    >
    <div class="input-min-max-mean__errors">
      <mat-error class="input-min-max-mean__error--required" *ngIf="form?.hasError('required')">{{
        'txWebCore.errorMessage.required' | translate
      }}</mat-error>
      <mat-error
        class="input-min-max-mean__error--low-bound"
        *ngIf="form?.hasError('isLowThanLowBoundValue')"
        >{{ 'txWebCore.errorMessage.minValue' | translate }}{{ lowerBound }}</mat-error
      >
      <mat-error
        class="input-min-max-mean__error--up-bound"
        *ngIf="form?.hasError('isSupThanUpperBoundValue')"
        >{{ 'txWebCore.errorMessage.maxValue' | translate }}{{ upperBound }}</mat-error
      >
      <mat-error
        class="input-min-max-mean__error--min-up"
        *ngIf="form?.hasError('minUpperThanMax')"
        >{{ 'txWebCore.errorMessage.minGreaterMax' | translate }}</mat-error
      >
      <mat-error
        class="input-min-max-mean__error--mean-out"
        *ngIf="form?.hasError('meanOutOfMinAndMax')"
        >{{ 'txWebCore.errorMessage.meanOutOfMinMax' | translate }}</mat-error
      >
    </div>
  </div>
</ng-container>

<ng-template #minMaxInput>
  <tx-input-number
    class="input-min-max-mean__control input-min-max-mean__control--min"
    [required]="required"
    [attr.disabled]="disabled"
    [label]="minLabel"
    [formControl]="minControl"
    [lowerBound]="lowerBound"
    [lowerBoundIncluded]="lowerBoundIncluded"
    [upperBound]="upperBound"
    [upperBoundIncluded]="upperBoundIncluded"
    (focusEvent)="changeFocus($event)">
  </tx-input-number>
  <span class="input-min-max-mean__separator--number">
    {{ 'txWebCore.components.inputNumberField.separatorMinMax' | translate }}
  </span>
  <tx-input-number
    class="input-min-max-mean__control input-min-max-mean__control--max"
    [required]="required"
    [attr.disabled]="disabled"
    [label]="maxLabel"
    [formControl]="maxControl"
    [lowerBound]="lowerBound"
    [lowerBoundIncluded]="lowerBoundIncluded"
    [upperBound]="upperBound"
    [upperBoundIncluded]="upperBoundIncluded"
    (focusEvent)="changeFocus($event)">
  </tx-input-number>
</ng-template>
