import { catchError } from 'rxjs/operators';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { of, Observable, Subject } from 'rxjs';
import { TxConfigService } from '@bassetti-group/tx-web-core/src/lib/data-access/config';

@Injectable({
  providedIn: 'root',
})
export class AuthenticationService {
  private readonly accessTokenStorageKey = 'access_token';
  protected subSiteApplicationName = '/TxAdministration';

  private userActionSub: Subject<void> = new Subject();

  constructor(
    private route: ActivatedRoute,
    private http: HttpClient,
    private router: Router,
    private configService: TxConfigService
  ) {}

  get userActionOccured(): Observable<void> {
    return this.userActionSub.asObservable();
  }

  notifyUserAction() {
    this.userActionSub.next();
  }

  /**
   * Stores the token for later reuse
   *
   * @param accessToken The JSON web Token to store
   */
  setSession(accessToken: string): void {
    const item = {
      value: accessToken,
      expiry: new Date().getTime() + 7200000,
    };
    localStorage.setItem(this.accessTokenStorageKey, JSON.stringify(item));
  }

  /**
   * Update the token with new expiry
   *
   * @param timer The expiration time of the token (in ms)
   */
  updateSession(timer: number): void | null {
    // eslint-disable-next-line max-len
    const token = localStorage.getItem(this.accessTokenStorageKey);
    if (!token) {
      return null;
    }
    const item = JSON.parse(token);
    item.expiry = new Date().getTime() + timer;
    localStorage.setItem(this.accessTokenStorageKey, JSON.stringify(item));
  }

  /**
   * Clears the stored token
   */
  clearSession(): void {
    localStorage.removeItem(this.accessTokenStorageKey);
  }

  /**
   * Gets the stored JSON web token
   *
   * @return string | null : The stored access token if it exists. null is returned if it is not found.
   */
  getAccessToken(): string | null {
    // eslint-disable-next-line max-len
    const token = localStorage.getItem(this.accessTokenStorageKey);
    if (!token) {
      return null;
    }
    try {
      const item = JSON.parse(token);
      return item.value as string;
    } catch (e) {
      return token;
    }
  }

  isTokenActive(): boolean {
    const token = localStorage.getItem(this.accessTokenStorageKey);
    if (!token) {
      return false;
    }

    let item;
    try {
      item = JSON.parse(token);
    } catch (e) {
      return false;
    }
    const now = new Date();

    if (now.getTime() > item.expiry) {
      // If the item is expired, delete the item from storage
      // and return null
      this.clearSession();
      return false;
    }

    return item.value !== '' && item.value !== undefined && item.value !== null;
  }

  retrieveToken(): Observable<string> {
    // eslint-disable-next-line max-len
    const nextRoute =
      this.route.snapshot.queryParams.r === undefined
        ? 'dashboard'
        : this.route.snapshot.queryParams.r;

    return new Observable((observer) => {
      this.http
        .get<string>(`${this.configService.getAuthUrl()}api/RetrieveToken`, {
          withCredentials: true,
        })
        .pipe(
          catchError((err: HttpErrorResponse) => {
            observer.error(err);
            return of('');
          })
          // eslint-disable-next-line import/no-deprecated
        )
        .subscribe((token) => {
          this.setSession(token);
          observer.next(token);
          observer.complete();
          this.router.navigateByUrl(nextRoute);
        });
    });
  }

  checkLoggedIn(): boolean {
    if (!this.isTokenActive()) {
      this.login();
      return false;
    }

    return true;
  }

  login(manualLogout: boolean = false): void {
    const urlContainsApplicationName = window.location.pathname
      .toLocaleUpperCase()
      .startsWith(this.subSiteApplicationName.toLocaleUpperCase());
    const applicationName = urlContainsApplicationName ? this.subSiteApplicationName : '';
    const nextRoute =
      (urlContainsApplicationName
        ? window.location.pathname.slice(this.subSiteApplicationName.length)
        : window.location.pathname) + window.location.search;
    const autoRedirectionParameter = manualLogout ? '&autoRedirection=false' : '';
    const url = `${this.configService.getAuthUrl()}?returnUrl=${
      window.location.origin
    }${applicationName}/auth-callback?r=${nextRoute}${autoRedirectionParameter}`;
    window.location.href = url;
  }

  logout() {
    // execute reload before clear session : delete files from cache(onBeforeonloadEvent)
    this.clearSession();
    this.login(true);
  }
}
