import { Component, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import {
  MAT_CHIPS_DEFAULT_OPTIONS,
  MatChipEditedEvent,
  MatChipInputEvent,
  MatChipsModule,
} from '@angular/material/chips';
import { TxChip, txChip } from './models';
import {
  AbstractControl,
  ControlValueAccessor,
  FormControl,
  FormsModule,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { Subject, map, startWith, takeUntil } from 'rxjs';
import { head } from 'ramda';
import { IconName } from '@fortawesome/fontawesome-svg-core';

@Component({
  selector: 'tx-chips-field',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatTooltipModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    FormsModule,
    MatChipsModule,
    FontAwesomeModule,
  ],
  templateUrl: './chips-field.component.html',
  styleUrls: ['./chips-field.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: TxChipsFieldComponent,
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: TxChipsFieldComponent,
      multi: true,
    },
    {
      provide: MAT_CHIPS_DEFAULT_OPTIONS,
      useValue: {
        separatorKeyCodes: [COMMA, ENTER],
      },
    },
  ],
})
export class TxChipsFieldComponent implements ControlValueAccessor {
  @Input({ required: true }) readMode!: boolean;
  @Input() placeHolder: string = '';
  @Input() selectable: boolean = true;
  @Input() removable: boolean = true;
  @Input() addOnBlur = true;
  @Input() multiple: boolean = true;
  @Input() maxChipsMessage = '';
  @Input() pattern!: RegExp;
  @Input() maxChipDisplay: number = 100;
  @Input() hideTitleImageThumbnail: boolean = false;
  @Input() multipleSelection: boolean = true;
  @Input() label: string = '';
  @Input() labelTooltip: string = '';
  @Input() hintLabel: string = '';
  @Input() required: boolean = false;
  //@Input() disabled: boolean = false;
  @Input() editable: boolean = false;
  @Input() actionIconToolTip: string = '';
  @Input() actionIcon: string = '';
  @Input() chipsIcon: IconName | undefined;
  @Input() displayExtenderChip: boolean = false;
  @Output() chipClick = new EventEmitter<TxChip>();
  @Output() allChipsLink = new EventEmitter<any>();
  @Output() actionIconClick = new EventEmitter();

  control: FormControl<string[] | null> | undefined;
  chips: TxChip[] = [];
  _destroying$ = new Subject<void>();
  _onTouched: any;
  checkedChips: any;
  disabled = false;

  get canAddChips() {
    return (this.multiple || this.chips.length === 0) && this.chips.length < this.maxChipDisplay;
  }

  constructor(private cd: ChangeDetectorRef) {}

  writeValue(chips: TxChip[]): void {
    this.chips = this.prepareChips(chips);
    const chipsNames = this.chips.map((chips) => chips.name);
    if (this.control) {
      this.control.setValue(chipsNames);
    } else {
      this.control = new FormControl(chipsNames);
    }
  }

  registerOnChange(fn: () => void): void {
    this.control?.valueChanges
      .pipe(
        takeUntil(this._destroying$),
        startWith(this.control?.value),
        map(() => this.chips)
      )
      .subscribe(fn);
  }

  registerOnTouched(fn: any): void {
    this._onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    isDisabled ? this.control?.disable() : this.control?.enable();
    this.disabled = isDisabled;
  }

  validate(control: AbstractControl): ValidationErrors | null {
    const requiredError = this.required ? Validators.required(control) : null;
    this.control?.setErrors(requiredError);
    this.cd.detectChanges();
    return requiredError;
  }

  remove(index: number): void {
    this.chips = this.chips.filter((chip, i) => i !== index);
    this.control?.setValue(this.chips.map((chip) => chip.name));
  }

  edit(event: MatChipEditedEvent, index: number) {
    this.chips = this.chips.map((chip, i) => {
      if (i === index) {
        return { ...chip, name: event.value };
      }
      return chip;
    });
    this.control?.setValue(this.chips.map((chip) => chip.name));
  }

  onActionIconClick() {
    this.actionIconClick.emit(this.chips);
  }

  onChipClick(chip: TxChip) {
    this.changeSelection(chip);
    this.chipClick.emit(chip);
  }

  onAllChipsLink() {
    this.allChipsLink.emit({
      name: this.label,
      values: this.control?.value ?? [],
    });
  }

  onAdd(event: MatChipInputEvent) {
    const value = event.value.trim();
    if (value && this.canAddChips) {
      this.add(value);
    }
    event.chipInput.clear();
  }

  private changeSelection(chip: TxChip): void {
    if (chip.selectable && !this.disabled) {
      chip.selected ? this.deselectChip(chip) : this.selectChip(chip);
      this.control?.setValue(this.chips.map((chip) => chip.name));
    }
  }

  private prepareChips(chips: TxChip[]): TxChip[] {
    return !this.multiple ? [head(chips) ?? []].flat() : chips;
  }

  private add(value: string) {
    let valid = this.pattern ? this.pattern.test(value) : true;
    if (valid) {
      this.chips = [
        ...this.chips,
        txChip({
          type: 'standard',
          name: value,
          removable: this.removable,
          selectable: this.selectable,
          selected: false,
          icon: this.chipsIcon ? ['fal', this.chipsIcon] : undefined,
        }),
      ];
      this.control?.setValue(this.chips.map((chip) => chip.name));
    }
  }
  private deselectChip(chip: TxChip): void {
    chip.selected = false;
  }
  private selectChip(chip: TxChip): void {
    if (!this.multipleSelection) {
      this.chips = this.chips.map((chip) => {
        chip.selected = false;
        return chip;
      });
    }
    chip.selected = true;
  }
}
