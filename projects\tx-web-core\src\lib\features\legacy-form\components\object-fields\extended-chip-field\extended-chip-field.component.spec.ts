import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxExtendedChipFieldComponent } from './extended-chip-field.component';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';

describe('TxExtendedChipFieldComponent', () => {
  let component: TxExtendedChipFieldComponent;
  let fixture: ComponentFixture<TxExtendedChipFieldComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TxExtendedChipFieldComponent],
      imports: [MatDividerModule, MatChipsModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxExtendedChipFieldComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
