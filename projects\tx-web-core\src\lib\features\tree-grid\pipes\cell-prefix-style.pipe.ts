import { Pipe, PipeTransform } from '@angular/core';
@Pipe({
  standalone: true,
  name: 'cellPrefixStyle',
})
export class CellPrefixStylePipe implements PipeTransform {
  transform(
    data: { [key: string]: string | number },
    childMapping: string
  ): { [key: string]: string } {
    const noChildMapping = !(data[childMapping] || data.isParent) ? 1 : 0;
    const level = data.level as number;

    return {
      'min-width': 40 * (level + noChildMapping) + 'px',
    };
  }
}
