import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RightPaneSettings } from 'src/app/core/right-pane/right-pane.models';
import { RightPaneRef } from 'src/app/core/right-pane/right-pane.ref';
import { rightPaneServiceMock } from 'src/app/core/right-pane/right-pane.service.mock';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { SessionService } from 'src/app/core/services/session/session.service';
import { MockComponent, MockDirective, MockProvider } from 'ng-mocks';
import { sessionServiceMock } from 'src/app/core/services/session/session.service.mock';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { TRANSLATIONS_MOCK } from 'src/app/core/translation/translations.mock';
import { CoreModelExportFormComponent } from './core-model-export-form.component';
import { CoreModelExportHistory } from '../../models/core-model-export-history.model';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/material/form-field';
import { MatSelect } from '@angular/material/select';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { MatOption } from '@angular/material/core';
import { MatDivider } from '@angular/material/divider';

export const rightPaneSettingsMock: RightPaneSettings<CoreModelExportHistory> = {
  data: {
    version: '0.0.1',
    name: 'core model test',
    explanation: 'description core model historic',
    comment: 'comment core model historic',
    date: new Date(),
    username: 'Joe Doe',
  },
};

export const rightPaneRefMock: RightPaneRef = rightPaneServiceMock.showNewPane(
  CoreModelExportFormComponent,
  rightPaneSettingsMock
);
describe('CoreModelsExportFormComponent', () => {
  let component: CoreModelExportFormComponent;
  let fixture: ComponentFixture<CoreModelExportFormComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        CoreModelExportFormComponent,
        MockComponent(MatFormField),
        MockComponent(FaIconComponent),
        MockComponent(MatSelect),
        MockComponent(MatOption),
        MockComponent(MatDivider),
        MockDirective(MatError),
        MockDirective(MatLabel),
      ],
      imports: [TranslateTestingModule.withTranslations(TRANSLATIONS_MOCK), ReactiveFormsModule],
      providers: [
        FormBuilder,
        MockProvider(RightPaneRef, rightPaneRefMock, 'useValue'),
        MockProvider(RightPaneSettings, rightPaneSettingsMock, 'useValue'),
        MockProvider(SessionService, sessionServiceMock, 'useValue'),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CoreModelExportFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
