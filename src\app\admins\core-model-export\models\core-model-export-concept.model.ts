import { TxObjectType, TxConcept, ConceptType } from '@bassetti-group/tx-web-core';

export interface CoreModelExportConcept extends TxConcept {
  type: ConceptType;
  objectType?: TxObjectType;
  metaDataList: CoreModelExportConceptMetaData[];
  errors: string[];
  icon?: number;
}
export interface FlatCoreModelExportConcept {
  id: number;
  tags: string;
  name: string;
  explanation?: string;
  metaDataList: string;
  type: ConceptType;
  objectType?: string;
  errors: string;
  icon?: number;
  objectTypeIcon?: number;
}
export interface CoreModelExportConceptMetaData {
  key: string;
  header: string;
  value: string | TxConcept;
}

export interface MissingConfig {
  key: CoreModelExportConceptErrorKeys;
  missingObject?: MissingObject | null;
}

export interface MissingObject {
  tags: string[];
  id: number;
}

export enum CoreModelExportConceptErrorKeys {
  MissingObjectTypeTag = 'MissingObjectTypeTag',
  MissingModelTag = 'MissingModelTag',
  MissingAttributeTag = 'MissingAttributeTag',
  MissingUnitTag = 'MissingUnitTag',
  MissingObjectTypeSourceTag = 'MissingObjectTypeSourceTag',
  MissingObjectTypeDestinationTag = 'MissingObjectTypeDestinationTag',
  MissingFileTypeTag = 'MissingFileTypeTag',
  MissingTableTypeTag = 'MissingTableTypeTag',
  MissingConditionnalFilterAttributeTag = 'MissingConditionnalFilterAttributeTag',
  MissingAssociativeClassLinkTag = 'MissingAssociativeClassLinkTag',
  MissingAssociativeClassObjectTypeTag = 'MissingAssociativeClassObjectTypeTag',
  MissingParentFilterObjectTag = 'MissingParentFilterObjectTag',
  MissingAttributeSetTag = 'MissingAttributeSetTag',
  MissingSeriesTypeUnitTag = 'MissingSeriesTypeUnitTag',
  MissingEquivalence = 'MissingEquivalence',
  MissingEquivalenceSetTag = 'MissingEquivalenceSetTag',
  MissingObjectTag = 'MissingObjectTag',
  MissingExplanation = 'MissingExplanation',
}

export enum CoreModelExportConceptFieldEnum {
  Id = 'id',
  Tags = 'tags',
  Explanation = 'explanation',
  MetaDataList = 'metaDataList',
  Name = 'name',
  ObjectType = 'objectType',
  Errors = 'errors',
  View = 'view',
  Type = 'type',
}
