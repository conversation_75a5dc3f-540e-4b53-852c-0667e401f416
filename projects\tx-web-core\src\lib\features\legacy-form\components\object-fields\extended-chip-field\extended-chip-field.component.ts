import { Component, HostListener, Input, OnInit } from '@angular/core';
import { TxChip } from '../../../models/formFields/setting-model';

@Component({
  selector: 'tx-extended-chip-field',
  templateUrl: './extended-chip-field.component.html',
  styleUrls: ['./extended-chip-field.component.scss'],
})
export class TxExtendedChipFieldComponent implements OnInit {
  @Input() label!: string;
  @Input() chips!: TxChip[];

  constructor() {}

  ngOnInit(): void {}
}
