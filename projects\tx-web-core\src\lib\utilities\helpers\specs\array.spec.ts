import { TestBed } from '@angular/core/testing';
import { ArrayUtils } from '../array.util';

describe('ArrayUtils', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [ArrayUtils],
    });
  });

  it('should compare array differences', () => {
    const baseArray = ['a', 'b', 'c', 'd'];
    const compareArray = ['c', 'd', 'e', 'f'];
    const result = ArrayUtils.getItemsDifferenceBetweenArrays(baseArray, compareArray);
    expect(result.addedItems).toEqual(['e', 'f']);
    expect(result.removedItems).toEqual(['a', 'b']);
  });

  it('should add and remove elements from array', () => {
    const array = ['a', 'b', 'c', 'd'];
    const add = ['e', 'f'];
    const remove = ['b', 'c'];
    const result = ArrayUtils.addAndRemoveItems(array, add, remove);
    expect(result).toEqual(['a', 'd', 'e', 'f']);
  });
});
