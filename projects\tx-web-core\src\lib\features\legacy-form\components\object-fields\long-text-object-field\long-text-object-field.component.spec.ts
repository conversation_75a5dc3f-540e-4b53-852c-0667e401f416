import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxLongTextObjectFieldComponent } from './long-text-object-field.component';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { AttributesMockService } from '../../../testing.mock';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatChipsModule } from '@angular/material/chips';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { LegacyTxDataString } from '../../../services/structure/models/data';
import { MockComponent } from 'ng-mocks';
import { TxInputTextFieldComponent } from '../../generic-fields/input-text-field/input-text-field.component';

describe('TxLongTextObjectFieldComponent', () => {
  let component: TxLongTextObjectFieldComponent;
  let fixture: ComponentFixture<TxLongTextObjectFieldComponent>;
  let element;
  let attributesService: LegacyTxAttributesService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxLongTextObjectFieldComponent, MockComponent(TxInputTextFieldComponent)],
      providers: [{ provide: LegacyTxAttributesService, useClass: AttributesMockService }],
      imports: [
        MatChipsModule,
        MatTooltipModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatInputModule,
        BrowserAnimationsModule,
      ],
    }).compileComponents();
    attributesService = TestBed.inject(LegacyTxAttributesService);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxLongTextObjectFieldComponent);
    component = fixture.componentInstance;
    element = fixture.nativeElement;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('testing initialized component...', () => {
    it('input type should be text', () => {
      expect(component.inputType).toBe('text');
    });

    it('inTextArea should be true', () => {
      expect(component.inTextArea).toBeTruthy();
    });

    it('default line number should be 5', () => {
      expect(component.numberOfLine).toBe(5);
    });

    it('textAreaHeight be be nulberOfLine * 14', () => {
      expect(component.textAreaHeight).toBe(70);
    });
  });

  describe('testing initValue...', () => {
    it('should update value from data', () => {
      const data = new LegacyTxDataString(0, 0, 'test');
      component.data = data;
      component.initValue();
      expect(component.value).toBe('test');
    });
  });

  describe('testing getData...', () => {
    it('should return data from control value', () => {
      component.idObject = 1;
      component.idAttribute = 2;
      component.control.setValue('test');
      expect(component.getData()).toEqual({
        idObject: 1,
        idAttribute: 2,
        value: 'test',
        action: 1,
      });
    });
  });
});
