import { LegacyTxObjectType } from '../services/structure/models/object-type';
import { TxStep } from './step.model';

export class TxObjectConfiguration {
  steps: TxStep[];
  objectType!: LegacyTxObjectType;

  constructor(public tag: string) {
    this.steps = [];
  }

  assign(objConf?: Partial<TxObjectConfiguration>) {
    this.tag = objConf?.tag as string;
    this.steps = objConf?.steps as TxStep[];
    this.objectType = objConf?.objectType as LegacyTxObjectType;
  }
}
