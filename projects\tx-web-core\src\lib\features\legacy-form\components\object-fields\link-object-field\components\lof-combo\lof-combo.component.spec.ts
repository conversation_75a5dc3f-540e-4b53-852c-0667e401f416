import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxLofComboComponent } from './lof-combo.component';
import { LegacyTxObjectsService } from '../../../../../services/structure/services/objects.service';
import { MockComponent, MockService } from 'ng-mocks';
import { TxLinkObjectFieldService } from '../../services/link-object-field.service';
import { TxDropdownListFieldComponent } from '../../../../generic-fields/dropdown-list-field/dropdown-list-field.component';

describe('LofComboComponent', () => {
  let component: TxLofComboComponent;
  let fixture: ComponentFixture<TxLofComboComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxLofComboComponent, MockComponent(TxDropdownListFieldComponent)],
      providers: [
        {
          provide: LegacyTxObjectsService,
          useValue: MockService(LegacyTxObjectsService),
        },
        {
          provide: TxLinkObjectFieldService,
          useValue: MockService(TxLinkObjectFieldService),
        },
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxLofComboComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
