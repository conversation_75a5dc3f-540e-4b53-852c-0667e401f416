import { inject } from '@angular/core';
import { TxObject, TxObjectType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TxObjectsService } from '@bassetti-group/tx-web-core/src/lib/data-access/structure';
import { omit, uniq } from 'ramda';
import {
  BehaviorSubject,
  Observable,
  combineLatest,
  filter,
  finalize,
  first,
  forkJoin,
  map,
  mergeMap,
  of,
  switchMap,
  tap,
} from 'rxjs';
import {
  AsyncTreeGridTXDataSource,
  AsyncTxTreeGrid,
} from '../data-sources/async-tree-tx-data-source';
import { TxTreeGrid } from '../tree-grid.models';
import { flattenTree, generateTree } from '../tree-utils';
import { TxTreeExpandState } from '../tree-grid.interface';
export interface SelectionEvent {
  selectionValues: number[];
  deselect: boolean;
}
export class ObjectTreeGridDataSource extends AsyncTreeGridTXDataSource<TxObject> {
  private readonly objectsService: TxObjectsService;
  private readonly selectionChangesSubject = new BehaviorSubject<any>(null);
  objectType?: TxObjectType;
  idParentObjectFiltering?: number;
  multipleSelection: boolean = true;
  readonly txObjectParentKey: keyof TxObject = 'idObjectParent';

  get changes(): Observable<any> {
    return this.selectionChangesSubject.asObservable();
  }
  constructor(endPoint = of([]), primaryKey: keyof TxTreeGrid<TxObject> | undefined = undefined) {
    super(endPoint, primaryKey);
    this.objectsService = inject(TxObjectsService);
    this.loadChildrenFn = this.getObjectsFromParent;
    this.createTreeGridObjects = this.formatTreeObject;
    this.disableSelectionUpdate = true;
    this.expandState = TxTreeExpandState.Collapsed;
  }
  override addData(data?: TxTreeGrid<TxObject>[] | Observable<TxTreeGrid<TxObject>[]>) {
    data ??= this.loadTreeData();
    super.addData(data);
    this.expandedNodes.clear();
  }
  override isChildLoaded(node: TxObject & { children: TxObject[] }): boolean {
    if (!node.children?.length) {
      return false;
    }
    const nodeKey = node[this.primaryKey as keyof TxObject];
    const childKey = node.children[0][this.primaryKey as keyof TxObject];

    const nodeKeyNum = Number(nodeKey);
    const childKeyNum = Number(childKey);
    if (isNaN(nodeKeyNum) || isNaN(childKeyNum)) {
      return false;
    }
    return !(node.children.length === 1 && childKeyNum - 0.1 === nodeKeyNum);
  }
  getObjectsFromParent(idOT: number, idParent: number | string): Observable<TxObject[]> {
    return this.objectsService
      .getObjectsFromParent(idOT, idParent as number)
      .pipe(map((res) => res.objects));
  }
  loadSearchIdData(id: number) {
    if (this.idObjectType) {
      this.getObjectsFromParent(this.idObjectType, id).subscribe((objects) => {
        let data = objects.map((object) => Object(omit([this.txObjectParentKey], object)));
        data = this.formatTreeObject(data);
        data = generateTree(
          data,
          this.idMapping as keyof TxTreeGrid<TxObject>,
          this.txObjectParentKey
        );
        super.addData(data);
      });
    }
  }
  loadSearchStringData(searchText: string) {
    this.changeLoadingState(true);
    this.expandedNodes.clear();
    if (this.idObjectType) {
      forkJoin([
        this.objectsService.getChildrenObjectsByIds(
          this.getSelectedElements() as number[],
          this.idObjectType
        ),
        this.objectsService.searchObjects(searchText, this.idObjectType, undefined, false),
      ])
        .pipe(map((val) => this.removeDuplicates(val.flat(), this.primaryKey as keyof TxObject)))
        .subscribe({
          next: (objects) => {
            let data = objects.map((object) => Object(omit([this.txObjectParentKey], object)));
            data = this.formatTreeObject(data, true);
            super.addData(data);
          },
          complete: () => {
            this.changeLoadingState(false);
          },
        });
    }
  }
  loadSearchInputData(data: TxObject[], searchText: string) {
    if (!data) {
      return;
    }
    //set all data sources first
    this.prepareAddData(data);

    const selectedElementIds = this.getSelectedElements();
    let matchedData: TxTreeGrid<TxObject>[] = [];

    matchedData = this.formatTreeObject(
      data
        .filter((obj) => selectedElementIds.includes(obj.id))
        .map((obj) => {
          return { ...obj, idObjectParent: 0 };
        }),
      true
    );

    function searchTreeLocally(dataList: AsyncTxTreeGrid<TxObject>[]): void {
      dataList.forEach((item) => {
        if (
          !item.txObject.isFolder &&
          item.name.toString().toLowerCase().includes(searchText.toLowerCase())
        ) {
          if (!selectedElementIds.includes(item.id)) {
            matchedData.push({
              ...item,
              idParent: undefined,
            });
          }
        } else if (item.children?.length) {
          searchTreeLocally(item.children);
        }
      });
    }
    searchTreeLocally(this.dataList);
    super.addData(matchedData);
  }
  loadSelectedData() {
    this.changeLoadingState(true);
    if (this.idObjectType) {
      this.objectsService
        .getChildrenObjectsByIds(this.getSelectedElements() as number[], this.idObjectType)
        .subscribe({
          next: (objects) => {
            let data = objects.map((object) => Object(omit([this.txObjectParentKey], object)));
            data = this.formatTreeObject(data, true);
            super.addData(data);
          },
          complete: () => {
            this.changeLoadingState(false);
          },
        });
    }
  }

  prepareAddData(data: TxObject[]) {
    this.otService
      .isReady()
      .pipe(first())
      .subscribe(() => {
        this.addData(this.formatTreeObject(data, true));
      });
  }

  loadChildrenSelectAll(
    callback?: (success: boolean, passCheck?: boolean, processedData?: TxObject[]) => void
  ) {
    if (this.multipleSelection) {
      this.changeLoadingState(true);
      this.objectsService.getAllObjectsChildren(this.idObjectType ?? 0).subscribe({
        next: (children) => {
          this.select(...children.objects.map((obj) => obj.id));
          if (callback) {
            callback(true, false, children.objects);
          }
        },
        complete: () => this.changeLoadingState(false),
      });
    }
  }

  loadRootToParentIds(selectedItemId: number) {
    return this.objectsService.getRootToParentIds().pipe(
      map((ids) => {
        const parentToRootIdArr = ids;
        parentToRootIdArr.push(selectedItemId);
        return parentToRootIdArr;
      }),
      mergeMap((parentToRootIdArr) => this.processNodeIdsSequentially(parentToRootIdArr))
    );
  }
  childSelection(
    selectionIds: number[],
    deselect?: boolean,
    complete?: (checked: boolean, passCheck?: boolean, processedData?: TxObject[]) => void
  ) {
    this.changeLoadingState(true);
    if (!this.idObjectType) {
      throw new Error('idObjectType is not set');
    }
    const { parentIds, childrenIds } = this.separateParentChildren(selectionIds);
    if (deselect) {
      this.selection.deselect(...childrenIds);
    } else if (childrenIds.length > 0) {
      this.select(...(this.multipleSelection ? childrenIds : [childrenIds[0]]));
    }
    this.objectsService.getObjectsFromParent(this.idObjectType, 0, true, true).subscribe({
      next: (objects) => {
        const recursiveChildrenIds = parentIds.flatMap((parentId) =>
          this.getRecursiveChildrenId(objects.objects, parentId)
        );

        if (deselect) {
          this.selection.deselect(...recursiveChildrenIds);
        } else {
          this.select(
            ...(this.multipleSelection ? recursiveChildrenIds : [recursiveChildrenIds[0]])
          );
        }
        const selectedObjects = this.getObjectsFromIds(
          objects.objects,
          this.getSelectedElements() as number[]
        );
        if (complete) {
          complete(!deselect, false, selectedObjects);
        }
      },
      complete: () => this.changeLoadingState(false),
    });
  }

  loadAllNodeData() {
    this.changeLoadingState(true);
    return this.objectsService
      .getObjectsFromParent(this.objectType?.id as number, 0, true)
      .pipe(map((object) => object.objects))
      .subscribe({
        next: (value) => {
          const data = this.formatTreeObject(value, true);
          super.addData(data);
          this.expandNodes(this.dataList);
          this.triggerUpdatePageRequest();
          this.changeLoadingState(false);
        },
      });
  }
  loadAllLocalNodeData() {
    this.expandNodes(this.dataList);
    this.triggerUpdatePageRequest();
  }
  separateParentChildren(ids: number[]) {
    const nonFolderParents: number[] = [];
    const parents = new Set(
      flattenTree(this.dataList)
        .filter((child) => {
          const parent =
            child?.txObject?.isFolder ??
            child.isParent ??
            child?.txObject?.isParent ??
            child.children;

          const nonFolderParent = child?.txObject?.isFolder;

          if (parent && !nonFolderParent && ids.includes(child.id)) {
            nonFolderParents.push(child.id);
          }
          return parent;
        })
        .map((child) => child.id)
    );
    let childrenIds: number[] = [];
    const parentIds: number[] = [];
    ids.forEach((id) => {
      if (parents.has(id)) {
        parentIds.push(id);
      } else {
        childrenIds.push(id);
      }
    });
    childrenIds = uniq([...childrenIds, ...nonFolderParents]);
    return { parentIds, childrenIds };
  }

  private getObjectsFromIds(items: TxObject[], ids: number[]): TxObject[] {
    // Build a lookup map for O(1) access to items by ID
    const itemMap = new Map<number, TxObject>();
    items.forEach((item) => itemMap.set(item.id, item));

    // Map IDs to their corresponding objects
    const result: TxObject[] = [];
    ids.forEach((id) => {
      const item = itemMap.get(id);
      if (item) {
        result.push(item);
      }
    });

    return result;
  }

  private getRecursiveChildrenId(items: TxObject[], parentId: number): number[] {
    const itemMap = new Map<number, TxObject>();
    const childrenMap = new Map<number, TxObject[]>();

    items.forEach((item) => {
      itemMap.set(item.id, item);
      if (item.idObjectParent !== undefined) {
        if (!childrenMap.has(item.idObjectParent)) {
          childrenMap.set(item.idObjectParent, []);
        }
        childrenMap.get(item.idObjectParent)!.push(item);
      }
    });

    function collectFileIds(currentId: number): number[] {
      const current = itemMap.get(currentId);
      if (!current) return [];

      const directChildren = childrenMap.get(currentId) || [];
      const result: number[] = [];

      for (const child of directChildren) {
        if (!child.isFolder) {
          result.push(child.id);
        } else {
          const childFileIds = collectFileIds(child.id);
          result.push(...childFileIds);
        }
      }

      return result;
    }

    return collectFileIds(parentId);
  }
  private loadTreeData(): Observable<TxTreeGrid<TxObject>[]> {
    this.changeLoadingState(true);
    if (!this.objectType && !this.idObjectType) {
      throw new Error('idObjectType or idObjectType are mandatory');
    }

    if (!this.objectType && this.idObjectType) {
      return combineLatest([of(this.idObjectType), this.otService.isReady()]).pipe(
        switchMap(([idObjectType]) => of(this.otService.getByID(idObjectType))),
        map((objectType) => objectType),
        filter((objectType: TxObjectType | undefined): objectType is TxObjectType => {
          const result = objectType !== undefined;
          if (!result) {
            throw new Error(
              'idObjectType supplied to ObjectTreeGridComponent does not correspond to any ObjectType'
            );
          }
          return objectType !== undefined;
        }),
        tap((objectType) => {
          this.objectType = objectType;
        }),
        switchMap(() =>
          this.loadData(this.objectType?.id as number).pipe(
            map((objects) => this.formatTreeObject(objects))
          )
        ),
        finalize(() => {
          this.changeLoadingState(false);
        })
      );
    } else {
      return this.loadData(this.objectType?.id as number).pipe(
        map((objects) => this.formatTreeObject(objects)),
        finalize(() => {
          this.changeLoadingState(false);
        })
      );
    }
  }
  private loadData(idObjectType: number): Observable<TxObject[]> {
    if (this.idParentObjectFiltering) {
      return this.getObjectsFromParent(idObjectType, this.idParentObjectFiltering).pipe(
        map((res) => res.map((object) => Object(omit([this.txObjectParentKey], object))))
      );
    }
    return this.loadRoot(idObjectType);
  }
  private loadRoot(idObjectType: number): Observable<TxObject[]> {
    return this.getObjectsFromParent(idObjectType, 0);
  }

  private formatTreeObject(objects: TxObject[], isInputData?: boolean): TxTreeGrid<TxObject>[] {
    if (objects.length > 0 && this.idObjectType !== objects[0].idObjectType) {
      return [];
    }
    let res: TxTreeGrid<TxObject>[];
    return objects
      .map((obj) => {
        res = [];
        res.push({
          id: obj.id,
          idParent: obj.idObjectParent === 0 ? undefined : obj.idObjectParent,
          icon: this.getIconPath(obj),
          name: obj.name,
          expanded: false,
          objectType: this.objectType,
          txObject: { ...obj },
        });
        if (!isInputData && (obj.isParent || obj.isFolder)) {
          // add a fake child to have plus
          res.push({
            id: obj.id + 0.1,
            idParent: obj.id,
            icon: this.objectsService.getIconPath(0),
            name: obj.name,
            expanded: false,
            objectType: this.objectType,
            txObject: {} as TxObject,
          });
        }
        return res;
      })
      .reduce((acc, val) => acc.concat(val), []);
  }

  private removeDuplicates(array: TxObject[], key: keyof TxObject) {
    const uniqueItems = new Map();
    array.forEach((item) => uniqueItems.set(item[key], item));
    return Array.from(uniqueItems.values());
  }
  private getIconPath(object: TxObject): string {
    try {
      if (object.isFolder) {
        return this.objectsService.getIconPath(0);
      } else if (this.objectType?.icon === undefined) {
        return this.otService.getIconPath(object.idObjectType);
      }
      return this.objectsService.getIconPath(this.objectType.icon);
    } catch (err) {
      return this.objectsService.getIconPath(-1);
    }
  }
}
