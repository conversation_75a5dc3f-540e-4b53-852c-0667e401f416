import { AfterViewChecked, AfterViewInit, Component, Input, ViewChild } from '@angular/core';
import { MatButtonToggle } from '@angular/material/button-toggle';
import { LegacyTxDataBoolean } from '../../../services/structure/models/data';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { of } from 'rxjs';
import { max } from 'rxjs/operators';
import { TxObjectFieldComponent } from '../_system/object-field/object-field.component';

@Component({
  selector: 'tx-boolean-object-field',
  templateUrl: './boolean-field.component.html',
  styleUrls: ['./boolean-object-field.component.scss'],
})
export class TxBooleanObjectFieldComponent
  extends TxObjectFieldComponent
  implements AfterViewChecked
{
  @Input() trueCaption = 'true';
  @Input() falseCaption = 'false';

  @ViewChild('leftBtn') leftBtn!: MatButtonToggle;
  @ViewChild('rightBtn') rightBtn!: MatButtonToggle;

  declare data: LegacyTxDataBoolean;

  minWidthComputed = false;

  constructor(public attributeService: LegacyTxAttributesService) {
    super(attributeService);
  }

  minWidthButtonToggle!: number;
  widthBtnLeft!: number;
  widthBtnRight!: number;

  get isTrueValue() {
    return this.control.value ? this.control.value[0] : false;
  }

  get isFalseValue() {
    return this.control.value ? !this.control.value[0] : false;
  }

  ngAfterViewChecked(): void {
    this.computeMinWidth();
  }

  computeMinWidth(): void {
    if (!this.minWidthComputed && !this.readMode) {
      of(
        this.rightBtn._buttonElement.nativeElement.offsetWidth,
        this.leftBtn._buttonElement.nativeElement.offsetWidth
      )
        .pipe(max())
        .subscribe((x) => (this.minWidthButtonToggle = x));
      if (this.minWidthButtonToggle) {
        this.minWidthComputed = true;
      }
    }
    // of(50, 100).pipe(
    //   max(),
    // )
    // .subscribe(x => this.widthButtonToggle = x);
  }

  valueIsEmpty(): boolean {
    if (!this.control.value) {
      return true;
    }
    return this.control.value.length < 1 || this.control.value[0] === undefined;
  }

  initPropertiesFromField() {
    super.initPropertiesFromField();
    // if (this.trueCaption === undefined){
    //   this.trueCaption = this.field.trueCaption;
    // }

    // if (this.falseCaption === undefined){
    //   this.falseCaption = this.field.falseCaption;
    // }
  }

  initValue() {
    if (this.data) {
      this.value = this.data.value;
    }
  }

  initProperties(): void {
    super.initProperties();
  }

  // initFormControl() {
  //   this.control = new FormControl('');
  //   this.form.addControl(this.field.id.toString(), this.control);
  // }

  setData(data: LegacyTxDataBoolean) {
    this.control.setValue([data.value]);
  }

  getData(): LegacyTxDataBoolean {
    return new LegacyTxDataBoolean(
      this.idObject,
      this.idAttribute,
      this.control.value ? this.control.value[0] : null
    );
  }

  toggleChange(event: any): void {
    const toggle = event.source;
    if (toggle) {
      const group = toggle.buttonToggleGroup;
      if (event.value.some((item: any) => item === toggle.value)) {
        group.value = [toggle.value];
        this.control.setValue(group.value);
        this.control.updateValueAndValidity();
      }
    }
  }
}
