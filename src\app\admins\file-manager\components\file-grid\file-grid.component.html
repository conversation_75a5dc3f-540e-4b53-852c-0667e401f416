<div class="fm-container-right">
  <div class="file-container-text border-grey">
    <span [matTooltip]="getAddTooltip() | translate" class="fm-button-left">
      <button
        mat-button
        id="fm-add-file-button"
        [disabled]="!canAddFile()"
        (click)="addFileInGrid()">
        <div class="main-button-icon">
          <fa-icon [icon]="['fal', 'upload']" size="lg"></fa-icon>
          {{ 'button.upload' | translate }}
        </div>
      </button>
    </span>
    <span [matTooltip]="getCreateFolderTooltip() | translate" class="fm-button-left">
      <button
        mat-button
        id="fm-create-folder-button"
        [disabled]="!canCreateFolderInGrid()"
        (click)="createFolder()">
        <div class="main-button-icon">
          <fa-icon [icon]="['fal', 'folder-plus']" size="lg"></fa-icon>
          {{ 'admins.resources.newFolder' | translate }}
        </div>
      </button>
    </span>
    <span [matTooltip]="getDeleteTooltip() | translate" class="fm-button-left">
      <button
        mat-button
        id="fm-delete-file-button"
        [disabled]="!canDeleteFileInGrid()"
        (click)="deleteFileInGrid()">
        <div class="main-button-icon">
          <fa-icon [icon]="['fal', 'trash-alt']" size="lg"></fa-icon>
          {{ 'button.delete' | translate }}
        </div>
      </button>
    </span>
    <span [matTooltip]="getDownloadTooltip() | translate" class="fm-button-left">
      <button
        mat-button
        id="fm-download-file-button"
        [disabled]="!canDownloadFileInGrid()"
        (click)="download()">
        <div class="main-button-icon">
          <fa-icon [icon]="['fal', 'download']" size="lg"></fa-icon>
          {{ 'button.download' | translate }}
        </div>
      </button>
    </span>
    <span [matTooltip]="getRenameTooltip() | translate" class="fm-button-left">
      <button
        mat-button
        id="fm-rename-file-button"
        [disabled]="!canRenameFileInGrid()"
        (click)="rename()">
        <div class="main-button-icon">
          <fa-icon [icon]="['fal', 'pencil-alt']" size="lg"></fa-icon>
          {{ 'admins.resources.rename' | translate }}
        </div>
      </button>
    </span>
    <span [matTooltip]="getOpenHistoryTooltip() | translate" class="fm-button-left">
      <button
        mat-button
        id="fm-open-history-button"
        [disabled]="!canOpenHistoryInGrid()"
        (click)="openHistoryInGrid()">
        <div class="main-button-icon">
          <fa-icon [icon]="['fal', 'history']" size="lg"></fa-icon>
          {{ 'admins.resources.history' | translate }}
        </div>
      </button>
    </span>
    <span [matTooltip]="getOpenEditorTooltip() | translate" class="fm-button-left">
      <button
        mat-button
        id="fm-open-editor-button"
        [disabled]="!canOpenFileInEditor()"
        (click)="openFileInEditor()">
        <div class="main-button-icon">
          <fa-icon [icon]="['fal', 'code']" size="lg"></fa-icon>
          {{ 'admins.resources.openFileInEditor' | translate }}
        </div>
      </button>
    </span>
  </div>
  <div
    *ngIf="!isLangLoading"
    class="fm-grid-container"
    txDragDrop
    (fileDropped)="fileDropGrid($event)"
    (showIndicator)="showIndicator($event)"
    (hideIndicator)="hideIndicator($event)"
    (dragover)="containsFiles($event)">
    <div
      @insertDrop
      *ngIf="isDropzoneHovered"
      class="dbr-dragzone background border-grey"
      txDragDrop
      (fileDropped)="handleFileInputGrid($event)"
      (drop)="isDropzoneHovered = false"
      (dragleave)="isDropzoneHovered = false">
      <div class="dbr-drag">
        <span class="accent dbr-file">
          <fa-icon [icon]="['fal', 'file-upload']" size="lg"></fa-icon>
        </span>
        <div class="dbr-file-message">{{ 'admins.resources.dropFileHere' | translate }}</div>
        <div class="dbr-file-extension">
          {{
            'admins.resources.supportedFileFormat' | translate : { format: getSupportedFormat() }
          }}
        </div>
      </div>
    </div>

    <div
      *ngIf="!filesInFolder || (filesInFolder && filesInFolder.length === 0)"
      class="fm-no-record-placeholder background border-grey"
      (contextmenu)="onContextMenu($event)">
      <tx-no-record class="tx-no-record"> </tx-no-record>
    </div>

    <div *ngIf="isGridLoading" @insertTrigger class="fm-spinner-loadingGrid border-grey">
      <app-loader></app-loader>
    </div>
    <tx-grid
      #filesGrid
      id="filesGrid"
      [data]="filesInFolder"
      [allowRowDragAndDrop]="true"
      [columns]="txGridColumns"
      primaryKey="name"
      [allowEditing]="true"
      [rightClickSection]="true"
      [enableMultiSelect]="true"
      [dragOption]="dragOption"
      (rowDoubleClick)="onCellDoubleClick($event)"
      (keydown.enter)="onF2Key($event)"
      (actionComplete)="onActionComplete($event)"
      (rowDragStart)="onCellDragStart($event)"
      (rowDrop)="onCellDragStop($event)"
      [droppedTargetContainer]="[treeview]">
      <tx-grid-column fieldName="name">
        <ng-template let-data>
          <div>
            <fa-icon
              [icon]="[
                isFolderGrid(data.type) ? 'fas' : 'fal',
                isFolderGrid(data.type) ? 'folder' : data.isTemp ? 'file-upload' : 'file'
              ]"
              size="lg"></fa-icon
            ><span>{{ getNameWithoutExtensionInGrid(data) }}</span>
          </div>
        </ng-template>
      </tx-grid-column>
      <tx-grid-column fieldName="extension">
        <ng-template #template let-data>
          <div>{{ getExtensionForFileGrid(data) | lowercase }}</div>
        </ng-template>
      </tx-grid-column>
      <tx-grid-column fieldName="lastWriteTime">
        <ng-template #template let-data>
          <div>{{ data.lastWriteTime | localizedDate : 'medium' : true }}</div>
        </ng-template>
      </tx-grid-column>
      <tx-grid-column fieldName="length">
        <ng-template #template let-data>
          <div>
            {{
              data.length !== -1
                ? (formatFileSizeInGrid(data.length) | localizedNumber) +
                  ' ' +
                  ('admins.resources.kilobyteDiminutive' | translate)
                : ''
            }}
          </div>
        </ng-template>
      </tx-grid-column>
    </tx-grid>
  </div>
</div>
<tx-context-menu
  trigger="#filesGrid"
  [items]="menuFiles"
  [disableItems]="disableMenuItems"
  (beforeOpen)="beforeOpenFile()"
  (select)="contextMenuClick($event)"></tx-context-menu>
<ng-template #fileEditor>
  <app-file-editor
    [fileToEdit]="selectedFile"
    [isFileReadOnly]="isCurrentFileEditable"
    [paneRef]="fileEditorPaneRef"></app-file-editor>
</ng-template>
