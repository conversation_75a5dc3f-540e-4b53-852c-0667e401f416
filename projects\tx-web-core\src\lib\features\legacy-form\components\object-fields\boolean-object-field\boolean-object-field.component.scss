:host ::ng-deep .mat-button-toggle-label-content {
  max-width: 118px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 13px;
  height: 30px;
}

.mat-form-label {
  pointer-events: auto;
  width: auto;
}

.boolean-error {
  margin-left: 10px;
  font-size: 10.5px;
}

.empty-div {
  height: 13px;
}

.mat-button-toggle-group {
  height: 24px;
  align-items: center;
}

.mat-button-toggle-disabled {
  opacity: 0.5;
}

.mat-icon-button {
  height: 25px;
  line-height: 25px;
}

.mat-button-toggle-disabled.cdk-focused {
  outline: none !important;
}

mat-label,
mat-error {
  display: block;
}

mat-label {
  font-size: 12px;
  font-family: Roboto, sans-serif;
  margin-bottom: 8px;
}

:host .mat-button-toggle-group {
  border-radius: 10px !important;
}

.read-form-field {
  padding-top: 5px;
}

.read-bool-container {
  height: 16px;
  width: fit-content;
  padding: 3px 16px;
  border-radius: 12px;
  min-height: 0px !important;
}
