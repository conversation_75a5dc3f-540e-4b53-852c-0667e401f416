import { TestBed } from '@angular/core/testing';
import { TxConfigService } from '@bassetti-group/tx-web-core';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { ConfigServiceMock } from 'src/app/app.testing.mock';
import { TxAutomaticNamingService } from './automatic-naming.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

describe('AutomaticNamingService', () => {
  const apiUrl = 'https://localhost:44336/';

  let automaticNamingService: TxAutomaticNamingService;
  let http: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
    imports: [],
    providers: [
        { provide: TxConfigService, useClass: ConfigServiceMock },
        TxAutomaticNamingService,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
    ]
});
    automaticNamingService = TestBed.inject(TxAutomaticNamingService);
    http = TestBed.inject(HttpTestingController);
  });

  it('should be created', () => {
    expect(automaticNamingService).toBeTruthy();
  });

  describe('Get automatic naming configuration list', () => {
    it('should get config list', () => {
      const mockResponse = {
        txStrGenConfiguration: [
          {
            id: 1,
            name: 'testconfig1',
            objectTypeTag: 'otPeople',
            tag: 'configTagTest',
            data: null,
          },
          {
            id: 2,
            name: 'testconfig Naming',
            objectTypeTag: 'otTagTestfromOthers',
            tag: 'configTagTestOthertest',
            data: null,
          },
          {
            id: 10,
            name: 'testconfig people',
            objectTypeTag: 'otPeople',
            tag: 'configTagTestnaming',
            data: null,
          },
        ],
      };
      let configList: any;
      automaticNamingService
        .getConfigurations()
        .subscribe((res) => (configList = res.txStrGenConfiguration));

      http.expectOne(`${apiUrl}api/Naming`).flush(mockResponse);
      expect(configList.length).toBe(3);
    });
  });
});
