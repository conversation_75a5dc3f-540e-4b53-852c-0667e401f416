import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { DialogData } from './dialog-data';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';

const DEFAULT_DATA: DialogData = {
  type: 'confirm',
  icon: 'check-circle',
  message: '',
  okCaption: _('txWebCore.window.ok'),
  confirmButtonDisabled: false,
  title: _('txWebCore.window.confirmation'),
};

@Component({
  standalone: true,
  imports: [TranslateModule, FontAwesomeModule, CommonModule, MatButtonModule, MatDialogModule],
  templateUrl: './dialog-dyn.component.html',
  styleUrls: ['./dialog-dyn.component.scss'],
})
export class TxDialogDynComponent {
  public data: DialogData;

  constructor(
    public dialogRef: MatDialogRef<TxDialogDynComponent>,
    @Inject(MAT_DIALOG_DATA) private readonly confirmData: DialogData
  ) {
    this.data = { ...DEFAULT_DATA, ...confirmData };
  }
  get actionButtonDisabled() {
    return typeof this.data.confirmButtonDisabled === 'function'
      ? this.data.confirmButtonDisabled()
      : this.data.confirmButtonDisabled;
  }
  onConfirm(): void {
    this.dialogRef.close(true);
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
