import type { Meta, StoryObj } from '@storybook/angular';
import { TagListComponent } from './tag-list.component';

const meta: Meta<TagListComponent> = {
  component: TagListComponent,
  title: 'TagListComponent',
  tags: ['autodocs'],
};
export default meta;
type Story = StoryObj<TagListComponent>;

export const Primary: Story = {
  args: {
    tags: ['tag1', 'tag2', 'tag3'],
    tagListWidth: 0,
    tagListSearchValue: 'tag2',
  },
};
