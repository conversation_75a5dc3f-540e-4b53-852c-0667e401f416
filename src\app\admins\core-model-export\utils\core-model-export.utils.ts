import {
  CoreModelExportConcept,
  FlatCoreModelExportConcept,
} from '../models/core-model-export-concept.model';

export const toFlatCoreModelExportConcepts = (
  concepts: CoreModelExportConcept[]
): FlatCoreModelExportConcept[] =>
  concepts.map((concept) => {
    const tags = concept.tags.join('|');
    const name = concept.name;
    const id = concept.id;
    const explanation = concept.explanation;
    const metaDataList = concept.metaDataList
      .map(
        (metaData) =>
          metaData.header +
          ':' +
          (typeof metaData.value === 'string' ? metaData.value : metaData.value.name)
      )
      .join('|');
    const type = concept.type;
    const objectType = concept.objectType?.name ?? '';
    const errors = concept.errors.join('|');
    const icon = concept.icon;
    const objectTypeIcon = concept.objectType?.icon;
    return {
      tags,
      name,
      id,
      explanation,
      metaDataList,
      type,
      objectType,
      errors,
      icon,
      objectTypeIcon,
    };
  });
