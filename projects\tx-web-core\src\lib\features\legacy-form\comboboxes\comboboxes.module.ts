import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import {
  DropDownListModule,
  MultiSelectModule,
  ComboBoxModule,
  MultiSelectAllModule,
} from '@syncfusion/ej2-angular-dropdowns';
import { TxComboboxComponent } from './combobox/combobox.component';
import { TxComboboxObjectComponent } from './combobox-object/combobox-object.component';
import { TxComboboxObjectTypeComponent } from './combobox-object-type/combobox-object-type.component';

@NgModule({
  imports: [
    CommonModule,
    DropDownListModule,
    MultiSelectModule,
    ComboBoxModule,
    MultiSelectAllModule,
  ],
  declarations: [TxComboboxComponent, TxComboboxObjectComponent, TxComboboxObjectTypeComponent],
  exports: [
    TxComboboxComponent,
    TxComboboxObjectComponent,
    TxComboboxObjectTypeComponent,
    CommonModule,
    DropDownListModule,
    MultiSelectModule,
    ComboBoxModule,
    MultiSelectAllModule,
  ],
})
export class TxComboboxesModule {}
