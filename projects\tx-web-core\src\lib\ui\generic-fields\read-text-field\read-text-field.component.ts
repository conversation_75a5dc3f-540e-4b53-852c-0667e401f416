import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { ControlValueAccessor } from '@angular/forms';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'tx-read-text-field',
  templateUrl: './read-text-field.component.html',
  styleUrls: ['./read-text-field.component.scss'],
  standalone: true,
  imports: [MatTooltipModule, CommonModule],
})
export class TxReadTextFieldComponent implements ControlValueAccessor {
  @Input() value: string = '';
  @Input() withBottomSpace = false;
  @Input() label: string = '';
  @Input() labelTooltip: string = '';

  writeValue(obj: any): void {
    throw new Error('Method not implemented.');
  }
  registerOnChange(fn: any): void {
    throw new Error('Method not implemented.');
  }
  registerOnTouched(fn: any): void {
    throw new Error('Method not implemented.');
  }
  setDisabledState?(isDisabled: boolean): void {
    throw new Error('Method not implemented.');
  }
}
