import { FormControl } from '@angular/forms';

export enum InputNumberFieldsType {
  MinMax = 'minMax',
  MinMaxMean = 'minMaxMean',
}

export interface Standard {
  standard: number;
  unit: number;
}
export interface MinMax {
  min: number | null;
  max: number | null;
  unit: number | null;
}
export interface MinMaxMean {
  min: number | null;
  max: number | null;
  mean: number | null;
  unit: number | null;
}

export type ToForm<T> = {
  [P in keyof T]: FormControl<T[P] | null>;
};

export type MinMaxForm = ToForm<MinMax>;
export type MinMaxMeanForm = ToForm<MinMaxMean>;

export interface TxUnit {
  name: string;
  id: number;
}
