<ng-container *ngIf="control" class="upload-file-field">
  <div
    class="upload-file-field__uploader-dragzone uploader-dragzone accent-border"
    [ngClass]="{ 'upload-file-field__uploader-dragzone--hovered': isDropzoneHovered }"
    txDragDrop
    (fileDropped)="onFileSelected($event)"
    (drop)="(isHovered)"
    (dragover)="containsFiles($event)"
    (dragleave)="onDragLeave($event)">
    <div class="uploader-dragzone__drag">
      <span class="accent uploader-dragzone__file-icon">
        <fa-icon [icon]="['fal', 'file-upload']" size="lg"></fa-icon>
      </span>
      <div class="uploader-dragzone__file-message">
        {{ 'txWebCore.components.uploadFileField.dragDropFile' | translate }}
      </div>
    </div>
  </div>
  <mat-label
    class="upload-file-field__label"
    matTooltipClass="upload-file-field__tooltip"
    matTooltipShowDelay="500"
    matTooltipPosition="above"
    [matTooltip]="labelTooltip">
    {{ label | translate }}
    <span *ngIf="required">*</span>
  </mat-label>
  <input
    type="file"
    class="upload-file-field__file-input"
    #fileUpload
    [accept]="requiredFileType"
    [multiple]="multiple"
    (change)="onFileSelected($event)"
    [disabled]="disabled"
    [required]="required" />

  <div
    class="upload-file-field__files-container"
    (dragover)="containsFiles($event)"
    id="{{ label }}ChipListContainer">
    <div
      *ngIf="multiple || files.length < 1"
      class="upload-file-field__file-uploader file-uploader">
      <fa-icon [icon]="['fal', 'file-upload']" class="file-uploader__upload-icon"></fa-icon>
      <div>
        <div class="file-uploader__text">
          <span>{{ 'txWebCore.components.uploadFileField.dropFileLabel' | translate }}</span>
          <span class="file-uploader__clickable-url" (click)="fileUpload.click()">{{
            'txWebCore.components.uploadFileField.browseLabel' | translate
          }}</span>
        </div>
        <div class="file-uploader__size-text field-form-hint">
          <span>{{
            'txWebCore.components.uploadFileField.maximumSizeMo'
              | translate : { maxMoFileSize: maxMoFileSize }
          }}</span>
        </div>
      </div>
    </div>

    <mat-chip-listbox>
      <div class="upload-file-field__chips-listbox">
        <div *ngFor="let file of files">
          <tx-file-field-box
            *ngIf="file.action < 3"
            [file]="file"
            (removed)="removeFile($event)"
            [hideVisualisationToggle]="hideVisualisationToggle"></tx-file-field-box>
        </div>
      </div>
    </mat-chip-listbox>
    <div class="upload-file-field__errors">
      <mat-error *ngIf="control?.hasError('required')" class="upload-file-field__error">{{
        'txWebCore.errorMessage.required' | translate
      }}</mat-error>
      <mat-error *ngIf="control?.hasError('FileTooBig')" class="upload-file-field__error">
        {{ 'txWebCore.errorMessage.fileTooBig' | translate }}
      </mat-error>
    </div>
  </div>
</ng-container>
