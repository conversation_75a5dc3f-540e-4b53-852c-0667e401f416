import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { TxObjectTypeTreeViewComponent } from '../object-type-tree-view.component';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTreeModule } from '@angular/material/tree';
import { TxTreeViewService } from '../object-type-tree-view.service';
import { TreeViewServiceMock } from './tree-view.service.mocks';
import { Component } from '@angular/core';
import { FlatTreeNode, TreeData, TreeDataOptions } from '../object-type-tree-view.model';
import { FlatTreeControl } from '@angular/cdk/tree';
import { TxIsNodeDisabledPipe } from '../is-node-disabled.pipe';
@Component({
  selector: 'app-host-component',
  template: ` <tx-object-type-tree-view
    [treeData]="testDataSource"
    [filteredTreeData]="[]"
    [treeDataOptions]="testDataOptions"
    [checkedIds]="[1, 3, 6]">
  </tx-object-type-tree-view>`,
})
class TestHostComponent {
  public testDataSource: TreeData[] = [
    { name: 'obj1', myObjectID: 0, testAttString: 'toto', testAttArr: [] },
    { name: 'obj2', myObjectID: 1, testAttString: 'tyty', testAttArr: [] },
    {
      name: 'obj3',
      myObjectID: 2,
      myParentID: 4,
      testAttString: 'toto',
      testAttArr: [],
    },
    {
      name: 'obj4',
      myObjectID: 3,
      myParentID: 5,
      testAttString: 'toto',
      testAttArr: [],
    },
    {
      name: 'obj5',
      myObjectID: 4,
      myParentID: 1,
      testAttString: 'titi',
      testAttArr: [],
    },
    {
      name: 'obj6',
      myObjectID: 5,
      myParentID: 0,
      testAttString: 'toto',
      testAttArr: [],
    },
    {
      name: 'obj7',
      myObjectID: 6,
      myParentID: 1,
      testAttString: 'tata',
      testAttArr: [],
    },
  ];

  public testDataOptions: TreeDataOptions = {
    idProperty: 'myObjectID',
    idParentProperty: 'myParentID',
  };
}

describe('TreeViewComponent', () => {
  let hostComponent: TestHostComponent;
  let hostFixture: ComponentFixture<TestHostComponent>;
  let component: TxObjectTypeTreeViewComponent<any>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TestHostComponent],
      imports: [
        MatCheckboxModule,
        MatTreeModule,
        FontAwesomeTestingModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
        TxObjectTypeTreeViewComponent,
        TxIsNodeDisabledPipe,
      ],
      providers: [{ provide: TxTreeViewService, useClass: TreeViewServiceMock }],
    })
      .overrideComponent(TxObjectTypeTreeViewComponent, {
        set: {
          providers: [
            { provide: TxTreeViewService, useClass: TreeViewServiceMock },
            { provide: TxIsNodeDisabledPipe, useClass: TxIsNodeDisabledPipe },
          ],
        },
      })
      .compileComponents();
  }));

  beforeEach(() => {
    hostFixture = TestBed.createComponent(TestHostComponent);
    hostComponent = hostFixture.componentInstance;
    component = hostFixture.debugElement.children[0].componentInstance;
    component.treeControl = new FlatTreeControl<FlatTreeNode<TreeData>, FlatTreeNode<TreeData>>(
      component.getLevel,
      component.isExpandable
    );
    hostFixture.detectChanges();
  });

  it('should create', () => {
    expect(hostComponent).toBeTruthy();
    expect(component).toBeTruthy();
  });

  it('should check all nodes', () => {
    component.checkAll();
    const selectedNodes = component.checklistSelection.selected;
    expect(selectedNodes.length).toBe(component.treeControl.dataNodes.length);
  });

  it('should uncheck all nodes except 1 disabled', () => {
    component.checkedIds = [1, 3, 6];
    component.disabledIds = [6];
    component.uncheckAll();
    const selectedNodes = component.checkedIds;
    expect(selectedNodes.length).toBe(1);
  });

  it('should uncheck all nodes', () => {
    component.checkedIds = [1, 3, 6];
    component.disabledIds = [];
    component.uncheckAll();
    const selectedNodes = component.checkedIds;
    expect(selectedNodes.length).toBe(0);
  });

  it('should update checkedIds', () => {
    component.checkedIds = [1, 2, 3];
    component.disabledIds = [1, 2];
    const checkedIds: (string | number)[] = [4, 5, 6];
    component.updateCheckedIds(true, checkedIds);
    expect(component.checkedIds).toEqual(checkedIds);
  });
});
