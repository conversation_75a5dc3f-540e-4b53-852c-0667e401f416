<mat-spinner
  class="tx-tree-spinner"
  *ngIf="showSpinner"
  [diameter]="30"
  [strokeWidth]="3"
  color="accent"></mat-spinner>
<tx-grid
  class="tree-grid__grid"
  [ngClass]="{ 'remove-border': enableNativeTreeLook }"
  [parentDataSource]="tableDataSource"
  [columns]="columns"
  [allowEditing]="allowEditing"
  [isRowSelectable]="isRowSelectable"
  [allowEditOnDblClick]="allowEditOnDblClick"
  [primaryKey]="primaryKey"
  [displayedColumns]="displayedColumns"
  [disableBindSelectReset]="true"
  [disableSearchDataFilter]="true"
  [disableInitialSort]="true"
  [enableColumnResize]="enableColumnResize"
  [allowAutoRowSelect]="allowAutoRowSelect"
  [allowCellEditOnDblClick]="allowCellEditOnDblClick"
  [disableEditOnCellClick]="disableEditOnCellClick"
  [enableSearching]="enableSearching"
  [rowHeight]="rowHeight"
  [inputPlaceholder]="inputPlaceholder"
  [inputValue]="inputValue"
  [searchInputIsFiltered]="searchInputIsFiltered"
  [disableSelectionHandling]="disableSelectionHandling"
  [autoGrowColumns]="autoGrowColumns"
  [enableSearchHighlight]="enableSearchHighlight"
  [enableRowTooltip]="enableRowTooltip"
  (dataBound)="dataBound.emit($event)"
  (rowDoubleClick)="rowDoubleClick.emit($event)"
  (rowSelected)="onRowSelected($event)"
  (rowDeselected)="rowDeselected.emit($event)"
  (cellEdit)="onCellEdit($event)"
  (actionCellComplete)="actionCellComplete.emit($event)"
  (searchClear)="searchClear.emit($event)"
  (searchInputChange)="searchInputChange.emit($event)"
  (filterChange)="filterChange.emit($event)"
  (columnResize)="detectChanges()">
  <tx-cell-prefix [fieldNames]="[displayedColumns[treeColumnIndex]]" *ngIf="disableColumnShift">
    <ng-template let-data>
      <span [ngStyle]="data | cellPrefixStyle : childMapping"></span>
      <button
        type="button"
        class="tree-control-btn tree-control-margin"
        *ngIf="data[childMapping] !== undefined || data.isParent"
        (dblclick)="$event.stopPropagation()"
        mat-icon-button
        (click)="treeToggle(data, $event)">
        <fa-icon
          [icon]="['fal', dataSource.isExpanded(data) ? 'angle-down' : 'angle-right']"
          [transform]="'shrink-4'"></fa-icon>
      </button>
    </ng-template>
  </tx-cell-prefix>
  <ng-container matColumnDef="tree-control">
    <mat-header-cell *matHeaderCellDef class="tree-control-flex"> </mat-header-cell>
    <mat-cell
      class="tree-grid__tree-control-cell"
      *matCellDef="let element"
      [ngStyle]="element.level | treeCellStyle : enableNativeTreeLook">
      <ng-container *ngIf="enableNativeTreeLook">
        <span
          class="tree-border tree-control-flex"
          *ngFor="let level of [].constructor(element.level)"></span>
      </ng-container>
      <button
        class="tree-control-btn"
        type="button"
        *ngIf="element[childMapping] !== undefined || element.isParent"
        (dblclick)="$event.stopPropagation()"
        mat-icon-button
        (click)="treeToggle(element, $event)">
        <fa-icon
          [icon]="['fal', dataSource.isExpanded(element) ? 'angle-down' : 'angle-right']"
          [transform]="'shrink-4'"></fa-icon>
      </button>
    </mat-cell>
  </ng-container>
</tx-grid>
