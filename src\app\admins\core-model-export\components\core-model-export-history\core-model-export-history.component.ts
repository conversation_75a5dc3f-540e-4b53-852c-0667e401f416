import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import {
  CoreModelExportHistoryFieldEnum,
  CoreModelExportHistory,
} from '../../models/core-model-export-history.model';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TxGridColumn, TxGridDataType } from '@bassetti-group/tx-web-core';

const TABLE_HEADERS = {
  version: _('admins.coreModels.version'),
  name: _('admins.coreModels.name'),
  explanation: _('admins.coreModels.description'),
  date: _('admins.coreModels.date'),
  username: _('admins.coreModels.username'),
  comment: _('admins.coreModels.comment'),
};
@Component({
  selector: 'app-core-model-export-history',
  templateUrl: './core-model-export-history.component.html',
  styleUrls: ['./core-model-export-history.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CoreModelExportHistoryComponent {
  @Input() history: CoreModelExportHistory[] = [];
  @Input() isLoading: boolean | null = null;
  columns: TxGridColumn<CoreModelExportHistory>[] = Object.values(
    CoreModelExportHistoryFieldEnum
  ).map((value) => ({
    field: value,
    headerText: _(`admins.coreModels.${value}`),
    width: this.getTxGridColumnWidth(value),
    type: TxGridDataType.TEXT,
    activeFilter: true,
    isSearchable: true,
    sorting: true,
  }));
  getTxGridColumnWidth(field: CoreModelExportHistoryFieldEnum): string | undefined {
    switch (field) {
      case CoreModelExportHistoryFieldEnum.Username:
      case CoreModelExportHistoryFieldEnum.Date:
        return '240px';
      case CoreModelExportHistoryFieldEnum.Version:
        return '140px';
      default:
        return undefined;
    }
  }
}
