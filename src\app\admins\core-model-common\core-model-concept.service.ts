import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, distinctUntilChanged } from 'rxjs';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateService } from '@ngx-translate/core';
import { ErrorMessagesService } from 'src/app/core/error-messages/error-messages.service';
import { ErrorService } from 'src/app/core/services/errors/error.service';
import { ErrorUtils } from 'src/app/core/utils/error.utils';
import { NbErrors } from './models/nb-errors.model';
import {
  ConceptType,
  TxCommonService,
  TxObjectType,
  TxObjectsTypeService,
  TxConcept,
} from '@bassetti-group/tx-web-core';
import { equals } from 'ramda';

const META_DATA_HEADER = {
  idSourceObjectType: _('admins.coreModels.idSourceObjectType'),
  idDestinationObjectType: _('admins.coreModels.idDestinationObjectType'),
  idModel: _('admins.coreModels.idModel'),
  filePath: _('admins.coreModels.filePath'),
};
const UNKNOWN_LABEL = _('generic.unknown');
@Injectable({
  providedIn: 'root',
})
export class CoreModelConceptService<T extends TxConcept> {
  concepts$: Observable<T[]>;
  isLoading$: Observable<boolean>;
  conceptsNbErrors$: Observable<NbErrors>;
  showOnlyErrors$: Observable<boolean>;
  protected _conceptsSub = new BehaviorSubject<T[]>([]);
  protected _isLoadingSub = new BehaviorSubject<boolean>(false);
  protected _conceptsNbErrorsSub = new BehaviorSubject<NbErrors>({
    errors: 0,
  });
  protected _showOnlyErrorsSub = new BehaviorSubject<boolean>(false);

  protected _concepts: T[] = [];
  protected _filteredOnErrorsConcepts: T[] = [];

  constructor(
    protected _translateService: TranslateService,
    protected _objectsTypeService: TxObjectsTypeService,
    protected _errorService: ErrorService,
    protected _errorMessagesService: ErrorMessagesService,
    protected _commonService: TxCommonService
  ) {
    this.concepts$ = this._conceptsSub
      .asObservable()
      .pipe(distinctUntilChanged((prev, curr) => equals(prev, curr)));
    this.isLoading$ = this._isLoadingSub.asObservable();
    this.conceptsNbErrors$ = this._conceptsNbErrorsSub.asObservable();
    this.showOnlyErrors$ = this._showOnlyErrorsSub.asObservable();
  }

  removeFilterOnConceptsInErrors(): void {
    this._conceptsSub.next(this._concepts);
    this._showOnlyErrorsSub.next(false);
  }

  protected filterOnErrors(): void {
    this._conceptsSub.next(this._filteredOnErrorsConcepts);
    this._showOnlyErrorsSub.next(true);
  }

  protected updateConcepts(concepts: T[]) {
    this._concepts = concepts;
    this._conceptsSub.next(concepts);
  }
  protected updateConceptsAndFilterOnErrors<
    V extends T & Record<K, { length: number }>,
    K extends keyof V
  >(concepts: V[], errorsFieldName: K) {
    this._concepts = concepts;
    const conceptsInErrors = this.filterOnErrorKeys(concepts, errorsFieldName);
    this._filteredOnErrorsConcepts = conceptsInErrors;
    this._conceptsNbErrorsSub.next(this.nbErrors(conceptsInErrors, errorsFieldName));
    this._showOnlyErrorsSub.next(this.showOnlyErrors(this._conceptsNbErrorsSub.value.errors));
    this._conceptsSub.next(
      this._showOnlyErrorsSub.value ? this._filteredOnErrorsConcepts : this._concepts
    );
  }
  protected getObjectType(idObjectType: number): TxObjectType | undefined {
    return this._objectsTypeService.getByID(idObjectType);
  }

  protected conceptIcon(
    type: ConceptType,
    objectType: TxObjectType | undefined,
    id: number
  ): number | undefined {
    return this._commonService.getConceptTypeIcon(type, this.objectTypeIcon(objectType, id));
  }
  private objectTypeIcon(objectType: TxObjectType | undefined, id: number): number | undefined {
    return objectType?.icon ?? this.getObjectType(id)?.icon;
  }

  private filterOnErrorKeys<V extends T & Record<K, { length: number }>, K extends keyof V>(
    concepts: V[],
    errorFieldName: K
  ): V[] {
    return concepts.filter((concept) => concept[errorFieldName].length > 0);
  }
  private nbErrors<V extends T & Record<K, { length: number }>, K extends keyof V>(
    concepts: V[],
    errorFieldName: K
  ): NbErrors {
    return {
      errors: ErrorUtils.concatenatedNbErrors(concepts, errorFieldName),
    };
  }
  private showOnlyErrors(nbErrors: number): boolean {
    return nbErrors > 0;
  }
}
