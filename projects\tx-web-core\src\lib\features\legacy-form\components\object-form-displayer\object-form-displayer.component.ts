import { LegacyTxObjectFormStepperComponent } from './../object-form-stepper/object-form-stepper.component';
import { TxRightPaneComponent } from '../../right-pane/right-pane.component';
import { Component, Input, OnInit, ViewChild, OnChanges, SimpleChanges } from '@angular/core';
import { TxEditionMode } from '../../models/formConfiguration/businessClass/form-enum';
import { TxStep } from '../../models/step.model';

import { TxFormSettings } from '../../models/formConfiguration/businessClass/form-settings';
import { LegacyTxAttribute } from '../../services/structure/models/attribute';

@Component({
  selector: 'tx-object-form-displayer',
  templateUrl: './object-form-displayer.component.html',
  styleUrls: ['./object-form-displayer.component.scss'],
})
export class TxObjectFormDisplayerComponent implements OnInit, OnChanges {
  @Input() inRightPane = false;
  @Input() isLinear = true;
  @Input() steps!: TxStep[];
  @Input() attributesIds!: number[];
  @Input() attributesTags!: string[];
  @Input() attributes: LegacyTxAttribute[] = [];
  @Input() keepStructureOrder = false;
  @Input() idObject!: number;
  @Input() idObjectType!: number;
  @Input() editionMode = TxEditionMode.read;
  @Input() tag = '';
  @Input() formSettings: TxFormSettings = new TxFormSettings();
  @Input() showBarAndButton: boolean = true;
  @Input() indexTabToFocusFirst = 0;

  @ViewChild('rightPane') rightPane!: TxRightPaneComponent;
  @ViewChild('objectFormStepper') objectFormStepper!: LegacyTxObjectFormStepperComponent;

  readMode!: boolean;

  constructor() {}

  ngOnInit() {}

  ngOnChanges(changes: SimpleChanges) {
    // console.log('ngOnChanges object-form-displayer', changes);
  }

  onReload() {
    this.objectFormStepper.ngOnInit();
  }

  toggle() {
    if (this.inRightPane) {
      this.rightPane.toggle();
    }
  }
}
