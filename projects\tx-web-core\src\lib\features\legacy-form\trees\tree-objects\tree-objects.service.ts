import { Injectable } from '@angular/core';
import { LegacyTxObject } from '../../services/structure/models/object';

@Injectable({
  providedIn: 'root',
})
export class LegacyTxTreeObjectsService {
  imagePath = '/assets/tx-web-core/imgs/objectTypesIcons/';

  constructor() {}

  fillTxObjectsNodes(txObjects: LegacyTxObject[], imgPath = this.imagePath, linear = false): any[] {
    return txObjects.map((txObject) => {
      if (txObject.image) {
        txObject.image = imgPath + (txObject.isFolder ? 'folderClosed.gif' : txObject.image);
      }

      if (linear) {
        txObject.isParent = false;
      }

      delete txObject.options.branchLoaded;

      return txObject;
    }) as [];
  }
}
