import { Injectable } from '@angular/core';
import { TreeData, TreeDataOptions, TreeNode } from './object-type-tree-view.model';

@Injectable()
export class TxTreeViewService<T extends TreeData> {
  buildTree(
    treeData: T[],
    treeDataOptions: TreeDataOptions = {
      idProperty: 'id',
      idParentProperty: 'idParent',
    }
  ): TreeNode<T>[] {
    const idToNodeMap: Record<number, TreeNode<T>> = {};
    // Create a mapping from id to the corresponding TreeNode
    treeData.forEach((obj) => {
      idToNodeMap[obj[treeDataOptions.idProperty] as number] = {
        objectData: obj,
        children: [],
        name: obj.name,
      };
    });

    const rootNodes: TreeNode<T>[] = [];

    treeData.forEach((obj) => {
      const idParent = obj[treeDataOptions.idParentProperty] as number;
      const currentNode = idToNodeMap[obj[treeDataOptions.idProperty] as number];
      const parentNode = idToNodeMap[idParent];

      if (parentNode) {
        parentNode.children.push(currentNode);
      } else {
        // If there is no parent, it's a root node
        rootNodes.push(currentNode);
      }
    });
    return rootNodes;
  }

  buildFilteredTree(
    treeData: T[],
    filteredTreeData: T[],
    treeDataOptions: TreeDataOptions = {
      idProperty: 'id',
      idParentProperty: 'idParent',
    }
  ): TreeNode<T>[] {
    let parentNodeID: number | undefined;
    let parentNode: T | undefined;
    for (const filderedNode of filteredTreeData) {
      parentNodeID = filderedNode[treeDataOptions.idParentProperty] as number;
      while (parentNodeID) {
        parentNode = treeData.find((node) => node[treeDataOptions.idProperty] === parentNodeID);
        if (parentNode) {
          if (!filteredTreeData.some((node) => node[treeDataOptions.idProperty] === parentNodeID)) {
            filteredTreeData.push(parentNode);
          }
          parentNodeID = parentNode[treeDataOptions.idParentProperty] as number;
        } else {
          parentNodeID = undefined;
        }
      }
    }
    return this.buildTree(filteredTreeData, treeDataOptions);
  }
}
