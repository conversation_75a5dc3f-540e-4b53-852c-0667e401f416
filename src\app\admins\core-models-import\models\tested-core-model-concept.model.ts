import {
  CoreModelImportConcept,
  FlatImportedCoreModelConcept,
} from './imported-core-model-concept.model';

export interface TestedCoreModelConcept extends CoreModelImportConcept {
  readonly objectType?: TestedCoreModelConcept;
  readonly conflicts: string[];
  readonly translatedConflicts: string[];
}
export enum TestedCoreModelConceptFieldEnum {
  Id = 'id',
  Name = 'name',
  Tags = 'tags',
  ObjectType = 'objectType',
  ModificationType = 'translatedModificationType',
  Conflicts = 'translatedConflicts',
  Type = 'type',
}
export interface FlatTestedCoreModelConcept extends FlatImportedCoreModelConcept {
  translatedConflicts: string;
  conflicts: string;
}
