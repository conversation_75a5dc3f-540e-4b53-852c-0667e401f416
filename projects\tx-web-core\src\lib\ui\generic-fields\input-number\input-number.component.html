<div
  *ngIf="control"
  class="input-number"
  (focusin)="onFocusIn($event)"
  (focusout)="onFocusOut($event)">
  <mat-form-field
    class="input-number__field"
    color="accent"
    [hintLabel]="hintLabel | translate"
    [hideRequiredMarker]="!required">
    <mat-label
      class="input-number__label"
      [matTooltip]="labelTooltip ?? label | translate"
      matTooltipClass="mat-label-tooltip mat-tooltip-multiline"
      matTooltipShowDelay="500"
      matTooltipPosition="above">
      {{ label | translate }}
    </mat-label>
    <input
      matInput
      required
      color="accent"
      class="number-input__field"
      type="number"
      [formControl]="control"
      autocomplete="off"
      (keyup)="onKeyUp($event)" />
  </mat-form-field>
</div>
