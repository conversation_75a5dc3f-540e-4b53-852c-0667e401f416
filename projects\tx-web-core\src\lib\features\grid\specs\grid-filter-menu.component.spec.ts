import { FormsModule } from '@angular/forms';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatMenuModule } from '@angular/material/menu';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxGridFilterMenuComponent } from '../grid-filter-menu/grid-filter-menu.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { MatInputModule } from '@angular/material/input';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { MockComponent, MockPipe } from 'ng-mocks';
import { TxDatepickerComponent } from '../../../ui/generic-fields/datepicker';
import { TxConceptDropdownComponent, TxObjectsTypeDropdownComponent } from '../../dropdown';
import { TxFormatTooltipFilterPipe } from '../grid-filter-menu/format-tooltip-filter.pipe';
import { TxNotEmptyFilterPipe } from '../grid-filter-menu/not-empty-filter.pipe';
import { LocalizedDatePipe } from '../../localized';

describe('GridFilterMenuComponent', () => {
  let component: TxGridFilterMenuComponent;
  let fixture: ComponentFixture<TxGridFilterMenuComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        TxGridFilterMenuComponent,
        MockComponent(TxDatepickerComponent),
        MockComponent(TxObjectsTypeDropdownComponent),
        MockComponent(TxConceptDropdownComponent),
        MockPipe(TxFormatTooltipFilterPipe),
        MockPipe(TxNotEmptyFilterPipe),
      ],
      imports: [
        NoopAnimationsModule,
        MatMenuModule,
        MatChipsModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatButtonModule,
        MatTooltipModule,
        FormsModule,
        FontAwesomeTestingModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
      ],
    })
      .overrideComponent(TxGridFilterMenuComponent, {
        set: {
          providers: [{ provide: LocalizedDatePipe, useClass: MockPipe(LocalizedDatePipe) }],
        },
      })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxGridFilterMenuComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
