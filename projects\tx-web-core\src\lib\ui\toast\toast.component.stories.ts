import { applicationConfig, type Meta, type StoryObj } from '@storybook/angular';
import { ToastComponent } from './toast.component';
import { TOAST_DATA_TOKEN } from './toast-data-token';
import { ToastService } from './toast.service';
import { ToastRef } from './toast.reference';

const meta: Meta<ToastComponent> = {
  component: ToastComponent,
  title: 'ToastComponent',
  tags: ['autodocs'],
  decorators: [
    applicationConfig({
      providers: [
        { provide: TOAST_DATA_TOKEN, useClass: ToastService },
        { provide: ToastRef, useClass: ToastService },
      ],
    }),
  ],
};
export default meta;
type Story = StoryObj<ToastComponent>;

export const Primary: Story = {
  args: {
    data : {
      description : "Succesful notification",
      type :'success',
      isPersistent : true
    }
  },
};

export const Secondary: Story = {
  args: {
    data : {
      description : "Information notification",
      type :'information',
      isPersistent : true
    }
  },
};

export const third: Story = {
  args: {
    data : {
      description : "Error notification",
      type :'error',
      isPersistent : true
    }
  },
};

export const fourth: Story = {
  args: {
    data : {
      description : "Warning notification",
      type :'warning',
      isPersistent : true
    }
  },
};

export const fifth: Story = {
  args: {
    data : {
      description : "Loading notification",
      type :'loading',
      isPersistent : true
    }
  },
};




