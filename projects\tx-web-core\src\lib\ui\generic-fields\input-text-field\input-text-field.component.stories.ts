import { Args, type Meta, type StoryObj } from '@storybook/angular';
import { TxInputTextFieldComponent } from './input-text-field.component';
import { FormControl } from '@angular/forms';

const control = new FormControl('');
const meta: Meta<TxInputTextFieldComponent> = {
  component: TxInputTextFieldComponent,
  title: 'Generic Fields/TxInputTextFieldComponent',
  tags: ['autodocs'],
};
export default meta;

export const Primary: StoryObj = {
  render: (args: Partial<Args>) => ({
    props: { ...args, formControl: control },
    template: `
    <tx-input-text-field
        [formControl]="formControl"
        [label]="label"
        [labelTooltip]="labelTooltip"
        [isTextArea]="isTextArea"
        [icon]="icon"
        [hintLabel]="hintLabel"
        [required]="required"
        [maxLength]="maxLength"
      ></tx-input-text-field>
   `,
  }),
};

Primary.args = {
  hintLabel: 'hint text',
  label: 'label text',
  icon: 'pen',
  isTextArea: false,
  required: true,
  labelTooltip: 'tooltip text',
  maxLength: 5,
};
