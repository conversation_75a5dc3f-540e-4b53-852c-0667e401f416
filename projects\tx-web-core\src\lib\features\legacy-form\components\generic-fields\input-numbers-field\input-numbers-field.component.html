<div
  class="form-field full-fields"
  [ngClass]="{ 'form-field-error': control && !control.valid && !disabled }">
  <mat-form-field
    *ngIf="!readMode"
    hideRequiredMarker
    #formField
    color="accent"
    [hintLabel]="information"
    [ngClass]="{ 'mat-form-field-disabled': disabled }">
    <mat-label
      class="form-label mat-form-label"
      [matTooltip]="''"
      matTooltipClass="mat-label-tooltip mat-tooltip-multiline"
      matTooltipShowDelay="500"
      matTooltipPosition="above">
      {{ label }}
      <span
        *ngIf="required"
        [class]="
          control && control.hasError('required') ? 'mat-form-label span-error' : 'mat-form-label'
        ">
        *</span
      >
    </mat-label>
    <tx-input-numbers-control
      #inputsNumber
      [form]="form"
      [formControl]="control"
      [minFormControl]="control"
      [maxFormControl]="maxFormControl"
      [meanFormControl]="meanFormControl"
      [minPlaceHolder]="minPlaceHolder"
      [maxPlaceHolder]="maxPlaceHolder"
      [meanPlaceHolder]="meanPlaceHolder"
      [lowerBoundValue]="lowerBoundValue"
      [upperBoundValue]="upperBoundValue"
      [lowerBoundInclude]="lowerBoundInclude"
      [upperBoundInclude]="upperBoundInclude"
      [withSecondInput]="withSecondInput"
      [withThirdInput]="withThirdInput"
      [inputWidthPx]="inputWidthPx"
      (focusEvent)="changeFocus($event)"
      (onKeyPress)="keyPress.emit($event)">
    </tx-input-numbers-control>

    <mat-hint align="end">{{ control.value?.length || 0 }}/{{ maxLength || minLength }}</mat-hint>
    <mat-hint
      class="hint-number"
      *ngIf="(lowerBoundValue || upperBoundValue) && focused"
      align="start"
      >{{ hintBound }}</mat-hint
    >
    <mat-error *ngIf="control && control.hasError('required')"><strong>Required</strong></mat-error>
    <mat-error *ngIf="control && control.hasError('min')"
      >min <strong>{{ lowerBoundValue }}</strong></mat-error
    >
    <mat-error *ngIf="control && control.hasError('max')"
      >max <strong>{{ upperBoundValue }}</strong></mat-error
    >
    <mat-error *ngIf="control && control.hasError('minUpperThanMax')"
      >Max value must be greater than min value</mat-error
    >
    <mat-error *ngIf="control && control.hasError('meanOutOfMinAndMax')"
      >Mean value must be between min value and max value</mat-error
    >
  </mat-form-field>

  <div
    *ngIf="!readMode && units?.length === 1 && units[0] && (focused || notEmpty || unitFocused)"
    class="one-unit-text">
    <span>{{ units[0].name }}</span>
  </div>

  <mat-form-field
    *ngIf="!readMode && idUnitSelected && units.length > 1 && (focused || notEmpty || unitFocused)"
    class="form-unit-select form-field-no-underline"
    color="accent">
    <mat-select
      [formControl]="getFormControl(idUnitSelected + '')"
      (selectionChange)="updateBoundValue()"
      (focus)="unitFocused = true"
      (blur)="blurUnit()"
      [(value)]="idUnitSelected">
      <mat-option class="form-unit-option" *ngFor="let unit of units" [value]="unit.id">{{
        unit.name
      }}</mat-option>
    </mat-select>
  </mat-form-field>
</div>
