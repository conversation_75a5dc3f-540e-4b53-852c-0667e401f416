import { Component, HostListener, Input, OnInit } from '@angular/core';
import { Observable, of } from 'rxjs';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { FileDescription, FileInEdition, FileItem } from '../../models/file-models';
import { FileManagerService } from '../../services/file-manager.service';
import * as m from 'monaco-editor';
import { ValidationError, XMLParser, XMLValidator } from 'fast-xml-parser';
import { ResourcesService } from '../../services/resources.service';
import { RightPaneRef } from 'src/app/core/right-pane/right-pane.ref';
import { ErrorService } from 'src/app/core/services/errors/error.service';
import { HttpErrorResponse } from '@angular/common/http';
import { TxConfigService, TxDialogService } from '@bassetti-group/tx-web-core';
import { CanSaveEditorPipe } from '../../pipes/can-save-editor.pipe';

const enum saveStatus {
  SAVED = 'saved',
  ERROR = 'error',
}

@Component({
  selector: 'app-file-editor',
  templateUrl: './file-editor.component.html',
  styleUrls: ['./file-editor.component.scss'],
  providers: [CanSaveEditorPipe],
})
export class FileEditorComponent implements OnInit {
  @Input() fileToEdit: FileDescription | undefined;
  @Input() paneRef: RightPaneRef | undefined;
  @Input() isFileReadOnly = true;

  public file: FileInEdition | undefined;
  public fileItem: FileItem | undefined;
  public theme = 'vs-dark';
  public editor: m.editor.ICodeEditor | undefined;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public monaco: any;
  public model: m.editor.IModel | null = null;
  public currentErrors: string[] | undefined;
  public isSaving = false;
  public isEditorLoading = true;
  public fileHasModifications = false;
  public lastSaveDate: Date | undefined;
  public lastSavedContent = '';
  public backEndErrorDecorations: m.editor.IModelDeltaDecoration[] = [];

  public xmlParser = new XMLParser({
    ignoreAttributes: false,
    allowBooleanAttributes: true,
    attributeNamePrefix: '',
  });

  public editorOptions = {
    theme: this.theme,
    language: 'xml',
    automaticLayout: true,
    readOnly: this.isFileReadOnly,
  };

  constructor(
    private fmService: FileManagerService,
    private resourcesService: ResourcesService,
    private dialogService: TxDialogService,
    private errorService: ErrorService,
    private canSavePipe: CanSaveEditorPipe,
    private configService: TxConfigService
  ) {}

  @HostListener('window:beforeunload', ['$event'])
  handleClose(e: BeforeUnloadEvent): void {
    if (this.isFileModified()) {
      e.preventDefault();
    }
  }

  ngOnInit() {
    if (this.fileToEdit) {
      this.editorOptions.readOnly = this.isFileReadOnly;
      this.lastSaveDate = new Date(this.fileToEdit.lastWriteTime);
      this.fileItem = this.fmService.createFileItem(this.fileToEdit);
      this.resourcesService.getFileContent(this.fileItem).subscribe((fileContent) => {
        if (this.fileToEdit) {
          this.file = {
            name: this.fileToEdit.name,
            content: fileContent,
          };
          this.errorService.registerUnhandledRequestURL(
            this.configService.getApiUrl() + 'api/TxSecureFileManager/editFile'
          );
          this.lastSavedContent = this.file.content;
        }
      });
    }
  }

  showUnsavedChangesWarning(): Observable<boolean> {
    if (this.isFileModified() || this.currentErrors) {
      return this.dialogService.open({
        message: _('admins.resources.editor.unsavedFileWarning'),
        annotation: _('admins.resources.editor.modificationsOnFileWillBeLost'),
      });
    } else {
      return of(true);
    }
  }

  editorInit(editor: m.editor.ICodeEditor) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    this.monaco = (window as any).monaco;
    this.editor = editor;
    this.model = editor.getModel();
    this.isEditorLoading = false;
    this.editor?.onKeyUp((e) => {
      this.fileHasModifications = this.isFileModified();
      if (!this.fileHasModifications) {
        this.backEndErrorDecorations = [];
        this.displayErrorInEditor();
      }
    });
    this.editor?.onKeyDown((e) => {
      if (e.code === 'KeyS' && e.ctrlKey) {
        e.preventDefault();
        this.saveFile();
      }
    });
  }

  canSaveFile(): boolean {
    return this.canSavePipe.transform(this.isSaving, this.fileHasModifications, this.currentErrors);
  }

  saveFile() {
    if (this.canSaveFile()) {
      this.isSaving = true;
      if (this.file && this.fileItem) {
        this.resourcesService.saveFileContent(this.fileItem, this.file.content).subscribe({
          next: () => {
            this.lastSaveDate = new Date();
            this.fileToEdit!.lastWriteTime = this.lastSaveDate.toString();
            this.backEndErrorDecorations = [];
            this.displayErrorInEditor();
            this.afterSaveFile(saveStatus.SAVED);
          },
          error: (err) => {
            this.manageErrorsAfterSave(err);
            this.afterSaveFile(saveStatus.ERROR);
          },
        });
      }
    }
  }

  createError(
    lineStart: number,
    lineEnd: number,
    colStart: number,
    colEnd: number,
    msg: string,
    isWholeLine?: boolean
  ): m.editor.IModelDeltaDecoration {
    return {
      range: new this.monaco.Range(lineStart, colStart, lineEnd, colEnd),
      options: {
        isWholeLine: !!isWholeLine,
        inlineClassName: 'error-decoration',
        linesDecorationsClassName: 'line-error-decoration',
        hoverMessage: { value: msg },
      },
    };
  }

  displayErrorInEditor() {
    if (this.currentErrors) {
      this.editor?.removeDecorations(this.currentErrors);
      this.currentErrors = undefined;
    }
    if (this.backEndErrorDecorations.length > 0) {
      this.currentErrors = this.editor?.deltaDecorations([], this.backEndErrorDecorations);
    }
  }

  switchTheme() {
    this.theme = this.theme === 'vs-dark' ? 'vs-light' : 'vs-dark';
    this.editorOptions = { ...this.editorOptions, theme: this.theme };
  }

  isFileModified(): boolean {
    return (this.file?.content && this.lastSavedContent !== this.file?.content) || false;
  }

  closePane() {
    this.showUnsavedChangesWarning().subscribe((isValid) => {
      if (isValid) {
        this.paneRef?.close();
      }
    });
  }

  private manageErrorsAfterSave(backEndError: HttpErrorResponse) {
    if (this.file?.content) {
      let sLine: number;
      let eLine: number;
      let sCol: number;
      let eCol: number;
      let msg: string;
      let frontEndError: boolean | ValidationError = false;
      try {
        frontEndError = XMLValidator.validate(this.file.content, {
          allowBooleanAttributes: true,
        });
      } catch (error) {
        frontEndError = false;
      }
      if (typeof frontEndError !== 'boolean') {
        const matches = frontEndError.err.msg.match(/line (\\d+), col (\\d+)/);
        if (
          matches &&
          typeof parseInt(matches[1]) === 'number' &&
          typeof parseInt(matches[2]) === 'number'
        ) {
          sLine = parseInt(matches[1]);
          eLine = frontEndError.err.line;
          sCol = parseInt(matches[2]);
          eCol = frontEndError.err.col;
          msg = frontEndError.err.msg;
        } else {
          sLine = frontEndError.err.line;
          eLine = frontEndError.err.line;
          sCol = 0;
          eCol = frontEndError.err.col;
          msg = frontEndError.err.msg;
        }
      } else {
        sLine = backEndError.error.lineNumber;
        eLine = backEndError.error.lineNumber;
        sCol = 0;
        eCol = backEndError.error.linePosition;
        msg = backEndError.error.message;
      }
      this.backEndErrorDecorations = [this.createError(sLine, eLine, sCol, eCol, msg, true)];
      this.displayErrorInEditor();
    }
  }

  private afterSaveFile(_saveStatus: saveStatus) {
    this.isSaving = false;

    if (this.file?.content && _saveStatus === saveStatus.SAVED) {
      this.lastSavedContent = this.file.content;
      this.fileHasModifications = this.isFileModified();
    }
  }
}
