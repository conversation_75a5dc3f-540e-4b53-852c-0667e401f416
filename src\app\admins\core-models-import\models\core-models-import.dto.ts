export interface CoreModelsArchiveSummaryDTO {
  readonly id: string;
  readonly cacheId: number;
  readonly version: string;
  readonly name: string;
  readonly explanation: string;
  readonly date: string;
  readonly user: string;
  readonly history: {
    readonly version: string;
    readonly comment: string;
  }[];
}

export interface CoreModelImportHistoryDTO {
  importHistory: FailedImportHistoryDTO[] | CompleteImportHistoryDTO[];
}

export type ImportedCoreModelConceptsDTO = Omit<TestedCoreModelConceptDTO, 'conflicts'>;
export interface ImportedDTO {
  readonly isValid?: boolean;
  readonly importedConcepts: ImportedCoreModelConceptsDTO[];
}
export interface TestedImportDTO {
  readonly isValid: boolean;
  readonly importedConcepts: TestedCoreModelConceptDTO[];
}

export interface TestedCoreModelConceptDTO {
  readonly type: string;
  readonly id: number;
  readonly tag: string;
  readonly idObjectType: number;
  readonly icon: number;
  readonly name: string;
  readonly modificationType: string;
  readonly conflicts: Array<string>;
}

export interface FailedImportHistoryDTO {
  readonly id: string;
  readonly date: string;
  readonly success: boolean;
  readonly username: string;
  readonly name: string;
  readonly version: string;
  readonly explanation: string;
  readonly comment: string;
  readonly importedConcepts: TestedCoreModelConceptDTO[];
}

type MapCompleteImportHistoryDTO<T> = {
  [P in keyof T]: P extends 'importedConcepts' ? ImportedCoreModelConceptsDTO[] : T[P];
};

export type CompleteImportHistoryDTO = MapCompleteImportHistoryDTO<FailedImportHistoryDTO>;
