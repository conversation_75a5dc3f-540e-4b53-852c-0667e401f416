import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxObjectFieldComponent } from './object-field.component';
import { AbstractControl, FormControl, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { AttributesMockService } from '../../../../testing.mock';
import {
  LegacyTxAttribute,
  LegacyTxAttributeRight,
} from '../../../../services/structure/models/attribute';
import { LegacyTxAttributesService } from '../../../../services/structure/services/attributes.service';
import { TxAttributeField } from '../../../../models/formConfiguration/businessClass/attribute-field';
import {
  LegacyTxData,
  LegacyTxDataString,
  LegacyTxDataType,
} from '../../../../services/structure/models/data';

const formControlsMock: FormControl[] = [
  new FormControl({ value: 2, disabled: true }),
  new FormControl(),
  new FormControl(),
];
const mockAttribute = new LegacyTxAttribute({
  id: 1,
  name: 'otherTest',
  dataType: LegacyTxDataType.ArchivedGraphic,
  idObjectType: 0,
  idAttributeParent: 0,
  tags: [],
  right: LegacyTxAttributeRight.None,
  isInherited: false,
  order: 0,
  idInheritedAttribute: 1,
  idLinkType: 0,
  option: {},
});
const mockField = new TxAttributeField();
const mockFieldNoAttr = new TxAttributeField();
const mockFieldMandatProp = new TxAttributeField();
mockField.assign({ id: 1, attribute: mockAttribute });
mockFieldNoAttr.assign({ id: 2 });
mockFieldMandatProp.assign({ id: 3, properties: { mandatory: true } });

describe('TxObjectFieldComponent', () => {
  let component: TxObjectFieldComponent;
  let fixture: ComponentFixture<TxObjectFieldComponent>;
  let element;
  let attributesService: LegacyTxAttributesService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxObjectFieldComponent],
      providers: [{ provide: LegacyTxAttributesService, useClass: AttributesMockService }],
    }).compileComponents();
    attributesService = TestBed.inject(LegacyTxAttributesService);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxObjectFieldComponent);
    component = fixture.componentInstance;
    element = fixture.nativeElement;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('testing ngAfterViewInit...', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should emit afterInit output', () => {
      const spyAfterInitEmit = jest.spyOn(component.afterInit, 'emit');
      component.ngAfterViewInit();
      expect(spyAfterInitEmit).toHaveBeenCalled();
    });

    it('should emit onChange output', () => {
      component.control = formControlsMock[0];
      const spyOnChangeEmit = jest.spyOn(component.onChange, 'emit');
      component.ngAfterViewInit();
      component.control.setValue('5');
      expect(spyOnChangeEmit).toHaveBeenCalled();
    });

    it('should not emit onChange output', () => {
      component.control = formControlsMock[0];
      component.control.setValue('5');
      const spyOnChangeEmit = jest.spyOn(component.onChange, 'emit');
      component.ngAfterViewInit();
      expect(spyOnChangeEmit).not.toHaveBeenCalled();
    });
  });

  describe('testing initProperties...', () => {
    beforeEach(() => {
      fixture.detectChanges();
      component.control = new FormControl();
    });

    it('should save attribute of field property in attribute property', () => {
      component.field = mockField;
      component.initProperties();
      expect(component.attribute).toBe(mockAttribute);
    });

    // it('should throw no attribute error', () => {
    //   component.attribute = null;
    //   component.value = 1;
    //   expect(component.initProperties).toThrowError('No attribute for this field...');
    // })

    it('should update the idAttribute from the attribute', () => {
      component.attribute = mockAttribute;
      component.initProperties();
      expect(component.idAttribute).toBe(mockAttribute.id);
    });

    it('should not update the idAttribute from the attribute', () => {
      component.idAttribute = 5;
      component.attribute = mockAttribute;
      component.initProperties();
      expect(component.idAttribute).not.toBe(mockAttribute.id);
    });

    it('should call initPropertiesFromField', () => {
      const spyInitFromField = jest.spyOn(component, 'initPropertiesFromField');
      component.field = mockField;
      component.initProperties();
      expect(spyInitFromField).toHaveBeenCalled();
    });

    it('should not call initPropertiesFromField', () => {
      const spyInitFromField = jest.spyOn(component, 'initPropertiesFromField');
      component.initProperties();
      expect(spyInitFromField).not.toHaveBeenCalled();
    });
    it('should create a control with good required value (1)', () => {
      component.required = true;
      component.value = null;
      component.initProperties();
      component.control.setValue(null);
      expect(component.control.hasError('required')).toBeTruthy();
    });

    it('should create a control with good required value (2)', () => {
      component.required = false;
      component.value = null;
      component.initProperties();
      expect(component.control.hasError('required')).toBeFalsy();
    });

    it('control should be touched', () => {
      component.initProperties();
      expect(component.control.touched).toBeTruthy();
    });

    it('control should be added in the form', () => {
      component.form = new FormGroup({ '2': formControlsMock[1] });
      component.control = formControlsMock[0];
      component.id = '1';
      component.initProperties();
      expect(component.form.get('1')).toBe(formControlsMock[0]);
    });

    it('should call setData', () => {
      const spySetdata = jest.spyOn(component, 'setData');
      const data = new LegacyTxData(23, 22);
      component.data = data;
      component.initProperties();
      expect(spySetdata).toHaveBeenCalledWith(data);
    });

    it('should not call setData', () => {
      const spySetdata = jest.spyOn(component, 'setData');
      component.initProperties();
      expect(spySetdata).not.toHaveBeenCalled();
    });
  });

  describe('testing initFormControl...', () => {
    it('Should call createformControl', () => {
      component.value = 5;
      component.disabled = true;
      const spyCreateFormControl = jest.spyOn(component, 'createFormControl');
      component.initFormControl('test');
      expect(spyCreateFormControl).toHaveBeenCalledWith('test', 5, [], true);
    });
  });

  describe('testing getValidators...', () => {
    it('should return array with required validator', () => {
      component.required = true;
      expect(component.getValidators()).toContain(Validators.required);
    });
  });

  describe('testing createFormControl...', () => {
    let name: string;
    let value;
    let validator: ValidatorFn[];
    let disabled;
    let control: AbstractControl;
    beforeEach(() => {
      name = 'test';
      value = 5;
      validator = [Validators.required];
      disabled = true;
      component.form = new FormGroup({});
      control = component.createFormControl(name, value, validator, disabled);
    });

    it('should return formControl with good value', () => {
      expect(control.value).toBe(5);
    });

    it('should return formControl with good disabled value', () => {
      expect(control.disabled).toBeTruthy();
    });

    // it('should return formControl with good required validator', () => {
    //   control.setValue(null);
    //   control.markAsTouched();
    //   expect(control.hasError('required')).toBeTruthy();
    // });

    it('should add the formControl to the FormGroup with the key parametred', () => {
      expect(component.form.controls['test']).toBe(control);
    });
  });

  describe('testing getDataToSave...', () => {
    beforeEach(() => {
      component.control = new FormControl();
    });

    it('should return null because value unchanged', () => {
      component.firstValue = 'test';
      component.value = 'test';
      expect(component.getDataToSave()).toBeNull;
    });

    it('should return null because value unvalid', () => {
      component.control.setValidators(Validators.email);
      component.control.setValue('test');
      expect(component.getDataToSave()).toBeNull;
    });

    it('should return a deleted data object', () => {
      component.idObject = 2;
      component.idAttribute = 3;
      component.firstValue = '1';
      component.control.setValue(null);
      expect(component.getDataToSave()).toEqual(LegacyTxData.removedData(2, 3));
    });

    it('should call getData if value changed to non-empty value', () => {
      const sypGetData = jest.spyOn(component, 'getData');
      component.firstValue = '1';
      component.control.setValue('2');
      component.getDataToSave();
      expect(sypGetData).toHaveBeenCalled();
    });
  });

  describe('testing isMandatory...', () => {
    it('should return value required if assigned (1)', () => {
      component.required = true;
      expect(component.isMandatory()).toBe(true);
    });

    it('should return value required if assigned (2)', () => {
      component.required = false;
      expect(component.isMandatory()).toBe(false);
    });

    it('should assign a value to required', () => {
      component.isMandatory();
      expect(component.required).toBeDefined();
    });

    it('should return true when field has mandatory property', () => {
      component.field = mockFieldMandatProp;
      expect(component.isMandatory()).toBeTruthy();
    });

    it('should return false when field has not mandatory property', () => {
      component.field = mockField;
      expect(component.isMandatory()).toBeFalsy();
    });
  });

  describe('testing setData', () => {
    it('should set value to control', () => {
      component.control = formControlsMock[0];
      const data = new LegacyTxDataString(1, 1, 'test');
      component.setData(data);
      expect(component.control.value).toBe('test');
    });
  });
});
