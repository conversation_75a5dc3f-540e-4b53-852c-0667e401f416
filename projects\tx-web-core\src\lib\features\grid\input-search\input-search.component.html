<mat-form-field
  appearance="fill"
  color="accent"
  [style.width]="fieldWidth"
  [ngStyle]="{ 'max-width': fieldWidth, width: '100%' }"
  class="no-label-input"
  [appAdjustWidth]="placeholderValue | translate">
  <input
    matInput
    [placeholder]="placeholderValue | translate"
    (keyup)="onKeyUpSearch($event)"
    (input)="keyupStream$.next(); onInputSearchChanged($event)"
    #inputSearch />
  <fa-icon
    *ngIf="inputSearch.value !== ''"
    matSuffix
    class="input-icon-remove"
    [icon]="['fal', 'times']"
    size="lg"
    (click)="clearInputValue(); keyupStream$.next()"></fa-icon>
  <fa-icon [icon]="['fal', 'search']" size="lg" matSuffix></fa-icon>
</mat-form-field>
