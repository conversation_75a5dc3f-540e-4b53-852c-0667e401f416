import { Pipe, PipeTransform } from '@angular/core';
import { TestedCoreModelConcept } from '../models/tested-core-model-concept.model';
import { ImportedCoreModelConcept } from '../models/imported-core-model-concept.model';
import { isTestedConcepts } from '../utils/core-models-import.utils';

@Pipe({
  name: 'isTestedConcepts',
})
export class IsTestedConceptsPipe implements PipeTransform {
  transform(value: TestedCoreModelConcept[] | ImportedCoreModelConcept[]): unknown {
    return isTestedConcepts(value);
  }
}
