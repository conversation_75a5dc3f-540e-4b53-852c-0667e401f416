import { Component, Input, OnInit } from '@angular/core';
import { LegacyTxAttributesService } from '../../../../services/structure/services/attributes.service';
import { TxObjectFieldComponent } from '../../_system/object-field/object-field.component';
import { _LegacyStringUtils } from '../../../../utilities/legacy-string-utils';

@Component({
  selector: 'tx-element-selection-base-field',
  templateUrl: './element-selection-base-field.component.html',
})
export class TxElementSelectionBaseFieldComponent extends TxObjectFieldComponent implements OnInit {
  @Input() elements: any[] = [];
  @Input() selection!: 'Single' | 'Multiple' | 'None';
  @Input() selectedElements: any[] = [];
  @Input() maxElementSize = 20;
  @Input() mainIcon!: string;
  @Input() textKey = 'text';
  @Input() valueKey = 'value';

  constructor(public attributeService: LegacyTxAttributesService) {
    super(attributeService);
  }
  ngOnInit() {
    super.ngOnInit();

    // add ellypsis on element with too long text value
    this.elements.forEach((e) => {
      if (!e.hintValue) {
        e.hintValue = e[this.textKey];
      }

      if (this.mainIcon && !e.avatarIconCss) {
        e.avatarIconCss = this.mainIcon;
      }

      e.text = _LegacyStringUtils.split(e[this.textKey], this.maxElementSize);
    });
  }

  getElement(value: any) {
    return this.elements.find((e) => e[this.valueKey] === value);
  }

  isSelected(element: any): boolean {
    return this.selectedElements.some((e) => e[this.valueKey] === element[this.valueKey]);
  }

  addElementSelected(element: any) {
    if (!this.isSelected(element)) {
      this.selectedElements.push(element);
    }
  }

  removeElementSelected(element: any) {
    if (this.isSelected(element)) {
      this.selectedElements = this.selectedElements.filter(
        (e) => e[this.valueKey] !== element[this.valueKey]
      );
    }
  }
}
