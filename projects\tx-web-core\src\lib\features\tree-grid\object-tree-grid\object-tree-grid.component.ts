import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import {
  MatSlideToggle,
  MatSlideToggleChange,
  MatSlideToggleModule,
} from '@angular/material/slide-toggle';
import { TxObject, TxObjectType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import {
  InputSearchEventInfo,
  TxGridChecked,
  TxGridColumn,
  TxGridRowSelectArgs,
  DataSourceInitialized,
  dataSourceInitialized,
  PaginatedTableDataSource,
} from '@bassetti-group/tx-web-core/src/lib/features/grid';
import {
  BeforeOpenCloseMenuEventArgs,
  TxContextMenuComponent,
  TxContextMenuEventArgs,
  TxContextMenuItem,
} from '@bassetti-group/tx-web-core/src/lib/ui/context-menu';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription, switchMap } from 'rxjs';
import { AsyncTxTreeGrid } from '../data-sources/async-tree-tx-data-source';
import { provideTreeGridDataSource, TxTreeGridComponent } from '../tree-grid.component';
import { TxDataTreeGridDirective } from '../data-tree-grid.directive';
import { TxTreeGridChildren } from '../tree-grid.interface';
import { TxTreeGridModule } from '../tree-grid.module';
import { flattenTree, parentToRootIds } from '../tree-utils';
import { ObjectTreeGridDataSource, SelectionEvent } from './object-tree-grid-data-source';
import { TxObjectContextMenu } from './object-tree-grid.model';
import { equals, uniq } from 'ramda';
import { TxTreeGrid } from '../tree-grid.models';

@Component({
  standalone: true,
  imports: [
    CommonModule,
    TxTreeGridModule,
    MatCheckboxModule,
    MatButtonModule,
    TranslateModule,
    FontAwesomeModule,
    MatSlideToggleModule,
    TxContextMenuComponent,
  ],
  selector: 'tx-object-tree-grid',
  templateUrl: './object-tree-grid.component.html',
  styleUrls: ['./object-tree-grid.component.scss'],
  providers: provideTreeGridDataSource<AsyncTxTreeGrid<TxObject>>(ObjectTreeGridDataSource),
})
export class ObjectTreeGridComponent
  extends TxDataTreeGridDirective<AsyncTxTreeGrid<TxObject>>
  implements OnInit, OnChanges, AfterViewInit, OnDestroy
{
  @Input() objectType?: TxObjectType;
  @Input() idObjectType?: number;
  @Input() idParentObjectFiltering?: number;
  @Input() enableSearching: boolean = true;
  @Input() showSelectionByDefault: boolean = true;
  @Input() folderCheckable: boolean = false;
  @Input() memorizeSelection: boolean = true;
  @Input() data?: TxObject[];
  @Input() checkedObjects?: TxObject[];
  @Input() displayMasterCheckBox: boolean = false;
  @Input() displayCheckAllButton: boolean = true;
  @Input() displayUnCheckAllButton: boolean = true;

  @Output() checkChange: EventEmitter<TxGridChecked<TxObject>> = new EventEmitter();
  @ViewChild(TxTreeGridComponent) treeGrid!: TxTreeGridComponent<
    TxObject & TxTreeGridChildren<TxObject>
  >;
  @ViewChild(TxContextMenuComponent) contextMenu!: TxContextMenuComponent;
  @ViewChild('toggle') toggle!: MatSlideToggle;
  private selectionSubscription!: Subscription;
  isAllSelected: boolean = false;
  deselectionList: Set<number> = new Set();
  trackDeselect: boolean = false;
  markToViewId?: number;
  searchInputValue: string = '';
  contextMenuDataIds?: number[];
  contextOnBlank: boolean = false;
  allSelectableDataSize?: number;
  TxObjectContextMenu = TxObjectContextMenu;
  menuItems: TxContextMenuItem[] = [
    {
      label: this.translate.instant('txWebCore.contextMenu.expand'),
      id: this.TxObjectContextMenu.Expand,
    },
    {
      label: this.translate.instant('txWebCore.contextMenu.collapse'),
      id: this.TxObjectContextMenu.Collapse,
    },
    {
      label: this.translate.instant('txWebCore.contextMenu.select'),
      id: this.TxObjectContextMenu.Select,
    },
    {
      label: this.translate.instant('txWebCore.contextMenu.deselect'),
      id: this.TxObjectContextMenu.Deselect,
    },
  ];
  disabledMenuItems: string[] = [];
  initEvent: DataSourceInitialized<AsyncTxTreeGrid<TxObject>> | undefined;
  txColumns: TxGridColumn<Pick<TxObject, 'name'>>[] = [
    { headerText: _('txWebCore.admins.columns.name'), field: 'name', visible: true },
  ];

  get isFolderNotHighlighted(): boolean {
    return this.toggle?.checked ?? this.treeGrid.gridComponent.highlightRowsPK.length <= 0;
  }
  get selectionCount(): string {
    const totalCounts = this.dataSource.getSelectedElements().length;
    return totalCounts ? `(${totalCounts})` : '';
  }

  constructor(
    private readonly elementRef: ElementRef,
    private readonly dataSource: ObjectTreeGridDataSource,
    private readonly cdr: ChangeDetectorRef,
    private readonly translate: TranslateService
  ) {
    super();
    this.showSpinner$ = dataSource.isLoading$;
  }

  ngOnInit(): void {
    this.selectionSubscription = this.dataSource.changes.subscribe({
      next: (data: SelectionEvent) => {
        if (data) {
          if (data.deselect) {
            data.selectionValues.forEach((id) => this.deselectionList.add(id));
          } else {
            data.selectionValues.forEach((id) => this.deselectionList.delete(id));
          }
          this.emitCheckedChanged(!data.deselect);
          this.isAllSelected = this.trackDeselect && Array.from(this.deselectionList).length == 0;
        }
      },
    });
    this.dataSource.multipleSelection = this.multipleSelection;
  }
  ngOnChanges(changes: SimpleChanges): void {
    this.handleMultipleSelectionChange(changes);
    this.handleIdObjectTypeChange(changes);
  }
  ngAfterViewInit(): void {
    this.cdr.detectChanges();
    this.showSelectedObjects();
    this.initEvent = this.initDataSource();
  }

  ngOnDestroy(): void {
    this.selectionSubscription?.unsubscribe();
  }
  handleSearch(searchEvent: InputSearchEventInfo): void {
    this.treeGrid.gridComponent.highlightRowsPK = [];
    this.searchInputValue = searchEvent.inputSearch.nativeElement.value;
    if (this.searchInputValue === '') {
      this.toggle.checked = false;
      this.dataSource.expandedNodes.clear();
      this.data ? this.dataSource.prepareAddData(this.data) : this.dataSource.addData();
      return;
    }
    if (
      ((searchEvent.event as KeyboardEvent)?.code === 'Enter' ||
        (searchEvent.event as KeyboardEvent)?.code === 'NumpadEnter') &&
      this.searchInputValue.length > 2
    ) {
      this.toggle.checked = true;
      this.data
        ? this.dataSource.loadSearchInputData(this.data, this.searchInputValue)
        : this.dataSource.loadSearchStringData(this.searchInputValue);
    }
  }

  showSelectionToggle(state: MatSlideToggleChange) {
    this.treeGrid.gridComponent.selectedRowPKValue = undefined;
    this.treeGrid.gridComponent.highlightRowsPK = [];
    this.dataSource.expandedNodes.clear();
    const selectedState = state.checked;

    // Prepare data based on `selectedState`
    if (this.data) {
      if (selectedState) {
        this.dataSource.prepareAddData(
          this.data
            .filter((obj) => this.dataSource.getSelectedElements().includes(obj.id))
            .map((obj) => {
              return { ...obj, idObjectParent: 0 };
            })
        );
      } else if (this.searchInputValue != '') {
        this.dataSource.loadSearchInputData(this.data, this.searchInputValue);
      } else {
        this.dataSource.prepareAddData(this.data);
      }
    } else if (selectedState) {
      this.dataSource.loadSelectedData();
    } else if (
      this.searchInputValue != '' &&
      this.treeGrid.gridComponent.gridSearch?.inputSearch?.nativeElement.value
    ) {
      this.treeGrid.gridComponent.gridSearch.clearInputValue();
      this.dataSource.addData();
    } else {
      this.dataSource.addData();
    }

    // Handle row selection logic based on `selectedState`
    if (selectedState || !this.markToViewId) {
      this.markToViewId = undefined;
      return;
    }

    if (!this.data) {
      const loadingSubscription = this.dataSource.isLoading$
        .pipe(
          switchMap((state) => {
            if (!state && this.markToViewId !== undefined) {
              return this.dataSource.loadRootToParentIds(this.markToViewId);
            }
            return [];
          })
        )
        .subscribe((nodeIds) => {
          if (nodeIds) {
            this.scrollToRow(nodeIds, this.elementRef.nativeElement, this.cdr);
            loadingSubscription.unsubscribe();
          }
        });
    } else {
      // Process when data is present
      const parentToRootIdArr = parentToRootIds(
        this.data,
        this.markToViewId,
        'id',
        'idObjectParent'
      );
      const selectedNodeId = parentToRootIdArr.pop();
      parentToRootIdArr.forEach((nodeId) => {
        this.dataSource.expandedNodes.add(nodeId);
      });
      this.dataSource.triggerUpdatePageRequest();
      if (selectedNodeId) {
        this.scrollToRow(selectedNodeId as number, this.elementRef.nativeElement, this.cdr);
      }
    }
  }
  handleSearchClear() {
    this.searchInputValue = '';
    this.toggle.checked = false;
    this.dataSource.expandedNodes.clear();
    this.data ? this.dataSource.prepareAddData(this.data) : this.dataSource.addData();
    this.dataSource.triggerUpdatePageRequest();
  }

  handleSelection(element: AsyncTxTreeGrid<TxObject>, event: MatCheckboxChange) {
    if (!this.multipleSelection) {
      if (this.dataSource.isSelected(element.id)) {
        this.dataSource.deselectElement(element.id);
      } else {
        this.dataSource.clearSelection();
        this.dataSource.select(element.id);
      }
    } else {
      this.dataSource.toggleSelection(element.id);

      if (!this.dataSource.isSelected(element.id)) {
        this.deselectionList.add(element.id);
      } else {
        this.deselectionList.delete(element.id);
      }
      this.isAllSelected = this.data
        ? this.dataSource.getSelectedElements().length == this.allSelectableDataSize
        : this.trackDeselect && Array.from(this.deselectionList).length == 0;
    }
    this.emitCheckedChanged(event.checked);
  }

  handleNativeSelect(args: TxGridRowSelectArgs<AsyncTxTreeGrid<TxObject>>) {
    const event = args.event as PointerEvent;
    this.contextMenu?.clickedOut(event);

    const currentId = args.data[0].id;
    const highlights = new Set(this.treeGrid.gridComponent.highlightRowsPK);
    const highlightsLen = this.treeGrid.gridComponent.highlightRowsPK.length;
    const isReset = !event.ctrlKey;

    // Clear highlights if CTRL isn't pressed
    if (isReset) highlights.clear();

    // Handle deselection if clicking an already highlighted row
    if (this.handleDeselection(currentId, highlights)) {
      return;
    }

    if (this.multipleSelection) {
      this.handleMultiSelection(event, currentId, highlights);
    }

    // Handle toggle mode or single selection
    if (this.toggle.checked && !event.ctrlKey && !event.shiftKey) {
      this.markToViewId = currentId;
    }
    // Toggle selectedRowPKValue on second click
    if (
      currentId === this.treeGrid.gridComponent.selectedRowPKValue &&
      !event.ctrlKey &&
      highlightsLen <= 0
    ) {
      this.treeGrid.gridComponent.selectedRowPKValue = undefined;
    } else {
      this.treeGrid.gridComponent.selectedRowPKValue = currentId;
    }

    // Update final highlights
    this.treeGrid.gridComponent.highlightRowsPK = Array.from(highlights);
  }

  handleMasterCheckbox() {
    this.isAllSelected = !this.isAllSelected;
    this.trackDeselect = this.isAllSelected;
    this.deselectionList.clear();

    if (this.data) {
      this.handleSelectionForData();
    } else {
      this.handleSelectionForChildren();
    }
  }

  selectFolderChildren(deselect?: boolean) {
    if (
      this.toggle.checked &&
      this.treeGrid.gridComponent.highlightRowsPK.length == 0 &&
      !this.treeGrid.gridComponent.selectedRowPKValue &&
      !deselect
    ) {
      this.dataSource.select(...this.dataSource.dataList.map((item) => item.id));
      this.emitCheckedChanged(true, true);
      return;
    }
    let highlightedPK = this.treeGrid.gridComponent.highlightRowsPK;
    if (this.treeGrid.gridComponent.selectedRowPKValue) {
      highlightedPK.push(this.treeGrid.gridComponent.selectedRowPKValue);
    }
    highlightedPK = uniq(highlightedPK);
    if (highlightedPK.length === 0 && deselect) {
      this.dataSource.clearSelection();
      this.emitCheckedChanged(false);
      return;
    }
    if (this.data) {
      if (highlightedPK.length === 0) {
        this.dataSource.select(...this.getNonParentDataItems());
        this.emitCheckedChanged(true);
        return;
      }
      const selectionValues = this.data
        .filter((item) => highlightedPK.includes(item.idObjectParent) && !item.isFolder)
        .map((item) => item.id);

      if (deselect) {
        this.dataSource.selection.deselect(...selectionValues);
        selectionValues.forEach((id) => this.deselectionList.add(id));
      } else {
        this.dataSource.selection.select(...selectionValues);
        selectionValues.forEach((id) => this.deselectionList.delete(id));
      }
      this.isAllSelected =
        this.dataSource.getSelectedElements().length == this.allSelectableDataSize;
    } else {
      if (highlightedPK.length === 0) {
        this.dataSource.loadChildrenSelectAll(this.emitCheckedChanged.bind(this));
        return;
      }
      this.dataSource.childSelection(highlightedPK, deselect, this.emitCheckedChanged.bind(this));
    }
  }

  isSelected(element: AsyncTxTreeGrid<TxObject>) {
    return this.dataSource.isSelected(element.id);
  }
  hasSelectedItems(): boolean {
    return this.dataSource.selection.hasValue();
  }

  getNonParentDataItems(): number[] {
    return (
      this.data?.reduce((acc: number[], item) => {
        if (!item.isFolder) {
          acc.push(item.id);
        }
        return acc;
      }, []) || []
    );
  }
  beforeContextMenu(args: BeforeOpenCloseMenuEventArgs): void {
    const targetElement = args.event.target as HTMLElement;
    const matRow = targetElement.closest('mat-row');
    if (matRow) {
      const dataId = matRow.getAttribute('data-id');
      if (!dataId) {
        return;
      }
      const numDataId = +dataId;
      const grid = this.treeGrid.gridComponent;

      grid.selectedRowPKValue = numDataId;

      if (grid.highlightRowsPK.includes(numDataId)) {
        this.contextMenuDataIds = uniq([...grid.highlightRowsPK, grid.selectedRowPKValue]);
      } else {
        grid.highlightRowsPK = [];
        this.contextMenuDataIds = [grid.selectedRowPKValue];
      }

      this.treeGrid.detectChanges();

      return;
    }

    this.contextOnBlank = true;
    this.treeGrid.gridComponent.selectedRowPKValue = undefined;
    this.treeGrid.gridComponent.highlightRowsPK = [];
    this.contextMenuDataIds = undefined;
    this.treeGrid.detectChanges();
  }
  selectOnContextMenu(args: TxContextMenuEventArgs): void {
    switch (args.item.id) {
      case this.TxObjectContextMenu.Expand:
        this.handleExpand();
        break;
      case this.TxObjectContextMenu.Collapse:
        this.handleCollapse();
        break;
      case this.TxObjectContextMenu.Select:
        this.handleSelect();
        this.emitCheckedChanged(true, true);
        break;
      case this.TxObjectContextMenu.Deselect:
        this.handleDeselect();
        this.emitCheckedChanged(false, true);
        break;
    }
    this.resetContextMenu();
  }
  scrollToRow(id: number, element: HTMLElement, cdr: ChangeDetectorRef) {
    cdr.detectChanges();
    const rowElement = element.querySelector(`mat-row[data-id="${id}"]`);

    if (rowElement) {
      rowElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      rowElement.classList.add('table-row-selected');
      setTimeout(() => {
        rowElement.classList.remove('table-row-selected');
      }, 1000);
    }
  }
  clearSelection(event: MatCheckboxChange) {
    event.source.checked = false;
    this.isAllSelected = false;
    this.dataSource.clearSelection();

    this.deselectionList.clear();
  }
  protected initDataSource(): DataSourceInitialized<AsyncTxTreeGrid<TxObject>> {
    this.dataSource.clearSelection();
    this.dataSource.idObjectType = this.idObjectType;
    this.dataSource.idParentObjectFiltering = this.idParentObjectFiltering;
    this.dataSource.idMapping = 'id';
    this.dataSource.parentMapping = 'idParent';
    this.dataSource.buildTree = true;
    this.allSelectableDataSize = this.getNonParentDataItems().length;
    if (this.canSelectCheckedObjectsAtDataInit(this.checkedObjects)) {
      this.dataSource.prepareAddData(
        this.checkedObjects.map((item) => ({ ...item, idObjectParent: 0 }))
      );
      this.dataSource.select(...this.checkedObjects.map((item) => item.id));
      this.isAllSelected = this.checkedObjects.length === this.allSelectableDataSize;
    } else if (this.data) {
      this.dataSource.prepareAddData(this.data);
    } else {
      this.dataSource.addData();
    }
    return dataSourceInitialized(this.dataSource as PaginatedTableDataSource<TxTreeGrid<TxObject>>);
  }
  private showSelectedObjects() {
    if (this.canSelectCheckedObjectsAtDataInit(this.checkedObjects)) {
      this.toggle.checked = true;
    } else {
      this.toggle.checked = false;
    }
  }
  private canSelectCheckedObjectsAtDataInit(
    checkedObjects: TxObject[] | undefined
  ): checkedObjects is TxObject[] {
    return (
      this.showSelectionByDefault && checkedObjects !== undefined && checkedObjects?.length > 0
    );
  }
  private matchAndFill(ids: number[], objects: AsyncTxTreeGrid<TxObject>[]): TxObject[] {
    const objectMap = new Map(objects.map((obj) => [obj.id, obj]));
    return ids.flatMap((id) => {
      const object = objectMap.get(id);
      if (object === undefined) {
        return [];
      }
      return [object.txObject];
    });
  }
  private handleSelectionForData() {
    if (this.isAllSelected) {
      this.dataSource.select(...this.getNonParentDataItems());
    } else {
      this.dataSource.clearSelection();
    }
  }

  private handleSelectionForChildren() {
    if (this.isAllSelected) {
      this.dataSource.loadChildrenSelectAll();
    } else {
      this.dataSource.clearSelection();
    }
  }
  private handleExpand(): void {
    if (this.contextOnBlank) {
      this.expandAllNodes();
      return;
    }
    const { parentIds } = this.dataSource.separateParentChildren(this.contextMenuDataIds || []);
    parentIds.forEach((id) => {
      if (!this.dataSource.expandedNodes.has(id) && id !== undefined) {
        this.dataSource.toggleNode(id, !!this.data);
      }
    });
  }

  private handleCollapse(): void {
    if (this.contextOnBlank) {
      this.dataSource.clearExpandedNodes();
      return;
    }
    const { parentIds } = this.dataSource.separateParentChildren(this.contextMenuDataIds || []);
    parentIds.forEach((id) => {
      if (this.dataSource.expandedNodes.has(id) && id !== undefined) {
        this.dataSource.toggleNode(id);
      }
    });
  }

  private handleSelect(): void {
    if (!this.multipleSelection) {
      this.dataSource.clearSelection();
    }
    if (this.contextOnBlank) {
      this.dataSource.loadChildrenSelectAll();
      this.isAllSelected = true;
    } else if (this.contextMenuDataIds !== undefined) {
      this.data ? this.selectFromData() : this.selectFromChildren();
    }
  }

  private handleDeselect(): void {
    if (this.contextOnBlank) {
      this.dataSource.clearSelection();
      this.isAllSelected = false;
    } else if (this.contextMenuDataIds !== undefined) {
      this.data ? this.deselectFromData() : this.deselectFromChildren();
    }
  }

  private selectFromData(): void {
    if (this.data) {
      let selectionValues = this.data
        .filter(
          (item) =>
            this.contextMenuDataIds?.includes(item.idObjectParent as number) && !item.isFolder
        )
        .map((item) => item.id);
      if (this.multipleSelection) {
        this.dataSource.select(...selectionValues);
      } else {
        this.dataSource.select(selectionValues[0]);
      }
      this.dataSource.select(...selectionValues);
      selectionValues.forEach((id) => this.deselectionList.delete(id));
      this.isAllSelected =
        this.dataSource.getSelectedElements().length === this.allSelectableDataSize;
    }
  }

  private deselectFromData(): void {
    if (this.data) {
      let selectionValues = this.data
        .filter(
          (item) =>
            this.contextMenuDataIds?.includes(item.idObjectParent as number) && !item.isFolder
        )
        .map((item) => item.id);
      this.dataSource.selection.deselect(...selectionValues);
      selectionValues.forEach((id) => this.deselectionList.add(id));
      this.isAllSelected =
        this.dataSource.getSelectedElements().length === this.allSelectableDataSize;
    }
  }

  private selectFromChildren(): void {
    if (this.contextMenuDataIds) {
      this.dataSource.childSelection([...this.contextMenuDataIds]);
    }
  }

  private deselectFromChildren(): void {
    if (this.contextMenuDataIds) {
      this.dataSource.childSelection([...this.contextMenuDataIds], true);
    }
  }
  private resetContextMenu(): void {
    this.contextMenuDataIds = undefined;
    this.contextOnBlank = false;
  }
  private expandAllNodes() {
    if (this.data) {
      this.dataSource.loadAllLocalNodeData();
      return;
    }
    this.dataSource.loadAllNodeData();
  }
  private emitCheckedChanged(
    checked: boolean,
    passCheck: boolean = false,
    processedData?: TxObject[]
  ): void {
    let allData = flattenTree(this.dataSource.dataList);
    let selectedData =
      processedData ??
      this.matchAndFill(this.dataSource.getSelectedElements() as number[], allData);
    if (!equals(this.checkedObjects ?? [], selectedData) || passCheck) {
      this.checkChange.emit({ objects: selectedData, checked });
    }
  }
  private handleMultipleSelectionChange(changes: SimpleChanges): void {
    if (changes.multipleSelection === undefined) {
      return;
    }

    this.dataSource.multipleSelection = changes.multipleSelection.currentValue;

    if (changes.multipleSelection.currentValue) {
      this.menuItems = this.createMenuItemsWithSelection();
    } else {
      this.menuItems = this.createMenuItemsWithoutSelection();
    }
  }

  private createMenuItemsWithSelection() {
    return [
      {
        label: this.translate.instant('txWebCore.contextMenu.expand'),
        id: this.TxObjectContextMenu.Expand,
      },
      {
        label: this.translate.instant('txWebCore.contextMenu.collapse'),
        id: this.TxObjectContextMenu.Collapse,
      },
      {
        label: this.translate.instant('txWebCore.contextMenu.select'),
        id: this.TxObjectContextMenu.Select,
      },
      {
        label: this.translate.instant('txWebCore.contextMenu.deselect'),
        id: this.TxObjectContextMenu.Deselect,
      },
    ];
  }

  private createMenuItemsWithoutSelection() {
    return [
      {
        label: this.translate.instant('txWebCore.contextMenu.expand'),
        id: this.TxObjectContextMenu.Expand,
      },
      {
        label: this.translate.instant('txWebCore.contextMenu.collapse'),
        id: this.TxObjectContextMenu.Collapse,
      },
    ];
  }

  private handleIdObjectTypeChange(changes: SimpleChanges): void {
    if (changes?.idObjectType) {
      const { previousValue, currentValue } = changes.idObjectType;
      if (previousValue && previousValue !== currentValue) {
        this.dataSource.objectType = undefined;
        if (this.memorizeSelection) {
          this.showSelectedObjects();
        } else {
          this.checkedObjects = undefined;
          this.dataSource.clearSelection();
          if (this.toggle) this.toggle.checked = false;
        }
        this.treeGrid.gridComponent.highlightRowsPK = [];
        this.treeGrid.gridComponent.selectedRowPKValue = undefined;
        this.initEvent = this.initDataSource();
      }
    }
  }
  private handleDeselection(currentId: any, highlights: Set<any>): boolean {
    if (highlights.has(currentId)) {
      highlights.delete(currentId);
      this.treeGrid.gridComponent.selectedRowPKValue = undefined;
      this.treeGrid.gridComponent.highlightRowsPK = Array.from(highlights);
      return true;
    }
    return false;
  }
  private handleMultiSelection(event: PointerEvent, currentId: any, highlights: Set<any>) {
    if (event.ctrlKey) {
      this.handleCtrlClickSelection(currentId, highlights);
    }
    if (event.shiftKey && this.treeGrid.gridComponent.highlightRowsPK.length <= 1) {
      this.handleShiftClickSelection(currentId, highlights);
    }
  }
  private handleCtrlClickSelection(currentId: any, highlights: Set<any>) {
    const selected = this.treeGrid.gridComponent.selectedRowPKValue;
    if (selected !== undefined) {
      highlights.add(selected);
      this.treeGrid.gridComponent.selectedRowPKValue = undefined;
    }
    highlights.add(currentId);
  }
  private handleShiftClickSelection(currentId: any, highlights: Set<any>) {
    const selected = this.treeGrid.gridComponent.selectedRowPKValue;
    const matRowElements = this.elementRef.nativeElement.getElementsByClassName('mat-mdc-row');
    this.elementRef.nativeElement?.selection?.empty();
    if (!matRowElements?.[0]) {
      return;
    }

    const dataIds = Array.from(matRowElements).map(
      (element: any) => +element.getAttribute('data-id')
    );

    if (selected !== undefined) {
      const selectedIdx = dataIds.findIndex((item) => item === selected) ?? -1;
      const currentIdx = dataIds.findIndex((item) => item === currentId) ?? -1;
      if (selectedIdx >= 0 && currentIdx >= 0) {
        const [start, end] =
          selectedIdx < currentIdx ? [selectedIdx, currentIdx] : [currentIdx, selectedIdx];
        for (let i = start; i <= end; i++) {
          highlights.add(dataIds[i]);
        }
      }
    }
  }
}
