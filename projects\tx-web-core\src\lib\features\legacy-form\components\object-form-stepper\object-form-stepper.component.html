<mat-progress-bar
  *ngIf="isLoaderActive; else emptyTemplate"
  color="accent"
  mode="indeterminate"></mat-progress-bar>

<ng-container *ngIf="steps && steps.length > 0">
  <ng-container *ngIf="steps.length > 1; then withStepper; else withoutStepper"></ng-container>
</ng-container>

<ng-template #withStepper>
  <mat-horizontal-stepper [linear]="isLinear" #stepper>
    <mat-step *ngFor="let step of steps; let index = index" [label]="step.name">
      <tx-object-form
        #formObject
        [tabs]="step.formConfig.tabs"
        [idObject]="idObject"
        [idObjectType]="idObjectType"
        [inRightPane]="inRightPane"
        [attributesTags]="attributesTags"
        [mandatoriesIdsAtt]="mandatoriesIdsAtt"
        [editionMode]="editionMode"
        [formConfigTag]="step.formConfig.tag"
        (loadEvent)="onDataLoaded($event)"></tx-object-form>
      <div>
        <button *ngIf="isBackVisible()" mat-button matStepperPrevious>Back</button>
        <button *ngIf="isNextVisible()" mat-button color="primary" matStepperNext>Next</button>
      </div>
    </mat-step>
  </mat-horizontal-stepper>
</ng-template>

<ng-template #withoutStepper>
  <tx-object-form
    #formObject
    [tabs]="steps[0].formConfig.tabs"
    [formConfigTag]="steps[0].formConfig.tag"
    [idObject]="idObject"
    [idObjectType]="idObjectType"
    [inRightPane]="inRightPane"
    [attributesTags]="attributesTags"
    [mandatoriesIdsAtt]="mandatoriesIdsAtt"
    [editionMode]="editionMode"
    (loadEvent)="onDataLoaded($event)"
    [showBarAndButton]="showBarAndButton"
    [indexTabToFocusFirst]="indexTabToFocusFirst"></tx-object-form>
</ng-template>

<ng-template #emptyTemplate>
  <div style="height: 4px"></div>
</ng-template>
