import { TxConcept } from './tx-concept.model';
import { TxAttribute } from './tx-attribute.model';
import { DataBaseRights } from './tx-data.model';

export enum TxObjectTypeType {
  Standard = 'ottStandard',
  User = 'ottUser',
  Source = 'ottSource',
  Information = 'ottInformation',
  Listing = 'ottEnumeration',
  Portal = 'ottPortal',
  Associativity = 'ottAssociativity',
}

export enum TxLockingType {
  Undefined = 'ltUndefined',
  None = 'ltNone',
  Auto = 'ltAuto',
  Manual = 'ltManual',
}

export interface TxObjectType extends TxConcept {
  idObjectTypeParent?: number;
  icon: number;
  isFolder: boolean;
  type: TxObjectTypeType;
  hasDistinctName: boolean;
  isVisible: boolean;
  lockingType: TxLockingType;
  lockingDuration: number;
  displayResultInTextSearch: boolean;
  right: DataBaseRights;
  order: number;
}

export interface EmitObjectType {
  ot: TxObjectType;
  ischeked: boolean;
}

export interface EmitObjectTypeList {
  ot: TxObjectType[];
  ischeked: boolean;
}

export interface TxObjectTypeDetailed extends TxObjectType {
  attributes?: TxAttribute[];
}

export interface TxObjectTypeFormPane extends TxObjectType {
  associatedObjectTypesIds: number[];
  idOTAssociativity0: number;
  idOTAssociativity1: number;
}
