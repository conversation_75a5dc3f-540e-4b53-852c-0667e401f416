import * as unzipit from 'unzipit';
import { toSpecificIconsUrls } from '../object-type-icon-utils';
import { SpecificIconsUrls } from '../object-type-icon.model';

const fileMockCommon = {
  name: '',
  nameBytes: new Uint8Array(2),
  size: 0,
  compressedSize: 0,
  comment: '',
  commentBytes: new Uint8Array(2),
  lastModDate: new Date(),
  isDirectory: true,
  encrypted: true,
  externalFileAttributes: 0,
  versionMadeBy: 0,
};
const comment = {
  comment: '',
  commentBytes: new Uint8Array(),
};
const resolvePromise = (a: any, fileNames: string[]): Promise<any> => {
  const fileInfos = fileNames.reduce((acc, file) => {
    return {
      ...acc,
      ['' + file]: {
        blob: (type?: string) =>
          Promise.resolve(
            new File(['' + file], '' + file, {
              type: 'image/png',
            }) as Blob
          ),
        arrayBuffer: () => (a as Blob).arrayBuffer(),
        text: () => (a as Blob).text(),
        json: () => (a as Blob).text(),
        ...fileMockCommon,
      },
    };
  }, {} as any);

  return Promise.resolve({
    zip: { ...comment },
    entries: fileInfos,
  });
};
describe('Object Type Icon Utils', () => {
  window.URL.createObjectURL = jest.fn((obj) => 'blob:http://localhost/' + (obj as File).name);
  const mockUnzip = jest.spyOn(unzipit, 'unzip');
  mockUnzip.mockImplementation((a) => {
    if ((a as File).name === 'successfully') {
      return resolvePromise(a, ['302.png', '301.svg']);
    }
    return Promise.resolve({
      zip: { ...comment },
      entries: {
        ['error.png']: {
          blob: (type?: string) =>
            Promise.resolve(
              new File(['302.png'], '302.png', {
                type: 'image/png',
              }) as Blob
            ),
          arrayBuffer: () => (a as Blob).arrayBuffer(),
          text: () => (a as Blob).text(),
          json: () => (a as Blob).text(),
          ...fileMockCommon,
        },
      },
    });
  });

  it('should successfully unzip files and return SpecificIconsUrls', async () => {
    const mockZipBlob = new File(['301.svg', '302.png'], 'successfully', {
      type: 'application/zip',
    });
    const expectedSpecificIconsUrls: SpecificIconsUrls = {
      iconShocks: new Map([[301, 'blob:http://localhost/301.svg']]),
      txIcons: new Map([[302, 'blob:http://localhost/302.png']]),
    };
    const result = await toSpecificIconsUrls(mockZipBlob);
    expect(result).toEqual(expectedSpecificIconsUrls);
  });
});
