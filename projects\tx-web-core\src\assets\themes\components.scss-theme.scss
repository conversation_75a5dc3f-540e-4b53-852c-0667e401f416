@use '@angular/material' as mat;

// mixin name will be used in main style.scss
@mixin components-theme($theme) {
  // retrieve variables from theme
  // (all possible variables, use only what you really need)
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);
  $white: #ffffff;

  // generics //
  .primary {
    color: mat.m2-get-color-from-palette($primary) !important;
  }
  .accent {
    color: mat.m2-get-color-from-palette($accent) !important;
  }
  .warn {
    color: mat.m2-get-color-from-palette($warn) !important;
  }
  .primary-bg {
    background-color: mat.m2-get-color-from-palette($primary) !important;
    color: mat.m2-get-color-from-palette($primary, default-contrast) !important;
  }
  .accent-bg {
    background-color: mat.m2-get-color-from-palette($accent) !important;
    color: mat.m2-get-color-from-palette($accent, default-contrast) !important;
  }
  .warn-bg {
    background-color: mat.m2-get-color-from-palette($warn) !important;
    color: mat.m2-get-color-from-palette($warn, default-contrast) !important;
  }
  .primary-hover:hover {
    color: mat.m2-get-color-from-palette($primary) !important;
  }
  .contrasted-primary {
    color: mat.m2-get-color-from-palette($primary, default-contrast) !important;
  }

  .h1-title {
    color: mat.m2-get-color-from-palette($foreground, title);
  }
  .h2-subtitle {
    color: mat.m2-get-color-from-palette($foreground, subtitle);
  }
  .h2-section-title {
    color: mat.m2-get-color-from-palette($foreground, title);
  }
  .h2-section-subtitle {
    color: mat.m2-get-color-from-palette($foreground, subtitle);
  }
  .introduction-text {
    color: mat.m2-get-color-from-palette($foreground, grey80);
  }
  .show-btn {
    color: mat.m2-get-color-from-palette($accent);
  }
  .legend {
    color: mat.m2-get-color-from-palette($foreground, grey60);
  }
  .annotation {
    color: mat.m2-get-color-from-palette($foreground, grey60);
  }

  .e-warning-color {
    color: #ffca1c !important;
  }
  .background-e-warning {
    background-color: #ffca1c !important;
    color: $white;
  }
  .background-warning-pastel {
    background-color: mat.m2-get-color-from-palette($background, pastel-yellow) !important;
    color: mat.m2-get-color-from-palette($foreground, base);
  }
  .background-e-success {
    background-color: #22b24b !important;
    color: $white;
  }
  .background-success-pastel {
    background-color: mat.m2-get-color-from-palette($background, pastel-green) !important;
    color: mat.m2-get-color-from-palette($foreground, base);
  }
  .background-e-information {
    background-color: #489bd5 !important;
    color: $white;
  }
  .background-e-error {
    background-color: mat.m2-get-color-from-palette($warn) !important;
    color: mat.m2-get-color-from-palette($warn, default-contrast);
  }
  .background-error-pastel {
    background-color: mat.m2-get-color-from-palette($background, pastel-red) !important;
    color: mat.m2-get-color-from-palette($foreground, base);
  }
  .error {
    color: mat.m2-get-color-from-palette($warn) !important;
  }
  .color-grey80 {
    color: mat.m2-get-color-from-palette($foreground, grey80) !important;
  }
  .color-grey60 {
    color: mat.m2-get-color-from-palette($foreground, grey60) !important;
  }
  .color-grey40 {
    color: mat.m2-get-color-from-palette($foreground, grey40) !important;
  }
  .color-grey20 {
    color: mat.m2-get-color-from-palette($foreground, grey20) !important;
  }
  .color-success-pastel {
    color: mat.m2-get-color-from-palette($foreground, pastel-green) !important;
  }
  .link-grey80:hover {
    color: mat.m2-get-color-from-palette($foreground, grey80) !important;
  }
  .primary-disable {
    background-color: mat.m2-get-color-from-palette($primary, 50);
    color: mat.m2-get-contrast-color-from-palette($primary, 50);
  }
  .background {
    background-color: mat.m2-get-color-from-palette($background, base) !important;
  }
  .background-grey5,
  .panel-header {
    background-color: mat.m2-get-color-from-palette($foreground, grey5);
  }
  .background-grey10 {
    background-color: mat.m2-get-color-from-palette($foreground, grey10);
  }
  .background-grey20 {
    background-color: mat.m2-get-color-from-palette($foreground, grey20);
  }
  .hover-grey20:hover {
    background-color: mat.m2-get-color-from-palette($foreground, grey20);
    cursor: pointer;
  }
  .background-accent {
    background-color: mat.m2-get-color-from-palette($accent);
    color: mat.m2-get-color-from-palette($accent, default-contrast);
  }
  .background-accent-light {
    background-color: mat.m2-get-color-from-palette($accent, 800, 0.3);
    color: mat.m2-get-contrast-color-from-palette($accent, 800);
  }
  .background-accent-light:hover {
    background-color: mat.m2-get-color-from-palette($accent, 800, 0.6);
  }
  .background-primary {
    background-color: mat.m2-get-color-from-palette($primary);
    color: mat.m2-get-color-from-palette($primary, default-contrast);

    .close-button:hover {
      background-color: mat.m2-get-color-from-palette($primary, 400);
      color: mat.m2-get-contrast-color-from-palette($primary, 400);
    }
  }

  .border-grey {
    border: 1px solid mat.m2-get-color-from-palette($foreground, borders);
  }
  .border-accent-dashed {
    border: 1px dashed mat.m2-get-color-from-palette($accent) !important;
  }

  .chip-filled {
    background-color: mat.m2-get-color-from-palette($foreground, grey10) !important;
    border: 1px solid mat.m2-get-color-from-palette($foreground, grey10) !important;
    color: mat.m2-get-color-from-palette($foreground, grey80) !important;
  }
  .chip-normal {
    background-color: transparent !important;
    border: 1px solid mat.m2-get-color-from-palette($foreground, text) !important;
    color: mat.m2-get-color-from-palette($foreground, text) !important;
  }
  .chip-light {
    background-color: transparent !important;
    border: 1px solid mat.m2-get-color-from-palette($foreground, grey40) !important;
    color: mat.m2-get-color-from-palette($foreground, grey40) !important;
  }
  .chip-active {
    background-color: transparent !important;
    border: 1px solid mat.m2-get-color-from-palette($foreground, pastel-green) !important;
    color: mat.m2-get-color-from-palette($foreground, pastel-green) !important;
  }
  .chip-inactive {
    background-color: transparent !important;
    border: 1px solid mat.m2-get-color-from-palette($foreground, grey60) !important;
    color: mat.m2-get-color-from-palette($foreground, grey60) !important;
  }

  .content-layer,
  .history-content,
  .pane-modal-sreen {
    color: mat.m2-get-color-from-palette($foreground, text);
  }

  .customErrorDialog,
  .dialog-content-container,
  .button-container {
    background-color: mat.m2-get-color-from-palette($background, base);
  }
}
