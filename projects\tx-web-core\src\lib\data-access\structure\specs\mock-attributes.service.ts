import { MockService } from 'ng-mocks';
import { Observable, of } from 'rxjs';
import { TxAttribute, TxAttributeRight, TxDataType, TxNumericalFloatFormat } from '../../../business-models';
import { TxAttributesService } from '../attributes.service';


export const MOCK_OBJECTS: TxAttribute[] = [
  {
      
      order: 1,
      idAttributeParent: 0,
      isTrackable: false,
      isList: true,
      precision: 5,
      isLBInclusive: true,
      isUBInclusive: true,
      isDisplayedInMainUnit: false,
      isInherited: false,
      dataType: 3,
      floatFormat: TxNumericalFloatFormat.General,
      digits: 0,
      idObjectType: 1,
      isTableDisplayed: false,
      isTransposed: false,
      isSeriesNameDisplayed: false,
      isIndexesDisplayed: false,
      color: "#000000",
      isUnderlined: false,
      linkDisplayMode: 0,
      right: TxAttributeRight.Modify,
      description: "",
      explanation: "",
      name: "Description",
      tags: [
          "attPeopleLastName",
          "ContactsLastName"
      ],
      id: 1,
  },
  {
      order: 0,
      idAttributeParent: 1,
      isTrackable: false,
      idFileType: 0,
      isList: false,
      precision: 0,
      lowerBound: 0,
      upperBound: 0,
      isLBInclusive: false,
      isUBInclusive: false,
      isDisplayedInMainUnit: false,
      isInherited: false,
      dataType: 3,
      floatFormat: TxNumericalFloatFormat.General,
      digits: 0,
      idObjectType: 1,
      isTableDisplayed: false,
      isTransposed: false,
      isSeriesNameDisplayed: false,
      isIndexesDisplayed: false,
      color: "#000000",
      isUnderlined: false,
      linkDisplayMode: 0,
      right: TxAttributeRight.Modify,
      description: "",
      explanation: "",
      name: "Nom",
      tags: ["attPeopleLastName",
          "ContactsLastName"],
      id: 2
  },
  {
      order: 2,
      idAttributeParent: 2,
      isTrackable: false,
      isList: false,
      precision: 0,
      isLBInclusive: false,
      isUBInclusive: false,
      isDisplayedInMainUnit: false,
      isInherited: false,
      dataType: 3,
      floatFormat: TxNumericalFloatFormat.General,
      digits: 0,
      idObjectType: 1,
      isTableDisplayed: false,
      isTransposed: false,
      isSeriesNameDisplayed: false,
      isIndexesDisplayed: false,
      color: "#000000",
      isUnderlined: false,
      linkDisplayMode: 0,
      right: TxAttributeRight.Modify,
      description: "",
      explanation: "",
      name: "E-Mail",
      tags: [
          "txAtt_User_Email"
      ],
      id: 3
  },
 
]
export const MOCK_ATTRIBUTES_SERVICE = MockService(TxAttributesService, {
  listAttributesFromObjectType: (idObjectType: number, types: TxDataType[] = []): Observable<TxAttribute[]> => of(MOCK_OBJECTS),

  listInParentOrder : (concepts: TxAttribute[] = MOCK_OBJECTS): TxAttribute[] =>MOCK_OBJECTS,

  listFromIds : (ids : number[]) : Observable<TxAttribute[]> =>{
    const attributes = MOCK_OBJECTS.filter((attr) => ids.includes(attr.id));
    return of(attributes);
  },
  getIconPath: (idAttribute: number): string  => "./img/icons/svg/184.svg",

  isLinkAttribute:(attribute: TxAttribute): boolean => false,
})
