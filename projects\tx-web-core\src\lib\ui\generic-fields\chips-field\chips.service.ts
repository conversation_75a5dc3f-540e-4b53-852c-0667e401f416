import { Injectable } from '@angular/core';
import {
  TxChip,
  ChipsOptions,
  TxChipFile,
  TxChipStandard,
  txChipStandard,
  txChipFile,
} from './models';
import { lastValueFrom, take } from 'rxjs';
import {
  TxObject,
  TxData,
  TxDataFile,
  TxDataLink,
  TxDataTab,
  TxFile,
} from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TxFileAttributeService } from '@bassetti-group/tx-web-core/src/lib/data-access/files/file-attribute.service';
import { FilesUtils } from '@bassetti-group/tx-web-core/src/lib/utilities/helpers/files-utils';

@Injectable({ providedIn: 'root' })
export class ChipsService {
  constructor(public fileService: TxFileAttributeService) {}
  chipsFromNames(
    options: ChipsOptions,
    names: string | string[],
    selectedChips: string | string[] = []
  ): TxChipStandard[] {
    const chips: TxChipStandard[] = [names]
      .flat()
      .filter((name) => name !== '')
      .map((name) =>
        txChipStandard({
          ...options,
          name,
          selected: this.isChipSelected([selectedChips].flat(), name),
          icon: options.icon ? ['fal', options.icon] : undefined,
        })
      );
    return chips;
  }
  async chipsFromData(
    options: ChipsOptions,
    data: TxData,
    selectedChips: string | string[] = []
  ): Promise<TxChip[]> {
    if (data instanceof TxDataTab) {
      return this.chipsFromTxDataTab(options, data.value, selectedChips);
    } else if (data instanceof TxDataFile) {
      return await this.chipsFromTxDataFile(options, data.files, selectedChips);
    } else if (data instanceof TxDataLink) {
      return this.chipsFromTxDataObjects(options, data.linkedObjects, selectedChips);
    }
    return [];
  }

  private isChipSelected(selectedChips: string[], name: string): boolean {
    return selectedChips.includes(name);
  }

  private chipsFromTxDataTab(
    options: ChipsOptions,
    value: string[] | string,
    selectedChips: string | string[]
  ): TxChipStandard[] {
    return this.chipsFromNames(options, value, selectedChips);
  }

  private async chipsFromTxDataFile(
    options: ChipsOptions,
    files: TxFile[],
    selectedChips: string | string[]
  ): Promise<TxChipFile[]> {
    const chipsPromises = files.map(async (file) => {
      return await this.fileChip(options, file, selectedChips);
    });
    const chips = await Promise.all(chipsPromises);
    return chips;
  }

  private chipsFromTxDataObjects(
    options: ChipsOptions,
    objects: TxObject[],
    selectedChips: string | string[]
  ): TxChipStandard[] {
    return objects.map((object) => {
      return txChipStandard({
        ...options,
        name: object.name,
        selected: this.isChipSelected([selectedChips].flat(), object.name),
        icon: options.icon ? ['fal', options.icon] : undefined,
      });
    });
  }

  private async fileChip(
    options: ChipsOptions,
    file: TxFile,
    selectedChips: string | string[]
  ): Promise<TxChipFile> {
    const chip: TxChipFile = txChipFile({
      ...options,
      name: file.name,
      selected: this.isChipSelected([selectedChips].flat(), file.name),
      icon: FilesUtils.getFileIcon(file.name),
      viewableFile: file.view && FilesUtils.isAFilePicture(file.name),
      idFile: file.idArchivedFile,
    });

    const image =
      chip.viewableFile && chip.idFile ? await lastValueFrom(this.getImage(chip.idFile)) : null;

    return image ? this.setFileUrl(image, chip) : chip;
  }

  private getImage(idFile: number) {
    return this.fileService?.download(idFile).pipe(take(1));
  }

  private setFileUrl(image: Blob, chip: TxChipFile): TxChipFile {
    const objectURL = URL.createObjectURL(image);
    const updatedChip = {
      ...chip,
      fileUrl: objectURL,
    };
    return updatedChip;
  }
}
