<div>
  <tx-input-text-field
    *ngIf="!readMode; else readField"
    [class]="classes"
    [form]="form"
    [control]="control"
    [id]="id"
    [label]="label"
    [value]="control.value ?? ''"
    [required]="required"
    [disabled]="disabled"
    [order]="order"
    [information]="information"
    [icon]="icon"
    [width]="width"
    [readMode]="readMode"
    [inTextArea]="inTextArea"
    [maxLength]="maxLength"
    [textAreaHeight]="textAreaHeight"
    (fieldChange)="fieldChange.emit()"
    (onComplete)="onComplete.emit()">
  </tx-input-text-field>

  <!-- <div *ngIf="readMode" class="full-field">
    <mat-label class="read-form-label form-label mat-form-label" [matTooltip]="labelToolTip" matTooltipClass="mat-label-tooltip" matTooltipShowDelay="500" matTooltipPosition="above" >
      {{label}}
    </mat-label>
    <div class="read-form-field">
      <span >{{control.value}}</span>
    </div>
  </div> -->

  <ng-template #readField>
    <tx-read-text-field
      [class]="field.classes"
      [form]="form"
      [control]="control"
      [id]="id"
      [label]="label"
      [value]="control.value ?? ''"
      [required]="required"
      [disabled]="disabled"
      [order]="order"
      [information]="information"
      [icon]="icon"
      [width]="width"
      [readMode]="readMode"
      [withBottomSpace]="inTextArea">
    </tx-read-text-field>
  </ng-template>
</div>
