import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxInputMinMaxMeanComponent } from './input-min-max-mean.component';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { Component, DebugElement } from '@angular/core';
import { MinMax, MinMaxMean } from './models';
import { HarnessLoader } from '@angular/cdk/testing';
import { MatSelectHarness } from '@angular/material/select/testing';
import { TxUnit } from '../../../features/legacy-form';
import { TestbedHarnessEnvironment } from '@angular/cdk/testing/testbed';

@Component({
  template: `<tx-min-max-mean
      [formControl]="controlMinMaxMean"
      [required]="true"
      [units]="units"></tx-min-max-mean>
    <tx-min-max-mean
      [formControl]="controlMinMax"
      [lowerBound]="1"
      [upperBound]="10"
      [lowerBoundIncluded]="true"
      [upperBoundIncluded]="true"></tx-min-max-mean> `,
})
class HostInputMinMaxMeanComponent {
  units = [
    new TxUnit({ name: 'm', id: 1 }),
    new TxUnit({ name: 'cm', id: 2 }),
    new TxUnit({ name: 'km', id: 3 }),
  ];
  controlMinMaxMean = new FormControl<MinMaxMean>({
    min: 0,
    max: 0,
    mean: 0,
    unit: 0,
  });
  controlMinMax = new FormControl<MinMax>({ min: 5, max: 5, unit: 1 });
}

describe('InputMinMaxMeanComponent', () => {
  let componentMinMax: TxInputMinMaxMeanComponent;
  let componentMinMaxMean: TxInputMinMaxMeanComponent;
  let componentMinMaxDebugElement: DebugElement;
  let componentMinMaxMeanDebugElement: DebugElement;
  let hostComponent: HostInputMinMaxMeanComponent;
  let hostFixture: ComponentFixture<HostInputMinMaxMeanComponent>;
  let loaderTest: HarnessLoader;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [HostInputMinMaxMeanComponent],
      imports: [
        TxInputMinMaxMeanComponent,
        ReactiveFormsModule,
        NoopAnimationsModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    hostFixture = TestBed.createComponent(HostInputMinMaxMeanComponent);
    hostComponent = hostFixture.componentInstance;
    const componentDebugElement = hostFixture.debugElement.queryAll(
      By.directive(TxInputMinMaxMeanComponent)
    );
    componentMinMaxMeanDebugElement = componentDebugElement[0];
    componentMinMaxDebugElement = componentDebugElement[1];
    componentMinMaxMean = componentMinMaxMeanDebugElement.componentInstance;
    componentMinMax = componentMinMaxDebugElement.componentInstance;
    loaderTest = TestbedHarnessEnvironment.loader(hostFixture);
    hostFixture.detectChanges();
  });
  it('should create', () => {
    expect(componentMinMaxMean).toBeTruthy();
    expect(componentMinMax).toBeTruthy();
  });
  it('input is required', () => {
    const inputEl = hostFixture.debugElement.query(By.css('input'));
    inputEl.nativeElement.value = '';
    inputEl.triggerEventHandler('input', { target: inputEl.nativeElement });
    hostFixture.detectChanges();
    expect(hostComponent.controlMinMaxMean.errors).toEqual({ required: true });
  });

  it('error if min is greater then max', () => {
    const inputEl = hostFixture.debugElement.queryAll(By.css('input'))[3];
    inputEl.nativeElement.value = '7';
    inputEl.triggerEventHandler('input', { target: inputEl.nativeElement });
    hostFixture.detectChanges();
    expect(hostComponent.controlMinMax.errors).toEqual({
      minUpperThanMax: true,
    });
  });

  it('error if mean is not between min and max', () => {
    const inputEl = hostFixture.debugElement.queryAll(By.css('input'))[2];
    inputEl.nativeElement.value = '4';
    inputEl.triggerEventHandler('input', { target: inputEl.nativeElement });
    hostFixture.detectChanges();
    expect(hostComponent.controlMinMaxMean.errors).toEqual({
      meanOutOfMinAndMax: true,
    });
  });

  it('error if min is lower than lowerBound', () => {
    const inputEl = hostFixture.debugElement.queryAll(By.css('input'))[3];
    inputEl.nativeElement.value = '0';
    inputEl.triggerEventHandler('input', { target: inputEl.nativeElement });
    hostFixture.detectChanges();
    expect(hostComponent.controlMinMax.errors).toEqual({
      isLowThanLowBoundValue: 1,
    });
  });

  it('error if max is greater than upperBound', () => {
    const inputElMin = hostFixture.debugElement.queryAll(By.css('input'))[3];
    inputElMin.nativeElement.value = '11';
    inputElMin.triggerEventHandler('input', {
      target: inputElMin.nativeElement,
    });
    const inputElMax = hostFixture.debugElement.queryAll(By.css('input'))[4];
    inputElMax.nativeElement.value = '13';
    inputElMax.triggerEventHandler('input', {
      target: inputElMax.nativeElement,
    });
    hostFixture.detectChanges();
    expect(hostComponent.controlMinMax.errors).toEqual({
      isSupThanUpperBoundValue: 10,
    });
  });

  it('input min entered', () => {
    const inputEl = hostFixture.debugElement.query(By.css('input'));
    inputEl.nativeElement.value = '3';
    inputEl.triggerEventHandler('input', { target: inputEl.nativeElement });
    hostFixture.detectChanges();
    expect(hostComponent.controlMinMaxMean.value?.min).toBe(3);
  });

  it('input max entered', () => {
    const inputEl = hostFixture.debugElement.queryAll(By.css('input'))[1];
    inputEl.nativeElement.value = '9';
    inputEl.triggerEventHandler('input', { target: inputEl.nativeElement });
    hostFixture.detectChanges();
    expect(hostComponent.controlMinMaxMean.value?.max).toBe(9);
  });

  it('input mean entered', () => {
    const inputEl = hostFixture.debugElement.queryAll(By.css('input'))[2];
    inputEl.nativeElement.value = '6';
    inputEl.triggerEventHandler('input', { target: inputEl.nativeElement });
    hostFixture.detectChanges();
    expect(hostComponent.controlMinMaxMean.value?.mean).toBe(6);
  });

  it('input unit chosen', async () => {
    const select = await loaderTest.getHarness(MatSelectHarness);
    await select.open();
    await select.clickOptions({ text: 'cm' });
    expect(hostComponent.controlMinMaxMean.value?.unit).toBe(2);
  });
});
