import { Mo<PERSON><PERSON><PERSON>po<PERSON>, MockProvider, MockService } from 'ng-mocks';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxObjectsTypeDropdownComponent } from './objects-type-dropdown.component';
import { ReactiveFormsModule } from '@angular/forms';
import { TxObjectsTypeDropdownService } from './objects-type-dropdown.service';
import { TxDropdownTreeComponent } from '../../dropdown-tree/dropdown-tree.component';
import { delay, of } from 'rxjs';
import { TxObjectsTypeService } from '../../../../data-access/structure';
import {
  TxObjectTypeIconService,
  MOCK_OBJECT_TYPE_ICON_SERVICE,
} from '../../../../data-access/session';

describe('ObjectsTypeDropdownComponent', () => {
  let component: TxObjectsTypeDropdownComponent;
  let fixture: ComponentFixture<TxObjectsTypeDropdownComponent>;
  let otService: TxObjectsTypeService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TxObjectsTypeDropdownComponent, MockComponent(TxDropdownTreeComponent)],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: TxObjectsTypeService, useValue: MockService(TxObjectsTypeService) },
        MockProvider(TxObjectTypeIconService, MOCK_OBJECT_TYPE_ICON_SERVICE, 'useValue'),
      ],
    })
      .overrideComponent(TxObjectsTypeDropdownComponent, {
        set: {
          providers: [
            {
              provide: TxObjectsTypeDropdownService,
              useValue: MockService(TxObjectsTypeDropdownService),
            },
          ],
        },
      })
      .compileComponents();

    otService = TestBed.inject(TxObjectsTypeService);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxObjectsTypeDropdownComponent);
    component = fixture.componentInstance;
    // resolve a problem of 'ExpressionChangedAfterItHasBeenCheckedError' with isContentInit value
    otService.filter = jest.fn().mockReturnValue(of([]).pipe(delay(100)));
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
