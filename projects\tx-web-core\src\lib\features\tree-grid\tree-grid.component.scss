:host {
  position: relative;
  --default-tree-grid-border-bottom-width: 1px;
  display: block;
  height: 100%;
}

.tx-tree-spinner {
  position: absolute;
  z-index: 2;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.mat-column-tree-control {
  padding: 0;
}

.remove-border {
  ::ng-deep .mat-mdc-cell {
    border: none;
  }
}
.tree-border {
  height: 100%;
}
.tree-control-btn {
  height: 1.45rem;
  width: 1.45rem;
  padding: 0;
}
.tree-grid__tree-control-cell {
  border-bottom-width: var(
    --grid-border-bottom-width,
    var(--default-tree-grid-border-bottom-width)
  );
}
.tree-control-flex {
  flex: 0 0 2.25rem;
}

.tree-control-margin {
  margin-right: 1rem;
}
