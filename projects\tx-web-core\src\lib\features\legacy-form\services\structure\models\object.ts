import { LegacyTxData } from './data';

let counter = -1;

export class LegacyTxObject extends Object {
  image!: string;
  tooltip!: string;
  options: any;
  data?: LegacyTxData[];

  public static assign(object: any): LegacyTxObject {
    const newObject = new LegacyTxObject(
      object.name,
      object.id,
      object.idObjectType,
      object.idObjectParent || 0,
      object.order,
      object.isParent,
      object.isFolder,
      object.idOwnerObject,
      object.creationDate,
      object.searchName || object.name,
      object.tags
    );
    newObject.image = object.image;

    return newObject;
  }

  public static new(idObjectType: number, name: string, image: string = ''): LegacyTxObject {
    const txObject = new LegacyTxObject(name, counter, idObjectType);
    counter--;
    txObject.image = image;
    return txObject;
  }

  constructor(
    public name: string,
    public id?: number,
    public idObjectType?: number,
    public idObjectParent?: number,
    public order?: number,
    public isParent?: boolean,
    public isFolder?: boolean,
    public idOwnerObject?: number,
    public creationDate?: string,
    public searchName?: string,
    public tags?: string
  ) {
    super();
    this.options = {};
  }
}
