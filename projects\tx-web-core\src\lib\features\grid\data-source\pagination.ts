import { Observable } from 'rxjs';
import { InjectionToken } from '@angular/core';
import { SortRequest } from './sort.util';
import { TxGridFilterOnData, TxGridFilterByColumn } from '../grid.interface';

export interface Page<T> extends Pagination {
  data: T[];
}

export interface PageRequest<T> {
  pageIndex: number;
  pageSize: number;
  sort?: SortRequest<T>;
}

export type PaginationEndpointFn<T> = (
  request: PageRequest<T>,
  query: TxGridFilterByColumn<T> | TxGridFilterOnData<T> | undefined
) => Observable<Page<T>>;

export const PAGINATION_END_POINT = new InjectionToken<PaginationEndpointFn<unknown>>(
  'PaginationEndpoint'
);
export interface Pagination {
  pageSize: number;
  pageIndex: number;
  length: number;
}
