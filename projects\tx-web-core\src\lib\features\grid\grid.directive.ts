import { Directive, EventEmitter, Input, Output } from '@angular/core';
import {
  TxGridColumn,
  TxGridDoubleClickArgs,
  TxGridRowSelectArgs,
  TxSaveEventArgs,
} from './grid.interface';
import { InputSearchEventInfo } from './input-search/input-search.component';
import { GridFilter } from './grid-filter-menu/grid-filter-menu.component';
import { PaginatedTableDataSource } from './data-source/paginated-table-data-source';

export interface DataSourceInitialized<T extends {}> {
  isInit: true;
  dataSource: PaginatedTableDataSource<T>;
}

export const dataSourceInitialized = <T extends {}>(
  dataSource: PaginatedTableDataSource<T>
): DataSourceInitialized<T> => ({ isInit: true, dataSource });

@Directive({
  selector: '[txGrid]',
  standalone: true,
})
export class GridDirective<T extends {}> {
  /**
   * Input property containing an array of TxGridColumn objects representing the columns in the grid.
   */
  @Input({ required: true }) columns!: TxGridColumn<T>[];
  /**
   * Input property to allow double click edit feature in the grid.
   */
  @Input() allowEditing = false;
  /**
   * Input property to enable or disable row selection in the grid.
   * If set to true, users can select rows in the grid.
   */
  @Input() isRowSelectable = false;
  @Input() allowEditOnDblClick = false;

  /**
   * Permit to define the row height, by default 30px.
   */
  @Input() rowHeight: number | string = 30;
  @Input() enableSearchHighlight: boolean = false;
  @Input() enableRowTooltip: boolean = false;
  @Input() initSortDirection: 'asc' | 'desc' = 'asc';

  @Output() rowDoubleClick: EventEmitter<TxGridDoubleClickArgs<T>> = new EventEmitter();
  /**
   * Output event emitter triggered when a row is selected in the grid.
   * Emits the selected row data.
   */
  @Output() rowSelected: EventEmitter<TxGridRowSelectArgs<T>> = new EventEmitter();
  @Output() cellEdit: EventEmitter<TxGridDoubleClickArgs<T>> = new EventEmitter();
  @Output() actionCellComplete: EventEmitter<TxSaveEventArgs<T>> = new EventEmitter();
  @Output() searchInputChange: EventEmitter<InputSearchEventInfo> = new EventEmitter();
  @Output() searchClear: EventEmitter<void> = new EventEmitter();
  @Output() filterChange: EventEmitter<GridFilter | string> = new EventEmitter();
  constructor() {}
}
