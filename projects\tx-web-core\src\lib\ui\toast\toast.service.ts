import { Overlay } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import {
  ComponentRef,
  EnvironmentInjector,
  Injectable,
  createEnvironmentInjector,
} from '@angular/core';
import { ToastComponent } from './toast.component';
import { ToastData } from './toast.models';
import { ToastRef } from './toast.reference';
import { TOAST_DATA_TOKEN } from './toast-data-token';

@Injectable({
  providedIn: 'root',
})
export class ToastService {
  private toasts: ToastRef[];
  private notifications: ToastData[];

  constructor(private overlay: Overlay, private parentInjector: EnvironmentInjector) {
    this.toasts = [];
    this.notifications = [];
  }

  show(data: ToastData): ToastComponent {
    const positionStrategy = this.getPositionStrategy(this.toasts.length - 1);
    const overlayRef = this.overlay.create({ positionStrategy });

    const toastRef = new ToastRef(overlayRef, this);
    this.toasts.push(toastRef);

    const injector = this.getInjector(data, toastRef, this.parentInjector);
    const toastPortal = new ComponentPortal(ToastComponent, null, injector);

    const compRef: ComponentRef<ToastComponent> = overlayRef.attach(toastPortal);

    /* add a notif */
    if (data.isPersistent) {
      this.notifications.unshift(data);
    }

    return compRef.instance;
  }

  afterDisplay(ref: ToastRef) {
    const index = this.toasts.findIndex((t) => t === ref);
    if (index < this.toasts.length - 1) {
      const positionStrategy = this.getPositionStrategy(index);
      this.toasts[index + 1].overlay.updatePositionStrategy(positionStrategy);
    }
  }

  close(ref: ToastRef) {
    const index = this.toasts.findIndex((t) => t === ref);
    this.toasts.splice(index, 1);
    this.toasts.forEach((t, i) => {
      if (i >= index) {
        const positionStrategy = this.getPositionStrategy(i - 1);
        this.toasts[i].overlay.updatePositionStrategy(positionStrategy);
      }
    });
  }

  getPositionStrategy(index: number) {
    return this.overlay
      .position()
      .global()
      .top(this.getPosition(index))
      .right(16 + 'px');
  }

  getPosition(index: number) {
    const lastToastIsVisible = this.toasts[index]?.isVisible();
    const position = lastToastIsVisible ? this.toasts[index].getPosition().bottom - 8 : 55 + 8;

    return position + 'px';
  }

  getNotifications(): ToastData[] {
    return this.notifications;
  }

  hasUnreadNotifications(): boolean {
    return this.notifications.some((notif) => notif.isUnread);
  }
  private getInjector(data: ToastData, toastRef: ToastRef, parentInjector: EnvironmentInjector) {
    return createEnvironmentInjector(
      [
        { provide: TOAST_DATA_TOKEN, useValue: data },
        { provide: ToastRef, useValue: toastRef },
      ],
      parentInjector
    );
  }
}
