import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { BreadcrumdMockComponent } from 'src/app/app.testing.mock';

import { FileRoutingComponent } from './file-routing.component';

describe('FileRoutingComponent', () => {
  let component: FileRoutingComponent;
  let fixture: ComponentFixture<FileRoutingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [FileRoutingComponent, BreadcrumdMockComponent],
      imports: [RouterTestingModule.withRoutes([])],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FileRoutingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
