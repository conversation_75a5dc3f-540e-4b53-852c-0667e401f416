@use '@angular/material' as mat;

@mixin grid-theme($theme) {
  $background: map-get($theme, background);
  $foreground: map-get($theme, foreground);
  $primary: map-get($theme, primary);

  .mat-mdc-table,
  .mat-mdc-paginator {
    background-color: mat.m2-get-color-from-palette($background, card);
  }

  .table-row {
    &:hover {
      background-color: mat.m2-get-color-from-palette($foreground, grey10);
    }
  }

  .cdk-drag-preview {
    background-color: mat.m2-get-color-from-palette($background, card);
  }

  .table-row-header-cell {
    color: mat.m2-get-color-from-palette($foreground, grey70);
  }

  .table-row-selected {
    background-color: mat.m2-get-color-from-palette($foreground, grey20);

    &:hover {
      background-color: mat.m2-get-color-from-palette($foreground, grey20);
    }
  }

  .hide-placeholder {
    &.cdk-drag-placeholder {
      background-color: mat.m2-get-color-from-palette($primary);
    }
  }

  .cdk-drag-placeholder {
    background-color: mat.m2-get-color-from-palette($foreground, grey40);
  }

  .table-paginator {
    border-left: 0.06rem solid mat.m2-get-color-from-palette($foreground, grey20);
    border-right: 0.06rem solid mat.m2-get-color-from-palette($foreground, grey20);
    border-bottom: 0.06rem solid mat.m2-get-color-from-palette($foreground, grey20);
  }

  .column-resize-handle:hover {
    background-color: mat.m2-get-color-from-palette($foreground, grey10) !important;
  }
  .mat-column-groupHeader {
    background-color: mat.m2-get-color-from-palette($foreground, grey10);
  }

  .expand-btn {
    background-color: mat.m2-get-color-from-palette($foreground, grey10);
  }

  .warning-cell {
    background-color: mat.m2-get-color-from-palette($background, pastel-red);
  }
}
