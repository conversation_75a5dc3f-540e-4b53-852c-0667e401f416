import { waitForAsync, TestBed } from '@angular/core/testing';
import { MultiSelectComponent, MultiSelectModule } from '@syncfusion/ej2-angular-dropdowns';

import { TxComboboxComponent } from './combobox.component';

describe('TxComboboxComponent', () => {
  let component: TxComboboxComponent;
  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxComboboxComponent, MultiSelectComponent],
      imports: [MultiSelectModule],
    }).compileComponents();
    const fixture = TestBed.createComponent(TxComboboxComponent);
    component = fixture.debugElement.componentInstance;
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
