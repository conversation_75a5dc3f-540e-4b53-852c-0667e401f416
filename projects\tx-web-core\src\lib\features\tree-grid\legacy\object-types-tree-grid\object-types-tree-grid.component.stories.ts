import { applicationConfig, type Meta, type StoryObj } from '@storybook/angular';
import { TxObjectTypesTreeGridComponent } from './object-types-tree-grid.component';
import { TxObjectTypesTreeGridService } from './object-types-tree-grid.service';
import { DataBaseRights, TxLockingType, TxObjectTypeType } from '../../../business-models';

const meta: Meta<TxObjectTypesTreeGridComponent> = {
  component: TxObjectTypesTreeGridComponent,
  title: 'Tree Grid/TxObjectTypesTreeGridComponent',
  tags: ['autodocs'],
  decorators: [
    applicationConfig({
      providers: [TxObjectTypesTreeGridService],
    }),
  ],
};
export default meta;
type Story = StoryObj<TxObjectTypesTreeGridComponent>;

export const Primary: Story = {
  args: {
    data: [
      {
        isParent: true,
        id: 5,
        tags: [],
        name: 'Description',
        txObject: {
          id: 5,
          idObjectTypeParent: 0,
          icon: 262,
          isFolder: true,
          type: TxObjectTypeType.Standard,
          hasDistinctName: true,
          isVisible: true,
          lockingType: TxLockingType.Auto,
          lockingDuration: 0,
          displayResultInTextSearch: true,
          right: DataBaseRights.DbrRead,
          order: 5,
          name: '',
          tags: [],
        },
      },
      {
        isParent: true,
        idParent: 5,
        id: 1,
        tags: [],
        icon: './img/icons/svg/0.svg',
        name: 'Informations générales',
        txObject: {
          id: 1,
          idObjectTypeParent: 0,
          icon: 262,
          isFolder: true,
          type: TxObjectTypeType.Standard,
          hasDistinctName: true,
          isVisible: true,
          lockingType: TxLockingType.Auto,
          lockingDuration: 0,
          displayResultInTextSearch: true,
          right: DataBaseRights.DbrRead,
          order: 1,
          name: '',
          tags: [],
        },
      },
      {
        id: 2,
        tags: ['attPrenom'],
        name: 'Prénom',
        icon: './img/icons/svg/184.svg',
        idParent: 1,
        txObject: {
          id: 2,
          idObjectTypeParent: 0,
          icon: 262,
          isFolder: false,
          type: TxObjectTypeType.Standard,
          hasDistinctName: true,
          isVisible: true,
          lockingType: TxLockingType.Auto,
          lockingDuration: 0,
          displayResultInTextSearch: true,
          right: DataBaseRights.DbrRead,
          order: 1,
          name: '',
          tags: [],
        },
      },
      {
        id: 3,
        tags: ['attNom'],
        name: 'Nom',
        icon: './img/icons/svg/184.svg',
        idParent: 1,
        txObject: {
          id: 3,
          idObjectTypeParent: 0,
          icon: 262,
          isFolder: false,
          type: TxObjectTypeType.Standard,
          hasDistinctName: true,
          isVisible: true,
          lockingType: TxLockingType.Auto,
          lockingDuration: 0,
          displayResultInTextSearch: true,
          right: DataBaseRights.DbrRead,
          order: 1,
          name: '',
          tags: [],
        },
      },
      {
        id: 4,
        tags: ['attEmail'],
        name: 'E-mail',
        icon: './img/icons/svg/184.svg',
        idParent: 1,
        txObject: {
          id: 4,
          idObjectTypeParent: 0,
          icon: 262,
          isFolder: false,
          type: TxObjectTypeType.Standard,
          hasDistinctName: true,
          isVisible: true,
          lockingType: TxLockingType.Auto,
          lockingDuration: 0,
          displayResultInTextSearch: true,
          right: DataBaseRights.DbrRead,
          order: 1,
          name: '',
          tags: [],
        },
      },
    ],
    objectTypesFilteredIds: [],
    isDataLoaded: true,
    showTagsColumn: false,
    showCheckbox: false,
    folderCheckable: false,
    checkedIds: [],
  },
};
