import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxEscfPopupComponent } from './escf-popup.component';
import { MockComponent, MockInstance, MockService } from 'ng-mocks';
import { Renderer2 } from '@angular/core';
import { LegacyTxObjectTypeService } from '../../../../../../services/structure/services/object-type.service';
import { LegacyTxObjectsService } from '../../../../../../services/structure/services/objects.service';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatTabsModule } from '@angular/material/tabs';
import { TxEscfFilteredElementsComponent } from '../escf-filtered-elements/escf-filtered-elements.component';
import { LegacyTxEscfTreeComponent } from '../escf-tree/escf-tree.component';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { MatTooltipModule } from '@angular/material/tooltip';

describe('EscfPopupComponent', () => {
  let component: TxEscfPopupComponent;
  let fixture: ComponentFixture<TxEscfPopupComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        TxEscfPopupComponent,
        MockComponent(TxEscfFilteredElementsComponent),
        MockComponent(LegacyTxEscfTreeComponent),
        MockComponent(FaIconComponent),
      ],
      imports: [BrowserAnimationsModule, MatTabsModule, MatTooltipModule],
      providers: [
        { provide: Renderer2, useValue: MockInstance(Renderer2) },
        {
          provide: LegacyTxObjectsService,
          useValue: MockService(LegacyTxObjectsService),
        },
        {
          provide: LegacyTxObjectTypeService,
          useValue: MockService(LegacyTxObjectTypeService),
        },
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxEscfPopupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
