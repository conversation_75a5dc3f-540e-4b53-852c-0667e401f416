import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class TxErrorService {
  private errors = new Subject<any>();
  private requestBlackList: string[] = [];

  constructor() {}

  public addError = (error: any): void => this.errors.next(error);

  public getErrors = () => this.errors.asObservable();

  public registerUnhandledRequestURL(url: string): void {
    this.requestBlackList.push(url);
  }

  public isRequestBlackListed(url: string): boolean {
    return this.requestBlackList.some((rq) => url.includes(rq));
  }
}
