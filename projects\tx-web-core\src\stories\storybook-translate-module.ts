import { NgModule } from '@angular/core';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { StorybookTranslationLoader } from './storybook-translation-loader';
import { HttpClient } from '@angular/common/http';

const storybookTranslateLoader = (http: HttpClient): StorybookTranslationLoader => {
  return new StorybookTranslationLoader(http, './i18n/', '.json');
};

@NgModule({
  imports: [
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: storybookTranslateLoader,
        deps: [HttpClient],
      },
    }),
    TranslateModule,
  ],
})
export class StorybookTranslateModule {
  constructor(translateService: TranslateService) {
    translateService.setDefaultLang('en');
    translateService.use('en');
  }
}
