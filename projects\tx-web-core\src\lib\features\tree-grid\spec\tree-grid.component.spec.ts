import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';
import { of } from 'rxjs';
import { By } from '@angular/platform-browser';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { TestbedHarnessEnvironment } from '@angular/cdk/testing/testbed';
import { MatButtonHarness } from '@angular/material/button/testing';
import { TxTreeGridComponent } from '../tree-grid.component';
import { PaginatedTreeGridDataSource } from '../data-sources/paginated-tree-grid-data-source';
import {
  PaginatedTableDataSource,
} from '@bassetti-group/tx-web-core/src/lib/features/grid';


describe('TxTreeGridComponent', () => {
  let component: TxTreeGridComponent<any>;
  let fixture: ComponentFixture<TxTreeGridComponent<any>>;
  const data = of([{ id: 1, name: 'Node 1', children: [{ id: 2, name: 'Node 2' }] }]);
  const TRANSLATIONS = {
    en: {},
    fr: {},
  };
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        TxTreeGridComponent,
        TranslateTestingModule.withTranslations(TRANSLATIONS),
        FontAwesomeTestingModule,
      ],
      providers:  [
        {
          provide: PaginatedTreeGridDataSource,
          useValue: new PaginatedTreeGridDataSource(of([])),
        },
        {
          provide: PaginatedTableDataSource,
          useExisting: PaginatedTreeGridDataSource,
        },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxTreeGridComponent);
    component = fixture.componentInstance;
    component.columns = [
      { field: 'id', header: 'ID' },
      { field: 'name', header: 'Name' },
    ] as any[];
    component.primaryKey = 'id';
    component.data = data;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set data input', () => {
    fixture.detectChanges();
    const matRowDes = fixture.debugElement.queryAll(By.css('.mat-mdc-row'));
    const matRowEls = matRowDes.map((row) => row.nativeElement);
    expect(matRowEls.length).toStrictEqual(2);
  });

  it('should toggle tree node', async () => {
    const loader = TestbedHarnessEnvironment.loader(fixture);
    const iconButton = await loader.getHarness(
      MatButtonHarness.with({ selector: '.mdc-icon-button' })
    );
    await iconButton.click();
    fixture.detectChanges();
    const matRowDes = fixture.debugElement.queryAll(By.css('.mat-mdc-row'));
    const matRowEls = matRowDes.map((row) => row.nativeElement);
    expect(matRowEls.length).toStrictEqual(1);
  });
});
