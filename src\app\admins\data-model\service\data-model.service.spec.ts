import { TestBed } from '@angular/core/testing';
import { DataModelService } from './data-model.service';
import {
  TxConfigService,
  TxFileTypesService,
  TxObjectsTypeService,
  TxTableTypesService,
} from '@bassetti-group/tx-web-core';
import {
  ConfigServiceMock,
  FileTypesServiceMock,
  ObjectsTypeServiceMock,
  TableTypeServiceMock,
  UnitsServiceMock,
} from 'src/app/app.testing.mock';
import { UnitsService } from 'src/app/core/services/structure/units.service';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

describe('Service: DataModel', () => {
  let service: DataModelService;
  beforeEach(() => {
    TestBed.configureTestingModule({
    imports: [],
    providers: [
        DataModelService,
        { provide: TxConfigService, useClass: ConfigServiceMock },
        { provide: TxObjectsTypeService, useClass: ObjectsTypeServiceMock },
        { provide: UnitsService, useClass: UnitsServiceMock },
        { provide: TxTableTypesService, useClass: TableTypeServiceMock },
        { provide: TxFileTypesService, useClass: FileTypesServiceMock },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
    ]
});

    service = TestBed.inject(DataModelService);
  });

  it('should create service', () => {
    expect(service).toBeTruthy();
  });
});
