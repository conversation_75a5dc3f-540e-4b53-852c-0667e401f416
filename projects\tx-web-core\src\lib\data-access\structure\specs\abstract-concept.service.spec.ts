import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MOCK_TX_CONFIG_SERVICE, TxConfigService } from '../../config';
import { TxAbstractConceptService } from '../abstract-concept.service';
import { Injectable } from '@angular/core';
import { MockProvider, MockService } from 'ng-mocks';
import { TxConcept } from '../../../business-models';
import { MOCK_OBJECT_TYPE_ICON_SERVICE, TxObjectTypeIconService } from '../../session';

@Injectable()
class ConceptService extends TxAbstractConceptService<TxConcept> {
  protected urlListAllConcepts = ' api/test';
}
describe('Service: Abstract Concept', () => {
  let service: ConceptService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        ConceptService,
        MockProvider(TxConfigService, MockService(MOCK_TX_CONFIG_SERVICE)),
        MockProvider(TxObjectTypeIconService, MOCK_OBJECT_TYPE_ICON_SERVICE, 'useValue'),
      ],
    });
    service = TestBed.inject(ConceptService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
