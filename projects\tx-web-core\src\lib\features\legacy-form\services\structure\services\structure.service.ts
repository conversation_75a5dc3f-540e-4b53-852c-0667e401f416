import { Injectable } from '@angular/core';
import { TxUnitService } from './unit.service';
import { Subject } from 'rxjs';
import { TxFileTypeService } from './file-type.service';
import { TxLinkTypeService } from './link-type.service';
import { LegacyTxTableTypeService } from './table-type.service';

@Injectable({
  providedIn: 'root',
})
export class TxStructureService {
  structureLoaded: Subject<boolean> = new Subject<boolean>();

  initialized = false;

  constructor(
    public fileTypeService: TxFileTypeService,
    public tableTypeService: LegacyTxTableTypeService,
    public linkTypeService: TxLinkTypeService,
    public unitService: TxUnitService
  ) {}

  checkValid() {
    if (
      this.tableTypeService.initialized &&
      this.linkTypeService.initialized &&
      this.fileTypeService.initialized &&
      this.unitService.initialized
    ) {
      this.structureLoaded.next(true);
      this.structureLoaded.complete();
    }
  }

  start() {
    this.fileTypeService.start();
    this.tableTypeService.start();
    this.linkTypeService.start();
    this.unitService.start();
    this.fileTypeService.fileTypesLoaded.subscribe(() => {
      this.checkValid();
    });

    this.linkTypeService.linkTypesLoaded.subscribe(() => {
      this.checkValid();
    });

    this.tableTypeService.tableTypesLoaded.subscribe(() => {
      this.checkValid();
    });

    this.unitService.unitsLoaded.subscribe(() => {
      this.checkValid();
    });

    // this.fileTypeService.fileTypesLoaded.subscribe( () => {
    //   this.linkTypeService.linkTypesLoaded.subscribe( () => {
    //     this.tableTypeService.tableTypesLoaded.subscribe(() => {
    //       this.structureLoaded.next(true);
    //       this.structureLoaded.complete();
    //     })
    //   })
    // })
  }
}
