import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subscriber, forkJoin, map, tap } from 'rxjs';
import { TxAttribute, TxDataType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TxConfigService } from '@bassetti-group/tx-web-core/src/lib/data-access/config';
import { TxAbstractConceptService } from './abstract-concept.service';
import { TxFileTypesService } from './file-types.service';
import { TxTableTypesService } from './table-types.service';
import { TxLinkTypesService } from './link-types.service';
import { TxObjectsTypeService } from './objects-type.service';
import { TxDialogService } from '@bassetti-group/tx-web-core/src/lib/utilities';
import { FormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TxObjectTypeIconService } from '@bassetti-group/tx-web-core/src/lib/data-access/session';

@Injectable({
  providedIn: 'root',
})
export class TxAttributesService extends TxAbstractConceptService<TxAttribute> {
  protected urlListAllConcepts = 'api/Structure/attribute';

  constructor(
    configService: TxConfigService,
    http: HttpClient,
    objectTypeIcon: TxObjectTypeIconService,
    private readonly fileTypeService: TxFileTypesService,
    private readonly tableTypeService: TxTableTypesService,
    private readonly linkTypeService: TxLinkTypesService,
    private readonly objectTypeService: TxObjectsTypeService,
    private readonly dialogConfirmService: TxDialogService,
    private readonly translate: TranslateService
  ) {
    super(configService, http, objectTypeIcon);
  }

  listInParentOrder(concepts: TxAttribute[]): TxAttribute[] {
    const _build = (attributes: TxAttribute[]) => {
      attributes.forEach((attribute) => {
        const children = concepts.filter((concept) => concept.idAttributeParent === attribute.id);
        if (children.length > 0) {
          rootAttributes = rootAttributes.concat(children);
          _build(children);
        }
      });
    };

    let rootAttributes = concepts.filter((concept) => !concept.idAttributeParent);

    _build(rootAttributes);

    return rootAttributes;
  }

  isLinkAttribute(attribute: TxAttribute): boolean {
    return [
      TxDataType.Link,
      TxDataType.LinkAss,
      TxDataType.LinkBi,
      TxDataType.LinkDirect,
      TxDataType.LinkInv,
      TxDataType.Listing,
    ].includes(attribute.dataType);
  }

  isMultipleLinkAttribute(attribute: TxAttribute): boolean {
    return (
      (attribute.linkType &&
        (((attribute.dataType === TxDataType.LinkDirect ||
          attribute.dataType === TxDataType.LinkBi ||
          attribute.dataType === TxDataType.Listing) &&
          attribute.linkType.multiplicity === true) ||
          (attribute.dataType === TxDataType.LinkInv &&
            attribute.linkType.multiplicityInv === true))) ??
      false
    );
  }

  getLinkAttributeIdObjectType(attribute: TxAttribute): number {
    const isInverseLink = attribute.dataType === TxDataType.LinkInv;

    return isInverseLink
      ? attribute.linkType?.idSourceObjectType ?? -1
      : attribute.linkType?.idDestinationObjectType ?? -1;
  }

  getParentsIds(attribute: TxAttribute): number[] {
    const _fillIds = (att: TxAttribute) => {
      if (att.idAttributeParent) {
        const parentAttribute = this.getByID(att.idAttributeParent);

        if (parentAttribute) {
          ids.push(parentAttribute.id);
          _fillIds(parentAttribute);
        }
      }
    };

    const ids: number[] = [];

    _fillIds(attribute);

    if (ids.length) {
      ids.reverse();
    }

    return ids;
  }

  override listAll(reload?: boolean): Observable<TxAttribute[]> {
    this.waitForRequiredConcepts().subscribe((ok) => {
      if (ok) {
        super.listAll(reload);
      }
    });

    return this.conceptsSub.asObservable();
  }

  listAttributesFromObjectType(
    idObjectType: number,
    types: TxDataType[] = []
  ): Observable<TxAttribute[]> {
    return new Observable((observer) => {
      this.waitForRequiredConcepts().subscribe((ok) => {
        if (ok) {
          this.http
            .get<TxAttribute[]>(
              this.apiUrl + `api/Structure/objectType/id/${idObjectType}/attributes`
            )
            .subscribe((attributes: TxAttribute[]) => {
              let newAttributes = this.add(attributes);
              if (types.length) {
                newAttributes = newAttributes.filter((a) => types.includes(a.dataType));
              }
              observer.next(newAttributes);
              observer.complete();
            });
        }
      });
    });
  }

  listFromIds(ids: number[]): Observable<TxAttribute[]> {
    return new Observable((observer) => {
      this.waitForRequiredConcepts().subscribe((ok) => {
        if (ok) {
          const idsToFind: number[] = [];
          ids.forEach((id) => {
            const att = this.getByID(id);
            if (!att) {
              idsToFind.push(id);
            }
          });
          if (idsToFind.length) {
            this.http
              .get<TxAttribute[]>(
                this.apiUrl +
                  `${this.urlListAllConcepts}/id?${idsToFind
                    .map((id, i) => 'idAttributes=' + id)
                    .join('&')}`
              )
              .subscribe((atts) => {
                this.add(atts);
                observer.next(this.findFromIds(ids));
                observer.complete();
              });
          } else {
            observer.next(this.findFromIds(ids));
            observer.complete();
          }
        }
      });
    });
  }

  listFromTags(tags: string[]): Observable<TxAttribute[]> {
    return new Observable((observer) => {
      this.waitForRequiredConcepts().subscribe((ok) => {
        if (ok) {
          const tagsToFind: string[] = [];
          tags.forEach((tag) => {
            const att = this.getByTag(tag);
            if (!att) {
              tagsToFind.push(tag);
            }
          });
          if (tagsToFind.length) {
            this.http
              .get<TxAttribute[]>(
                this.apiUrl +
                  `api/Structure/attribute/tag?${tagsToFind.map((tag) => 'tags=' + tag).join('&')}`
              )
              .subscribe((atts: TxAttribute[]) => {
                this.add(atts);
                observer.next(this.findFromTags(tags));
                observer.complete();
              });
          } else {
            observer.next(this.findFromTags(tags));
            observer.complete();
          }
        }
      });
    });
  }

  override create(attribute: TxAttribute): TxAttribute {
    switch (attribute.dataType) {
      case TxDataType.LinkDirect:
      case TxDataType.Listing:
      case TxDataType.LinkInv:
      case TxDataType.LinkBi: {
        const linkedAttribute = {
          ...attribute,
          linkType: this.linkTypeService.getByID(attribute.idLinkType ?? -1),
        };
        if (attribute.linkType?.isAssociative) {
          // load asso attributes
          this.listAttributesFromObjectType(attribute.linkType.idSourceObjectType);
        }
        return linkedAttribute;
      }
      case TxDataType.File:
        return {
          ...attribute,
          fileType: this.fileTypeService.getByID(attribute.idFileType ?? -1),
        };
      case TxDataType.Table:
        return {
          ...attribute,
          tableType: this.tableTypeService.getByID(attribute.idTableType ?? -1),
        };
      default:
        return attribute;
    }
  }

  getByTag(tag: string): TxAttribute | undefined {
    return super.getByTag(tag);
  }

  doesObjectTypeHaveOnlyTabOrGroupAttributes(idObjectType: number): Observable<boolean> {
    return this.listAttributesFromObjectType(idObjectType).pipe(
      map((res) => {
        if (!res) {
          return false;
        }
        if (
          res.some((att) => att.dataType !== TxDataType.Tab && att.dataType !== TxDataType.Group)
        ) {
          return false;
        }
        return true;
      })
    );
  }

  override getIconPath(idAttribute: number) {
    const attribute = this.getByID(idAttribute);
    if (!attribute) {
      return super.getIconPath(1);
    }

    const objectType = this.objectTypeService.getByID(attribute.idObjectType);

    return super.getIconPath(
      [TxDataType.Tab, TxDataType.Group].includes(attribute.dataType) ? 0 : objectType?.icon ?? -1
    );
  }

  autoTag(idsAttr: number[]): Observable<TxAttribute[]> {
    return this.http
      .post<TxAttribute[]>(`${this.apiUrl}api/Structure/tag/autotag?concept=Attribute`, idsAttr)
      .pipe(
        tap((attributes) => {
          attributes.forEach((attr) => {
            const existingAttr = this.concepts.find((u: TxAttribute) => u.id === attr.id);
            if (existingAttr) {
              Object.assign(existingAttr, attr);
              this.send();
            }
          });
        })
      );
  }

  checkAutoTag(observer: Subscriber<boolean>, formControl: FormControl<string>) {
    if (this.isAttributesTagsMissing(formControl)) {
      this.dialogConfirmService
        .open({
          message: this.translate.instant(_('txWebCore.components.autoTagConcepts'), {
            conceptType: this.translate.instant('txWebCore.admins.wording.attributes'),
          }),
          okCaption: _('txWebCore.button.create'),
        })
        .subscribe((confirmed: boolean) => {
          if (confirmed) {
            this.autoTag(this.getIdsAttributesTagsMissing(formControl)).subscribe({
              next: (attributes) => {
                // replace all placeholder tags by new generated tags
                let dataValue = JSON.stringify(formControl.value);
                attributes.forEach((att) => {
                  const placeholderTag = this.createTagPlaceholderFromId(att.id);
                  dataValue = dataValue.replaceAll(placeholderTag, att.tags[0]);
                });
                formControl.setValue(JSON.parse(dataValue));
                observer.next(true);
                observer.complete();
              },
              error: () => {
                observer.next(false);
                observer.complete();
              },
            });
          } else {
            observer.next(false);
            observer.complete();
          }
        });
    } else {
      observer.next(true);
      observer.complete();
    }
  }

  protected waitForRequiredConcepts(): Observable<boolean> {
    return new Observable((observer) => {
      forkJoin([
        this.linkTypeService.isReady(),
        this.fileTypeService.isReady(),
        this.objectTypeService.isReady(),
        this.tableTypeService.isReady(),
      ]).subscribe(() => {
        observer.next(true);
        observer.complete();
      });
    });
  }

  private createTagPlaceholderFromId(id: number): string {
    return `#attTag${id}#`;
  }

  private isAttributesTagsMissing(formControl: FormControl<string>): boolean {
    return /#attTag\d+#/.test(JSON.stringify(formControl.value));
  }

  private getIdsAttributesTagsMissing(formControl: FormControl<string>): number[] {
    const tags = JSON.stringify(formControl.value).match(/#attTag\d+#/g);
    if (tags) {
      const idAttributes = tags
        .map((tag) => {
          const match = /#attTag(\d+)#/.exec(tag);
          return match ? parseInt(match[1]) : -1;
        }) // get idAtt from placeholder Tag
        .filter((id) => id !== -1);
      return [...new Set(idAttributes)]; // remove duplicates
    } else {
      return [];
    }
  }
}
