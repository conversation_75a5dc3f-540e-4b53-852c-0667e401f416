<form
  class="core-model-export-form"
  [formGroup]="formGroup"
  (ngSubmit)="addCoreModelsExportHistoric()">
  <header class="core-model-export-form__header panel-header">
    <fa-icon [icon]="['fal', 'external-link']" class="panel-icon-title"></fa-icon>
    <div class="panel-title h2-section-title">
      {{ 'admins.coreModels.exportCoreModels' | translate }}
    </div>
  </header>
  <mat-divider></mat-divider>
  <section class="core-model-export-form__content panel-content">
    <div class="core-model-export-form__version">
      <h4>{{ 'generic.version' | translate }}</h4>
      <mat-form-field appearance="fill" color="accent">
        <mat-label color="accent">{{ 'generic.version' | translate }}</mat-label>
        <mat-select formControlName="version" required>
          <mat-option
            *ngFor="let version of versionsDisplay"
            class="core-model-export-form__version-option"
            [value]="version.versionValue">
            <span> {{ version.label | translate }} </span>
            <span> {{ version.versionValue }}</span>
          </mat-option>
        </mat-select>
        <mat-error *ngIf="versionControl.errors?.required" class="e-error">
          {{ 'generic.required' | translate }}
        </mat-error>
      </mat-form-field>
    </div>
    <div class="core-model-export-form__name">
      <h4>{{ 'generic.name' | translate }}</h4>
      <mat-form-field>
        <input
          matInput
          formControlName="name"
          [placeholder]="'generic.LIMS' | translate"
          required />
        <mat-error *ngIf="nameControl.errors?.required" class="e-error">
          {{ 'generic.required' | translate }}
        </mat-error>
        <mat-error *ngIf="nameControl.errors?.maxlength" class="e-error">
          {{
            'generic.maxlength'
              | translate
                : { controlName: 'generic.name' | translate, maxlength: nameControlMaxLength }
          }}
        </mat-error>
      </mat-form-field>
    </div>
    <div class="core-model-export-form__description">
      <h4>
        {{ 'admins.coreModels.explanation' | translate }}
      </h4>
      <mat-form-field appearance="fill" color="accent">
        <textarea
          matInput
          [placeholder]="'admins.coreModels.historyExplanationPlaceholder' | translate"
          cdkTextareaAutosize
          cdkAutosizeMinRows="5"
          cdkAutosizeMaxRow="8"
          formControlName="explanation"
          required></textarea>
        <mat-error *ngIf="explanationControl.errors?.required" class="e-error">
          {{ 'generic.required' | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="core-model-export-form__comment">
      <h4>{{ 'admins.coreModels.comment' | translate }}</h4>
      <mat-form-field appearance="fill" color="accent">
        <textarea
          matInput
          [placeholder]="'admins.coreModels.historyCommentPlaceholder' | translate"
          cdkTextareaAutosize
          cdkAutosizeMinRows="5"
          cdkAutosizeMaxRow="8"
          formControlName="comment"
          required></textarea>
        <mat-error *ngIf="commentControl.errors?.required" class="e-error">
          {{ 'generic.required' | translate }}
        </mat-error>
      </mat-form-field>
    </div>
  </section>
  <footer class="core-model-export-form__footer add-modif-form mat-elevation-z4">
    <button
      mat-flat-button
      class="panel-button add-modif-form__save"
      [disabled]="formGroup.invalid"
      color="accent"
      type="submit">
      {{ 'button.export' | translate }}
    </button>
    <button mat-stroked-button class="panel-button add-modif-form__cancel" (click)="cancel()">
      {{ 'button.cancel' | translate }}
    </button>
  </footer>
</form>
