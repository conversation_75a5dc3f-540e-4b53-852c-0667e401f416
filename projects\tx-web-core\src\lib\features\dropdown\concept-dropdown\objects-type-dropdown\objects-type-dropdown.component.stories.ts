import { type Meta, type StoryObj } from '@storybook/angular';
import { TxObjectsTypeDropdownComponent } from './objects-type-dropdown.component';
import { TxObjectTypeType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { Provider } from '@angular/core';

interface MyComponentAnnotations extends Meta<TxObjectsTypeDropdownComponent> {
  providers?: Provider[]; 
}

const meta: Meta<TxObjectsTypeDropdownComponent> = {
  component: TxObjectsTypeDropdownComponent,
  title: 'Dropdown/TxObjectsTypeDropdownComponent',
  tags: ['autodocs'],
};
export default meta;
type Story = StoryObj<TxObjectsTypeDropdownComponent>;

export const Primary: Story = {
  // Dropdown data injected from the mock-object-type service
  args: {
    showCheckBox : true,
    onlyVisible: true,
    filtered: false,
    types: [TxObjectTypeType.Standard, TxObjectTypeType.User],
    label : "Select an Object",
    dataOptions :{ idProperty: 'id', idParentProperty: 'idObjectTypeParent', },
  },
};
