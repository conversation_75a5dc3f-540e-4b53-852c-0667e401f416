import { ValidationErrors } from '@angular/forms';
import { concatenateErrors } from './validators.utils';

let errors: (ValidationErrors | null)[];

beforeEach(() => {
  errors = [
    { required: true },
    null,
    {
      maxlength: { requiredLength: 3, actualLength: 4 },
    },
  ];
});

it('concatenate errors given', () => {
  const concatErrors = concatenateErrors(errors);
  expect(concatErrors).toEqual({
    '0': { required: true },
    '1': null,
    '2': {
      maxlength: {
        actualLength: 4,
        requiredLength: 3,
      },
    },
  });
});
