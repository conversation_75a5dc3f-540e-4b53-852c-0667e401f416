.upload-file-field {
  &__file-input {
    display: none;
  }

  &__file-uploader {
    display: flex;
    padding-top: 0.2rem;

    .file-uploader {
      &__upload-icon {
        font-size: 1.8rem;
      }
      &__text {
        margin-top: 0.2rem;
        margin-left: 0.5rem;
        font-weight: 500;
      }
      &__clickable-url:hover {
        text-decoration: underline;
        cursor: pointer;
      }
      &__size-text {
        font-size: 0.6rem;
        margin-left: 0.5rem;
      }
    }
  }

  &__uploader-dragzone {
    opacity: 0;
    border-radius: 0.6rem;
    position: absolute;
    z-index: -1;
    top: -0.06rem;
    bottom: -0.06rem;
    left: -0.06rem;
    right: -0.06rem;
    transition: border 0.3s;
    transition: opacity 0.3s;

    &--hovered {
      z-index: 2;
      opacity: 0.97;
    }

    .uploader-dragzone {
      &__drag {
        vertical-align: middle;
        text-align: center;
        top: calc(50% - 2rem);
        left: calc(50% - 3.25rem);
        position: absolute;
      }

      &__file-icon {
        font-size: 1.75rem;
      }

      &__file-message {
        font-size: 1rem;
        line-height: 1.1rem;
        margin-top: 0.5rem;
        font-weight: 600;
      }
    }
  }

  &__files-container {
    padding-top: 0.3rem;
  }

  &__chips-listbox {
    display: flex;
    max-height: 12rem;
    max-width: 19.5rem;
    overflow-x: scroll;
    flex-flow: column wrap;
  }

  &__error {
    padding-right: 1rem;
  }
}
