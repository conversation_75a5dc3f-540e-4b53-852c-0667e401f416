import { TxTableType } from './../models/table-type';
import { Injectable } from '@angular/core';
import { TxApiService } from './api.service';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LegacyTxTableTypeService {
  tableTypes: TxTableType[] = [];

  tableTypesLoaded: Subject<boolean> = new Subject<boolean>();
  initialized = false;

  private fill(tableType: any): TxTableType {
    const newTableType = new TxTableType(tableType);

    this.tableTypes.push(newTableType);

    return newTableType;
  }

  private add(tableTypes: any[]): TxTableType[] {
    return tableTypes.map((t) => this.fill(t));
  }

  constructor(public apiService: TxApiService) {
    this.tableTypesLoaded.subscribe((value) => {
      this.initialized = true;
    });
  }

  start() {
    this.apiService.listTableTypes().subscribe((tableTypes: TxTableType[]) => {
      this.add(tableTypes);

      this.tableTypesLoaded.next(true);
      this.tableTypesLoaded.complete();
    });
  }

  get(idTableType: number): TxTableType {
    return this.tableTypes.find((t) => t.id === idTableType) as TxTableType;
  }
}
