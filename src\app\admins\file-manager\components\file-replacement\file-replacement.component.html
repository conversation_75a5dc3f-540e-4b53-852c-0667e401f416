<ng-template #dialogContent>
  <div class="dialog-header background-accent">
    <fa-icon [icon]="['fal', 'exchange']" size="lg"></fa-icon>
    <span>{{ 'admins.resources.replaceOrIgnoreItems' | translate }}</span>
  </div>
  <div *ngIf="!useDetailedChoice" class="dialog-content-container">
    <div class="copy-message annotation">
      {{
        'admins.resources.copyOfElements'
          | translate : { number: totalFiles, folder: destinationParentNode?.name }
      }}
    </div>
    <div class="same-files-message">
      {{
        'admins.resources.itemsWithIdenticalNames' | translate : { number: files.sameFiles?.length }
      }}
    </div>
    <div class="files-actions">
      <div class="row-action hover-grey20" (click)="replaceAllFiles()">
        <fa-icon [icon]="['fal', 'check']" size="lg"></fa-icon>
        <span>{{ 'admins.resources.replaceItems' | translate }}</span>
      </div>
      <div class="row-action hover-grey20" (click)="ignoreAllFiles()">
        <fa-icon [icon]="['fal', 'undo']" size="lg"></fa-icon>
        <span>{{ 'admins.resources.ignoreItems' | translate }}</span>
      </div>
      <div class="row-action hover-grey20" (click)="switchToDetailedChoice()">
        <fa-icon [icon]="['fal', 'copy']" size="lg"></fa-icon>
        <span>{{ 'admins.resources.letMeDecideForItems' | translate }}</span>
      </div>
    </div>
  </div>
  <div *ngIf="useDetailedChoice" class="dialog-content-container container-detailed-choice">
    <div class="same-files-message">{{ 'admins.resources.selectItemsToKeep' | translate }}</div>
    <div class="copy-message annotation">
      {{ 'admins.resources.bothVersionsSelected' | translate }}
    </div>
    <div class="file-selection-header">
      <div class="file-left-column">
        <mat-checkbox
          class=""
          [checked]="allFTUSelected"
          [indeterminate]="someFTUSelect()"
          (change)="setAllFTU($event.checked)">
          <span *ngIf="isMovingItems; else itemsToUpload">{{
            'admins.resources.itemsToMoveFrom' | translate : { folder: sourceParentNode?.name }
          }}</span>
          <ng-template #itemsToUpload
            ><span>{{ 'admins.resources.itemsToUpload' | translate }}</span></ng-template
          >
        </mat-checkbox>
      </div>
      <div class="file-right-column">
        <mat-checkbox
          class=""
          [checked]="allFIDSelected"
          [indeterminate]="someFIDSelect()"
          (change)="setAllFID($event.checked)">
          {{
            'admins.resources.itemsInDestination'
              | translate : { folder: destinationParentNode?.name }
          }}
        </mat-checkbox>
      </div>
    </div>
    <mat-divider></mat-divider>
    <div *ngFor="let fileTU of filesToUpload; let i = index">
      <div class="file-selection-row">
        <div class="annotation">{{ fileTU.file.name }}</div>
        <div class="file-left-column">
          <mat-checkbox [(ngModel)]="fileTU.selected" (ngModelChange)="updateAllFTUSelect()">
            <fa-icon
              *ngIf="isFolderReplacement(fileTU.file.type); else iconFileUpload"
              [icon]="['fal', 'folder-upload']"
              size="3x"></fa-icon>
            <ng-template #iconFileUpload
              ><fa-icon [icon]="['fal', 'file-upload']" size="3x"></fa-icon
            ></ng-template>
            <div class="file-detailed">
              <div>{{ fileTU.file.lastWriteTime | localizedDate : 'short' : true }}</div>
              <div class="annotation">
                <span *ngIf="fileTU.file.length !== -1">{{
                  (formatFileSizeInReplacement(fileTU.file.length) | localizedNumber) +
                    ' ' +
                    ('admins.resources.kilobyteDiminutive' | translate)
                }}</span
                > 
              </div>
            </div>
          </mat-checkbox>
        </div>
        <div class="file-right-column">
          <mat-checkbox
            [(ngModel)]="filesInDestination[i].selected"
            (ngModelChange)="updateAllFIDSelect()">
            <fa-icon
              *ngIf="isFolderReplacement(filesInDestination[i].file.type); else iconFileDestination"
              [icon]="['fal', 'folder']"
              size="3x"></fa-icon>
            <ng-template #iconFileDestination
              ><fa-icon [icon]="['fal', 'file']" size="3x"></fa-icon
            ></ng-template>
            <div class="file-detailed">
              <div>
                {{ filesInDestination[i].file.lastWriteTime | localizedDate : 'short' : true }}
              </div>
              <div class="annotation">
                <span *ngIf="filesInDestination[i].file.length !== -1">{{
                  (formatFileSizeInReplacement(filesInDestination[i].file.length)
                    | localizedNumber) +
                    ' ' +
                    ('admins.resources.kilobyteDiminutive' | translate)
                }}</span
                > 
              </div>
            </div>
          </mat-checkbox>
        </div>
      </div>
      <mat-divider></mat-divider>
    </div>
  </div>
  <div *ngIf="!useDetailedChoice" class="button-container">
    <button mat-stroked-button mat-dialog-close (click)="reset()">
      {{ 'window.close' | translate }}
    </button>
  </div>
  <div *ngIf="useDetailedChoice" class="button-container">
    <button mat-flat-button color="accent" (click)="validDetailedChoice()">
      {{ 'window.ok' | translate }}
    </button>
    <button mat-stroked-button (click)="reset()">{{ 'window.cancel' | translate }}</button>
  </div>
</ng-template>
