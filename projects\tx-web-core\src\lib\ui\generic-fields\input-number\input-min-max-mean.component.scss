$control-number-padding-bottom: 0.3rem;
$error-number-padding-right: 1.5rem;

:host {
  --text-area-height: none;
  display: block;
  width: 100%;
}

.input-min-max-mean {
  &__label {
    padding-bottom: 0.2rem;
  }

  &__controls {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(4rem, 1fr) minmax(0.7rem, max-content));
    align-items: baseline;
  }

  &__control {
    padding-bottom: $control-number-padding-bottom;
  }

  &__separator {
    &--number {
      width: 1.5rem;
      max-width: 1.5rem;
      text-align: center;
    }

    &--unit {
      width: 0.7rem;
      max-width: 0.7rem;
    }
  }

  &__errors {
    padding-right: $error-number-padding-right;
  }
}
