import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxMailObjectFieldComponent } from './mail-object-field.component';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { AttributesMockService } from '../../../testing.mock';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatChipsModule } from '@angular/material/chips';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import {
  LegacyTxAttributeRight,
  TxAttributeMail,
} from '../../../services/structure/models/attribute';
import { LegacyTxDataType } from '../../../services/structure/models/data';
import { MockComponent } from 'ng-mocks';
import { TxChipsFieldComponent } from '../../generic-fields/chips-field/chips-field.component';

describe('TxMailObjectFieldComponent', () => {
  let component: TxMailObjectFieldComponent;
  let fixture: ComponentFixture<TxMailObjectFieldComponent>;
  let element;
  let attributesService: LegacyTxAttributesService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxMailObjectFieldComponent, MockComponent(TxChipsFieldComponent)],
      providers: [{ provide: LegacyTxAttributesService, useClass: AttributesMockService }],
      imports: [
        MatChipsModule,
        MatTooltipModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatInputModule,
        BrowserAnimationsModule,
      ],
    }).compileComponents();
    attributesService = TestBed.inject(LegacyTxAttributesService);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxMailObjectFieldComponent);
    component = fixture.componentInstance;
    element = fixture.nativeElement;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('testing initProperties...', () => {
    it('should init multiple from attribute', () => {
      const attribute = new TxAttributeMail({
        isList: true,
        id: 0,
        name: '',
        dataType: LegacyTxDataType.ArchivedGraphic,
        idObjectType: 0,
        idAttributeParent: 0,
        tags: [],
        right: LegacyTxAttributeRight.None,
        isInherited: false,
        order: 0,
        idInheritedAttribute: 0,
        idLinkType: 0,
        option: {},
      });
      component.attribute = attribute;
      component.initProperties();
      expect(component.multiple).toBeTruthy();
    });
  });

  describe('testing getData...', () => {
    it('should return data from control value', () => {
      component.idObject = 1;
      component.idAttribute = 2;
      component.control.setValue(['test1', 'test2']);
      expect(component.getData()).toEqual({
        idObject: 1,
        idAttribute: 2,
        value: ['test1', 'test2'],
        action: 1,
      });
    });
  });
});
