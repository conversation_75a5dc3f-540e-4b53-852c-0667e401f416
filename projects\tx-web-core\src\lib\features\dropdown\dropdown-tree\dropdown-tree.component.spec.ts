/* tslint:disable:no-unused-variable */
import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxDropdownTreeComponent } from './dropdown-tree.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { MatDividerModule } from '@angular/material/divider';
import { MockComponent } from 'ng-mocks';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TxObjectTypeTreeViewComponent } from '../../trees';

describe('DropdownTreeComponent', () => {
  let component: TxDropdownTreeComponent<any>;
  let fixture: ComponentFixture<TxDropdownTreeComponent<any>>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatDividerModule,
        MatTooltipModule,
        FontAwesomeTestingModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
        TxDropdownTreeComponent,
        MockComponent(TxObjectTypeTreeViewComponent),
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxDropdownTreeComponent);
    component = fixture.componentInstance;
    // To remove when add a host component
    component.control = new FormControl('');
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
