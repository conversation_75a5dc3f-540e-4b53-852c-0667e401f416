import { Args, type Meta, type StoryObj } from '@storybook/angular';
import { FormControl } from '@angular/forms';
import { TxInputMinMaxMeanComponent } from './input-min-max-mean.component';
import { MinMax, MinMaxMean } from './models';
import { TxUnit } from '../../../features/legacy-form';

const controlMMM = new FormControl<MinMaxMean>({
  min: null,
  max: null,
  mean: null,
  unit: null,
});
const controlMM = new FormControl<MinMax>({ min: null, max: null, unit: null });
const units = [
  new TxUnit({ name: 'm', id: 1 }),
  new TxUnit({ name: 'cm', id: 2 }),
  new TxUnit({ name: 'km', id: 3 }),
];

const meta: Meta<TxInputMinMaxMeanComponent> = {
  component: TxInputMinMaxMeanComponent,
  title: 'Generic Fields/InputMinMaxMeanComponent',
  tags: ['autodocs'],
};
export default meta;

export const MinMaxStory: StoryObj = {
  render: (args: Partial<Args>) => ({
    props: { ...args, formControl: controlMM },
    template: `
        <tx-min-max-mean
            [formControl]="formControl"
            [label]="label"
            [labelTooltip]="labelTooltip"
            [lowerBound]="lowerBound"
            [upperBound]="upperBound"
            [lowerBoundIncluded]="lowerBoundIncluded"
            [upperBoundIncluded]="upperBoundIncluded"
            [required]="required"
            [units]="units"
            [idUnitRef]="idUnitRef"
        >
        </tx-min-max-mean>
    `,
  }),
};

MinMaxStory.args = {
  label: 'label min max',
  required: true,
  labelTooltip: 'tooltip min max',
  lowerBound: 2,
  upperBound: 1000,
  lowerBoundIncluded: false,
  upperBoundIncluded: true,
  units: units,
  idUnitRef: 2,
};

export const MinMaxMeanStory: StoryObj = {
  render: (args: Partial<Args>) => ({
    props: { ...args, formControl: controlMMM },
    template: `
        <div [style.width.px]="350">
            <tx-min-max-mean
                [formControl]="formControl"
                [label]="label"
                [labelTooltip]="labelTooltip"
                [lowerBound]="lowerBound"
                [upperBound]="upperBound"
                [lowerBoundIncluded]="lowerBoundIncluded"
                [upperBoundIncluded]="upperBoundIncluded"
                [required]="required"
                [units]="units"
                [idUnitRef]="idUnitRef"
            >
            </tx-min-max-mean>
        </div>
     `,
  }),
};

MinMaxMeanStory.args = {
  label: 'label min max mean',
  required: true,
  labelTooltip: 'tooltip min max mean',
  lowerBound: 5,
  upperBound: 100,
  lowerBoundIncluded: true,
  upperBoundIncluded: false,
  units: units,
  idUnitRef: 1,
};
