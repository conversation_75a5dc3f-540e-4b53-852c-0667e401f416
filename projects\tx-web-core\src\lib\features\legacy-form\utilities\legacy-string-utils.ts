export interface Char {
  name?: string;
  xmlEncoded?: string;
  urlEncoded?: string;
  id?: number;
}

export class LegacyStringUtilsHelper {
  chars: Char[] = [
    { id: 34, name: '"', xmlEncoded: '&quot;', urlEncoded: '%22' },
    { id: 38, name: '&', xmlEncoded: '&amp;', urlEncoded: '%26' },
    { id: 60, name: '<', xmlEncoded: '&lt;', urlEncoded: '%3C' },
    { id: 62, name: '>', xmlEncoded: '&gt;', urlEncoded: '%3E' },
    { id: 160, name: ' ', xmlEncoded: '&nbsp;', urlEncoded: '%20' },
    { id: 161, name: '¡', xmlEncoded: '&iexcl;', urlEncoded: '%A1' },
    { id: 162, name: '¢', xmlEncoded: '&cent;', urlEncoded: '%A2' },
    { id: 163, name: '£', xmlEncoded: '&pound;', urlEncoded: '%A3' },
    { id: 164, name: '¤', xmlEncoded: '&curren;', urlEncoded: '%A4' },
    { id: 165, name: '¥', xmlEncoded: '&yen;', urlEncoded: '%A5' },
    { id: 166, name: '¦', xmlEncoded: '&brvbar;', urlEncoded: '%A6' },
    { id: 167, name: '§', xmlEncoded: '&sect;', urlEncoded: '%A7' },
    { id: 168, name: '¨', xmlEncoded: '&uml;', urlEncoded: '%A8' },
    { id: 169, name: '©', xmlEncoded: '&copy;', urlEncoded: '%A9' },
    { id: 170, name: 'ª', xmlEncoded: '&ordf;', urlEncoded: '%AA' },
    { id: 171, name: '«', xmlEncoded: '&laquo;', urlEncoded: '%AB' },
    { id: 172, name: '¬', xmlEncoded: '&not;', urlEncoded: '%AC' },
    { id: 173, name: '­', xmlEncoded: '&shy;', urlEncoded: '%AD' },
    { id: 174, name: '®', xmlEncoded: '&reg;', urlEncoded: '%AE' },
    { id: 175, name: '¯', xmlEncoded: '&macr;', urlEncoded: '%AF' },
    { id: 176, name: '°', xmlEncoded: '&deg;', urlEncoded: '%B0' },
    { id: 177, name: '±', xmlEncoded: '&plusmn;', urlEncoded: '%B1' },
    { id: 178, name: '²', xmlEncoded: '&sup2;', urlEncoded: '%B2' },
    { id: 179, name: '³', xmlEncoded: '&sup3;', urlEncoded: '%B3' },
    { id: 180, name: '´', xmlEncoded: '&acute;', urlEncoded: '%B4' },
    { id: 181, name: 'µ', xmlEncoded: '&micro;', urlEncoded: '%B5' },
    { id: 182, name: '¶', xmlEncoded: '&para;', urlEncoded: '%B6' },
    { id: 183, name: '·', xmlEncoded: '&middot;', urlEncoded: '%B7' },
    { id: 184, name: '¸', xmlEncoded: '&cedil;', urlEncoded: '%B8' },
    { id: 185, name: '¹', xmlEncoded: '&sup1;', urlEncoded: '%B9' },
    { id: 186, name: 'º', xmlEncoded: '&ordm;', urlEncoded: '%BA' },
    { id: 187, name: '»', xmlEncoded: '&raquo;', urlEncoded: '%BB' },
    { id: 188, name: '¼', xmlEncoded: '&frac14;', urlEncoded: '%BC' },
    { id: 189, name: '½', xmlEncoded: '&frac12;', urlEncoded: '%BD' },
    { id: 190, name: '¾', xmlEncoded: '&frac34;', urlEncoded: '%BE' },
    { id: 191, name: '¿', xmlEncoded: '&iquest;', urlEncoded: '%BF' },
    { id: 192, name: 'À', xmlEncoded: '&Agrave;', urlEncoded: '%C0' },
    { id: 193, name: 'Á', xmlEncoded: '&Aacute;', urlEncoded: '%C1' },
    { id: 194, name: 'Â', xmlEncoded: '&Acirc;', urlEncoded: '%C2' },
    { id: 195, name: 'Ã', xmlEncoded: '&Atilde;', urlEncoded: '%C3' },
    { id: 196, name: 'Ä', xmlEncoded: '&Auml;', urlEncoded: '%C4' },
    { id: 197, name: 'Å', xmlEncoded: '&Aring;', urlEncoded: '%C5' },
    { id: 198, name: 'Æ', xmlEncoded: '&AElig;', urlEncoded: '%C6' },
    { id: 199, name: 'Ç', xmlEncoded: '&Ccedil;', urlEncoded: '%C7' },
    { id: 200, name: 'È', xmlEncoded: '&Egrave;', urlEncoded: '%C8' },
    { id: 201, name: 'É', xmlEncoded: '&Eacute;', urlEncoded: '%C9' },
    { id: 202, name: 'Ê', xmlEncoded: '&Ecirc;', urlEncoded: '%CA' },
    { id: 203, name: 'Ë', xmlEncoded: '&Euml;', urlEncoded: '%CB' },
    { id: 204, name: 'Ì', xmlEncoded: '&Igrave;', urlEncoded: '%CC' },
    { id: 205, name: 'Í', xmlEncoded: '&Iacute;', urlEncoded: '%CD' },
    { id: 206, name: 'Î', xmlEncoded: '&Icirc;', urlEncoded: '%CE' },
    { id: 207, name: 'Ï', xmlEncoded: '&Iuml;', urlEncoded: '%CF' },
    { id: 208, name: 'Ð', xmlEncoded: '&ETH;', urlEncoded: '%D0' },
    { id: 209, name: 'Ñ', xmlEncoded: '&Ntilde;', urlEncoded: '%D1' },
    { id: 210, name: 'Ò', xmlEncoded: '&Ograve;', urlEncoded: '%D2' },
    { id: 211, name: 'Ó', xmlEncoded: '&Oacute;', urlEncoded: '%D3' },
    { id: 212, name: 'Ô', xmlEncoded: '&Ocirc;', urlEncoded: '%D4' },
    { id: 213, name: 'Õ', xmlEncoded: '&Otilde;', urlEncoded: '%D5' },
    { id: 214, name: 'Ö', xmlEncoded: '&Ouml;', urlEncoded: '%D6' },
    { id: 215, name: '×', xmlEncoded: '&times;', urlEncoded: '%D7' },
    { id: 216, name: 'Ø', xmlEncoded: '&Oslash;', urlEncoded: '%D8' },
    { id: 217, name: 'Ù', xmlEncoded: '&Ugrave;', urlEncoded: '%D9' },
    { id: 218, name: 'Ú', xmlEncoded: '&Uacute;', urlEncoded: '%DA' },
    { id: 219, name: 'Û', xmlEncoded: '&Ucirc;', urlEncoded: '%DB' },
    { id: 220, name: 'Ü', xmlEncoded: '&Uuml;', urlEncoded: '%DC' },
    { id: 221, name: 'Ý', xmlEncoded: '&Yacute;', urlEncoded: '%DD' },
    { id: 222, name: 'Þ', xmlEncoded: '&THORN;', urlEncoded: '%DE' },
    { id: 223, name: 'ß', xmlEncoded: '&szlig;', urlEncoded: '%DF' },
    { id: 224, name: 'à', xmlEncoded: '&agrave;', urlEncoded: '%E0' },
    { id: 225, name: 'á', xmlEncoded: '&aacute;', urlEncoded: '%E1' },
    { id: 226, name: 'â', xmlEncoded: '&acirc;', urlEncoded: '%E2' },
    { id: 227, name: 'ã', xmlEncoded: '&atilde;', urlEncoded: '%E3' },
    { id: 228, name: 'ä', xmlEncoded: '&auml;', urlEncoded: '%E4' },
    { id: 229, name: 'å', xmlEncoded: '&aring;', urlEncoded: '%E5' },
    { id: 230, name: 'æ', xmlEncoded: '&aelig;', urlEncoded: '%E6' },
    { id: 231, name: 'ç', xmlEncoded: '&ccedil;', urlEncoded: '%E7' },
    { id: 232, name: 'è', xmlEncoded: '&egrave;', urlEncoded: '%E8' },
    { id: 233, name: 'é', xmlEncoded: '&eacute;', urlEncoded: '%E9' },
    { id: 234, name: 'ê', xmlEncoded: '&ecirc;', urlEncoded: '%EA' },
    { id: 235, name: 'ë', xmlEncoded: '&euml;', urlEncoded: '%EB' },
    { id: 236, name: 'ì', xmlEncoded: '&igrave;', urlEncoded: '%EC' },
    { id: 237, name: 'í', xmlEncoded: '&iacute;', urlEncoded: '%ED' },
    { id: 238, name: 'î', xmlEncoded: '&icirc;', urlEncoded: '%EE' },
    { id: 239, name: 'ï', xmlEncoded: '&iuml;', urlEncoded: '%EF' },
    { id: 240, name: 'ð', xmlEncoded: '&eth;', urlEncoded: '%F0' },
    { id: 241, name: 'ñ', xmlEncoded: '&ntilde;', urlEncoded: '%F1' },
    { id: 242, name: 'ò', xmlEncoded: '&ograve;', urlEncoded: '%F2' },
    { id: 243, name: 'ó', xmlEncoded: '&oacute;', urlEncoded: '%F3' },
    { id: 244, name: 'ô', xmlEncoded: '&ocirc;', urlEncoded: '%F4' },
    { id: 245, name: 'õ', xmlEncoded: '&otilde;', urlEncoded: '%F5' },
    { id: 246, name: 'ö', xmlEncoded: '&ouml;', urlEncoded: '%F6' },
    { id: 247, name: '÷', xmlEncoded: '&divide;', urlEncoded: '%F7' },
    { id: 248, name: 'ø', xmlEncoded: '&oslash;', urlEncoded: '%F8' },
    { id: 249, name: 'ù', xmlEncoded: '&ugrave;', urlEncoded: '%F9' },
    { id: 250, name: 'ú', xmlEncoded: '&uacute;', urlEncoded: '%FA' },
    { id: 251, name: 'û', xmlEncoded: '&ucirc;', urlEncoded: '%FB' },
    { id: 252, name: 'ü', xmlEncoded: '&uuml;', urlEncoded: '%FC' },
    { id: 253, name: 'ý', xmlEncoded: '&yacute;', urlEncoded: '%FD' },
    { id: 254, name: 'þ', xmlEncoded: '&thorn;', urlEncoded: '%FE' },
    { id: 255, name: 'ÿ', xmlEncoded: '&yuml;', urlEncoded: '%FF' },
    { id: 338, name: 'Œ', xmlEncoded: '&OElig;', urlEncoded: '%8C' },
    { id: 339, name: 'œ', xmlEncoded: '&oelig;', urlEncoded: '%9C' },
    { id: 352, name: 'Š', xmlEncoded: '&Scaron;', urlEncoded: '%8A' },
    { id: 353, name: 'š', xmlEncoded: '&scaron;', urlEncoded: '%9A' },
    { id: 376, name: 'Ÿ', xmlEncoded: '&Yuml;', urlEncoded: '%9F' },
    { id: 402, name: 'ƒ', xmlEncoded: '&fnof;', urlEncoded: '%83' },
    { id: 710, name: 'ˆ', xmlEncoded: '&circ;', urlEncoded: '%88' },
    { id: 732, name: '˜', xmlEncoded: '&tilde;', urlEncoded: '%98' },
    { id: 913, name: 'Α', xmlEncoded: '&Alpha;' },
    { id: 914, name: 'Β', xmlEncoded: '&Beta;' },
    { id: 915, name: 'Γ', xmlEncoded: '&Gamma;' },
    { id: 916, name: 'Δ', xmlEncoded: '&Delta;' },
    { id: 917, name: 'Ε', xmlEncoded: '&Epsilon;' },
    { id: 918, name: 'Ζ', xmlEncoded: '&Zeta;' },
    { id: 919, name: 'Η', xmlEncoded: '&Eta;' },
    { id: 920, name: 'Θ', xmlEncoded: '&Theta;' },
    { id: 921, name: 'Ι', xmlEncoded: '&Iota;' },
    { id: 922, name: 'Κ', xmlEncoded: '&Kappa;' },
    { id: 923, name: 'Λ', xmlEncoded: '&Lambda;' },
    { id: 924, name: 'Μ', xmlEncoded: '&Mu;' },
    { id: 925, name: 'Ν', xmlEncoded: '&Nu;' },
    { id: 926, name: 'Ξ', xmlEncoded: '&Xi;' },
    { id: 927, name: 'Ο', xmlEncoded: '&Omicron;' },
    { id: 928, name: 'Π', xmlEncoded: '&Pi;' },
    { id: 929, name: 'Ρ', xmlEncoded: '&Rho;' },
    { id: 931, name: 'Σ', xmlEncoded: '&Sigma;' },
    { id: 932, name: 'Τ', xmlEncoded: '&Tau;' },
    { id: 933, name: 'Υ', xmlEncoded: '&Upsilon;' },
    { id: 934, name: 'Φ', xmlEncoded: '&Phi;' },
    { id: 935, name: 'Χ', xmlEncoded: '&Chi;' },
    { id: 936, name: 'Ψ', xmlEncoded: '&Psi;' },
    { id: 937, name: 'Ω', xmlEncoded: '&Omega;' },
    { id: 945, name: 'α', xmlEncoded: '&alpha;' },
    { id: 946, name: 'β', xmlEncoded: '&beta;' },
    { id: 947, name: 'γ', xmlEncoded: '&gamma;' },
    { id: 948, name: 'δ', xmlEncoded: '&delta;' },
    { id: 949, name: 'ε', xmlEncoded: '&epsilon;' },
    { id: 950, name: 'ζ', xmlEncoded: '&zeta;' },
    { id: 951, name: 'η', xmlEncoded: '&eta;' },
    { id: 952, name: 'θ', xmlEncoded: '&theta;' },
    { id: 953, name: 'ι', xmlEncoded: '&iota;' },
    { id: 954, name: 'κ', xmlEncoded: '&kappa;' },
    { id: 955, name: 'λ', xmlEncoded: '&lambda;' },
    { id: 956, name: 'μ', xmlEncoded: '&mu;' },
    { id: 957, name: 'ν', xmlEncoded: '&nu;' },
    { id: 958, name: 'ξ', xmlEncoded: '&xi;' },
    { id: 959, name: 'ο', xmlEncoded: '&omicron;' },
    { id: 960, name: 'π', xmlEncoded: '&pi;' },
    { id: 961, name: 'Ρ', xmlEncoded: '&rho;' },
    { id: 962, name: 'ς', xmlEncoded: '&sigmaf;' },
    { id: 963, name: 'σ', xmlEncoded: '&sigma;' },
    { id: 964, name: 'τ', xmlEncoded: '&tau;' },
    { id: 965, name: 'υ', xmlEncoded: '&upsilon;' },
    { id: 966, name: 'φ', xmlEncoded: '&phi;' },
    { id: 967, name: 'χ', xmlEncoded: '&chi;' },
    { id: 968, name: 'ψ', xmlEncoded: '&psi;' },
    { id: 969, name: 'ω', xmlEncoded: '&omega;' },
    { id: 977, name: 'ϑ', xmlEncoded: '&thetasym;' },
    { id: 978, name: 'ϒ', xmlEncoded: '&upsih;' },
    { id: 982, name: 'ϖ', xmlEncoded: '&piv;' },
    { id: 8194, xmlEncoded: '&ensp;' },
    { id: 8195, xmlEncoded: '&emsp;' },
    { id: 8201, xmlEncoded: '&thinsp;' },
    { id: 8204, xmlEncoded: '&zwnj;' },
    { id: 8205, xmlEncoded: '&zwj;' },
    { id: 8206, xmlEncoded: '&lrm;' },
    { id: 8207, xmlEncoded: '&rlm;' },
    { id: 8211, name: '–', xmlEncoded: '&ndash;', urlEncoded: '%96' },
    { id: 8212, name: '—', xmlEncoded: '&mdash;', urlEncoded: '%97' },
    { id: 8216, name: '‘', xmlEncoded: '&lsquo;', urlEncoded: '%91' },
    { id: 8217, name: '’', xmlEncoded: '&rsquo;', urlEncoded: '%92' },
    { id: 8218, name: '‚', xmlEncoded: '&sbquo;', urlEncoded: '%82' },
    { id: 8220, name: '“', xmlEncoded: '&ldquo;', urlEncoded: '%93' },
    { id: 8221, name: '”', xmlEncoded: '&rdquo;', urlEncoded: '%94' },
    { id: 8222, name: '„', xmlEncoded: '&bdquo;', urlEncoded: '%84' },
    { id: 8224, name: '†', xmlEncoded: '&dagger;', urlEncoded: '%86' },
    { id: 8225, name: '‡', xmlEncoded: '&Dagger;', urlEncoded: '%87' },
    { id: 8226, name: '•', xmlEncoded: '&bull;', urlEncoded: '%95' },
    { id: 8230, name: '…', xmlEncoded: '&hellip;', urlEncoded: '%85' },
    { id: 8240, name: '‰', xmlEncoded: '&permil;', urlEncoded: '%89' },
    { id: 8242, name: '′', xmlEncoded: '&prime;' },
    { id: 8243, name: '″', xmlEncoded: '&Prime;' },
    { id: 8249, name: '‹', xmlEncoded: '&lsaquo;', urlEncoded: '%8B' },
    { id: 8250, name: '›', xmlEncoded: '&rsaquo;' },
    { id: 8254, name: '‾', xmlEncoded: '&oline;' },
    { id: 8260, name: '⁄', xmlEncoded: '&frasl;' },
    { id: 8364, name: '€', xmlEncoded: '&euro;' },
    { id: 8465, name: 'ℑ', xmlEncoded: '&image;' },
    { id: 8472, name: '℘', xmlEncoded: '&weierp;' },
    { id: 8476, name: 'ℜ', xmlEncoded: '&real;' },
    { id: 8482, name: '™', xmlEncoded: '&trade;', urlEncoded: '%99' },
    { id: 8501, name: 'ℵ', xmlEncoded: '&alefsym;' },
    { id: 8592, name: '←', xmlEncoded: '&larr;' },
    { id: 8593, name: '↑', xmlEncoded: '&uarr;' },
    { id: 8594, name: '→', xmlEncoded: '&rarr;' },
    { id: 8595, name: '↓', xmlEncoded: '&darr;' },
    { id: 8596, name: '↔', xmlEncoded: '&harr;' },
    { id: 8629, name: '↵', xmlEncoded: '&crarr;' },
    { id: 8656, name: '⇐', xmlEncoded: '&lArr;' },
    { id: 8657, name: '⇑', xmlEncoded: '&uArr;' },
    { id: 8658, name: '⇒', xmlEncoded: '&rArr;' },
    { id: 8659, name: '⇓', xmlEncoded: '&dArr;' },
    { id: 8660, name: '⇔', xmlEncoded: '&hArr;' },
    { id: 8704, name: '∀', xmlEncoded: '&forall;' },
    { id: 8706, name: '∂', xmlEncoded: '&part;' },
    { id: 8707, name: '∃', xmlEncoded: '&exist;' },
    { id: 8709, name: '∅', xmlEncoded: '&empty;' },
    { id: 8711, name: '∇', xmlEncoded: '&nabla;' },
    { id: 8712, name: '∈', xmlEncoded: '&isin;' },
    { id: 8713, name: '∉', xmlEncoded: '&notin;' },
    { id: 8715, name: '∋', xmlEncoded: '&ni;' },
    { id: 8719, name: '∏', xmlEncoded: '&prod;' },
    { id: 8721, name: '∑', xmlEncoded: '&sum;' },
    { id: 8722, name: '−', xmlEncoded: '&minus;' },
    { id: 8727, name: '∗', xmlEncoded: '&lowast;' },
    { id: 8730, name: '√', xmlEncoded: '&radic;' },
    { id: 8733, name: '∝', xmlEncoded: '&prop;' },
    { id: 8734, name: '∞', xmlEncoded: '&infin;' },
    { id: 8736, name: '∠', xmlEncoded: '&ang;' },
    { id: 8743, name: '∧', xmlEncoded: '&and;' },
    { id: 8744, name: '∨', xmlEncoded: '&or;' },
    { id: 8745, name: '∩', xmlEncoded: '&cap;' },
    { id: 8746, name: '∪', xmlEncoded: '&cup;' },
    { id: 8747, name: '∫', xmlEncoded: '&int;' },
    { id: 8756, name: '∴', xmlEncoded: '&there4;' },
    { id: 8764, name: '∼', xmlEncoded: '&sim;' },
    { id: 8773, name: '≅', xmlEncoded: '&cong;' },
    { id: 8776, name: '≈', xmlEncoded: '&asymp;' },
    { id: 8800, name: '≠', xmlEncoded: '&ne;' },
    { id: 8801, name: '≡', xmlEncoded: '&equiv;' },
    { id: 8804, name: '≤', xmlEncoded: '&le;' },
    { id: 8805, name: '≥', xmlEncoded: '&ge;' },
    { id: 8834, name: '⊂', xmlEncoded: '&sub;' },
    { id: 8835, name: '⊃', xmlEncoded: '&sup;' },
    { id: 8836, name: '⊄', xmlEncoded: '&nsub;' },
    { id: 8838, name: '⊆', xmlEncoded: '&sube;' },
    { id: 8839, name: '⊇', xmlEncoded: '&supe;' },
    { id: 8853, name: '⊕', xmlEncoded: '&oplus;' },
    { id: 8855, name: '⊗', xmlEncoded: '&otimes;' },
    { id: 8869, name: '⊥', xmlEncoded: '&perp;' },
    { id: 8901, name: '⋅', xmlEncoded: '&sdot;' },
    { id: 8968, name: '⌈', xmlEncoded: '&lceil;' },
    { id: 8969, name: '⌉', xmlEncoded: '&rceil;' },
    { id: 8970, name: '⌊', xmlEncoded: '&lfloor;' },
    { id: 8971, name: '⌋', xmlEncoded: '&rfloor;' },
    { id: 9001, name: '⟨', xmlEncoded: '&lang;' },
    { id: 9002, name: '⟩', xmlEncoded: '&rang;' },
    { id: 9674, name: '◊', xmlEncoded: '&loz;' },
    { id: 9824, name: '♠', xmlEncoded: '&spades;' },
    { id: 9827, name: '♣', xmlEncoded: '&clubs;' },
    { id: 9829, name: '♥', xmlEncoded: '&hearts;' },
    { id: 9830, name: '♦', xmlEncoded: '&diams;' },
    { name: '!', urlEncoded: '%21' },
    { name: '#', urlEncoded: '%23' },
    { name: '$', urlEncoded: '%24' },
    { name: '%', urlEncoded: '%25' },
    { name: '(', urlEncoded: '%28' },
    { name: ')', urlEncoded: '%29' },
    { name: '*', urlEncoded: '%2A' },
    { name: '+', urlEncoded: '%2B' },
    { name: ',', urlEncoded: '%2C' },
    { name: '-', urlEncoded: '%2D' },
    { name: '.', urlEncoded: '%2E' },
    { name: '/', urlEncoded: '%2F' },
    { name: ':', urlEncoded: '%3A' },
    { name: ';', urlEncoded: '%3B' },
    { name: '=', urlEncoded: '%3D' },
    { name: '?', urlEncoded: '%3F' },
    { name: '@', urlEncoded: '%40' },
    { name: '[', urlEncoded: '%5B' },
    { name: "'", urlEncoded: '%5C' },
    { name: ']', urlEncoded: '%5D' },
    { name: '^', urlEncoded: '%5E' },
    { name: '_', urlEncoded: '%5F' },
    { name: '`', urlEncoded: '%60' },
    { name: '{', urlEncoded: '%7B' },
    { name: '|', urlEncoded: '%7C' },
    { name: '}', urlEncoded: '%7D' },
    { name: '~', urlEncoded: '%7E' },
  ];

  whiteListedEncodedValues: any = {
    brTag: {
      regExp: /&lt;\s*\/*\s*br\s*\/*\s*&gt;/g,
      decodedValue: '<br />',
    },
  };

  private replaceToXmlExt(string: string): string {
    string = string + '';
    if (!this.isAssigned(string)) return string;
    const caracters = string.split('');
    let s = '';

    caracters.forEach((aChar) => {
      s += this.encodeCharToXml(aChar);
    }, this);

    return s;
  }

  private _findCharFromProperty(property: string, value: string): Char | undefined {
    let result; // Use loop for instead of "find" for performance issues (IE11)
    for (var i = 0, iLength = this.chars.length; i < iLength; i++) {
      if ((this.chars[i] as any)[property] == value) {
        result = this.chars[i];
        break;
      }
    }

    return result;
  }

  private _findChar(value: string): Char | undefined {
    return this._findCharFromProperty('sName', value);
  }

  /**
   * @param s a string value
   * @param nbChar the number to characters to split
   */
  left(s: string, nbChar: number): string {
    return s.substring(0, nbChar);
  }

  getUniqueId(): string {
    return new Date().valueOf() + (Math.random() + '').split('.')[1];
  }

  /**
   * Check if a value is empty
   * @param s a string value
   */
  isEmpty(s: string): boolean {
    if (s === undefined) {
      return true;
    } else if (s === '') {
      return true;
    } else {
      return false;
    }
  }

  /**
   * Check if a sub string is present in a string
   * @param s a string value
   * @param value a targeted string value to find in the first param
   */
  inStr(s: string, value: string): boolean {
    return s.indexOf(value) !== -1;
  }

  /**
   * Add an ellipsys in the middle (or the end) of the string
   * @param s a string value
   * @param max the number of caracter after what the ellipsys is added
   * @param middle is the ellipsys is added in the middle of the string or the end.
   */
  split(s: string, max: number, middle = false): string {
    if (max >= s.length) {
      return s;
    }

    if (middle) {
      const part1 = s.substring(0, max / 2);
      const part2 = s.substring(s.length - max / 2, s.length);

      return part1 + '...' + part2;
    } else {
      return s.substring(0, max) + '...';
    }
  }

  isAssigned(string: string): boolean {
    return string != undefined && string != null;
  }

  replaceToXml(string: string, whiteList: any = {}): string {
    let result = this.replaceToXmlExt(string).replace(/&nbsp;/g, ' ');

    for (const key in whiteList) {
      if (whiteList.hasOwnProperty(key) && whiteList[key])
        result = result.replace(
          this.whiteListedEncodedValues[key].regExp,
          this.whiteListedEncodedValues[key].decodedValue
        );
    }

    return result;
  }

  encodeCharToXml(value: string): string {
    const char = this._findChar(value);
    return char?.xmlEncoded ? char.xmlEncoded : value;
  }
}

export const _LegacyStringUtils = new LegacyStringUtilsHelper();
