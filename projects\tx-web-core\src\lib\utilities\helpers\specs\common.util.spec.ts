import { TxCommonUtils } from '../common.util';

interface ObjectTest {
  id: number;
  name: string;
  parent?: ObjectTest;
  concepts: ConceptTest[];
}

interface ConceptTest {
  id: number;
  name: string;
  tag: string;
}

let object: ObjectTest;

beforeEach(() => {
  object = {
    id: 2,
    name: 'test',
    parent: {
      id: 1,
      name: 'parent',
      concepts: [{ id: 0, name: 'name1', tag: 'tag1' }],
    },
    concepts: [
      { id: 0, name: 'name1', tag: 'tag1' },
      { id: 1, name: 'name2', tag: 'tag2' },
      { id: 2, name: 'name3', tag: 'tag3' },
    ],
  };
});

describe('Copy Object', () => {
  it('should copy the whole object', () => {
    expect(TxCommonUtils.deepCopy<ObjectTest>(object)).toStrictEqual({
      id: 2,
      name: 'test',
      parent: {
        id: 1,
        name: 'parent',
        concepts: [{ id: 0, name: 'name1', tag: 'tag1' }],
      },
      concepts: [
        { id: 0, name: 'name1', tag: 'tag1' },
        { id: 1, name: 'name2', tag: 'tag2' },
        { id: 2, name: 'name3', tag: 'tag3' },
      ],
    });
  });
  it('should not modify the original object when modifying prop "name" of the copy', () => {
    const copy = TxCommonUtils.deepCopy<ObjectTest>(object);
    copy.name = 'This is a copy';
    expect(object.name).toBe('test');
  });
  it('should not modify the original object when modifying the "concepts" list of the copy', () => {
    const copy = TxCommonUtils.deepCopy<ObjectTest>(object);
    copy.concepts.push({ id: 3, name: 'name4', tag: 'tag4' });
    expect(object.concepts.length).toBe(3);
  });
  it('should not modify the original object when modifying an element of the "concepts" list of the copy', () => {
    const copy = TxCommonUtils.deepCopy<ObjectTest>(object);
    copy.concepts[0].tag = 'new tag';
    expect(object.concepts[0].tag).toBe('tag1');
  });
});

describe('Copy parent', () => {
  it('should not modify the original object when modifying prop "name" of the parent copy', () => {
    const copy = TxCommonUtils.deepCopy<ObjectTest>(object);
    const parent = copy.parent as ObjectTest;
    parent.name = 'This is a copy';
    expect(object.parent?.name).toBe('parent');
  });
  it('should not modify the original object when modifying the "concepts" list of the parent copy', () => {
    const copy = TxCommonUtils.deepCopy<ObjectTest>(object);
    const parent = copy.parent as ObjectTest;
    parent.concepts.splice(0, 1);
    expect(object.parent?.concepts.length).toBe(1);
  });
  it('should not modify the original object when modifying an element of the "concepts" list of the parent copy', () => {
    const copy = TxCommonUtils.deepCopy<ObjectTest>(object);
    const parent = copy.parent as ObjectTest;
    parent.concepts[0].tag = 'new tag';
    expect(object.parent?.concepts[0].tag).toBe('tag1');
  });
});
