import { ReactiveFormsModule } from '@angular/forms';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxConceptDropdownComponent } from './concept-dropdown.component';
import { MockComponent, MockProvider } from 'ng-mocks';
import { TxDropdownTreeComponent } from '../dropdown-tree/dropdown-tree.component';
import { MOCK_COMMON_SERVICE, TxCommonService } from '../../../data-access/structure';
import {
  MOCK_OBJECT_TYPE_ICON_SERVICE,
  TxObjectTypeIconService,
} from '../../../data-access/session';

describe('ConceptDropdownComponent', () => {
  let component: TxConceptDropdownComponent;
  let fixture: ComponentFixture<TxConceptDropdownComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TxConceptDropdownComponent, MockComponent(TxDropdownTreeComponent)],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: TxCommonService, useValue: MOCK_COMMON_SERVICE },
        MockProvider(TxObjectTypeIconService, MOCK_OBJECT_TYPE_ICON_SERVICE, 'useValue'),
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxConceptDropdownComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
