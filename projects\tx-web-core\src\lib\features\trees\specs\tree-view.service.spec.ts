import { TestBed, inject } from '@angular/core/testing';
import { TxTreeViewService } from '../object-type-tree-view.service';
import { TreeData, TreeDataOptions } from '../object-type-tree-view.model';

const testDataSource: TreeData[] = [
  { name: 'obj1', myObjectID: 0, testAttString: 'toto', testAttArr: [] },
  { name: 'obj2', myObjectID: 1, testAttString: 'tyty', testAttArr: [] },
  { name: 'obj3', myObjectID: 2, myParentID: 4, testAttString: 'toto', testAttArr: [] },
  { name: 'obj4', myObjectID: 3, myParentID: 5, testAttString: 'toto', testAttArr: [] },
  { name: 'obj5', myObjectID: 4, myParentID: 1, testAttString: 'titi', testAttArr: [] },
  { name: 'obj6', myObjectID: 5, myParentID: 0, testAttString: 'toto', testAttArr: [] },
  { name: 'obj7', myObjectID: 6, myParentID: 1, testAttString: 'tata', testAttArr: [] },
];

const testFilteredDataSource: TreeData[] = [
  { name: 'obj2', myObjectID: 1, testAttString: 'tyty', testAttArr: [] },
  { name: 'obj4', myObjectID: 3, myParentID: 5, testAttString: 'toto', testAttArr: [] },
  { name: 'obj7', myObjectID: 6, myParentID: 1, testAttString: 'tata', testAttArr: [] },
];

const testDataOptions: TreeDataOptions = {
  idProperty: 'myObjectID',
  idParentProperty: 'myParentID',
};

describe('Service: TreeView', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [TxTreeViewService],
    });
  });

  it('should create', inject([TxTreeViewService], (service: TxTreeViewService<any>) => {
    expect(service).toBeTruthy();
  }));

  it('should build tree', inject([TxTreeViewService], (service: TxTreeViewService<any>) => {
    expect(service.buildTree(testDataSource, testDataOptions)).toStrictEqual([
      {
        children: [
          {
            children: [
              {
                children: [],
                name: 'obj4',
                objectData: {
                  myObjectID: 3,
                  myParentID: 5,
                  name: 'obj4',
                  testAttArr: [],
                  testAttString: 'toto',
                },
              },
            ],
            name: 'obj6',
            objectData: {
              myObjectID: 5,
              myParentID: 0,
              name: 'obj6',
              testAttArr: [],
              testAttString: 'toto',
            },
          },
        ],
        name: 'obj1',
        objectData: { myObjectID: 0, name: 'obj1', testAttArr: [], testAttString: 'toto' },
      },
      {
        children: [
          {
            children: [
              {
                children: [],
                name: 'obj3',
                objectData: {
                  myObjectID: 2,
                  myParentID: 4,
                  name: 'obj3',
                  testAttArr: [],
                  testAttString: 'toto',
                },
              },
            ],
            name: 'obj5',
            objectData: {
              myObjectID: 4,
              myParentID: 1,
              name: 'obj5',
              testAttArr: [],
              testAttString: 'titi',
            },
          },
          {
            children: [],
            name: 'obj7',
            objectData: {
              myObjectID: 6,
              myParentID: 1,
              name: 'obj7',
              testAttArr: [],
              testAttString: 'tata',
            },
          },
        ],
        name: 'obj2',
        objectData: { myObjectID: 1, name: 'obj2', testAttArr: [], testAttString: 'tyty' },
      },
    ]);
  }));

  it('should filter tree', inject([TxTreeViewService], (service: TxTreeViewService<any>) => {
    expect(
      service.buildFilteredTree(testDataSource, testFilteredDataSource, testDataOptions)
    ).toStrictEqual([
      {
        children: [
          {
            children: [],
            name: 'obj7',
            objectData: {
              myObjectID: 6,
              myParentID: 1,
              name: 'obj7',
              testAttArr: [],
              testAttString: 'tata',
            },
          },
        ],
        name: 'obj2',
        objectData: { myObjectID: 1, name: 'obj2', testAttArr: [], testAttString: 'tyty' },
      },
      {
        children: [
          {
            children: [],
            name: 'obj4',
            objectData: {
              myObjectID: 3,
              myParentID: 5,
              name: 'obj4',
              testAttArr: [],
              testAttString: 'toto',
            },
          },
        ],
        name: 'obj6',
        objectData: {
          myObjectID: 5,
          myParentID: 0,
          name: 'obj6',
          testAttArr: [],
          testAttString: 'toto',
        },
      },
    ]);
  }));
});
