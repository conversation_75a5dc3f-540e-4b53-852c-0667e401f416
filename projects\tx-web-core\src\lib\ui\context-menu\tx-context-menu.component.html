<div
  class="context-menu-position"
  [style.left]="contextMenuTestPosition.x"
  [style.top]="contextMenuTestPosition.y"
  [matMenuTriggerFor]="contextMenuTest"></div>
<mat-menu
  (closed)="closed.emit($event)"
  #contextMenuTest="matMenu"
  #menu
  [overlapTrigger]="true"
  [hasBackdrop]="false">
  <button
    class="context-menu-item-height"
    (click)="onSelect(item, $event)"
    *ngFor="let item of items"
    [disabled]="disableItems.includes(item.id)"
    mat-menu-item>
    {{ item.label | translate }}
  </button>
</mat-menu>
