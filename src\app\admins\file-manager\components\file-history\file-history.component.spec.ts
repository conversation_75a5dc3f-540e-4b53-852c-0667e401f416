import { Component } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { By } from '@angular/platform-browser';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import {
  ToastMockComponent,
  ToastServiceMock,
  LocalizedDateMockPipe,
} from 'src/app/app.testing.mock';
import { ResourcesService } from 'src/app/admins/file-manager/services/resources.service';
import { ToastService, DataBaseRights, TxDialogService } from '@bassetti-group/tx-web-core';
import { ActionType, FileHistory, FileItemType, FileTree } from '../../models/file-models';
import { FileHistoryComponent } from './file-history.component';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { FilesUtils } from 'src/app/core/utils/files';
import { FileManagerService } from '../../services/file-manager.service';
import { FileManagerServiceMock, ResourcesServiceMock } from '../../tests/file-manager.mock';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { DialogConfirmServiceMock } from 'src/app/shared/tests/shared-testing-mock';
import { of } from 'rxjs';
import { MockPipe } from 'ng-mocks';
import { SwitchMultiCasePipe } from 'src/app/shared/pipes/switch/switch-multi-case.pipe';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

const TRANSLATIONS = {
  en: {},
  fr: {},
};

const historiesMock: FileHistory[] = [
  {
    alias: 'CRs',
    path: 'testPath',
    itemName: 'testName',
    itemType: FileItemType.file,
    actionType: ActionType.atDeleteFile,
    authorContactName: 'bonjour',
    authorOSUser: 'Z_Administrator',
    authorIP: '::1',
    dateOfAction: '09/28/2021 14:36:33',
    guid: 'e0652e59-6e1c-4597-ad0a-065b4f6217c6',
    length: 5,
    otherInfo: '',
  },
];
const currentFileHistoryMock: any = { name: 'testName', type: FileItemType.file };
const selectedNodeMock: FileTree = {
  id: '1',
  name: 'Models',
  isFolder: true,
  expanded: false,
  resource: {
    alias: 'CRs',
    right: DataBaseRights.DbrStructure,
    originalRight: DataBaseRights.DbrStructure,
    isFolder: true,
    lastModification: '2021-11-02T13:50:47.2091990Z',
  },
};
const isLoaderActiveMock = true;

@Component({
  selector: 'app-host-component',
  template: `<app-file-history
    [histories]="histories"
    [currentFileHistory]="currentFileHistory"
    [isLoaderActive]="isLoaderActive"
    (hideForm)="hidePanel()"
    (restoreFileVersion)="updateVersionFile($event)"></app-file-history>`,
})
class TestHostComponent {
  histories: FileHistory[] = historiesMock;
  currentFileHistory: any = currentFileHistoryMock;
  isLoaderActive: boolean = isLoaderActiveMock;

  hidePanel() {}
  updateVersionFile() {}
}

describe('FileHistoryComponent', () => {
  let component: FileHistoryComponent;
  let hostComponent: TestHostComponent;
  let hostFixture: ComponentFixture<TestHostComponent>;
  let fileManagerService: FileManagerService;
  let dialogConfirmService: TxDialogService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
    declarations: [
        FileHistoryComponent,
        TestHostComponent,
        LocalizedDateMockPipe,
        MockPipe(SwitchMultiCasePipe),
        ToastMockComponent,
    ],
    imports: [MatDividerModule,
        FontAwesomeTestingModule,
        RouterTestingModule,
        MatTooltipModule,
        MatProgressBarModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} })],
    providers: [
        { provide: ResourcesService, useClass: ResourcesServiceMock },
        { provide: ToastService, useClass: ToastServiceMock },
        { provide: FileManagerService, useClass: FileManagerServiceMock },
        { provide: TxDialogService, useClass: DialogConfirmServiceMock },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
    ]
}).compileComponents();
    fileManagerService = TestBed.inject(FileManagerService);
    dialogConfirmService = TestBed.inject(TxDialogService);
  });

  beforeEach(waitForAsync(() => {
    hostFixture = TestBed.createComponent(TestHostComponent);
    hostComponent = hostFixture.componentInstance;
    component = hostFixture.debugElement.children[0].componentInstance;
    fileManagerService.selectedNode = selectedNodeMock;
    hostFixture.detectChanges();
  }));

  it('should create', () => {
    expect(hostComponent).toBeTruthy();
    expect(component).toBeTruthy();
  });

  it('should be initialized with good Input', () => {
    expect(component.currentFileHistory).toBe(currentFileHistoryMock);
    expect(component.histories).toBe(historiesMock);
    expect(component.selectedNode).toBe(selectedNodeMock);
  });

  describe('Get download version tooltip', () => {
    beforeEach(() => {
      hostComponent.histories = historiesMock;
      fileManagerService.selectedNode = selectedNodeMock;
      hostComponent.isLoaderActive = isLoaderActiveMock;
    });

    it('should return "admins.resources.folderCannotDownload" with Directory', () => {
      hostComponent.histories[0].itemType = FileItemType.directory;
      hostFixture.detectChanges();
      expect(component.getDownloadVersionTooltip(hostComponent.histories[0])).toBe(
        'admins.resources.folderCannotDownload'
      );
    });

    it('should return "admins.resources.folderCannotDownload" with no selectedNode and Directory', () => {
      fileManagerService.selectedNode = undefined;
      hostComponent.histories[0].itemType = FileItemType.directory;
      hostFixture.detectChanges();
      expect(component.getDownloadVersionTooltip(hostComponent.histories[0])).toBe(
        'admins.resources.folderCannotDownload'
      );
    });

    it('should return "admins.resources.folderCannotDownload" with Directory and dbrStructure', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      hostComponent.histories[0].itemType = FileItemType.directory;
      expect(component.getDownloadVersionTooltip(hostComponent.histories[0])).toBe(
        'admins.resources.folderCannotDownload'
      );
    });

    it('should return "admins.resources.fileRestorationNotAllow" with File and no selectedNode', () => {
      component.selectedNode = undefined;
      hostComponent.histories[0].itemType = FileItemType.file;
      hostFixture.detectChanges();
      expect(component.getDownloadVersionTooltip(hostComponent.histories[0])).toBe(
        'admins.resources.fileRestorationNotAllow'
      );
    });

    it('should return "admins.resources.fileRestorationNotAllow" with File and dbrNone', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      hostComponent.histories[0].itemType = FileItemType.file;
      expect(component.getDownloadVersionTooltip(hostComponent.histories[0])).toBe(
        'admins.resources.fileRestorationNotAllow'
      );
    });

    it('should return "button.download" with File and dbrRead', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      hostComponent.histories[0].itemType = FileItemType.file;
      expect(component.getDownloadVersionTooltip(hostComponent.histories[0])).toBe(
        'button.download'
      );
    });

    it('should return "button.download" with File and dbrStructure', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      hostComponent.histories[0].itemType = FileItemType.file;
      expect(component.getDownloadVersionTooltip(hostComponent.histories[0])).toBe(
        'button.download'
      );
    });

    it('should return "button.download" with File and dbrWrite', () => {
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrWrite;
      hostComponent.histories[0].itemType = FileItemType.file;
      expect(component.getDownloadVersionTooltip(hostComponent.histories[0])).toBe(
        'button.download'
      );
    });
  });

  describe('Get restore version tooltip', () => {
    beforeEach(() => {
      hostComponent.histories = historiesMock;
      fileManagerService.selectedNode = selectedNodeMock;
      hostComponent.isLoaderActive = isLoaderActiveMock;
    });

    it('should return "button.restore" with File and drbStructure', () => {
      hostComponent.histories[0].itemType = FileItemType.file;
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      hostFixture.detectChanges();
      expect(component.getRestoreVersionTooltip(hostComponent.histories[0])).toBe('button.restore');
    });

    it('should return "admins.resources.folderCannotRestored" with Directory', () => {
      hostComponent.histories[0].itemType = FileItemType.directory;
      expect(component.getRestoreVersionTooltip(hostComponent.histories[0])).toBe(
        'admins.resources.folderCannotRestored'
      );
    });

    it('should return "admins.resources.folderCannotRestored" with File and dbrRead', () => {
      hostComponent.histories[0].itemType = FileItemType.file;
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      hostFixture.detectChanges();
      expect(component.getRestoreVersionTooltip(hostComponent.histories[0])).toBe(
        'admins.resources.fileAddAndModifNotAllow'
      );
    });

    it('should return "button.restore" with no selectedNode', () => {
      component.selectedNode = undefined;
      hostFixture.detectChanges();
      expect(component.getRestoreVersionTooltip(hostComponent.histories[0])).toBe('button.restore');
    });
  });

  describe('Restore version file', () => {
    beforeEach(() => {
      component.canRestoreVersion = jest.fn().mockReturnValue(true);
    });

    it('should call "restoreVersionFile"', () => {
      const spyRestoreVersionFile = jest.spyOn(component, 'restoreVersionFile');
      hostFixture.debugElement
        .query(By.css('#fm-restore-version-button'))
        .triggerEventHandler('click', null);
      expect(spyRestoreVersionFile).toHaveBeenCalledTimes(1);
      expect(spyRestoreVersionFile).toHaveBeenCalledWith(component.histories?.[0]);
    });

    it('should assign a file to "restoringFile"', () => {
      component.confirmRestoration = jest.fn();
      component.restoringFile = undefined;
      component.restoreVersionFile(component?.histories[0]);
      expect(component.restoringFile).toBe(component.histories?.[0]);
    });

    it('should show confirmation dialog', () => {
      const spyDialog = jest.spyOn(dialogConfirmService, 'open');
      component.restoreVersionFile(component?.histories[0]);
      expect(spyDialog).toHaveBeenCalled();
    });

    it('should call confirmRestoration after user clicked ok', () => {
      const spyConfirmRestoration = jest.spyOn(component, 'confirmRestoration');
      component.restoreVersionFile(component?.histories[0]);
      expect(spyConfirmRestoration).toHaveBeenCalled();
    });

    it('should not call confirmRestoration if user clicked cancel', () => {
      const spyConfirmRestoration = jest.spyOn(component, 'confirmRestoration');
      dialogConfirmService.open = jest.fn().mockReturnValue(of(false));
      hostFixture.detectChanges();

      component.restoreVersionFile(component?.histories[0]);

      expect(spyConfirmRestoration).not.toHaveBeenCalled();
    });

    it('should set the restoringFile to undefined if the user clicked cancel', () => {
      dialogConfirmService.open = jest.fn().mockReturnValue(of(false));
      hostFixture.detectChanges();

      component.restoreVersionFile(component.histories[0]);

      expect(component.restoringFile).toBe(undefined);
    });
  });

  describe('Download version file', () => {
    let mockToast: ToastMockComponent;

    beforeEach(() => {
      mockToast = {
        data: {
          isUnread: true,
          isPersistent: true,
          type: 'success',
          title: 'title',
          description: 'description',
        },
      };
      component.createNotification = jest.fn().mockReturnValue(mockToast);
      component.updateNotification = jest.fn();
      component.canDownloadVersionFile = jest.fn().mockReturnValue(true);
      FilesUtils.downloadBlobFile = jest.fn();
    });

    it('should call "downloadVersionFile"', () => {
      const spyDownloadVersionFile = jest.spyOn(component, 'downloadVersionFile');
      hostFixture.debugElement
        .query(By.css('#fm-download-version-button'))
        .triggerEventHandler('click', null);
      expect(spyDownloadVersionFile).toHaveBeenCalled();
    });

    it('should call "updateNotification"', () => {
      const spyUpdateNotification = jest.spyOn(component, 'updateNotification');
      if (component.histories) {
        component.downloadVersionFile(component.histories[0]);
      }
      expect(spyUpdateNotification).toHaveBeenCalledWith(
        mockToast,
        'success',
        'admins.resources.downloadComplete'
      );
    });

    it('should call "createNotification"', () => {
      const spyCreateNotification = jest.spyOn(component, 'createNotification');
      if (component.histories) {
        component.downloadVersionFile(component.histories[0]);
      }
      expect(spyCreateNotification).toHaveBeenCalled();
    });
  });

  describe('Can restore version', () => {
    beforeEach(() => {
      hostComponent.histories = historiesMock;
      fileManagerService.selectedNode = selectedNodeMock;
      hostComponent.isLoaderActive = isLoaderActiveMock;
    });

    it('should return "true" with File and dbrStructure', () => {
      hostComponent.histories[0].itemType = FileItemType.file;
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      hostFixture.detectChanges();
      expect(component.canRestoreVersion(hostComponent.histories[0])).toBe(true);
    });

    it('should return "true" with Directory', () => {
      hostComponent.histories[0].itemType = FileItemType.directory;
      hostFixture.detectChanges();
      expect(component.canRestoreVersion(hostComponent.histories[0])).toBe(false);
    });

    it('should return "true" with File and dbrRead"', () => {
      hostComponent.histories[0].itemType = FileItemType.file;
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      hostFixture.detectChanges();
      expect(component.canRestoreVersion(hostComponent.histories[0])).toBe(false);
    });

    it('should return "true" with no selectedNode"', () => {
      fileManagerService.selectedNode = undefined;
      hostFixture.detectChanges();
      expect(component.canRestoreVersion(hostComponent.histories[0])).toBe(false);
    });
  });

  describe('Can download version file', () => {
    beforeEach(() => {
      hostComponent.histories = historiesMock;
      fileManagerService.selectedNode = selectedNodeMock;
      hostComponent.isLoaderActive = isLoaderActiveMock;
    });

    it('should return "true" with File and dbrStructure', () => {
      hostComponent.histories[0].itemType = FileItemType.file;
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrStructure;
      hostFixture.detectChanges();
      expect(component.canDownloadVersionFile(hostComponent.histories[0])).toBe(true);
    });

    it('should return "false" with Directory', () => {
      hostComponent.histories[0].itemType = FileItemType.directory;
      hostFixture.detectChanges();
      expect(component.canDownloadVersionFile(hostComponent.histories[0])).toBe(false);
    });

    it('should return "true" with File and dbrRead', () => {
      hostComponent.histories[0].itemType = FileItemType.file;
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrRead;
      hostFixture.detectChanges();
      expect(component.canDownloadVersionFile(hostComponent.histories[0])).toBe(true);
    });

    it('should return "true" with File and dbrWrite', () => {
      hostComponent.histories[0].itemType = FileItemType.file;
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrWrite;
      hostFixture.detectChanges();
      expect(component.canDownloadVersionFile(hostComponent.histories[0])).toBe(true);
    });

    it('should return "false" with File and dbrNone', () => {
      hostComponent.histories[0].itemType = FileItemType.file;
      (fileManagerService.selectedNode as FileTree).resource.right = DataBaseRights.DbrNone;
      hostFixture.detectChanges();
      expect(component.canDownloadVersionFile(hostComponent.histories[0])).toBe(false);
    });

    it('should return "false" with File and no selectedNode', () => {
      hostComponent.histories[0].itemType = FileItemType.file;
      fileManagerService.selectedNode = undefined;
      hostFixture.detectChanges();
      expect(component.canDownloadVersionFile(hostComponent.histories[0])).toBe(false);
    });
  });

  describe('Confirm Restoration', () => {
    beforeEach(() => {
      component.createNotification = jest.fn();
      component.restoringFile = {
        alias: 'CRs',
        path: 'testPath',
        itemName: 'testName',
        itemType: FileItemType.file,
        actionType: ActionType.atDeleteFile,
        authorContactName: 'bonjour',
        authorOSUser: 'Z_Administrator',
        authorIP: '::1',
        dateOfAction: '09/28/2021 14:36:33',
        guid: 'e0652e59-6e1c-4597-ad0a-065b4f6217c6',
        length: 5,
        otherInfo: '',
      };
    });

    it('should assign "null" to restoringFile', () => {
      component.confirmRestoration();
      expect(component.restoringFile).toBe(undefined);
    });

    it('should emit "restoreFileVersion" when calling "confirmRestoration"', () => {
      const file = component.restoringFile;
      jest.spyOn(component.restoreFileVersion, 'emit');
      component.confirmRestoration();
      expect(component.restoreFileVersion.emit).toHaveBeenCalledWith(file);
    });

    it('should call "createNotification"', () => {
      const spyCreateNotification = jest.spyOn(component, 'createNotification');
      component.confirmRestoration();
      expect(spyCreateNotification).toHaveBeenCalledWith(
        'success',
        'admins.resources.restoredFile',
        false,
        0
      );
    });
  });
});
