import { Observable } from 'rxjs';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { AbstractControl, FormControl, FormGroup, Validators } from '@angular/forms';
import { _Utils } from '@bassetti-group/tx-web-core/src/lib/utilities';

@Component({
  selector: 'tx-base-field',
  templateUrl: './base-field.component.html',
  styleUrls: ['./base-field.component.scss'],
})
export class TxBaseFieldComponent implements OnInit, OnChanges {
  @Input() form: FormGroup = new FormGroup({});
  @Input() control!: AbstractControl;
  @Input() id!: string;
  @Input() label!: string;
  @Input() labelTooltip!: string;
  @Input() value: any;
  @Input() required!: boolean;
  @Input() disabled!: boolean;
  @Input() order!: number;
  @Input() information!: string;
  @Input() icon!: string;
  @Input() classes!: string;
  @Input() width = 120;
  @Input() readMode!: boolean;
  @Input() actionIconToolTip!: string;
  @Input() actionIcon!: string[];

  @Output() actionIconClick = new EventEmitter();
  @Output() fieldChange = new EventEmitter<any>();
  @Output() onComplete = new EventEmitter<TxBaseFieldComponent>();
  @Output() valueChange = new EventEmitter<any>();

  public firstValue: any;

  public get isValid() {
    return this.control.valid;
  }

  constructor() {}

  ngOnChanges(changes: SimpleChanges) {
    this.fieldChange.emit(this);
    if (changes.required && this.control) {
      if (changes.required.currentValue) {
        this.control.addValidators(Validators.required);
      } else {
        this.control.removeValidators(Validators.required);
      }
      this.control.updateValueAndValidity();
    }
    if (changes.disabled && this.control) {
      if (changes.disabled.currentValue) {
        this.control.disable();
      } else {
        this.control.enable();
      }
      this.control.updateValueAndValidity();
    }
  }

  ngOnInit() {
    // check if there is someting to load before initializing the field
    this.loadSomething().subscribe((result) => {
      this.initProperties();
      this.initValue();
      this.onComplete.emit(this);
    });
  }

  loadSomething(): Observable<unknown> {
    return new Observable((observer) => {
      observer.next();
      observer.complete();
    });
  }

  getFormControl(id: string) {
    return this.form.get(id);
  }

  initProperties() {
    this.firstValue = this.value;

    if (!this.control) {
      this.control = new FormControl({ value: null, disabled: this.disabled });
      this.form.addControl(this.id, this.control);
    }
    this.control.markAsTouched();
    if (this.required) {
      this.control.setValidators(Validators.required);
    }
    this.initValueChangeEmitter();
  }

  initValueChangeEmitter() {
    this.control.valueChanges.subscribe(() => {
      this.value = this.control.value;
      this.valueChange.emit(this.value);
    });
  }

  initValue() {
    // abstract
  }

  valueHasChanged(): boolean {
    const valueAssigned = _Utils.isAssigned(this.control.value);
    const firstValueAssigned = _Utils.isAssigned(this.firstValue);

    if (!firstValueAssigned && !valueAssigned) {
      return false;
    }

    if (!firstValueAssigned && valueAssigned) {
      // if (this.label === 'test_tracking') {
      //   console.log('test_tracking', valueAssigned, firstValueAssigned, this.control.value[0]);
      // }
      return !this.valueIsEmpty();
    }

    return this.firstValue !== this.control.value;
  }

  valueIsEmpty(): boolean {
    if (!this.control) return false;

    return (
      this.control.value === '' ||
      this.control.value === undefined ||
      this.control.value === null ||
      (Array.isArray(this.control.value) &&
        this.control.value.length <= 1 &&
        this.control.value[0] !== 0 &&
        !this.control.value[0])
    );
  }

  onFieldChange(param?: any) {
    this.fieldChange.emit(param);
  }

  onClearButtonClicked() {
    this.control ? this.control.setValue('') : (this.value = '');
    this.onFieldChange(null);
  }

  onActionIconClick() {
    if (!this.disabled) {
      this.actionIconClick.emit();
    }
  }
}
