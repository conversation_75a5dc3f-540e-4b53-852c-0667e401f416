import { Injectable } from '@angular/core';

@Injectable()
export class LegacyTxArrayUtilsHelper {
  private sortByProperty(a: any, b: any, property: string) {
    if (a[property] < b[property]) {
      return -1;
    }
    if (a[property] > b[property]) {
      return 1;
    }
    return 0;
  }

  constructor() {}

  /**
   * transform a date object into a float value
   */
  sortBy(array: Array<any>, property: string) {
    return array.sort((a, b) => this.sortByProperty(a, b, property));
  }

  sortByName(array: Array<any>) {
    return this.sortBy(array, 'name');
  }

  sortById(array: Array<any>) {
    return this.sortBy(array, 'id');
  }

  sortByOrder(array: Array<any>) {
    return this.sortBy(array, 'order');
  }

  filterFromId(array: any[], value: number): Object[] {
    return array.filter((o) => o['id'] === value);
  }

  instanceFromId(array: Object[], id: any): boolean {
    return this.filterFromId(array, id).length > 0;
  }

  insert(array: Array<any>, index: number, item: any) {
    array.splice(index, 0, item);
  }
}

export const _ArrayUtils = new LegacyTxArrayUtilsHelper();
