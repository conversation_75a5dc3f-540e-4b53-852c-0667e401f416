import { TxNotEmptyFilterPipe } from '../grid-filter-menu/not-empty-filter.pipe';

describe('NotEmptyFilterPipe', () => {
  let pipe: TxNotEmptyFilterPipe;
  beforeEach(() => {
    pipe = new TxNotEmptyFilterPipe();
  });

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return false if filter is empty string', () => {
    expect(pipe.transform('')).toBeFalsy();
  });
  it('should return false if filter is null', () => {
    expect(pipe.transform(null)).toBeFalsy();
  });
  it('should return false if filter is undefined', () => {
    expect(pipe.transform(undefined)).toBeFalsy();
  });
  it('should return false if filter empty array', () => {
    expect(pipe.transform([])).toBeFalsy();
  });
  it('should return true if filter is a string not empty', () => {
    expect(pipe.transform('test')).toBeTruthy();
  });
  it('should return true if filter is a date', () => {
    expect(pipe.transform(new Date())).toBeTruthy();
  });
  it('should return true if filter is a number', () => {
    expect(pipe.transform(1)).toBeTruthy();
  });
  it('should return true if filter is an array not empty', () => {
    expect(pipe.transform(['test'])).toBeTruthy();
  });
});
