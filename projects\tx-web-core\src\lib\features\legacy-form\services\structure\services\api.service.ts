import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { throwError, Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class TxApiService {
  private apiUrl = '';
  public productionMode = true;

  constructor(private httpClient: HttpClient) {}

  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'Unknown error!';
    if (error.error instanceof ErrorEvent) {
      // Client-side errors
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side errors
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
    }
    return throwError(errorMessage);
  }

  private checkApiUrlValid() {
    if (!this.apiUrl) {
      return false;
    }

    return true;
  }

  start(apiUrl: string, productionMode = true) {
    this.apiUrl = apiUrl;
    this.productionMode = productionMode;
  }

  sendGet(url: string, header?: any): Observable<any> {
    if (this.checkApiUrlValid()) {
      return this.httpClient.get(this.apiUrl + url, header).pipe(catchError(this.handleError));
    } else {
      return new Observable();
    }
  }

  sendPost(url: string, args: any, header?: any): Observable<any> {
    if (this.checkApiUrlValid()) {
      return this.httpClient
        .post(this.apiUrl + url, args, header)
        .pipe(catchError(this.handleError));
    } else {
      return new Observable();
    }
  }

  getUrl(url: string): string {
    if (this.checkApiUrlValid()) {
      return this.apiUrl + url;
    }
    return undefined as unknown as string;
  }

  getUserGroups() {
    return this.sendGet('/UserGroups');
  }

  getUsers() {
    return this.sendGet('/Users');
  }

  getUser(id: number) {
    return this.sendGet(`/Users/<USER>
  }

  getUploadFileUrlSave() {
    return this.getUrl('api/File/save');
  }

  getUploadFileUrlRemove() {
    return this.getUrl('api/File/remove');
  }

  readData(arg: any, maxReturnedResult: number = 200): Observable<any> {
    return this.sendPost(`api/Data/read?maxReturnedResult=${maxReturnedResult}`, arg);
  }

  writeData(arg: any): Observable<any> {
    return this.sendPost('api/Data/Write/', arg);
  }

  writeObjectWithData(arg: any): Observable<any> {
    return this.sendPost('api/Objects/write/objectswithdata/', arg);
  }

  listObjectTypes(): Observable<any> {
    return this.sendGet('api/Structure/objectType/');
  }

  listObjectTypesFromIds(idObjectTypes: number[]): Observable<any> {
    return this.sendPost('api/Structure/objectType/id/', idObjectTypes);
  }

  listObjectTypesFromTags(tags: string[]): Observable<any> {
    return this.sendPost('api/Structure/objectType/tag/', tags);
  }

  listAttributesFromObjectType(idObjectType: number): Observable<any> {
    return this.sendGet(`api/Structure/objectType/id/${idObjectType}/attributes`);
  }

  listAttributesFromIds(ids: number[]): Observable<any> {
    return this.sendGet('api/Structure/attribute/id/', { params: { idAttributes: ids } });
  }

  listAttributesFromTags(tags: string[]): Observable<any> {
    return this.sendGet('api/Structure/attribute/tag/', { params: { tags: tags } });
  }

  searchObjects(
    sAndWords: string,
    sOrWords?: string,
    sWithoutWords?: string,
    idObjectType?: number,
    searchData: boolean = true,
    searchFolder: boolean = false,
    limit: number = 3
  ) {
    return this.sendGet(
      `api/Objects/search?sAndWords=${sAndWords}&sOrWords=${sOrWords}&sWithoutWords=${sWithoutWords}&idObjectType=${idObjectType}&searchData=${searchData}&searchFolder=${searchFolder}&limit=${limit}`
    );
  }

  listLinkTypes() {
    return this.sendGet('api/Structure/linkType');
  }

  listFileTypes() {
    return this.sendGet('api/Structure/fileType');
  }

  listTableTypes() {
    return this.sendGet('api/Structure/tableType');
  }

  listUnits() {
    return this.sendGet('api/Structure/unit');
  }

  listObjects(
    idObjectType: number,
    idParentObject: number,
    recursive: boolean,
    includeFolders: boolean,
    maxReturnedResults: number
  ): Observable<any> {
    return this.sendGet(
      `api/Objects/read/childrens?idObjectType=${idObjectType}&idParent=${idParentObject}&isRecursive=${recursive}&includeFolder=${includeFolders}&pageSize=${maxReturnedResults}`
    );
  }

  listObjectsIds(
    idObjectType: number,
    idParentObject: number,
    recursive: boolean,
    includeFolders: boolean
  ): Observable<any> {
    return this.sendPost('api/Objects/listsIds/', {
      idObjectType,
      idParentObject,
      recursive,
      includeFolders,
    });
  }

  getObjects(idObjects: number | number[]): Observable<any> {
    if (Array.isArray(idObjects)) {
      let urlParams = '';
      idObjects.forEach((id) => (urlParams += '&' + 'idObjects=' + id.toString()));
      return this.sendGet('api/Objects/read/objects?' + urlParams);
    } else {
      return this.sendGet(`api/Objects/read/objects?idObjects=${idObjects}`);
    }
  }

  getRLCount(rlTag: string) {
    return Math.floor(Math.random() * 1000 + 1);
  }

  downloadFile(id: number) {
    return this.sendGet(`api/File/download/${id}`, { responseType: 'blob' });
  }

  uploadFile(files: FormData, idAttribute: number) {
    return this.sendPost(`api/File/upload?idAttribute=${idAttribute}`, files, {
      reportProgress: true,
      observe: 'events',
    });
  }

  deleteCacheFile(idsFile: number[]) {
    return this.sendPost(`api/File/delete/cache`, idsFile);
  }
}
