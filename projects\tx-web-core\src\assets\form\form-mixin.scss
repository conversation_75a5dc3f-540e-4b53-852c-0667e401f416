@include mat-core();

@import '../themes/blue-theme.scss';
@import '../themes/teexma-theme.scss';
@import '../themes/components.scss-theme.scss';
@import '../node_modules/@fortawesome/fontawesome-pro/css/all.min.css';

// mixin name will be used in main style.scss
@mixin forms-theme($theme) {
  // retrieve variables from theme
  // (all possible variables, use only what you really need)
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);
  $white: #ffffff;

  // all of these variables contain many additional variables
  .primary {
    color: mat.get-color-from-palette($primary) !important;
  }
  .accent {
    color: mat.get-color-from-palette($accent) !important;
  }
  .warn {
    color: mat.get-color-from-palette($warn) !important;
  }
  /* Forms */
  .form-label {
    color: mat.get-color-from-palette($foreground, grey40) !important;
  }

  .form-field {
    background-color: mat.get-color-from-palette($foreground, grey5);
    border: solid 1px transparent;
    transition: border 0.2s;
  }

  .form-field:hover {
    border: solid 1px mat.get-color-from-palette($foreground, grey40);
  }

  .form-field-error {
    border: solid 1px mat.get-color-from-palette($warn) !important;
  }

  .mat-form-field-disabled .mat-form-field-underline {
    background-color: mat.get-color-from-palette($foreground, grey5) !important;
    background-image: linear-gradient(
      to right,
      mat.get-color-from-palette($foreground, field-borders) 0%,
      mat.get-color-from-palette($foreground, field-borders) 33%,
      transparent 0%
    ) !important;
  }

  .form-field-disabled .mat-form-field-underline {
    background-color: mat.get-color-from-palette($foreground, grey5) !important;
    background-image: linear-gradient(
      to right,
      mat.get-color-from-palette($foreground, field-borders) 0%,
      mat.get-color-from-palette($foreground, field-borders) 33%,
      transparent 0%
    ) !important;
    background-size: 4px 100%;
    background-repeat: repeat-x;
  }

  .form-information {
    background-color: mat.get-color-from-palette($foreground, grey5);
    color: mat.get-color-from-palette($foreground, grey40);
  }

  .form-fieldset:hover {
    border: 1px solid mat.get-color-from-palette($foreground, field-borders);
  }

  .fieldset-legend {
    color: mat.get-color-from-palette($foreground, subtitle);
  }

  .fieldset-legend .form-button-field {
    color: mat.get-color-from-palette($foreground, grey40);
  }

  .fieldset-legend:hover .form-button-field {
    color: mat.get-color-from-palette($foreground, text);
  }

  .fieldset-legend .display-group-icon-container {
    background-color: transparent;
  }

  .fieldset-legend:hover .display-group-icon-container {
    background-color: mat.get-color-from-palette($foreground, grey10);
  }

  .progress-bar .mat-progress-bar-fill::after {
    background-color: mat.get-color-from-palette($accent) !important;
  }

  .panel-header {
    height: 55px;
    line-height: 55px;
    padding: 16px 32px 8px;
  }

  .header-icon {
    color: mat.get-color-from-palette($foreground, title);
  }

  .panel-title {
    color: mat.get-color-from-palette($foreground, title);
    margin-right: 16px;
    display: inline-block;
    vertical-align: middle;
    max-width: 650px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .mat-menu-item:hover {
    background-color: mat.get-color-from-palette($foreground, grey20);
  }
  .mat-tooltip {
    background-color: mat.get-color-from-palette($foreground, text);
    color: mat.get-color-from-palette($background, base);
  }

  .mat-button-toggle-checked.mat-button-toggle {
    color: mat.get-color-from-palette($foreground, grey5);
    background-color: mat.get-color-from-palette($accent);
  }

  .mat-button-toggle-group {
    height: 30px;
    align-items: center;
  }

  .data-chip.uploaded-file {
    outline: 1px solid mat.get-color-from-palette($accent);
  }

  .form-switch {
    &.mat-checked {
      .mat-slide-toggle-bar {
        background-color: mat.get-color-from-palette($accent, 100);
      }
      .mat-slide-toggle-bar::after {
        color: mat.get-color-from-palette($accent);
      }
    }
    &:not(.mat-checked) {
      .mat-slide-toggle-bar {
        background-color: mat.get-color-from-palette($foreground, grey20);
      }
      .mat-slide-toggle-bar::after {
        color: mat.get-color-from-palette($foreground, text);
      }
    }
  }

  .mat-button-toggle {
    color: mat.get-color-from-palette($foreground, text);
    background-color: mat.get-color-from-palette($foreground, grey5);
    transition: background-color 0.3s;
  }

  .mat-button-toggle-right {
    border-left: 1px double mat.get-color-from-palette($foreground, borders);
  }

  .mat-select-panel {
    background-color: mat.get-color-from-palette($background, base);
  }

  .form-unit-option.mat-selected.mat-option {
    color: mat.get-color-from-palette($accent);
  }

  .uploader-dragzone {
    background-color: mat.get-color-from-palette($background, base);
  }

  .accent-border {
    border: solid 1px mat.get-color-from-palette($accent);
  }

  .form-unit-option.mat-option:hover {
    background-color: mat.get-color-from-palette($foreground, grey5);
  }

  .border-grey {
    border: 1px solid mat.get-color-from-palette($foreground, borders);
  }

  .field-form-hint {
    color: mat.get-color-from-palette($foreground, grey40);
  }

  .span-error {
    color: mat.get-color-from-palette($warn);
  }

  .disabled-icon {
    color: mat.get-color-from-palette($foreground, grey40) !important;
    cursor: default !important;
  }

  .panel-divider {
    border: 1px solid mat.get-color-from-palette($foreground, divider);
  }

  .chip-selected {
    color: mat.get-color-from-palette($foreground, grey5) !important;
    background-color: mat.get-color-from-palette($accent) !important;
  }

  .data-chip {
    background-color: mat.get-color-from-palette($background, base) !important;
    color: mat.get-color-from-palette($foreground, text) !important;
  }

  .data-chip.mat-chip.mat-standard-chip.mat-chip-selected {
    background-color: mat.get-color-from-palette($accent) !important;
    color: mat.get-color-from-palette($background, base) !important;
  }

  .pane-data-chip {
    background-color: mat.get-color-from-palette($foreground, grey5);
    color: mat.get-color-from-palette($foreground, text);
  }

  .error-number {
    background-color: mat.get-color-from-palette($warn) !important;
    color: mat.get-color-from-palette($background, base);
  }

  .mandatory-label .e-float-text::after {
    color: mat.get-color-from-palette($foreground, grey40);
  }
  .mandatory-label-error-field .e-float-text::after {
    color: mat.get-color-from-palette($warn);
  }

  .date-time-picker-error {
    .e-float-input {
      border-bottom-color: mat.get-color-from-palette($warn) !important;
    }
  }

  .action-icon {
    position: absolute;
    cursor: pointer;
    right: 16px;
    font-size: 16px;
    color: mat.get-color-from-palette($accent);
  }

  .action-icon-grey {
    color: mat.get-color-from-palette($foreground, grey60);
  }

  .chip-icon {
    color: mat.get-color-from-palette($foreground, grey60);
  }

  .form-icon {
    color: mat.get-color-from-palette($accent);
  }

  .form-tab-group .mat-ink-bar {
    background-color: mat.get-color-from-palette($accent) !important;
  }

  .true-caption-chip {
    background-color: mat.get-color-from-palette($accent);
    color: mat.get-color-from-palette($foreground, grey5) !important;
  }

  .false-caption-chip {
    border: 1px solid mat.get-color-from-palette($accent);
    color: mat.get-color-from-palette($accent) !important;
  }

  .mat-form-field-underline {
    /*change color of underline*/
    background-color: mat.get-color-from-palette($foreground, field-borders) !important;
  }

  .mat-form-field-label {
    width: 100% !important;
  }

  .point-field .mat-form-field-label-wrapper {
    overflow: visible;
  }

  .read-form-label {
    display: block;
    margin-bottom: 0px;
  }

  .point-field .mat-form-field-label-wrapper .mat-form-field-label {
    width: 260px !important;
  }

  /* material components */
  .mat-tab-nav-bar,
  .mat-tab-header {
    border-bottom: 1px solid mat.get-color-from-palette($foreground, grey20);
  }
  .mat-bottom-sheet-container {
    background-color: mat.get-color-from-palette($primary);
  }

  .show-detail-container {
    fa-icon {
      color: mat.get-color-from-palette($accent);
    }

    .display-detail-span {
      color: mat.get-color-from-palette($accent);
      text-decoration: underline;
      cursor: pointer;
    }
  }

  .mat-input-element {
    caret-color: mat.get-color-from-palette($foreground, text) !important;
  }
  /* This rule is missing because of theming */
  .mat-checkbox-mixedmark {
    background-color: #fafafa;
  }

  /* syncfusion components */
  label.e-float-text.e-label-top {
    color: mat.get-color-from-palette($foreground, grey40) !important;

    &.e-label-top {
      transform: translate3d(0, -6px, 0) scale(1) !important;
      font-size: 12px !important;
    }
  }

  .url-read {
    color: mat.get-color-from-palette($foreground, url-text);
  }

  .extender-chip-text {
    color: mat.get-color-from-palette($foreground, url-text);
  }

  input.e-input::selection,
  textarea.e-input::selection,
  .e-input-group input.e-input::selection,
  .e-input-group.e-control-wrapper input.e-input::selection,
  .e-float-input input::selection,
  .e-float-input.e-control-wrapper input::selection,
  .e-input-group textarea.e-input::selection,
  .e-input-group.e-control-wrapper textarea.e-input::selection,
  .e-float-input textarea::selection,
  .e-float-input.e-control-wrapper textarea::selection {
    background: mat.get-color-from-palette($background, selected-text);
    color: mat.get-color-from-palette($foreground, selected-text);
  }

  input.e-input::-moz-selection,
  textarea.e-input::-moz-selection,
  .e-input-group input.e-input::-moz-selection,
  .e-input-group.e-control-wrapper input.e-input::-moz-selection,
  .e-float-input input::-moz-selection,
  .e-float-input.e-control-wrapper input::-moz-selection,
  .e-input-group textarea.e-input::-moz-selection,
  .e-input-group.e-control-wrapper textarea.e-input::-moz-selection,
  .e-float-input textarea::-moz-selection,
  .e-float-input.e-control-wrapper textarea::-moz-selection {
    background: mat.get-color-from-palette($background, moz-selected-text);
    color: mat.get-color-from-palette($foreground, selected-text);
  }
}

@mixin custom-components-theme($theme) {
  @include forms-theme($theme);
}

// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component
// that you are using.
.blue-theme-light {
  @include angular-material-theme($Application-theme-blue-light);
  @include components-theme($Application-theme-blue-light);
  @include custom-components-theme($Application-theme-blue-light);

  .main-toolbar-menu {
    margin-top: 7px;
    border-radius: 0px !important;
    border-top: 4px solid mat.get-color-from-palette(map-get($Application-theme-blue-light, accent));
    max-width: 500px;
  }
  .snack-notification {
    background-color: mat.get-color-from-palette(map-get($Application-theme-blue-light, primary));
    position: absolute;
    top: 63px;
    right: 16px;
    margin-top: 0px !important;
    margin-right: 0px !important;
  }
}

.teexma-theme-light {
  @include angular-material-theme($Application-theme-teexma-light);
  @include components-theme($Application-theme-teexma-light);
  @include custom-components-theme($Application-theme-teexma-light);

  .main-toolbar-menu {
    margin-top: 7px;
    border-radius: 0px !important;
    border-top: 4px solid
      mat.get-color-from-palette(map-get($Application-theme-teexma-light, accent));
    max-width: 500px;
  }
  .snack-notification {
    background-color: mat.get-color-from-palette(map-get($Application-theme-teexma-light, primary));
    position: absolute;
    top: 63px;
    right: 16px;
    margin-top: 0px !important;
    margin-right: 0px !important;
  }
}

.blue-theme-dark {
  @include angular-material-theme($Application-theme-blue-dark);
  @include components-theme($Application-theme-blue-dark);
  @include custom-components-theme($Application-theme-blue-dark);

  .main-toolbar-menu {
    margin-top: 7px;
    border-radius: 0px !important;
    border-top: 4px solid mat.get-color-from-palette(map-get($Application-theme-blue-dark, accent));
    max-width: 500px;
  }
  .snack-notification {
    background-color: mat.get-color-from-palette(map-get($Application-theme-blue-dark, primary));
    position: absolute;
    top: 63px;
    right: 16px;
    margin-top: 0px !important;
    margin-right: 0px !important;
  }
}

.teexma-theme-dark {
  @include angular-material-theme($Application-theme-teexma-dark);
  @include components-theme($Application-theme-teexma-dark);
  @include custom-components-theme($Application-theme-teexma-dark);

  .main-toolbar-menu {
    margin-top: 7px;
    border-radius: 0px !important;
    border-top: 4px solid
      mat.get-color-from-palette(map-get($Application-theme-teexma-dark, accent));
    max-width: 500px;
  }
  .snack-notification {
    background-color: mat.get-color-from-palette(map-get($Application-theme-teexma-dark, primary));
    position: absolute;
    top: 63px;
    right: 16px;
    margin-top: 0px !important;
    margin-right: 0px !important;
  }
}

.e-btn {
  text-transform: none;
}

/* Form */
.form-label {
  user-select: text;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Angular material */
.mat-form-field.mat-form-field-should-float label {
  font-size: 12px;
  transform: translateY(-1.2em) scale(1) !important;
}
.mat-form-field label {
  font-size: 13px;
}
input.mat-input-element {
  margin-top: 8px;
}

.mat-tooltip-multiline {
  white-space: pre-line;
}

.mat-tab-group-short .mat-tab-label {
  width: 60px;
  min-width: 60px;
  max-width: 60px;
}

/* Chrome/Webkit scrollbar */
*::-webkit-scrollbar {
  width: 6px;
  height: 4px;
}

*::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0);
}

*::-webkit-scrollbar-thumb {
  background: #808080;
  border-radius: 10px;
}

/* Fireforx scrollbar */
* {
  scrollbar-color: #808080 rgba(0, 0, 0, 0);
  scrollbar-width: thin;
}

.mat-select-panel {
  background: #fff;
}

.mat-select-panel:not([class*='mat-elevation-z']) {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14),
    0 1px 10px 0 rgba(0, 0, 0, 0.12);
}

.h1-title {
  font-size: 30px;
  font-weight: 600;
}

.h2-section-title {
  font-size: 24px;
  font-weight: 600;
}

.form-field {
  border-radius: 10px;
  padding: 10px 20px 0px;
  margin: 10px 14px;
  width: calc(100% - 70px);
  min-height: 66px;
  position: relative;
}

.read-field {
  padding-bottom: 2px !important;
  min-height: 64px;
}

.form-unit-select {
  .mat-form-field-wrapper {
    padding-bottom: 0px !important;
  }

  .mat-form-field-infix {
    padding-bottom: 0;
  }

  .mat-select-value {
    max-width: 57px;
  }
}

.form-tab-group .mat-tab-header {
  margin-bottom: 16px !important;
}

.e-disabled {
  cursor: unset !important;
}

.clickable {
  cursor: pointer !important;
}

.hide-item {
  display: none;
}

.e-ddt.e-popup .e-popup-content.e-no-data {
  padding: 0 8px !important;

  .norecord {
    padding: 10px;
    display: block;
  }
}

.fields-container {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
}

.field-container {
  width: 100%;
}

.generic-form-field {
  float: left;

  min-height: 60px;
}

.generic-short-field {
  width: 340px;
}

.double-field {
  width: 680px !important;
}

.generic-large-field {
  width: 100%;
}

.form-group-field {
  display: inline-block;
  width: 100%;
}

.hidden-field {
  display: none;
}
