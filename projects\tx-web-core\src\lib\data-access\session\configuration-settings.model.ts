import { Lang } from './lang.model';

export interface ConfigurationSettings {
  administration: AdministrationSettings;
  authenticationModes: AuthenticationSettings;
  languages: Lang[];
  defaultLanguageCode?: string;
  preferences: PreferencesSettings;
  documentsStorageMode: DatabaseDocType;
  specificIcons: string[];
}

export interface AuthenticationSettings {
  internal: InternalAuthSettings;
  ldap: LDAPAuthSettings;
  saml: SamlAuthSettings;
  windows: WindowsAuthSettings;
}

export interface InternalAuthSettings {
  enabled: boolean;
  displayUsersList: boolean;
  useStrongPasswordPolicy: boolean;
}

export interface LDAPAuthSettings {
  enabled: boolean;
  displayUsersList: boolean;
  allowUserCreation: boolean;
  allowUserUpdate: boolean;
}

export interface WindowsAuthSettings {
  enabled: boolean;
  allowUserCreation: boolean;
  allowUserUpdate: boolean;
}

export interface SamlAuthSettings extends WindowsAuthSettings {
  certificatPassword: string;
}

export interface PreferencesSettings {
  sDateAndTimeFormat?: string;
  sDecimalSeparator?: string;
  sShortDateFormat?: string;
  sShortDescription?: string;
  bUseNewIconSet?: string;
}

export interface AdministrationSettings {
  teexmaAccessSettings: TEEXMAAccessSettings;
  timeoutDuration: number;
}

export interface TEEXMAAccessSettings {
  accessMode: TxApplicationAccessMode;
  url?: string;
}

export enum TxApplicationAccessMode {
  Disabled = 'aamDisabled',
  Absolute = 'aamAbsolute',
  Relative = 'aamRelative',
}

export enum DatabaseDocType {
  directory = 'dsmDirectory',
  separatedDatabase = 'dsmSeparatedDatabase',
  mainDatabase = 'dsmMainDatabase',
}
