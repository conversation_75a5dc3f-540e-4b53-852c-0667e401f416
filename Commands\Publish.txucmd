<TXUtils xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="D:\CodeBassetti\Others\Devs\Resources\TXUCmd.xsd">
    <Consts>
        <Const sName="#ProjectDir#" sValue="#FileDir#..\"/>
        <Const sName="#ProjectName#" sValue="TxAdministration"/>
		
        <Const sName="#Major#" sValue="1"/>
        <Const sName="#Minor#" sValue="0"/>
        <Const sName="#Release#" sValue="0"/>
        <Const sName="#PreRelease#" sValue=""/>  <!-- Do not use _ for this token -->      
        <Const sName="#PackageVersion#" sValue="#Major#.#Minor#.#Release#"/>
        <Const sName="#PackageFullVersion#" sValue="#PackageVersion##Prerelease#"/>


        <Const sName="#ProjectPublishTmpDir#" sValue="#ProjectDir#dist\AWeb\"/>
		
        <Const sName="#ProjectPublishDir#" sValue="\\vfiler01\TxDev\Internal\#ProjectName#\#PackageFullVersion#\"/>
    </Consts>

	<Execute sPath_File="npm" sParameters="install" bVisible="false" bUse_CreateProcess="false" sDir="#ProjectDir#" bWait="true"/>
	<Remove sPathes="#ProjectPublishTmpDir#"/>
	<Execute sPath_File="npm" sParameters="run build-prod-app" bVisible="false" bUse_CreateProcess="false" sDir="#ProjectDir#" bWait="true"/>
	<Execute sPath_File="npm" sParameters="run remove" bVisible="false" bUse_CreateProcess="false" sDir="#ProjectDir#" bWait="true"/>
	<Copy sPath_Src="#FILEDIR#..\web.config" sPath_Dest="#ProjectPublishTmpDir#web.config"/>
	<ZipDir sDir="#ProjectPublishTmpDir#" sPath_File_Dest="#ProjectPublishDir#Debug\#ProjectName#.7z"/>
	<ZipDir sDir="#ProjectPublishTmpDir#" sPath_File_Dest="#ProjectPublishDir#Release\#ProjectName#.7z"/>
</TXUtils>