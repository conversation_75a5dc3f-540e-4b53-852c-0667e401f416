import { Component, OnInit, Output, ViewChild, EventEmitter } from '@angular/core';
import { ChipListComponent, ClickEventArgs } from '@syncfusion/ej2-angular-buttons';
import { LegacyTxAttributesService } from '../../../../services/structure/services/attributes.service';
import { TxElementSelectionBaseFieldComponent } from '../_element-selection-base-field/element-selection-base-field.component';

@Component({
  selector: 'tx-elements-selection-alt-field',
  templateUrl: './elements-selection-alt-field.component.html',
  styleUrls: ['./elements-selection-alt-field.component.scss'],
})
export class TxElementsSelectionAltFieldComponent
  extends TxElementSelectionBaseFieldComponent
  implements OnInit
{
  @Output() clickEvent = new EventEmitter<ClickEventArgs>();
  @Output() createEvent = new EventEmitter<Event>();

  @ViewChild('chipList') chipList!: ChipListComponent;

  selectedElementIndexes: number[] = [];

  constructor(public attributeService: LegacyTxAttributesService) {
    super(attributeService);
  }

  ngOnInit() {
    super.ngOnInit();

    // transform selected elements ids into indexes
    if (this.selectedElements && this.selectedElements.length) {
      this.selectedElementIndexes = this.selectedElements.map((e) => {
        return this.elements.findIndex((f) => f.value === e);
      });
    }
  }

  onCreated(event: Event) {
    // bind on mouse event args
    this.createEvent.emit(event);

    const chips = this.chipList.element.getElementsByClassName('e-chip');
    for (let i = 0; i < chips.length; i++) {
      const chip = chips[i];

      const value = chip.getAttribute('data-value');
      const element = this.getElement(value);

      if (element)
        chip.getElementsByClassName('e-chip-text')[0].setAttribute('title', element.hintValue);
    }
  }

  onClicked(event: ClickEventArgs) {
    this.clickEvent.emit(event);
  }
}
