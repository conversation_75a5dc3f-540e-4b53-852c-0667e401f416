import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CTxFileType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TxConfigService } from '@bassetti-group/tx-web-core/src/lib/data-access/config';
import { TxAbstractConceptService } from './abstract-concept.service';
import { TxObjectTypeIconService } from '@bassetti-group/tx-web-core/src/lib/data-access/session';

@Injectable({
  providedIn: 'root',
})
export class TxFileTypesService extends TxAbstractConceptService<CTxFileType> {
  authorizedExtensions: string[] = [];

  protected urlListAllConcepts = 'api/Structure/filetype';

  constructor(
    configService: TxConfigService,
    http: HttpClient,
    objectTypeIcon: TxObjectTypeIconService
  ) {
    super(configService, http, objectTypeIcon);
  }

  reset() {
    super.reset();
    this.authorizedExtensions = [];
  }

  getIcon(extension: string): string {
    let icon = 'document.svg';

    switch (extension.toLowerCase()) {
      case 'csv':
        icon = 'file_format_csv.svg';
        break;
      case 'xml':
        icon = 'file_format_xml.svg';
        break;
      case 'xls':
        icon = 'file_format_xls.svg';
        break;
      case 'doc':
        icon = 'file_format_doc.svg';
        break;
      case 'ppt':
        icon = 'file_format_ppt.svg';
        break;
      case 'fon':
        icon = 'file_format_fon.svg';
        break;
      case 'otf':
        icon = 'file_format_otf.svg';
        break;
      case 'rtf':
        icon = 'file_format_rtf.svg';
        break;
      case 'txt':
        icon = 'file_format_txt.svg';
        break;
    }

    return icon;
  }

  addFileTypes(fileTypes: Omit<CTxFileType, 'id'>[]): Observable<CTxFileType[]> {
    return this.http.post<CTxFileType[]>(this.apiUrl + this.urlListAllConcepts, fileTypes).pipe(
      tap((newFileTypes) => {
        newFileTypes.forEach((ft) => {
          ft.baseName = '';
          this.concepts.push(this.create(ft));
        });
        this.send();
      })
    );
  }

  editFileTypes(fileTypes: CTxFileType[]): Observable<CTxFileType[]> {
    return this.http.put<CTxFileType[]>(this.apiUrl + this.urlListAllConcepts, fileTypes).pipe(
      tap(() => {
        fileTypes.forEach((ft) => {
          const existingFileType = this.concepts.find((tt) => tt.id === ft.id);
          if (existingFileType) {
            Object.assign(existingFileType, ft);
          }
        });
      })
    );
  }

  deleteFileType(idFileType: number): Observable<any> {
    return this.http.delete(this.apiUrl + this.urlListAllConcepts + '/' + idFileType).pipe(
      tap(() => {
        // remove table type
        this.concepts = this.concepts.filter((tt) => tt.id !== idFileType);
        this.send();
      })
    );
  }

  clean(idFileType: number): Observable<any> {
    return this.http
      .delete(this.apiUrl + this.urlListAllConcepts + '/' + idFileType + '/unusedfiles')
      .pipe();
  }

  cleanAll(): Observable<any> {
    return this.http.delete(this.apiUrl + this.urlListAllConcepts + '/unusedfiles').pipe();
  }

  getDirectories(): Observable<any> {
    return this.http.get(this.apiUrl + this.urlListAllConcepts + '/directories/').pipe();
  }

  getAuthorizedExtensions(reload = false): Observable<any> {
    return new Observable((observer) => {
      if (!this.authorizedExtensions.length || reload) {
        this.http
          .get(this.apiUrl + this.urlListAllConcepts + '/authorizedextensions')
          .subscribe((authorizedExtensions: any) => {
            this.authorizedExtensions = authorizedExtensions.filter((e: string) => e !== '');

            observer.next(this.authorizedExtensions);
            observer.complete();
          });
      } else {
        observer.next(this.authorizedExtensions);
        observer.complete();
      }
    });
  }

  getDefaultAuthorizedExtensions(): Observable<any> {
    return this.http
      .get(this.apiUrl + this.urlListAllConcepts + '/authorizedextensions/default')
      .pipe();
  }

  saveAuthorizedExtensions(extensions: string[]): Observable<any> {
    return this.http
      .put(this.apiUrl + this.urlListAllConcepts + '/authorizedextensions', extensions)
      .pipe(tap(() => (this.authorizedExtensions = extensions)));
  }

  override create(concept: CTxFileType): CTxFileType {
    return new CTxFileType(concept);
  }
}
