import { LegacyTxObject } from '../../structure/models/object';
import { LegacyTxObjectTypeService } from './object-type.service';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { TxApiService } from './api.service';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class LegacyTxObjectsService {
  // to remove
  txObjects: LegacyTxObject[] = [];

  constructor(
    private apiService: TxApiService,
    public objectsTypeService: LegacyTxObjectTypeService
  ) {}

  public createObjects(objects: any[]): LegacyTxObject[] {
    const txObjectType = objects.length
      ? this.objectsTypeService.getObjectType(objects[0].idObjectType)
      : null;

    return objects.map((o) => {
      const txObject = LegacyTxObject.assign(o);

      if (txObjectType) {
        txObject.image = txObjectType.icon + '.png';
      }

      return txObject;
    });
  }

  /**
   * Return an array of objects
   * @param idObjectType id of the object type
   * @param idParentObject id of the parent object
   * @param recursive check in all sub branches ?
   * @param includeFolders return folder ids too ?
   */
  listObjects(
    idObjectType: number,
    idParentObject: number = 0,
    recursive: boolean = false,
    includeFolders: boolean = true,
    fillHint: boolean = false,
    maxReturnResults = 200
  ): Observable<LegacyTxObject[]> {
    if (idParentObject == undefined) {
      idParentObject = 0;
    }

    return new Observable((observer) => {
      this.apiService
        .listObjects(idObjectType, idParentObject, recursive, includeFolders, maxReturnResults)
        .subscribe((result) => {
          observer.next(this.createObjects(result.objects));
          observer.complete();
        });
    });
  }

  /**
   * Return a list of TxObject(s)
   * @param paramIdObject id or array of id's object(s)
   * @returns an observable which return list of TxObject
   */
  getObjects(paramIdObject: number | number[]): Observable<LegacyTxObject[]> {
    const idObjects = Array.isArray(paramIdObject) ? paramIdObject : [paramIdObject];
    return new Observable((observer) => {
      this.apiService.getObjects(idObjects).subscribe((result) => {
        observer.next(this.createObjects(result.objects));
        observer.complete();
      });
    });
  }

  /**
   * Return a list of objects according a text search value
   * @param searchString the text search value
   * @param idObjectType id of the object type
   * @param _idAttribute id of an attribute in case of a search in a link from form
   * @param _idParentObject id of the parent object
   * @param _searchInLkdObjects search in linked objects ?
   * @param _searchInData search in data ?
   */
  searchObjects(
    sAndWords: string,
    sOrWords: string = '',
    sWithoutWords: string = '',
    idObjectType: number = 0,
    searchData: boolean = true,
    searchFolder: boolean = false,
    limit: number = 3
  ) {
    return this.apiService
      .searchObjects(
        sAndWords,
        sOrWords,
        sWithoutWords,
        idObjectType,
        searchData,
        searchFolder,
        limit
      )
      .pipe(
        map((result) => {
          return this.createObjects(result.objects);
          // return objects.map(object => {
          //   const txObject = TxObject.assign(object);
          //   const txObjectType = this.objectsTypeService.getObjectType(txObject.idObjectType);

          //   txObject.image = txObjectType.icon + '.png';

          //   return txObject;
          // });
        })
      );
  }
}
