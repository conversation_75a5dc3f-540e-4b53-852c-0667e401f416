import { CTxFileType } from 'src/app/shared/models/file-type';
import { TableType } from 'src/app/shared/models/table-types';
import { Unit } from 'src/app/shared/models/units';
import { ExportDataMapSettings } from '../models/export-data-map-settings';

export class DataModelServiceMock {
  private apiUrl?: string;
  private listUnits: Unit[] = [];
  private listTableType: TableType[] = [];
  private listFileType: CTxFileType[] = [];

  public mapToJson(map: Map<number, ExportDataMapSettings>) {}

  public exportDataModel(jsonTemplate: any) {}
}
