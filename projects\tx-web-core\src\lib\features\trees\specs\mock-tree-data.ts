const LOCKING_DURATION = 0.0006944444444444445;
export const MOCK_TREE_DATA = () => {
  return [
    {
      idObjectTypeParent: 4213,
      order: 0,
      icon: 232,
      lockingDuration: 0.0013888888888888887,
      description: 'On regroupe ici tous les services.',
      name: 'Services',
      label: 'Services',
      tags: ['OTLaboratories'],
      id: 24,
      iconPath: './img/icons/svg/232.svg',
    },
    {
      order: 1,
      icon: 262,
      lockingDuration: LOCKING_DURATION,
      description: 'On regroupe ici tous les départements.',
      name: 'Départements',
      label: 'Départements',
      tags: ['oTDepartements'],
      id: 4213,
      iconPath: './img/icons/svg/262.svg',
    },
    {
      order: 2,
      icon: 222,
      lockingDuration: LOCKING_DURATION,
      description: 'On regroupe ici tous Sociétés.',
      name: 'Sociétés',
      label: 'Sociétés',
      tags: ['oTSocietes'],
      id: 1234,
      iconPath: './img/icons/svg/222.svg',
    },
  ].map((data) => ({
    ...data,
    isFolder: false,
    type: 'ottStandard',
    hasDistinctNames: false,
    isVisible: true,
    lockingType: 'otltNone',
    displayResultInTextSearch: true,
    right: 'dbrStructure',
    associatedObjectTypesIds: [],
    explanation: '',
  }));
};
