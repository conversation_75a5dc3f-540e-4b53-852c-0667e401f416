export class StringUtils {
  /**
   * Generate a string with an the first increment available from a given list
   *
   * @param text The text to add the increment
   * @param list The complete list to check for the increment
   * @param prop The property name to check on the list
   * @param index The index to start for the increment (1: by default)
   */
  public static generateAutoIncrement<T>(
    text: string,
    list: T[],
    prop: keyof T,
    index?: number
  ): string {
    const result = index !== undefined ? text + index : text;
    if (list.some((l) => (l[prop] as string).toLowerCase() === result.toLowerCase())) {
      if (!index) {
        index = 0;
      }
      return this.generateAutoIncrement(text, list, prop, index + 1);
    } else {
      return result;
    }
  }

  /**
   * Generate a duplication name or tags using this format duplicatedname(index)
   *
   * @param text The name of duplicated object
   * @param list The complete list to check for the increment
   */
  public static generateDuplicationNameOrTag(text: string, list: any[], prop: string): string {
    const duplicationIndex = /\(\d+\)/;
    const originalObjName = text.replace(duplicationIndex, '');

    const escapedOriginalObjName = originalObjName.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&'); // in case it contains parentheses in its name
    const duplicatedObjectPattern = new RegExp(`${escapedOriginalObjName}\\(\\d+\\)`);

    const filteredArray = list.filter((item) => duplicatedObjectPattern.test(item[prop]));
    let highestIndex = 0;

    //to extract the index of duplicated objects and store the highest index
    if (filteredArray.length > 0) {
      filteredArray.forEach((element) => {
        const regex = /\((\d+)\)/;
        const match = element.name.match(regex);
        if (match) {
          const currentIndex = parseInt(match[1]);
          highestIndex = currentIndex > highestIndex ? currentIndex : highestIndex;
        }
      });
    }

    return originalObjName + '(' + (highestIndex + 1).toString() + ')';
  }

  /* Transform a given string to CamelCase ("This is an example" => "thisIsAnExample")
   *
   * @param value The string to transform
   * @param upper active the UpperCamelCase
   * @param separators The regExp with separator
   */
  public static toCamelCase(text: string, upper?: boolean, separator?: RegExp): string {
    if (!separator) {
      separator = new RegExp(/\s/);
    }
    const textWords = text
      .trim()
      .split(separator)
      .filter((word) => word !== '');
    return textWords.reduce((camelCase, word, index) => {
      if (index === 0 && !upper) {
        return camelCase + word.toLowerCase();
      }
      return camelCase + word[0].toUpperCase() + word.substring(1).toLowerCase();
    }, '');
  }

  public static toCamelCaseExt(text: string, upper?: boolean): string {
    return this.toCamelCase(this.replaceSpecialCharacter(text), upper, /[\s_-]/);
  }

  /**
   * Remove special characters & accent from a given string
   *
   * @param value The string to transform
   */
  public static replaceSpecialCharacter(value: string): string {
    return value
      .replace(/[@#$£%^&*+=[\]{};:|,.<>!?'"()°§/~\\`²¨¤]/g, '')
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');
  }

  public static removeCamelCasePrefix<T extends string>(type: T): T {
    const typeData = type.split(/([A-Z])/);
    const reducedType = typeData.slice(1);
    return reducedType.join('') as T;
  }

  public static generateNewName<T extends { name: string }>(
    newName: string,
    concepts: T[]
  ): string {
    return this.generateName(concepts, 0, newName);
  }

  public static toTittleCase(text: string): string {
    return text.length === 0
      ? ''
      : text.replace(/\w\S*/g, (txt) => txt[0].toUpperCase() + txt.slice(1).toLowerCase());
  }

  /**
   * Permit to complete a string to be a valid url
   *
   * @param value The string to transform
   */
  public static getExternalUrlFormatted(value: string): string {
    if (
      !value.startsWith('ftp://') &&
      !value.startsWith('http://') &&
      !value.startsWith('https://') &&
      !value.startsWith('..') &&
      !value.startsWith('\\\\')
    ) {
      return 'https://' + value;
    } else {
      return value;
    }
  }
  /**
   * Permit to check if a string value is empty
   *
   * @param value The string to check
   * @returns
   */
  public static isEmpty(value: string | undefined | null): boolean {
    return value === undefined || value === null || value === '';
  }

  /**
   * Remove the accent of the text.
   * @param value  The input string which accent will be removed.
   * @returns The accent free string.
   */
  public static removeAccents(value: string): string {
    return value.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
  }

  /**
   * Clean the input string by removing accents and converts it to lowercase.
   * @param value  The input string to clean.
   * @returns The cleaned and lowercase string.
   */
  public static cleanTextForSearch(value: string): string {
    return this.removeAccents(value.toLowerCase().trim());
  }

  private static generateName<T extends { name: string }>(
    concepts: T[],
    index: number,
    name: string
  ): string {
    const newName = index > 0 ? `${name} (${index})` : name;

    const isValidName = !concepts.some(
      (concept) => concept.name.toLowerCase() === newName.toLowerCase()
    );
    if (!isValidName) {
      return this.generateName(concepts, index + 1, name);
    }
    return newName;
  }
}
