import { ChangeDetectorRef, Component, ElementRef, Input, ViewChild } from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormControl,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { Subject, Subscription } from 'rxjs';
import { finalize, map, startWith, take, takeUntil } from 'rxjs/operators';
import { TxFileAttributeService } from '../../../data-access/files/file-attribute.service';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { MatChipsModule } from '@angular/material/chips';
import { CommonModule } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TxFileFieldBoxComponent } from './file-field-box/file-field-box.component';
import { head } from 'ramda';
import { TxDragDropDirective } from '../dragDrop.directive';
import { concatenateErrors } from '../validators.utils';
import { TxDataBaseAction, TxFile } from '@bassetti-group/tx-web-core/src/lib/business-models';

@Component({
  selector: 'tx-upload-file-field',
  templateUrl: './upload-file-field.component.html',
  standalone: true,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: TxUploadFileFieldComponent,
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: TxUploadFileFieldComponent,
      multi: true,
    },
  ],
  imports: [
    FontAwesomeModule,
    MatFormFieldModule,
    MatChipsModule,
    TranslateModule,
    CommonModule,
    MatTooltipModule,
    TxFileFieldBoxComponent,
    TxDragDropDirective,
  ],
  styleUrls: ['./upload-file-field.component.scss'],
})
export class TxUploadFileFieldComponent implements ControlValueAccessor {
  @Input() requiredFileType: string = '';
  @Input() multiple: boolean = true;
  @Input() maxMoFileSize: number = 40;
  @Input({ required: true }) idAttribute!: number;
  @Input() hideVisualisationToggle = false;
  @Input() required: boolean = false;
  @Input() label: string = '';
  @Input() labelTooltip: string = '';

  @ViewChild('fileUpload') fileInput!: ElementRef;

  uploadProgress = 0;
  isDropzoneHovered = false;
  uploadSub!: Subscription;
  files: TxFile[] = [];
  idUploadedFiles: number[] = [];
  defaultView = true;
  control!: FormControl;
  _destroying$ = new Subject<void>();
  validFiles = true;
  disabled: boolean = false;
  private _onValidationChange: () => void = () => {};
  private _onTouched: () => void = () => {};

  constructor(private cd: ChangeDetectorRef, private fileService: TxFileAttributeService) {}

  get isHovered() {
    this.isDropzoneHovered = false;
    return this.isDropzoneHovered;
  }

  writeValue(files: TxFile[]): void {
    this.files = !this.multiple ? [head(files) ?? []].flat() : files;
    const filesName = this.files.map((file) => file.name);
    if (this.control) {
      this.control.setValue(filesName);
    } else {
      this.control = new FormControl(filesName);
    }
    this.deleteUploadedFilesFromCache();
  }

  registerOnChange(fn: () => void): void {
    this.control?.valueChanges
      .pipe(
        takeUntil(this._destroying$),
        startWith(this.control?.value),
        map(() => this.files)
      )
      .subscribe(fn);
  }

  registerOnTouched(fn: any): void {
    this._onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    isDisabled ? this.control?.disable() : this.control?.enable();
    this.disabled = isDisabled;
  }

  validate(control: AbstractControl): ValidationErrors | null {
    const requiredError = this.required ? Validators.required(control) : null;
    const uploadInProgressError = this.isUploadInProgress();
    const filesTooBigError = this.isFileTooBig();
    const errors = concatenateErrors(requiredError, uploadInProgressError, filesTooBigError);
    this.control.setErrors(errors);
    this.cd.detectChanges();
    return errors;
  }

  registerOnValidatorChange?(fn: () => void): void {
    this._onValidationChange = fn;
  }

  public containsFiles(evt: any) {
    if (evt.dataTransfer?.types?.some((type: string) => type === 'Files')) {
      this.isDropzoneHovered = true;
    } else {
      this.isDropzoneHovered = false;
    }
  }

  onFileSelected(event: any) {
    const files: FileList = (
      event.dataTransfer ? event.dataTransfer.files : event.target.files
    ) as FileList;
    if (files) {
      for (const file of files) {
        if (this.validFileSize(file)) {
          const txFile = this.addNewFile(file);
          this.validFiles = true;
          if (txFile) {
            this.fileService
              .upload(file, this.idAttribute)
              .pipe(finalize(() => this.resetUpload(txFile)))
              .subscribe({
                next: (uploadData) => {
                  if (typeof uploadData === 'number') {
                    txFile.uploadProgress = uploadData;
                  } else if (uploadData) {
                    txFile.idArchivedFile = uploadData.idArchivedFile;
                    txFile.view = this.defaultView;
                    txFile.name = uploadData.name;
                    this.idUploadedFiles.push(Math.abs(uploadData.idArchivedFile));
                  }
                },
                error: (error) => {
                  this.removeFile(txFile);
                },
              });
          }
          this.deleteUploadedFilesFromCache();
        } else {
          this.validFiles = false;
        }
        this._onValidationChange();
      }
    }
  }

  removeFile(file: TxFile) {
    this.resetUpload(file);
    if (file.idArchivedFile == null || file.idArchivedFile < 0) {
      const index = this.getFileIndex(file);
      if (index > -1) {
        this.files.splice(index, 1);
      }
      file?.idArchivedFile ?? this.removeFromUploadedFilesAndCache(file.idArchivedFile as number);
    } else {
      file.action = TxDataBaseAction.Delete;
    }
    this.resetInput();
    this.control.setValue(this.files.map((file) => file.name));
  }

  onDragLeave(event: any) {
    if (event.fromElement.classList.contains('generic-form-field')) {
      this.isDropzoneHovered = false;
    }
  }

  private getNbFiles() {
    return this.files.length;
  }

  private areAnyUploadInProgress() {
    if (this.files.length === 0) {
      return false;
    } else {
      const filesUploading = this.files.filter((file) => file.uploadProgress !== 100);
      return filesUploading.length === 0;
    }
  }

  private isUploadInProgress(): ValidationErrors | null {
    const uploadInProgress = this.areAnyUploadInProgress();
    const uploadInProgressError = uploadInProgress ? { UploadInProgress: true } : null;
    return uploadInProgressError;
  }

  private isFileTooBig(): ValidationErrors | null {
    const filesTooBig = !this.validFiles;
    const filesTooBigError = filesTooBig ? { FileTooBig: true } : null;
    return filesTooBigError;
  }

  private deleteUploadedFilesFromCache() {
    if (this.idUploadedFiles.length > 0) {
      this.fileService.deleteCache(this.idUploadedFiles).pipe(take(1)).subscribe();
    }
  }

  private getFileIndex(file: File | TxFile): number {
    return this.files.findIndex((f) => f.name === file.name);
  }

  private fileExist(file: File | TxFile): boolean {
    return this.getFileIndex(file) > -1;
  }

  private addNewFile(file: File): TxFile | undefined {
    if (!this.fileExist(file) && (this.multiple || this.getNbFiles() < 1)) {
      const txFile: TxFile = {
        name: file.name,
        size: file.size,
        view: this.defaultView,
        idArchivedFile: undefined,
        action: TxDataBaseAction.Add,
        file: file,
      };
      this.files = [txFile].concat(this.files);
      this.control.setValue(this.files.map((file) => file.name));
      return txFile;
    }
  }

  private validFileSize(file: File | TxFile) {
    return file.size / (1024 * 1024) <= this.maxMoFileSize;
  }

  private resetInput() {
    this.fileInput.nativeElement.value = '';
  }

  private removeFromUploadedFilesAndCache(fileId: number) {
    const index = this.idUploadedFiles.indexOf(Math.abs(fileId));
    if (index > -1) {
      this.idUploadedFiles.splice(index, 1);
      this.fileService.deleteCache([Math.abs(fileId)]).subscribe();
    }
  }

  private resetUpload(txFile: TxFile) {
    txFile.uploadProgress = undefined;
    this.deleteUploadedFilesFromCache();
  }
}
