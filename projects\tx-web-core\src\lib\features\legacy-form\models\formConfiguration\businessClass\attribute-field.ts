import { TxIdConcept } from '../../generics/Id-concept';
import { TxLinkField } from './linked-field';
import { TxVirtualAttributeField } from './virtual-attribute-field';

export class TxAttributeField extends TxVirtualAttributeField {
  linkField!: TxLinkField;
  modelApplicationTrigger!: TxModelApplicationTrigger;
  value: any;

  static assign(object: Partial<TxAttributeField>): TxAttributeField {
    const attributeField = new TxAttributeField();
    attributeField.assign(object);
    return attributeField;
  }

  constructor() {
    super();
  }

  reset() {
    super.reset();
  }

  assign(object?: Partial<TxAttributeField>) {
    super.assign(object);
    this.linkField = object?.linkField || this.linkField;
    this.modelApplicationTrigger = object?.modelApplicationTrigger as TxModelApplicationTrigger;
  }
}

export class TxInheritedAttributeField extends TxAttributeField {
  linkField!: TxLinkField;
  modelApplicationTrigger!: TxModelApplicationTrigger;
  value: any;

  static assign(object: Partial<TxAttributeField>): TxAttributeField {
    const attributeField = new TxAttributeField();
    attributeField.assign(object);
    return attributeField;
  }

  constructor() {
    super();
  }

  reset() {
    super.reset();
  }

  assign(object?: Partial<TxAttributeField>) {
    super.assign(object);
    this.linkField = object?.linkField || this.linkField;
    this.modelApplicationTrigger = object?.modelApplicationTrigger as TxModelApplicationTrigger;
  }
}

export class TxModelApplicationTrigger extends TxIdConcept {
  applicationModelTag!: string;
  iconPath!: string;

  constructor() {
    super();
  }

  assign(modAppTrigger?: Partial<TxModelApplicationTrigger>) {
    super.assign(modAppTrigger as Partial<TxIdConcept>);
    this.applicationModelTag = modAppTrigger?.applicationModelTag as string;
    this.iconPath = modAppTrigger?.iconPath as string;
  }
}
