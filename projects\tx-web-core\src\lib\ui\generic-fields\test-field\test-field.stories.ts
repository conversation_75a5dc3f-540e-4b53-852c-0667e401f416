import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { TestFieldComponent } from './test-field.component';
import { TxGenericFieldsModule } from '../generic-fields.module';

const meta: Meta<TestFieldComponent> = {
  title: 'Generic Fields/GenericFields',
  id: 'test-field',
  component: TestFieldComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [TxGenericFieldsModule],
    }),
  ],
};

export default meta;

type TestField = StoryObj<TestFieldComponent>;

export const testFields: TestField = {};
