import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TxDialogDynComponent } from './dialog-dyn.component';
import { Observable } from 'rxjs';
import { DialogData } from './dialog-data';

@Injectable({
  providedIn: 'root',
})
export class TxDialogService {
  constructor(private dialog: MatDialog) {}

  /**
   * Create a new dialog confirm component.
   * @param data The settings used to load the dialog component.
   * @param width The width of the dialog component (default: 400px).
   * @returns The Observable launches when the dialog is closed. It returns a boolean (true: when the dialog is validated, false: when it is cancelled).
   */
  open(data: DialogData, width = '400px', minWidth?: number | string): Observable<boolean> {
    return this.dialog
      .open(TxDialogDynComponent, {
        data,
        width,
        minWidth,
        disableClose: true,
        panelClass: 'custom-confirm-dialog',
      })
      .afterClosed();
  }
}
