import { LegacyTxFile } from './attribute';
import { LegacyTxObject } from './object';

export enum LegacyTxDataBaseAction {
  None,
  Add,
  Modify,
  Delete,
}

export enum LegacyTxDataType {
  Boolean = 1,
  String = 3,
  Enum = 4,
  Decimal = 5,
  Table = 6,
  Text = 9,
  LinkAss = 11,
  Link = 12,
  Group = 13,
  DecUnique = 50,
  DecRange = 51,
  DecRangeMean = 52,
  Series = 61,
  TableValue = 62,
  ArchivedGraphic = 63,
  Date = 80,
  DateTime = 81,
  File = 100,
  Email = 110,
  URL = 111,
  LnkDirect = 121,
  LnkInv = 122,
  LnkBi = 123,
  Tab = -2,
}

export class LegacyTxData {
  action: LegacyTxDataBaseAction;

  public static assign(object?: Partial<LegacyTxData>): LegacyTxData {
    return new LegacyTxData(
      object?.idObject as number,
      object?.idAttribute as number,
      object?.action
    );
  }

  public static removedData(idObject: number, idAttribute: number): LegacyTxData {
    return new LegacyTxData(idObject, idAttribute, LegacyTxDataBaseAction.Delete);
  }

  constructor(
    public idObject?: number,
    public idAttribute?: number,
    action: LegacyTxDataBaseAction = LegacyTxDataBaseAction.Add
  ) {
    this.action = action;
  }
}

export class LegacyTxDataString extends LegacyTxData {
  public static assign(object?: Partial<LegacyTxDataString>): LegacyTxDataString {
    return new LegacyTxDataString(
      object?.idObject,
      object?.idAttribute,
      object?.value,
      object?.action
    );
  }

  constructor(
    public idObject?: number,
    public idAttribute?: number,
    public value?: string,
    action?: LegacyTxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
  }
}

export class LegacyTxDataBoolean extends LegacyTxData {
  public static assign(object?: Partial<LegacyTxDataBoolean>): LegacyTxDataBoolean {
    return new LegacyTxDataBoolean(
      object?.idObject,
      object?.idAttribute,
      object?.value,
      object?.action
    );
  }

  constructor(
    public idObject?: number,
    public idAttribute?: number,
    public value?: boolean,
    action?: LegacyTxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
  }
}

export class LegacyTxDataNumeric extends LegacyTxData {
  public static assign(object?: Partial<LegacyTxDataNumeric>): LegacyTxDataNumeric {
    return new LegacyTxDataNumeric(
      object?.idObject,
      object?.idAttribute,
      object?.min,
      object?.max,
      object?.mean,
      object?.idUnit,
      object?.action
    );
  }

  constructor(
    public idObject?: number,
    public idAttribute?: number,
    public min?: number,
    public max?: number,
    public mean?: number,
    public idUnit?: number,
    action?: LegacyTxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
  }
}

export class LegacyTxDataFile extends LegacyTxData {
  public static assign(object?: Partial<LegacyTxDataFile>): LegacyTxDataFile {
    return new LegacyTxDataFile(
      object?.idObject,
      object?.idAttribute,
      object?.files,
      object?.action
    );
  }

  constructor(
    public idObject?: number,
    public idAttribute?: number,
    public files?: LegacyTxFile[],
    action?: LegacyTxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
  }
}

export class LegacyTxDataTable extends LegacyTxData {
  public static assign(object?: Partial<LegacyTxDataTable>): LegacyTxDataTable {
    return new LegacyTxDataTable(
      object?.idObject,
      object?.idAttribute,
      object?.series,
      object?.action
    );
  }

  constructor(
    public idObject?: number,
    public idAttribute?: number,
    public series?: [],
    action?: LegacyTxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
  }
}

export class LegacyTxDataLink extends LegacyTxData {
  public static assign(object?: Partial<LegacyTxDataLink>): LegacyTxDataLink {
    return new LegacyTxDataLink(
      object?.idObject,
      object?.idAttribute,
      object?.linkedIds,
      object?.linkedObjects,
      object?.action
    );
  }

  constructor(
    public idObject?: number,
    public idAttribute?: number,
    public linkedIds?: number[],
    public linkedObjects?: LegacyTxObject[],
    action?: LegacyTxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
  }
}

export class LegacyTxDataTab extends LegacyTxData {
  public static assign(object?: Partial<LegacyTxDataTab>): LegacyTxDataTab {
    return new LegacyTxDataTab(
      object?.idObject,
      object?.idAttribute,
      object?.value,
      object?.action
    );
  }

  constructor(
    public idObject?: number,
    public idAttribute?: number,
    public value?: string | string[],
    action?: LegacyTxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
    if (!Array.isArray(value)) {
      this.value = value?.split(/\r\n/);
    }
  }
}

export class LegacyTxDataUrl extends LegacyTxData {
  public static assign(object?: Partial<LegacyTxDataUrl>): LegacyTxDataUrl {
    return new LegacyTxDataUrl(
      object?.idObject,
      object?.idAttribute,
      object?.value,
      object?.boolVisualisation,
      object?.action
    );
  }

  constructor(
    public idObject?: number,
    public idAttribute?: number,
    public value?: string | string[],
    public boolVisualisation?: boolean,
    action?: LegacyTxDataBaseAction
  ) {
    super(idObject, idAttribute, action);
    if (!Array.isArray(value)) {
      this.value = value?.split(/\r\n/);
    }
  }
}

export interface LegacyTxDataWithLinkedObjects {
  data?: LegacyTxData[];
  linkedObjects?: LegacyTxObject[];
}
