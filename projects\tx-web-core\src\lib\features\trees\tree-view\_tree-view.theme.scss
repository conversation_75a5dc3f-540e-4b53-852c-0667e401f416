@use '@angular/material' as mat;

@mixin tree-view-theme($theme) {
  $foreground: map-get($theme, foreground);
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);

  .mat-tree-node .tree-node {
    color: mat.m2-get-color-from-palette($foreground, grey70);

    &:hover {
      background-color: mat.m2-get-color-from-palette($foreground, grey10);
    }
  }
  .node-select {
    background-color: mat.m2-get-color-from-palette($foreground, grey20);
    .tree-content {
      color: mat.m2-get-color-from-palette($accent, 500);
    }
    &:hover {
      background-color: mat.m2-get-color-from-palette($foreground, grey20);
    }
  }
}
