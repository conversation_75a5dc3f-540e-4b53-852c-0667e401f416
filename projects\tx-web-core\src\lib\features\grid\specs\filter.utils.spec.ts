import { clientSideFilterBy } from '../data-source/filter.util';
import { TxGridFilterByColumn, TxGridFilterOnData, TxGridFilterOperators } from '../grid.interface';

describe('clientSideFilterBy', () => {
  const mockData = [
    { name: 'Alice', age: 25, createdAt: new Date('2021-01-01'), isEligible: true },
    { name: '<PERSON>', age: 30, createdAt: new Date('2022-05-12'), isEligible: true },
    { name: '<PERSON>', age: 22, createdAt: new Date('2020-08-15'), isEligible: false },
    { name: '<PERSON><PERSON>', age: 22, createdAt: new Date('2020-08-15'), isEligible: true },
    { name: '<PERSON>', age: 27, createdAt: new Date('2020-08-15'), isEligible: true },
    { name: '<PERSON>', age: 28, createdAt: new Date('2020-08-15'), isEligible: true },
  ];

  const runTest = (
    query: TxGridFilterOnData<(typeof mockData)[0]> | TxGridFilterByColumn<(typeof mockData)[0]>,
    expectedResult: any
  ) => {
    const result = clientSideFilterBy(mockData, query);
    expect(result).toEqual(expectedResult);
  };

  it('should filter data by fields when using "FilterOnData"', () => {
    runTest({ fields: ['name'], value: 'bob' }, [mockData[1]]);
  });

  it('should filter data with all fields specified', () => {
    runTest({ fields: ['age', 'name'], value: '28' }, [mockData[5]]);
  });

  it('should return data when no conditions are set', () => {
    runTest({ conditions: null, values: null }, mockData);
  });

  it('should return an empty array when no data matches the query', () => {
    runTest({ fields: ['name'], value: 'Nonexistent' }, []);
  });

  describe('Filter Strings', () => {
    const stringTests: Array<{ condition: TxGridFilterOperators; value: string; expected: any[] }> =
      [
        { condition: 'equal' as TxGridFilterOperators, value: 'Alice', expected: [mockData[0]] },
        {
          condition: 'notEqual' as TxGridFilterOperators,
          value: 'Alice',
          expected: mockData.filter((data) => data.name !== 'Alice'),
        },
        {
          condition: 'startsWith' as TxGridFilterOperators,
          value: 'S',
          expected: mockData.filter((data) => data.name.startsWith('S')),
        },
        {
          condition: 'endsWith' as TxGridFilterOperators,
          value: 'e',
          expected: mockData.filter((data) => data.name.endsWith('e')),
        },
        {
          condition: 'contains' as TxGridFilterOperators,
          value: 'h',
          expected: mockData.filter((data) => data.name.includes('h')),
        },
      ];

    stringTests.forEach(({ condition, value, expected }) => {
      it(`should filter ${condition} string`, () => {
        runTest(
          {
            conditions: { name: condition, age: null, createdAt: null, isEligible: null },
            values: { name: value, age: null, createdAt: null, isEligible: null },
          },
          expected
        );
      });
    });
  });

  describe('Filter Number', () => {
    const numberTests: Array<{ condition: TxGridFilterOperators; value: number; expected: any[] }> =
      [
        {
          condition: 'equal' as TxGridFilterOperators,
          value: 22,
          expected: [mockData[2], mockData[3]],
        },
        {
          condition: 'notEqual' as TxGridFilterOperators,
          value: 22,
          expected: mockData.filter((data) => data.age !== 22),
        },
        {
          condition: 'greaterThan' as TxGridFilterOperators,
          value: 27,
          expected: mockData.filter((data) => data.age > 27),
        },
        {
          condition: 'greaterThanOrEqual' as TxGridFilterOperators,
          value: 27,
          expected: mockData.filter((data) => data.age >= 27),
        },
        {
          condition: 'lessThan' as TxGridFilterOperators,
          value: 27,
          expected: mockData.filter((data) => data.age < 27),
        },
        {
          condition: 'lessThanOrEqual' as TxGridFilterOperators,
          value: 27,
          expected: mockData.filter((data) => data.age <= 27),
        },
      ];

    numberTests.forEach(({ condition, value, expected }) => {
      it(`should filter ${condition} number`, () => {
        runTest(
          {
            conditions: { name: null, age: condition, createdAt: null, isEligible: null },
            values: { name: null, age: value, createdAt: null, isEligible: null },
          },
          expected
        );
      });
    });
  });

  describe('Filter Date', () => {
    const dateTests: Array<{ condition: TxGridFilterOperators; value: Date; expected: any[] }> = [
      {
        condition: 'greaterThanOrEqual' as TxGridFilterOperators,
        value: new Date('2020-12-01'),
        expected: [mockData[0], mockData[1]],
      },
      {
        condition: 'lessThan' as TxGridFilterOperators,
        value: new Date('2020-12-01'),
        expected: mockData.slice(-4),
      },
      {
        condition: 'lessThanOrEqual' as TxGridFilterOperators,
        value: new Date('2021-01-01'),
        expected: mockData.filter((data) => data.createdAt <= new Date('2021-01-01')),
      },
    ];

    dateTests.forEach(({ condition, value, expected }) => {
      it(`should filter ${condition} date`, () => {
        runTest(
          {
            conditions: { age: null, isEligible: null, createdAt: condition, name: null },
            values: { name: null, createdAt: value, age: null, isEligible: null },
          },
          expected
        );
      });
    });
  });

  describe('Filter Boolean', () => {
    const booleanTests: Array<{
      condition: TxGridFilterOperators;
      value: boolean;
      expected: any[];
    }> = [
      {
        condition: 'equal',
        value: true,
        expected: mockData.filter((data) => data.isEligible === true),
      },
      { condition: 'notEqual', value: true, expected: [mockData[2]] },
    ];

    booleanTests.forEach(({ condition, value, expected }) => {
      it(`should filter ${condition} boolean`, () => {
        runTest(
          {
            conditions: { name: null, age: null, createdAt: null, isEligible: condition },
            values: { name: null, age: null, createdAt: null, isEligible: value },
          },
          expected
        );
      });
    });
  });
});
