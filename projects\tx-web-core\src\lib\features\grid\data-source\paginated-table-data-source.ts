import { CollectionViewer, DataSource, SelectionModel } from '@angular/cdk/collections';
import {
  BehaviorSubject,
  combineLatest,
  filter,
  map,
  merge,
  Observable,
  Subscription,
  switchMap,
  pipe,
  of,
  distinctUntilChanged,
} from 'rxjs';
import { PaginationEndpointFn, Page, Pagination } from './pagination';
import { MatSort, Sort as MatSortInterface } from '@angular/material/sort';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { Sort, SortRequest, sortData } from './sort.util';
import { FilterQuery, clientSideFilterBy } from './filter.util';
import { TxGridService } from '../grid.service';
import { ElementRef, InjectionToken } from '@angular/core';
import {
  BaseGridFilterValue,
  GridFilter,
  GridFilterValue,
} from '../grid-filter-menu/grid-filter-menu.component';
import { TxCommonUtils } from '@bassetti-group/tx-web-core/src/lib/utilities';
import { compose, curry, differenceWith, equals, isEmpty } from 'ramda';
import {
  FilterOperators,
  FilterValues,
  TxGridFilterByColumn,
  TxGridFilterOnData,
} from '../grid.interface';
import { TxGridFilterType } from '../grid-filter-menu/grid-filter-menu.model';
import { TxObjectType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TxGridComponent } from '../grid.component';
import { TxGroupByValue } from '../grid.const';

export const PRIMARY_KEY = new InjectionToken<unknown>('primaryKey');
export const PAGE_SIZE = new InjectionToken<number>('pageSize');

export class PaginatedTableDataSource<T extends {}> extends DataSource<T> {
  private readonly _pageIndex = new BehaviorSubject<number>(0);
  private readonly _sort: BehaviorSubject<SortRequest<T>>;
  /** Stream that emits when filter query is set on the data source */
  private readonly _filter: BehaviorSubject<FilterQuery<T>>;
  protected readonly _pageRequest = new BehaviorSubject<Page<T> | undefined>(undefined);
  private readonly _pageSize: BehaviorSubject<number>;
  private readonly _endPointChange: BehaviorSubject<PaginationEndpointFn<T> | T[]>;
  /** Stream that contain displayed page data */
  private readonly _page$ = this._pageRequest.asObservable();
  private readonly _data$: Observable<T[]>;
  private readonly _endPointChange$: Observable<PaginationEndpointFn<T> | T[]>;
  public checkboxSelectDisableOnSearch = false;
  private _matSort: MatSort | null = null;
  private _matPaginator: MatPaginator | null = null;
  private _matPaginatorSubscription = Subscription.EMPTY;
  private _matSortSubscription = Subscription.EMPTY;
  private _filteredData: T[] = [];
  private _filterForGroup: boolean = false;
  private _sortValue?: Sort<T>;
  private _primaryKey?: keyof T;
  expandAllGroup = false;
  groupRowValue: string[] = [];
  groupRows: T[] = [];
  pageSubscription: Subscription;
  dataSubscription: Subscription = Subscription.EMPTY;
  groupBy?: keyof T;
  disableSelectionUpdate: boolean = false;
  disableInitialSort?: boolean;

  set pageSize(value: number) {
    if (this._matPaginator) {
      this._matPaginator.pageSize = value;
    }
    this._pageSize.next(value);
  }
  addData(value: Observable<T[]> | T[]) {
    if (value instanceof Observable) {
      this.dataSubscription.unsubscribe();
      this.dataSubscription = value.subscribe((data) => {
        this.updateData(data);
      });
    } else {
      this.updateData(value);
    }
    this._pageIndex.next(0);
    if (this._matPaginator?.pageIndex !== 0) {
      this._matPaginator?.firstPage();
    }
  }

  get data$() {
    return this._data$;
  }
  get dataList() {
    if (!this._endPointChange || this._endPointChange.closed || !this._endPointChange.value) {
      return [];
    }

    return typeof this._endPointChange.value === 'function'
      ? this._pageRequest.value?.data ?? ([] as T[])
      : this._endPointChange.value;
  }
  pagination?: Pagination;
  searchId: number | undefined;

  /** Stream that emit pagination infos */
  pagination$: Observable<Pagination>;
  gridService = new TxGridService();
  public selection = new SelectionModel<T | number | string>(true, []);
  /**
   * Instance of the MatPaginator component used by the table to control what page of the data is
   * displayed. Page changes emitted by the MatPaginator will trigger an update to the
   * table's rendered data.
   */
  get matPaginator(): MatPaginator | null {
    return this._matPaginator;
  }
  set matPaginator(value: MatPaginator | null) {
    this._matPaginator = value;
    if (this._matPaginator !== null && this._matPaginator !== undefined) {
      this._matPaginator.pageSize = this._pageSize.value;
      this._matPaginatorSubscription.unsubscribe();
      const pageChange = merge(this._matPaginator.page, this._matPaginator.initialized);
      this._matPaginatorSubscription = pageChange.pipe().subscribe((pageEvent) => {
        if (pageEvent) {
          this._pageIndex.next(pageEvent.pageIndex);
        } else if (this.matPaginator && this._pageIndex.value !== this.matPaginator.pageIndex) {
          this._pageIndex.next(this.matPaginator.pageIndex); // in case of filter (reset page) for example MatPaginator is updated but not _pageIndex
        } else if (this.pagination) {
          this._updateMatPaginator(this.pagination);
        }
      });
    }
  }

  /**
   * Instance of the MatSort directive used by the table to control its sorting. Sort changes
   * emitted by the MatSort will trigger an update to the table's rendered data.
   */
  get matSort(): MatSort | null {
    return this._matSort;
  }
  set matSort(value: MatSort | null) {
    this._matSort = value;
    if (this._matSort !== null && this._matSort !== undefined) {
      this._matSortSubscription.unsubscribe();
      this._matSortSubscription = this._matSort.sortChange.subscribe((matSortInterface) => {
        this._sort.next(matSortInterface);
      });
    }
  }
  get primaryKey(): keyof T | undefined {
    if (!this._primaryKey) {
      console.warn('primaryKey not defined');
    }
    return this._primaryKey;
  }
  set primaryKey(value: keyof T) {
    this._primaryKey = value;
  }

  constructor(
    endPoint: PaginationEndpointFn<T> | Observable<T[]>,
    primaryKey: keyof T | undefined = undefined,
    initialSort: Sort<T> | undefined | null = undefined,
    initialQuery: FilterQuery<T> = undefined,
    pageSize = 100
  ) {
    super();
    if (primaryKey !== undefined) {
      this.primaryKey = primaryKey;
    }
    this._sort = new BehaviorSubject<SortRequest<T>>(initialSort);
    this._filter = new BehaviorSubject<FilterQuery<T>>(initialQuery);
    if (typeof endPoint === 'function') {
      this._endPointChange = new BehaviorSubject<PaginationEndpointFn<T> | T[]>(endPoint);
    } else {
      this._endPointChange = new BehaviorSubject<PaginationEndpointFn<T> | T[]>([]);
      this.dataSubscription = endPoint.subscribe((data) => {
        this.updateData(data);
      });
    }
    this._endPointChange$ = this._endPointChange.asObservable();
    this._pageSize = new BehaviorSubject<number>(pageSize);
    this.pageSubscription = combineLatest([
      this._pageIndex.pipe(distinctUntilChanged()),
      this._sort.pipe(distinctUntilChanged((prev, curr) => equals(prev, curr))),
      this._filter,
      this._pageSize.pipe(distinctUntilChanged()),
    ])
      .pipe(
        switchMap(([pageIndex, sort, query, pageSize]) =>
          this._endPointChange$.pipe(
            switchMap((endPoint) =>
              typeof endPoint === 'function'
                ? endPoint(
                    {
                      pageIndex,
                      sort,
                      pageSize,
                    },
                    query
                  )
                : of(endPoint).pipe(
                    map((data) => this.createPage(pageIndex, pageSize, data)),
                    this.detectSort(sort),
                    this.clientSideFilterBy(query),
                    this.sortOnClientSide(sort),
                    this.clientSideCurrentPage()
                  )
            )
          )
        )
      )
      .subscribe((page) => {
        this._pageRequest.next(page);
      });

    this._data$ = this._page$.pipe(
      filter((page: Page<T> | undefined): page is Page<T> => page !== undefined),
      map((page) => {
        this._updateMatPaginator(this._paginationFor(page));
        return page.data;
      })
    );

    this.pagination$ = this._page$.pipe(
      filter((page: Page<T> | undefined): page is Page<T> => page !== undefined),
      map((page) => {
        return this._paginationFor(page);
      })
    );

    this.pagination$.subscribe((pagination) => {
      this.pagination = pagination;
    });
  }

  isSortApplied(): boolean {
    return !!this._sort.value;
  }

  sortBy(sort: Sort<T>): void {
    const lastSort = this._sort.getValue();
    const nextSort = { ...(lastSort ?? {}), ...sort };
    this._sort.next(nextSort);
  }

  filterBy(filter: GridFilter | TxGridFilterOnData<T> | null, forGroup?: boolean): void {
    this._filterForGroup = forGroup ?? false;
    if (filter === null) {
      this._filter.next(undefined);
    } else if (this.isGridFilter(filter) && this.isValidFilter(filter)) {
      const lastQuery = this._filter.value as TxGridFilterByColumn<T>;
      const query = compose(
        this.curriedAppliedFilters(filter),
        this.getFormattedFilterValue.bind(this)
      );

      let formattedQuery: any = query(filter);
      const { values: newValues, conditions: newConditions } = formattedQuery;
      const nextQuery = lastQuery
        ? {
            values: { ...(lastQuery?.values ?? {}), ...newValues },
            conditions: { ...(lastQuery?.conditions ?? {}), ...newConditions },
          }
        : formattedQuery;
      // const nextQuery = { ...(lastQuery. ?? {}), ...formattedQuery };
      this._filter.next(isEmpty(nextQuery) ? undefined : (nextQuery as TxGridFilterByColumn<T>));
      if (this.matPaginator) {
        this.matPaginator.firstPage();
      }
    } else if (this.isGridFilter(filter) && filter.column) {
      this.removeFilteredColsByField(filter.column.field, forGroup);
    } else if (!this.isGridFilter(filter)) {
      const lastQuery = this._filter.value;
      const nextQuery = { ...(lastQuery ?? {}), ...filter };
      this._filter.next(nextQuery as TxGridFilterOnData<T>);
    }
  }

  fetch(pageEvent: PageEvent): void {
    this._pageIndex.next(pageEvent.pageIndex);
  }
  resetSearch(): number | undefined {
    this.searchId = this.gridService.resetSearch();
    return this.searchId;
  }
  search<T extends {}>(
    inputSearch: ElementRef<HTMLInputElement> | undefined,
    grid: TxGridComponent<T> | undefined,
    contentElement: ElementRef,
    event: KeyboardEvent | InputEvent,
    isRowSelected: boolean = false
  ): void {
    this.gridService.search(inputSearch, grid, contentElement, event, isRowSelected);
  }

  connect(collectionViewer?: CollectionViewer): Observable<T[]> {
    if (collectionViewer) {
      return collectionViewer.viewChange.pipe(switchMap(() => this._data$));
    } else {
      return this._data$;
    }
  }

  disconnect(): void {
    this._matPaginatorSubscription.unsubscribe();
    this._matSortSubscription.unsubscribe();
    this.pageSubscription.unsubscribe();
    this.dataSubscription.unsubscribe();
    this._endPointChange.unsubscribe();
  }

  removeFilteredColsByField(field: string, forGroup?: boolean) {
    this._filterForGroup = forGroup ?? false;
    const filterValue = {
      [field]: undefined,
    } as FilterValues<T>;
    const filterOpt = {
      [field]: undefined,
    } as unknown as FilterOperators<T>;
    const lastQuery = this._filter.value as TxGridFilterByColumn<T> | undefined;
    const query: TxGridFilterByColumn<T> = {
      ...lastQuery,
      values: { ...(lastQuery?.values ?? {}), ...filterValue },
      conditions: { ...(lastQuery?.conditions ?? {}), ...filterOpt },
    };
    this._filter.next(query);
  }

  triggerUpdatePageRequest(page?: Page<T>): void {
    this._pageRequest.next(this._pageRequest.value);
  }
  addGroups(data: T[] & { isTxGroup?: boolean }, groupByColumn?: keyof T): T[] {
    if (!groupByColumn) return data;
    this.groupRows = [];
    this.groupRowValue = [];
    data.forEach((row) => {
      if (!this.groupRows.find((element: any) => element[groupByColumn] === row[groupByColumn])) {
        let result: any = {};
        result.expanded = true;
        result.isTxGroup = true;
        result[groupByColumn] = row[groupByColumn];
        result[TxGroupByValue] = row[groupByColumn];
        this.groupRows.push(result);
        this.groupRowValue.push(row[groupByColumn] as string);
      }
    });
    return this.addSubGroup(data, groupByColumn);
  }

  addSubGroup(data: any[], groupByColumn: keyof T): T[] {
    let subGroups: any[] = [];
    this.groupRows.sort((group1, group2) =>
      (group1[groupByColumn] as string).localeCompare(group2[groupByColumn] as string)
    );
    this.groupRows.forEach(
      (group: T & { expanded?: boolean; totalCounts?: number; items?: T[] }) => {
        if (group.expanded) {
          const rowsInGroup = data.filter(
            (row) => group[groupByColumn] === row[groupByColumn] && !row.isTxGroup
          );
          rowsInGroup.forEach((item) => (item[TxGroupByValue] = item[groupByColumn]));
          group.totalCounts = rowsInGroup.length;
          group.items = [...rowsInGroup];
          rowsInGroup.unshift(group);
          subGroups = subGroups.concat(rowsInGroup);
        } else {
          subGroups = subGroups.concat(group);
        }
      }
    );
    return subGroups;
  }
  /**
   * Expands or collapses all displayed groups in the grid and updates the filter accordingly.
   *
   * @param {boolean} isExpanded - Determines whether to expand or collapse the groups.
   */
  expandOrCollapseDisplayedGroup(isExpanded: boolean) {
    if (!(this.dataList.length > 0 && this.groupBy && this.groupRows.length > 0)) return;
    this.groupRowValue = [];
    if (isExpanded) {
      this.groupRows.forEach((item: T & { [TxGroupByValue]?: string }) => {
        this.groupRowValue.push(item[TxGroupByValue] as string);
      });
    }
    const filterData: GridFilter = {
      column: {
        field: TxGroupByValue,
      },
      operator: 'equal',
      value: this.groupRowValue,
    };
    this.filterBy(filterData, true);
    //change group row arrow icon
    this.groupRows.forEach((item: T & { expanded?: boolean }) => {
      item.expanded = isExpanded;
    });
  }
  updateData(data: T[]): void {
    if (this.expandAllGroup && this.groupBy) {
      this.expandAllGroup = false;
      this.removeFilteredColsByField(TxGroupByValue, true);
    }
    this._filteredData = data;
    this._endPointChange.next(data);
    if (data.length > 0 && !this.disableSelectionUpdate) {
      this.updateSelection(this.primaryKey, data);
    }
  }
  getFilteredRecords() {
    return this._filteredData.filter((item: T & { isTxGroup?: boolean }) => !item.isTxGroup);
  }
  searchByDOM(
    tableData: T[],
    input: string,
    selectedRowPK: T[keyof T] | undefined,
    gridRef: HTMLElement
  ) {
    const listOfPK: any[] = [];
    const tableRows = gridRef.querySelectorAll('tx-grid mat-table mat-row');
    const searchResult: T[] = [];
    const getTableRowsIndex = (value: string) =>
      tableData.findIndex((data) => data[this.primaryKey as keyof T] == value);
    let selected: T | undefined;
    let searchedById: boolean = false;
    let selectedPK = selectedRowPK;
    tableRows.forEach((row, i) => {
      let rowText;
      const rowDataId: string = row.getAttribute('data-id') as string;

      rowText = row.textContent?.toLowerCase() ?? '';
      if (rowDataId == input || rowText?.toLowerCase().includes(input)) {
        if (rowDataId == input) {
          searchedById = true;
        }
        listOfPK.push(rowDataId as keyof T);
        searchResult.push(tableData[getTableRowsIndex(rowDataId)]);
      }
    });
    const rowIndex = selectedPK ? listOfPK.indexOf(selectedPK.toString()) : -1;
    if (!this.checkboxSelectDisableOnSearch) {
      this.selection.clear();
    }
    selectedPK = undefined;

    if (searchResult.length > 0 && rowIndex < searchResult.length - 1) {
      selectedPK = searchResult[rowIndex + 1][this.primaryKey as keyof T];
    }
    if (selectedPK !== undefined) {
      selected = tableData.find((dataRow: any) => dataRow[this.primaryKey] == selectedPK);
      if (selected && !this.checkboxSelectDisableOnSearch) {
        this.select(selected);
      }
    }
    return { selectedPK, rowIndex, selected, searchedById };
  }
  parseData(): T[] {
    return this.dataList;
  }

  searchByData(
    lowerCaseInput: string,
    selectedRowPK: T[keyof T] | undefined,
    searchableKeys: (keyof T)[],
    data?: T[]
  ) {
    let searchedById: boolean = false;
    const dataList = data ?? (this.groupBy ? this._filteredData : this.dataList);
    const searchResult = dataList.filter((row) => {
      const keys: (keyof T)[] =
        searchableKeys.length !== 0 ? searchableKeys : (Object.keys(row) as (keyof T)[]);
      const combinedValues = keys.reduce((currentTerm: string, key: any) => {
        return currentTerm + row[key as keyof T] + '◬';
      }, '');
      const primaryKeyValue = row[this.primaryKey as keyof T]?.toString().toLowerCase();
      if (primaryKeyValue === lowerCaseInput) {
        searchedById = true;
      }
      return (
        primaryKeyValue === lowerCaseInput || combinedValues.toLowerCase().includes(lowerCaseInput)
      );
    });
    const listOfPK = searchResult.map((data) => data[this.primaryKey as keyof T]);

    let rowIndex = selectedRowPK ? listOfPK.indexOf(selectedRowPK) : -1;
    let selected: T | undefined;
    let selectedPK = selectedRowPK;
    if (!this.checkboxSelectDisableOnSearch) {
      this.selection.clear();
    }
    selectedPK = undefined;

    if (selectedRowPK !== undefined && rowIndex !== -1 && rowIndex < searchResult.length - 1) {
      selectedPK = searchResult[rowIndex + 1][this.primaryKey as keyof T];
      rowIndex = rowIndex + 1;
    } else if (searchResult.length > 0) {
      selectedPK = searchResult[0][this.primaryKey as keyof T];
      rowIndex = 0;
    }
    if (selectedPK !== undefined) {
      selected = dataList.find((row: any) => row[this.primaryKey] == selectedPK);
      if (selected && !this.checkboxSelectDisableOnSearch) {
        this.selection.select(selected);
      }
    }
    return { selectedPK, rowIndex, selected, searchedById };
  }

  prepareToSelection(selectionList: T[], selectedRowPKValue?: T[keyof T]) {
    const rowIndex = selectionList.findIndex(
      (row) => row[this.primaryKey as keyof T] === selectedRowPKValue
    );
    const nextRow = selectionList[rowIndex + 1 < selectionList.length ? rowIndex + 1 : 0];
    return {
      rowIndex,
      nextRow,
      selectionList,
    };
  }
  getSelectedElements = (): (string | number | T)[] => this.selection.selected;
  isSelected = (element: T | number | string): boolean => this.selection.isSelected(element);
  toggleSelection(element: T | number | string): boolean | void {
    return this.selection.toggle(element);
  }
  select(...values: (T | number | string)[]): boolean | void {
    return this.selection.select(...values);
  }
  clearSelection(): boolean | void {
    return this.selection.clear();
  }
  deselectElement(element: T | number | string): void {
    this.selection.deselect(element);
  }
  protected createPage(pageIndex: number, pageSize: number, data: T[]): Page<T> {
    return {
      data,
      pageIndex,
      pageSize: this.getPageSize(this._matPaginator, pageSize, data),
      length: data.length,
    };
  }

  private _updateMatPaginator(pagination: Pagination): void {
    if (!this.matPaginator) {
      return;
    }
    this.matPaginator.length = pagination.length;
    if (this.matPaginator.pageIndex > 0) {
      const lastPageIndex =
        Math.ceil(this.matPaginator.length / this.matPaginator.pageSize) - 1 || 0;
      const newPageIndex = Math.min(this.matPaginator.pageIndex, lastPageIndex);

      if (newPageIndex !== this.matPaginator.pageIndex) {
        this.matPaginator.pageIndex = newPageIndex;
      }
    }
  }

  private _paginationFor(page: Page<T>): Pagination {
    const { data, ...pagination } = page;
    return pagination;
  }
  private isValidFilter(filterConfig: GridFilter): boolean {
    return TxCommonUtils.isValueDefined<GridFilterValue>(filterConfig?.value);
  }

  private getFormattedFilterValue(filter: GridFilter): BaseGridFilterValue {
    if (filter?.column?.filterOptions && filter.column.filterType !== undefined) {
      if (filter?.column?.filterType === TxGridFilterType.ObjectType) {
        return (filter.value as TxObjectType[]).map((option) => option.name);
      } else {
        const options =
          filter.column?.filterOptions.filter((o: { value: string }) =>
            (filter.value as string[]).includes(o.value)
          ) ?? [];
        return options.map((option: { text: string; value: string }) => option.value);
      }
    }
    return filter.value as BaseGridFilterValue;
  }

  private readonly getAppliedFilters = (
    filter: GridFilter,
    filterValue: BaseGridFilterValue | null
  ): TxGridFilterByColumn<T> | undefined => {
    if (filter.column) {
      if (Array.isArray(filter.value)) {
        if (filter.value.length === 1 && filter.value[0] === '') {
          // "All" option selected
          filter.operator = filter.operator === 'equal' ? 'notEqual' : 'equal';
          filterValue = null;
        }
      }
      return {
        values: { [filter.column.field]: filterValue },
        conditions: { [filter.column.field]: filter.operator },
      } as TxGridFilterByColumn<T>;
    }
  };

  private readonly curriedAppliedFilters = curry(this.getAppliedFilters.bind(this));

  private clientSideCurrentPage() {
    return pipe(
      map((page: Page<T>) => {
        const start = page.pageIndex * page.pageSize;
        const end = start + page.pageSize;
        return { ...page, data: page.data.slice(start, end) };
      })
    );
  }
  private clientSideFilterBy(query: FilterQuery<T>) {
    return pipe(
      map((page: Page<T>) => {
        if (!query || this.isUndefined(query)) {
          if (this.groupBy && page.data.length > 0) {
            this._filteredData = this.addGroups(page.data, this.groupBy);
            return this.updateFilteredPage(this._filteredData, page);
          }
          return page;
        } else {
          const data = this.processFilterData(page.data, query, this._filterForGroup);
          return this.updateFilteredPage(data, page);
        }
      })
    );
  }

  private updateFilteredPage(filteredData: T[], page: Page<T>): Page<T> {
    return {
      ...page,
      data: filteredData,
      length: filteredData.length,
      pageSize: this.getPageSize(this._matPaginator, this._pageSize.value, filteredData),
    };
  }
  private getTerminalValues(conditions: FilterOperators<T> | null | undefined): unknown[] {
    if (!conditions) {
      return [undefined];
    } else {
      let values: unknown[] = [];

      const explore = (value: any): void => {
        if (typeof value === 'object' && value !== null && value !== undefined) {
          for (let key in value) {
            if (value.hasOwnProperty(key)) {
              explore(value[key]);
            }
          }
        } else {
          values.push(value);
        }
      };

      explore(conditions);
      return values;
    }
  }
  private isUndefined(query: FilterQuery<T>): boolean {
    if (this.isTxGridFilterOnData(query)) {
      return query.value === '';
    }
    return this.getTerminalValues(query?.conditions).every(
      (value) => value === undefined || value === null
    );
  }

  private isTxGridFilterOnData(query: FilterQuery<T>): query is TxGridFilterOnData<T> {
    return (query as TxGridFilterOnData<T>).fields !== undefined;
  }
  private processFilterData(
    data: T[],
    query: TxGridFilterByColumn<T> | TxGridFilterOnData<T>,
    forGroup?: boolean
  ) {
    // Check if data is empty
    if (!data || data.length === 0) {
      this._filterForGroup = false;
      return [];
    }
    if (forGroup) {
      const result = clientSideFilterBy(this._filteredData, query, this._filterForGroup);
      this._filterForGroup = false;
      return result;
    } else {
      this._filteredData = this.addGroups(clientSideFilterBy(data, query), this.groupBy);
      return this._filteredData;
    }
  }
  private sortOnClientSide(sort: Sort<T> | MatSortInterface | undefined | null) {
    return pipe(
      map((page: Page<T>) => {
        return !sort
          ? page
          : { ...page, data: sortData(sort, page.data, this.groupBy, this.disableInitialSort) };
      })
    );
  }
  private detectSort(sort: Sort<T> | MatSortInterface | undefined | null) {
    if (
      this.groupBy &&
      sort &&
      (sort.active != this._sortValue?.active || sort.direction != this._sortValue?.direction)
    ) {
      this._sortValue = sort as Sort<T>;
      this.expandOrCollapseDisplayedGroup(true);
    }
    return pipe(
      map((page: Page<T>) => {
        return page;
      })
    );
  }
  private getPageSize(paginator: MatPaginator | null, pageSize: number, data: T[]) {
    return paginator !== null ? pageSize : data.length;
  }
  private isGridFilter(filter: GridFilter | TxGridFilterOnData<T>): filter is GridFilter {
    return (filter as GridFilter).operator !== undefined;
  }

  private updateSelection(primaryKey: keyof T | undefined, value: T[]) {
    if (primaryKey !== undefined) {
      const cmp = (x: T, y: T) => x[primaryKey] === y[primaryKey];
      const itemToUnselects = differenceWith(cmp, this.selection.selected as T[], value);
      itemToUnselects.forEach((item) => {
        this.selection.deselect(item);
      });
    } else {
      this.selection.clear();
    }
  }
}
