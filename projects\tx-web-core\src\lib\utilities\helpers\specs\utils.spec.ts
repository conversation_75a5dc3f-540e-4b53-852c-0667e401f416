import { _Utils } from '../utils';

describe('Helpers: Utils', () => {
  it('isAssigned: null return false', () => {
    expect(_Utils.isAssigned(null)).toBeFalsy();
  });

  it('isAssigned: undefined return false', () => {
    expect(_Utils.isAssigned(undefined)).toBeFalsy();
  });

  it('isAssigned: 0 return true', () => {
    expect(_Utils.isAssigned(0)).toBeTruthy();
  });

  it('isAssigned: Object return true', () => {
    expect(_Utils.isAssigned({})).toBeTruthy();
  });

  it('isAssigned: string value return true', () => {
    expect(_Utils.isAssigned('a string')).toBeTruthy();
  });
});
