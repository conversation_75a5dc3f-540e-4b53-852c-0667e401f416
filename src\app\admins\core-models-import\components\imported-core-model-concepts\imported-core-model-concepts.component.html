<tx-grid
  #coreModelGrid
  primaryKey="id"
  class="imported-core-model-concepts__table"
  [columns]="columns"
  [filterColumns]="filterColumns"
  [filterOptions]="gridFilterOptions"
  [data]="flatConcepts"
  [groupByField]="ImportedCoreModelConceptFieldEnum.Type"
  [enableFiltering]="true"
  [enableSearching]="true"
  [isRowSelectable]="true"
  (searchInputChange)="searchItem($event)">
  <tx-grid-group>
    <ng-template let-data let-searchValue="searchValue">
      <tx-concept-accordion-group
        class="imported-core-model-concepts__concept-accordion-group"
        [data]="data"
        [searchValue]="searchValue"></tx-concept-accordion-group>
    </ng-template>
  </tx-grid-group>
  <tx-grid-column [fieldName]="ImportedCoreModelConceptFieldEnum.Name">
    <ng-template let-data let-searchValue="searchValue" let-searchById="searchById">
      <tx-concept-name-table-cell
        [searchById]="searchById"
        [id]="data[ImportedCoreModelConceptFieldEnum.Id]"
        [name]="data[ImportedCoreModelConceptFieldEnum.Name]"
        [searchValue]="searchValue"
        [icon]="data.icon">
      </tx-concept-name-table-cell>
    </ng-template>
  </tx-grid-column>
  <tx-grid-column [fieldName]="ImportedCoreModelConceptFieldEnum.ObjectType">
    <ng-template let-data let-searchValue="searchValue" let-searchById="searchById">
      <tx-text-icon
        *ngIf="data[ImportedCoreModelConceptFieldEnum.ObjectType]"
        [icon]="data.objectTypeIcon"
        [text]="data[ImportedCoreModelConceptFieldEnum.ObjectType]"
        [searchValue]="searchValue"></tx-text-icon>
    </ng-template>
  </tx-grid-column>
  <tx-grid-column [fieldName]="ImportedCoreModelConceptFieldEnum.ModificationType">
    <ng-template let-data let-searchValue="searchValue">
      <span
        [innerHTML]="
          data[ImportedCoreModelConceptFieldEnum.ModificationType]
            | escapeHtml
            | highlightSearch : (searchValue | escapeHtml)
        "></span>
    </ng-template>
  </tx-grid-column>
  <tx-grid-column [fieldName]="ImportedCoreModelConceptFieldEnum.Tags">
    <ng-template let-data let-searchValue="searchValue" let-searchById="searchById">
      <tx-texts-to-copy
        [texts]="data[ImportedCoreModelConceptFieldEnum.Tags]"
        [searchValue]="searchValue"></tx-texts-to-copy>
    </ng-template>
  </tx-grid-column>
</tx-grid>
<div class="imported-core-model-concepts__table-info border-grey">
  {{ flatConcepts.length }} {{ 'generic.item-s' | translate }}
</div>
