import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ObjectSelectionFormComponent } from './object-selection-form.component';
import { MockComponent } from 'ng-mocks';
import { DialogConfirmServiceMock } from 'src/app/shared/tests/shared-testing-mock';
import { ObjectsServiceMock, ObjectsTypeServiceMock } from 'src/app/app.testing.mock';
import { FormPaneTemplateComponent } from 'src/app/shared/components/form-pane/form-pane-template.component';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { of } from 'rxjs';
import {
  TxDialogService,
  ObjectTreeGridComponent,
  TxObjectsService,
  TxObjectsTypeDropdownComponent,
  TxObjectsTypeService,
} from '@bassetti-group/tx-web-core';

describe('ObjectSelectionFormComponent', () => {
  let component: ObjectSelectionFormComponent;
  let fixture: ComponentFixture<ObjectSelectionFormComponent>;
  let otService: TxObjectsTypeService;
  let objectsService: TxObjectsService;
  let dialogConfirmService: TxDialogService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        ObjectSelectionFormComponent,
        MockComponent(FormPaneTemplateComponent),
        MockComponent(TxObjectsTypeDropdownComponent),
        MockComponent(ObjectTreeGridComponent),
      ],
      imports: [TranslateTestingModule.withTranslations({ en: {}, fr: {} })],
      providers: [
        { provide: TxObjectsTypeService, useClass: ObjectsTypeServiceMock },
        { provide: TxObjectsService, useClass: ObjectsServiceMock },
        { provide: TxDialogService, useClass: DialogConfirmServiceMock },
      ],
    }).compileComponents();
    otService = TestBed.inject(TxObjectsTypeService);
    objectsService = TestBed.inject(TxObjectsService);
    dialogConfirmService = TestBed.inject(TxDialogService);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ObjectSelectionFormComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load all object types at initialization', () => {
    const spyListOT = jest.spyOn(otService, 'listAll');
    fixture.detectChanges();
    expect(spyListOT).toHaveBeenCalled();
  });

  it('should call AutoTag if user confirmed adding autotag', () => {
    fixture.detectChanges();
    component.objectsSelected = [
      {
        order: 1,
        isParent: false,
        isFolder: false,
        idObjectType: 1,
        name: 'object test',
        id: 12,
        idOwnerObject: 9,
        creationDate: null,
        tags: [],
      },
    ];
    const spyAutotagObject = jest.spyOn(objectsService, 'autotag');
    component.beforeConfirm();
    expect(spyAutotagObject).toHaveBeenCalled();
  });

  it('should not emit object if autotag is cancelled', () => {
    dialogConfirmService.open = jest.fn().mockReturnValue(of(false));
    fixture.detectChanges();
    const object = {
      order: 1,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      name: 'object test',
      id: 12,
      idOwnerObject: 9,
      creationDate: null,
      tags: [],
    };
    component.objectsSelected = [object];
    const spyOutputConfirm = jest.spyOn(component.confirm, 'emit');
    component.beforeConfirm();
    expect(spyOutputConfirm).not.toBeCalled();
  });

  it('should emit object selected on confirm', () => {
    fixture.detectChanges();
    const object = {
      order: 1,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      name: 'object test',
      id: 12,
      idOwnerObject: 9,
      creationDate: null,
      tags: ['tag'],
    };
    component.objectsSelected = [object];
    const spyOutputConfirm = jest.spyOn(component.confirm, 'emit');
    component.beforeConfirm();
    expect(spyOutputConfirm).toHaveBeenCalledWith([object]);
  });
});
