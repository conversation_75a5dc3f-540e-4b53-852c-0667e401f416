import { LegacyTxObjectType } from '../models/object-type';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { TxApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class LegacyTxObjectTypeService {
  objectTypes: LegacyTxObjectType[] = [];

  objectTypesLoaded: Subject<boolean> = new Subject<boolean>();

  initialized = false;

  constructor(public apiService: TxApiService) {
    this.objectTypesLoaded.subscribe((value) => {
      this.initialized = true;
    });
  }

  private addObjectType(objectType: any): LegacyTxObjectType {
    if (!this.objectTypes.some((a) => a.id === objectType.id)) {
      this.objectTypes.push(objectType);
    }

    return objectType;
  }

  private createObjectTypes(objectTypes: any[]): LegacyTxObjectType[] {
    return objectTypes.map((ot) => this.addObjectType(ot));
  }

  private loadFromTags(tags: string[]): Promise<LegacyTxObjectType[]> {
    return new Promise((resolve, reject) => {
      this.apiService.listObjectTypesFromTags(tags).subscribe((result) => {
        const objectType = this.createObjectTypes(result);
        resolve(objectType);
      });
    });
  }

  private loadFromIds(ids: number[]): Promise<LegacyTxObjectType[]> {
    return new Promise((resolve, reject) => {
      this.apiService.listObjectTypesFromIds(ids).subscribe((result) => {
        const objectType = this.createObjectTypes(result);
        resolve(objectType);
      });
    });
  }

  start() {
    this.apiService.listObjectTypes().subscribe((result) => {
      this.createObjectTypes(result);
      this.objectTypesLoaded.next(true);
      this.objectTypesLoaded.complete();
    });
  }

  getObjectType(idAttribute: number): LegacyTxObjectType;
  getObjectType(tag: string): LegacyTxObjectType;
  getObjectType(param: any) {
    return this.objectTypes.find((ot) => {
      if (typeof param === 'number') {
        return ot.id === param;
      } else if (typeof param === 'string') {
        return ot.tags.indexOf(param.toString()) > -1;
      }
    });
  }

  getName(idAttribute: number): string;
  getName(tag: string): string;
  getName(param: any) {
    const objectType = this.getObjectType(param);
    return objectType ? objectType.name : '';
  }

  listFromIds(ids: number[]): Observable<LegacyTxObjectType[]> {
    return new Observable((observer) => {
      const ots: LegacyTxObjectType[] = [];
      const idsToLoad: number[] = [];

      ids.forEach((id) => {
        const objectType = this.getObjectType(id);
        if (objectType) {
          ots.push(objectType);
        } else {
          idsToLoad.push(id);
        }
      });

      if (ots.length) {
        observer.next(ots);
      }

      if (idsToLoad.length) {
        this.loadFromIds(idsToLoad).then((newObjectTypes) => {
          observer.next(newObjectTypes);
          observer.complete();
        });
      } else {
        observer.complete();
      }
    });
  }

  listFromTags(tags: string[]): Observable<LegacyTxObjectType[]> {
    return new Observable((observer) => {
      const ots: LegacyTxObjectType[] = [];
      const tagsToLoad: string[] = [];

      tags.forEach((tag) => {
        const attribute = this.getObjectType(tag);
        if (attribute) {
          ots.push(attribute);
        } else {
          tagsToLoad.push(tag);
        }
      });

      if (ots.length) {
        observer.next(ots);
      }

      if (tagsToLoad.length) {
        this.loadFromTags(tagsToLoad).then((newObjectTypes) => {
          observer.next(newObjectTypes);
          observer.complete();
        });
      } else {
        observer.complete();
      }
    });
  }

  getObjectTypeAttributes(idObjectType: number): Observable<any[]> {
    return this.apiService.listAttributesFromObjectType(idObjectType);
  }
}
