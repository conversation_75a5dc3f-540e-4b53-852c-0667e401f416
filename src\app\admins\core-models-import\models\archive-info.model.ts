import { FileHistory } from 'src/app/shared/models/files/file-history.model';
import { Info } from 'src/app/shared/models/info.model';
export interface ArchiveData {
  readonly id: string;
  readonly cacheId: number;
  readonly file: File;
  readonly archiveInfo: ArchiveInfo;
  readonly status: ArchiveStatus;
  readonly history: readonly FileHistory[];
}
/**
 * tuples of 5 following infos : version, date, user, explanation, comment
 */
export type ArchiveInfo = [Info, Info, Info, Info, Info];

export enum ArchiveStatus {
  Valid = 'valid',
  Invalid = 'invalid',
  TestImportValid = 'testImportValid',
  TestImportInvalid = 'testImportInvalid',
  Imported = 'imported',
  ImportFailed = 'importFailed',
}
