import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { LegacyTxEscfTreeComponent } from './escf-tree.component';
import { MockComponent, MockService } from 'ng-mocks';
import { LegacyTxObjectTypeService } from '../../../../../../services/structure/services/object-type.service';
import { TxTreeObjectsComponent } from '../../../../../../trees/tree-objects/tree-objects.component';

describe('TxEscfTreeComponent', () => {
  let component: LegacyTxEscfTreeComponent;
  let fixture: ComponentFixture<LegacyTxEscfTreeComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [LegacyTxEscfTreeComponent, MockComponent(TxTreeObjectsComponent)],
      providers: [
        {
          provide: LegacyTxObjectTypeService,
          useValue: MockService(LegacyTxObjectTypeService),
        },
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(LegacyTxEscfTreeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
