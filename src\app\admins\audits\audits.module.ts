import { NgModule } from '@angular/core';
import { AuditsRoutingModule } from './audits-routing.module';
import { AuditsComponent } from './components/audits.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { AuditsService } from './services/audits.service';
import { TxTreeGridModule } from '@bassetti-group/tx-web-core';

@NgModule({
  declarations: [AuditsComponent],
  imports: [SharedModule, AuditsRoutingModule, TxTreeGridModule],
  providers: [AuditsService],
})
export class AuditsModule {}
