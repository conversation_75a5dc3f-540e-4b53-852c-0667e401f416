import { Injectable } from '@angular/core';
import { Observable, map, of } from 'rxjs';
import { TxApiFileService } from './api-file.service';
import { HttpEventType } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class TxFileAttributeService {
  filesSaved: Map<number, Blob>;

  constructor(public apiService: TxApiFileService) {
    this.filesSaved = new Map<number, Blob>();
  }

  download(id: number): Observable<Blob | undefined> {
    if (this.filesSaved.has(id)) {
      return of(this.filesSaved.get(id));
    } else {
      return new Observable((observer) => {
        this.apiService.downloadFile(id).subscribe((blob) => {
          this.filesSaved.set(id, blob);
          observer.next(blob);
          observer.complete();
        });
      });
    }
  }

  upload(
    file: File,
    idAttribute: number
  ): Observable<number | { idArchivedFile: number; name: string } | undefined> {
    const formData = new FormData();
    formData.append('UploadFiles', file);
    return this.apiService.uploadFile(formData, idAttribute).pipe(
      map((event) => {
        if (event.type === HttpEventType.UploadProgress) {
          return Math.round(100 * (event.loaded / event.total));
        }
        if (event.type === HttpEventType.Response) {
          const idArchivedFile: number = event.body[0].id;
          const name: string = event.body[0].name;
          return { idArchivedFile, name };
        }
      })
    );
  }

  deleteCache(idsFile: number[]) {
    return this.apiService.deleteCacheFile(idsFile);
  }
}
