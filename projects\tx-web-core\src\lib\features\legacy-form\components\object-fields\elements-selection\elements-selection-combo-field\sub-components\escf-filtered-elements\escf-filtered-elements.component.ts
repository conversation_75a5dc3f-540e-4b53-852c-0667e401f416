import {
  Component,
  Input,
  OnInit,
  Output,
  EventEmitter,
  AfterViewInit,
  OnChanges,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { ListBoxChangeEventArgs, ListBoxComponent } from '@syncfusion/ej2-angular-dropdowns';
import { SortOrder } from '@syncfusion/ej2-angular-navigations';
import { SelectionSettingsModel } from '@syncfusion/ej2-dropdowns';

export interface TxListCheckEventArgs {
  /**
   * Return the name of action like check or un-check.
   */
  checked: boolean;
  /**
   * Return the currently checked element as JSON object from data source.
   */
  element: any;
}

@Component({
  selector: 'tx-escf-filtered-elements',
  templateUrl: './escf-filtered-elements.component.html',
  styleUrls: ['./escf-filtered-elements.component.scss'],
})
export class TxEscfFilteredElementsComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() elements: any[] = [];
  @Input() selectedElements: any[] = [];
  @Input() multiple = true;
  @Input() enabled = true;
  @Input() textKey = 'text';
  @Input() height = 300;
  @Input() valueKey = 'id';
  @Input() groupByKey: any;
  @Input() sortOrder: SortOrder = 'Ascending';
  @Input() idObjectType!: number;

  @Output() elementChanged = new EventEmitter<ListBoxChangeEventArgs>();
  @Output() elementChecked = new EventEmitter<TxListCheckEventArgs>();

  @ViewChild('listBox') listBox!: ListBoxComponent;

  public selectionSettings!: SelectionSettingsModel;

  filedsSettings = {};
  lastCheckedElements!: any[];

  constructor() {}

  ngOnInit() {
    this.selectionSettings = {
      showCheckbox: this.multiple,
      mode: this.multiple ? 'Multiple' : 'Single',
    };
  }

  ngOnChanges(changes: SimpleChanges) {
    // if (this.listBox && changes.elements)
    //   this.reloadElements(changes.elements.currentValue, changes.elements.currentValue);
  }

  ngAfterViewInit() {
    this.filedsSettings = { groupBy: this.groupByKey, text: this.textKey, value: this.valueKey };
  }

  findElement(value: any) {
    return this.elements.find((e) => e[this.valueKey] === value);
  }

  isElementExist(value: any): boolean {
    return this.findElement(value) !== undefined;
  }

  reloadElements(elements: any[], selectedElements: any[]) {
    this.elements = elements;
    this.selectedElements = selectedElements.map((e) => e[this.valueKey]);

    this.lastCheckedElements = selectedElements.filter((c) =>
      this.elements.some((e) => e[this.valueKey] === c[this.valueKey])
    );
  }

  addElements(elements: any[]) {
    elements.forEach((e) => this.addElement(e));
  }

  addElement(element: any) {
    if (!this.isElementExist(element[this.valueKey])) {
      this.elements.push(element);
    }
  }

  removeElement(element: any) {
    if (this.isElementExist(element[this.valueKey])) {
      this.elements = this.elements.filter((e) => e[this.valueKey] !== element[this.valueKey]);
    }
  }

  onElementChanged(args: ListBoxChangeEventArgs) {
    const checkedElements: any[] = [];
    const values = args.value as unknown as number[];

    values.forEach((v) => {
      const checkedElement = this.findElement(v);
      if (checkedElement) {
        checkedElements.push(checkedElement);
      }
    });

    let element: any;
    let checked = true;
    if (this.multiple) {
      checked = this.lastCheckedElements.length < checkedElements.length;
      if (checked) {
        // one element has been checked
        if (this.lastCheckedElements.length) {
          element = checkedElements.find((i) => {
            return !this.lastCheckedElements.some((e) => e[this.valueKey] === i[this.valueKey]);
          });
        } else {
          element = checkedElements[0];
        }

        this.lastCheckedElements.push(element);
      } else {
        // one element has been unchecked
        if (checkedElements.length) {
          element = this.lastCheckedElements.find((i) => {
            return !checkedElements.some((e) => e[this.valueKey] === i[this.valueKey]);
          });

          this.lastCheckedElements = this.lastCheckedElements.filter(
            (e) => e[this.valueKey] !== element[this.valueKey]
          );
        } else {
          element = this.lastCheckedElements[0];
          this.lastCheckedElements = [];
        }
      }
    } else {
      element = checkedElements[0];
    }

    this.elementChecked.emit({ checked, element });
    this.elementChanged.emit(args);
  }
}
