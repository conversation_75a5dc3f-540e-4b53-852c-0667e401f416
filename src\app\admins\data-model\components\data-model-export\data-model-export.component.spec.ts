import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DataModelExportComponent } from './data-model-export.component';
import { MatTabsModule } from '@angular/material/tabs';
import { MockComponent } from 'ng-mocks';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { AttributesServiceMock, ObjectsTypeServiceMock } from 'src/app/app.testing.mock';
import { DataModelService } from '../../service/data-model.service';
import { DataModelServiceMock } from '../../tests/data-model.testing.mock';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { MatDivider } from '@angular/material/divider';
import {
  TxAttributesService,
  TxObjectTypesTreeGridComponent,
  TxObjectsTypeService,
} from '@bassetti-group/tx-web-core';

describe('DataModelExportComponent', () => {
  let component: DataModelExportComponent;
  let fixture: ComponentFixture<DataModelExportComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        DataModelExportComponent,
        MockComponent(TxObjectTypesTreeGridComponent),
        MockComponent(MatDivider),
      ],
      imports: [
        FontAwesomeTestingModule,
        MatTabsModule,
        TranslateTestingModule.withTranslations({
          en: {},
          fr: {},
        }),
        MatTabsModule,
      ],
      providers: [
        { provide: TxAttributesService, useClass: AttributesServiceMock },
        { provide: TxObjectsTypeService, useClass: ObjectsTypeServiceMock },
        { provide: DataModelService, useClass: DataModelServiceMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(DataModelExportComponent);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DataModelExportComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
