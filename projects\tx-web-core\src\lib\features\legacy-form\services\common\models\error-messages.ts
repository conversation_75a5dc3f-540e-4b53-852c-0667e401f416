import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';

export class TxErrorMessages {
  private defaultMessages: Array<DefaultMessage> = [
    {
      errorCode: -1,
      type: 'warn',
      header: _('txWebCore.errors.unhandled.header'),
      content: _('txWebCore.errors.unhandled.content'),
      details: '',
    },
    {
      errorCode: 0,
      type: 'warn',
      header: _('txWebCore.errors.0.header'),
      content: _('txWebCore.errors.0.content'),
      details: '',
    },
    {
      errorCode: 400,
      type: 'warn',
      header: _('txWebCore.errors.400.default.header'),
      content: _('txWebCore.errors.400.default.content'),
      details: '',
    },
    {
      errorCode: 401,
      type: 'warn',
      header: _('txWebCore.errors.401.default.header'),
      content: _('txWebCore.errors.401.default.content'),
      details: '',
    },
    {
      errorCode: 403,
      type: 'warn',
      header: _('txWebCore.errors.403.default.header'),
      content: _('txWebCore.errors.403.default.content'),
      details: '',
    },
    {
      errorCode: 404,
      type: 'warn',
      header: _('txWebCore.errors.404.default.header'),
      content: _('txWebCore.errors.404.default.content'),
      details: '',
    },
    {
      errorCode: 500,
      type: 'warn',
      header: _('txWebCore.errors.500.default.header'),
      content: _('txWebCore.errors.500.default.content'),
      details: '',
    },
  ];

  private keyMessages: Array<KeyMessage> = [
    {
      key: 'ATTRIBUTE_NOT_FOUND',
      type: 'accent',
      header: _('txWebCore.errors.404.attribute.header'),
      content: _('txWebCore.errors.404.attribute.content'),
    },
    {
      key: 'RL_NOT_FOUND',
      type: 'accent',
      header: _('txWebCore.errors.404.requirementList.header'),
      content: _('txWebCore.errors.404.requirementList.content'),
    },
    {
      key: 'OBJECT_NOT_FOUND',
      type: 'accent',
      header: _('txWebCore.errors.404.object.header'),
      content: _('txWebCore.errors.404.object.content'),
    },
    {
      key: 'OT_NOT_FOUND',
      type: 'accent',
      header: _('txWebCore.errors.404.objectType.header'),
      content: _('txWebCore.errors.404.objectType.content'),
    },
    {
      key: 'USER_GROUP_NOT_FOUND',
      type: 'accent',
      header: _('txWebCore.errors.404.userGroup.header'),
      content: _('txWebCore.errors.404.userGroup.content'),
    },
    {
      key: 'USER_NOT_FOUND',
      type: 'accent',
      header: _('txWebCore.errors.404.user.header'),
      content: _('txWebCore.errors.404.user.content'),
    },
    {
      key: 'PREFERENCES.XML_NOT_FOUND',
      type: 'accent',
      header: _('txWebCore.errors.404.preferenceXML.header'),
      content: _('txWebCore.errors.404.preferenceXML.content'),
    },
    {
      key: 'DATA_PAGE_READING_NOT_ALLOWED',
      type: 'warn',
      header: _('txWebCore.errors.400.pageReadingNotAllowed.header'),
      content: _('txWebCore.errors.400.pageReadingNotAllowed.content'),
    },
    {
      key: 'INVALID_SEARCH_STRING',
      type: 'accent',
      header: 'txWebCore.errors.400.invalidSearchString.header',
      content: 'txWebCore.errors.400.invalidSearchString.content',
    },
    {
      key: 'USER_DELETION_NOT_ALLOWED',
      type: 'warn',
      header: _('txWebCore.errors.400.userDeletionNotAllowed.header'),
      content: _('txWebCore.errors.400.userDeletionNotAllowed.content'),
    },
    {
      key: 'USER_CREATION_FAILED',
      type: 'warn',
      header: _('txWebCore.errors.400.userCreationFailed.header'),
      content: _('txWebCore.errors.400.userCreationFailed.content'),
    },
    {
      key: 'USER_EDITION_FAILED',
      type: 'warn',
      header: _('txWebCore.errors.400.userEditionFailed.header'),
      content: _('txWebCore.errors.400.userEditionFailed.content'),
    },

    {
      key: 'INVALID_SUPPORT_OBJECT',
      type: 'warn',
      header: _('txWebCore.errors.400.invalidSupportObject.header'),
      content: _('txWebCore.errors.400.invalidSupportObject.content'),
    },
    {
      key: 'ALREADY_BOUND_SUPPORT_OBJECT',
      type: 'warn',
      header: _('txWebCore.errors.400.alreadyBoundSupportObject.header'),
      content: _('txWebCore.errors.400.alreadyBoundSupportObject.content'),
    },

    {
      key: 'INVALID_DATABASE_REQUEST',
      type: 'warn',
      header: _('txWebCore.errors.400.invalidDatabaseRequest.header'),
      content: _('txWebCore.errors.400.invalidDatabaseRequest.content'),
    },

    {
      key: 'INVALID_FILE_ECRYPTION',
      type: 'warn',
      header: _('txWebCore.errors.400.invalidFileEncryption.header'),
      content: _('txWebCore.errors.400.invalidFileEncryption.content'),
    },
    {
      key: 'USER_IS_DISCONNECTED',
      type: 'warn',
      header: _('txWebCore.errors.401.userDisconnected.header'),
      content: _('txWebCore.errors.401.userDisconnected.content'),
    },
  ];

  constructor() {}

  public getMessageByKey(msgKey: string): KeyMessage | undefined {
    return this.keyMessages.find((msg) => msg.key === msgKey);
  }

  public getDefaultMessageByCode(errorCode: number, errorDetails: string): DefaultMessage {
    const defaultMessage = this.defaultMessages.find(
      (msg) => msg.errorCode === errorCode
    ) as DefaultMessage;
    defaultMessage.details = errorDetails;
    return defaultMessage;
  }
}

export interface KeyMessage {
  key: string;
  type: string;
  header: string;
  content: string;
  details?: string;
  action?: any;
}

export interface DefaultMessage {
  errorCode: number;
  type: string;
  header: string;
  content: string;
  details: string;
}
