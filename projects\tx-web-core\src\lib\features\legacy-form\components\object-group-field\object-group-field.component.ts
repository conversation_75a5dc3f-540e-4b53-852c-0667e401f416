import { animate, state, style, transition, trigger } from '@angular/animations';
import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  QueryList,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { LegacyTxData, LegacyTxDataType } from '../../services/structure/models/data';
import { TxGroupAttributeField } from '../../models/formConfiguration/businessClass/group-attribute-field';
import { TxVirtualAttributeField } from '../../models/formConfiguration/businessClass/virtual-attribute-field';
import { TxObjectFieldComponent } from '../object-fields/_system/object-field/object-field.component';
import { LegacyTxAttributesService } from '../../services/structure/services/attributes.service';

@Component({
  selector: 'tx-object-group-field',
  templateUrl: './object-group-field.component.html',
  styleUrls: [
    './object-group-field.component.scss',
    '../container-field/container-field.component.scss',
  ],
  animations: [
    trigger('openClose', [
      state(
        'open',
        style({
          height: '*',
        })
      ),
      state(
        'closed',
        style({
          height: '0',
        })
      ),
      transition('open <=> closed', animate('400ms ease-in-out')),
    ]),
  ],
})
export class LegacyTxObjectGroupFieldComponent implements OnInit {
  @Input() form!: FormGroup;
  @Input() groupField: TxGroupAttributeField | undefined;
  @Input() idObject!: number;
  @Input() readMode!: boolean;

  @Output() mouseEnterEvent = new EventEmitter();
  @Output() mouseLeaveEvent = new EventEmitter();
  @Output() displayPaneEvent = new EventEmitter<any>();

  @ViewChildren('fieldComponent')
  fieldComponents!: QueryList<TxObjectFieldComponent>;
  @ViewChildren('groupComponent')
  groupComponents!: QueryList<LegacyTxObjectGroupFieldComponent>;

  // @ViewChild('container') containerField: TxContainerFieldComponent;
  @ViewChild('fieldset') fieldset!: ElementRef;

  groupsClosedByDefault: boolean = false;
  fieldSetClosed = this.groupsClosedByDefault;
  fieldSetContentHidden = this.groupsClosedByDefault;
  labelTooltip = '';
  fields!: TxVirtualAttributeField[];
  field!: TxVirtualAttributeField;
  groupName!: string;
  fieldType = LegacyTxDataType;

  constructor(public attributeService: LegacyTxAttributesService) {}

  ngOnInit(): void {
    this.fields = this.groupField?.children ?? [];
    this.groupName = this.groupField?.attribute.name ?? '';
    this.labelTooltip = this.attributeService.getHint(this.groupField?.attribute.id ?? -1);
    if (
      this.groupField?.properties &&
      this.groupField.properties.groupsClosedByDefault !== undefined
    ) {
      this.fieldSetClosed = this.groupField.properties.groupsClosedByDefault;
      this.fieldSetContentHidden = this.fieldSetClosed;
    }
  }

  onSubmit(): LegacyTxData[] {
    let data: LegacyTxData[] = [];
    for (const field of this.fieldComponents) {
      const dataToSave = field.getDataToSave();

      if (dataToSave) {
        data.push(dataToSave);
      }
    }
    for (const group of this.groupComponents) {
      data = data.concat(group.onSubmit());
    }

    return data;
    // return this.containerField.onSubmit();
  }

  selectedTabChange() {
    // if (this.booleanFields) {
    //   for ( const booleanField of this.booleanFields) {
    //     booleanField.computeMinWidth();
    //   }
    // }
    //this.containerField.selectedTabChange();
  }

  getNbError(): number {
    let res = 0;
    if (this.form) {
      for (const field of this.fields) {
        if (
          this.form.contains(field.id.toString()) &&
          this.form.get(field.id.toString())?.invalid
        ) {
          res += 1;
        }
      }
      for (const group of this.groupComponents) {
        res += group.getNbError();
      }
    }
    return res;
  }

  // onMouseEnter() {
  //   this.mouseEnterEvent.emit(null);
  // }

  // onMouseLeave() {
  //   this.mouseLeaveEvent.emit(null);
  // }

  // onChildMouseEnter() {
  //   this.fieldset.nativeElement.classList.remove('form-fieldset-background');
  // }

  // onChildMouseLeave() {
  //   setTimeout( ()  => {
  //     this.fieldset.nativeElement.classList.add('form-fieldset-background');
  //   }, 100);
  // }

  loadData(dataList: LegacyTxData[]) {
    for (const data of dataList) {
      for (const fieldComponent of this.fieldComponents) {
        if (data.idAttribute === fieldComponent.idAttribute) {
          fieldComponent.setData(data);
          const index = dataList.indexOf(data);
          dataList.slice(index, 1);
        }
      }
    }
    for (const group of this.groupComponents) {
      group.loadData(dataList);
    }
  }

  showOrHideFieldSet() {
    this.fieldSetClosed = !this.fieldSetClosed;
    if (this.fieldSetClosed) {
      setTimeout(() => {
        this.fieldSetContentHidden = !this.fieldSetContentHidden;
      }, 300);
    } else {
      this.fieldSetContentHidden = false;
    }
  }

  displayRightPane(event: any) {
    this.displayPaneEvent.emit(event);
  }

  getFieldCmpntIndexFromGlobalIndex(globalIndex: number) {
    let resIndex = globalIndex;
    for (let i = 0; i < globalIndex; i++) {
      if (this.fields[i].attribute.dataType === this.fieldType.Group) {
        resIndex--;
      }
    }
    return resIndex;
  }

  haveToBeHidden(index: number) {
    const i = this.getFieldCmpntIndexFromGlobalIndex(index);
    if (this.fieldComponents) {
      const fieldComponent = this.fieldComponents.get(i);
      if (fieldComponent) {
        return fieldComponent.haveToBeHidden();
      }
    } else {
      return false;
    }
  }
}
