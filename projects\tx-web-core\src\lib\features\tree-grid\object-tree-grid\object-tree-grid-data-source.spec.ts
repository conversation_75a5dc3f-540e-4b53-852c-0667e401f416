import { CollectionViewer } from '@angular/cdk/collections';
import { HttpClient } from '@angular/common/http';
import { runInInjectionContext } from '@angular/core';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { TxConfigService, TxObjectTypeIconService } from '@bassetti-group/tx-web-core';
import { TxObject } from '@bassetti-group/tx-web-core/src/lib/business-models';
import {
  TxObjectsService,
  TxObjectsTypeService,
} from '@bassetti-group/tx-web-core/src/lib/data-access/structure';
import { BehaviorSubject, of, throwError } from 'rxjs';
import { ObjectTreeGridDataSource } from './object-tree-grid-data-source';

interface TreeNode {
  id: string;
  txObject: TxObject;
  children: TreeNode[];
}

describe('ObjectTreeGridDataSource', () => {
  let dataSource: ObjectTreeGridDataSource;
  let service: TxObjectsService;
  let httpClient: HttpClient;
  let txConfigService: TxConfigService;
  let txObjectTypeIconService: TxObjectTypeIconService;
  let txObjectsTypeService: TxObjectsTypeService;

  const mockTxObject: TxObject = {
    id: 1,
    name: 'Test Object',
    order: 0,
    isParent: false,
    isFolder: false,
    idObjectType: 123,
    idOwnerObject: 0,
    creationDate: new Date(),
    tags: [],
  };

  const mockNodes: TxObject[] = [
    {
      idObjectParent: 0,
      order: 0,
      isParent: true,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2013-05-21T00:00:00.0000000Z',
      searchName: '',
      name: 'New',
      tags: [],
      id: 28,
    },
    {
      idObjectParent: 28,
      order: 1,
      isParent: true,
      isFolder: true,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2013-05-21T00:00:00.0000000Z',
      searchName: '',
      name: 'New1',
      tags: [],
      id: 29,
    },
    {
      idObjectParent: 28,
      order: 2,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2022-09-04T16:42:31.0000000Z',
      searchName: '',
      name: 'Administrator',
      tags: [],
      id: 1,
    },
    {
      idObjectParent: 29,
      order: 3,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2014-04-28T16:09:06.0000000Z',
      searchName: '',
      name: 'New2',
      tags: [],
      id: 174,
    },
    {
      idObjectParent: 0,
      order: 4,
      isParent: true,
      isFolder: true,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2017-09-13T15:03:19.0000000Z',
      searchName: '',
      name: 'UserRole1',
      tags: [],
      id: 198,
    },
    {
      idObjectParent: 198,
      order: 5,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2017-09-13T15:03:25.0000000Z',
      searchName: '',
      name: 'UserRole2',
      tags: [],
      id: 199,
    },
    {
      idObjectParent: 198,
      order: 6,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2017-09-13T15:03:31.0000000Z',
      searchName: '',
      name: 'UserRole3',
      tags: [],
      id: 200,
    },
    {
      idObjectParent: 0,
      order: 7,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2018-10-09T10:02:09.0000000Z',
      searchName: '',
      name: 'Standard_User_M+',
      tags: [],
      id: 201,
    },
    {
      idObjectParent: 0,
      order: 8,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2018-10-12T08:58:51.0000000Z',
      searchName: '',
      name: 'pmusernorights',
      tags: [],
      id: 202,
    },
    {
      idObjectParent: 0,
      order: 9,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2022-03-15T08:32:26.0000000Z',
      searchName: '',
      name: 'Admin Read CR',
      tags: [],
      id: 215,
    },
    {
      idObjectParent: 0,
      order: 10,
      isParent: false,
      isFolder: false,
      idObjectType: 1,
      idOwnerObject: 1,
      creationDate: '2022-03-15T11:27:48.0000000Z',
      searchName: '',
      name: 'File Manager',
      tags: [],
      id: 216,
    },
  ];

  const mockTxObjectsService = {
    getObjectsFromParent: jest.fn().mockReturnValue(
      of({
        objects: [mockNodes[0]],
      })
    ),
    searchObjects: jest.fn(),
    getAllObjectsChildren: jest.fn(),
    getRootToParentIds: jest.fn(),
    getIconPath: jest.fn(),
    getSelectedObject: jest.fn().mockReturnValue(of({ objects: [] })),
    getChildrenObjectsByIds: jest.fn().mockReturnValue(of({ objects: [] })),
  };

  const mockTxObjectsTypeService = {
    getObjectsType: jest.fn(),
    getObjectTypes: jest.fn(),
    getObjectsByType: jest.fn(),
    isReady: jest.fn().mockReturnValue(of(true)),
  };

  const mockHttpClient = {
    get: jest.fn(),
    post: jest.fn(),
  };

  const mockTxConfigService = {
    getConfig: jest.fn(),
  };

  const mockTxObjectTypeIconService = {
    getIconPath: jest.fn(),
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        { provide: TxObjectsService, useValue: mockTxObjectsService },
        { provide: TxObjectsTypeService, useValue: mockTxObjectsTypeService },
        { provide: HttpClient, useValue: mockHttpClient },
        { provide: TxConfigService, useValue: mockTxConfigService },
        { provide: TxObjectTypeIconService, useValue: mockTxObjectTypeIconService },
      ],
    });
    txObjectTypeIconService = TestBed.inject(TxObjectTypeIconService);
    txObjectsTypeService = TestBed.inject(TxObjectsTypeService);

    runInInjectionContext(TestBed.inject(TestBed), () => {
      dataSource = new ObjectTreeGridDataSource(of(mockNodes as never[]), 'id');
      dataSource.idObjectType = 123;
    });
  });

  describe('toggleNode', () => {
    it('should expand a collapsed node and trigger page update', fakeAsync(() => {
      mockTxObjectsService.getObjectsFromParent.mockReturnValue(
        of({
          objects: [{ ...mockNodes[1], idObjectParent: mockNodes[0].id }],
        })
      );

      const spyTriggerPageUpdate = jest.spyOn(dataSource, 'triggerUpdatePageRequest');

      runInInjectionContext(TestBed.inject(TestBed), () => {
        expect(dataSource.isExpanded({ ...mockNodes[0], txObject: mockNodes[0] })).toBe(false);
        dataSource.toggleNode(mockNodes[0].id);
        tick();
        expect(spyTriggerPageUpdate).toHaveBeenCalled();
        expect(dataSource.isExpanded({ ...mockNodes[0], txObject: mockNodes[0] })).toBe(true);
        expect(mockTxObjectsService.getObjectsFromParent).toHaveBeenCalledWith(
          123,
          mockNodes[0].id
        );
      });
    }));
  });

  describe('addData', () => {
    beforeEach(() => {
      mockTxObjectsTypeService.isReady.mockReturnValue(of(true));
    });

    it('should handle errors during data preparation', fakeAsync(() => {
      mockTxObjectsTypeService.isReady.mockReturnValue(
        throwError(() => new Error('Service not ready'))
      );

      runInInjectionContext(TestBed.inject(TestBed), () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        const wrappedPrepareAddData = () => {
          return new Promise((resolve) => {
            dataSource.prepareAddData([mockNodes[0]]);
            tick();
            resolve(null);
          });
        };
        return wrappedPrepareAddData()
          .then(() => {
            expect(consoleSpy).toHaveBeenCalledWith(expect.any(Error));
            expect(dataSource.expandedNodes.size).toBe(0);
          })
          .finally(() => {
            consoleSpy.mockRestore();
          });
      });
    }));

    it('should handle empty data arrays correctly', fakeAsync(() => {
      runInInjectionContext(TestBed.inject(TestBed), () => {
        dataSource.toggleNode(mockNodes[0].id);
        tick();
        expect(dataSource.isExpanded({ ...mockNodes[0], txObject: mockNodes[0] })).toBe(true);
        dataSource.prepareAddData([]);
        tick();
        expect(dataSource.expandedNodes.size).toBe(0);
        expect(dataSource.isExpanded({ ...mockNodes[0], txObject: mockNodes[0] })).toBe(false);
        expect(mockTxObjectsTypeService.isReady).toHaveBeenCalled();
      });
    }));
    it('should add data and clear expanded nodes', fakeAsync(() => {
      runInInjectionContext(TestBed.inject(TestBed), () => {
        dataSource.toggleNode(mockNodes[0].id);
        tick();
        expect(dataSource.isExpanded({ ...mockNodes[0], txObject: mockNodes[0] })).toBe(true);
        const newData = [mockNodes[0]];
        dataSource.prepareAddData(newData);
        tick();
        expect(dataSource.isExpanded({ ...mockNodes[0], txObject: mockNodes[0] })).toBe(false);
        expect(dataSource.expandedNodes.size).toBe(0);
        expect(mockTxObjectsTypeService.isReady).toHaveBeenCalled();
      });
    }));
  });
  describe('loadSearchIdData', () => {
    it('should fetch and format objects for given ID', fakeAsync(() => {
      const mockResponse = {
        objects: [mockNodes[0]],
      };

      mockTxObjectsService.getObjectsFromParent.mockReturnValue(of(mockResponse));

      runInInjectionContext(TestBed.inject(TestBed), () => {
        dataSource.loadSearchIdData(1);
      });
      tick();

      expect(mockTxObjectsService.getObjectsFromParent).toHaveBeenCalledWith(123, 1);
    }));
  });

  describe('loadSearchStringData', () => {
    it('should search objects and remove duplicates', fakeAsync(() => {
      const mockSearchResults = [mockNodes[0], mockNodes[1], mockNodes[0]];
      const selectedElements = new Set([1]);

      mockTxObjectsService.searchObjects.mockImplementation((query) => {
        return of(mockSearchResults);
      });

      mockTxObjectsService.getChildrenObjectsByIds.mockImplementation((ids) => {
        return of({ objects: [] });
      });

      mockTxObjectsService.getSelectedObject.mockImplementation((ids, type) => {
        return of({ objects: [] });
      });

      runInInjectionContext(TestBed.inject(TestBed), () => {
        dataSource.getSelectedElements = jest.fn().mockImplementation(() => {
          return selectedElements;
        });
        dataSource.loadSearchStringData('test');

        tick(100);
        expect(mockTxObjectsService.searchObjects).toHaveBeenCalledWith(
          'test',
          123,
          undefined,
          false
        );

        expect(mockTxObjectsService.getChildrenObjectsByIds).toHaveBeenCalledWith(
          selectedElements,
          123
        );
      });
    }));

    it('should handle search with no selected elements', fakeAsync(() => {
      const mockSearchResults = [mockNodes[0]];

      mockTxObjectsService.searchObjects.mockReturnValue(of(mockSearchResults));
      mockTxObjectsService.getChildrenObjectsByIds.mockReturnValue(of({ objects: [] }));
      mockTxObjectsService.getSelectedObject.mockReturnValue(of({ objects: [] }));

      runInInjectionContext(TestBed.inject(TestBed), () => {
        dataSource.getSelectedElements = jest.fn().mockReturnValue(new Set());

        dataSource.loadSearchStringData('test');
        tick();

        expect(mockTxObjectsService.searchObjects).toHaveBeenCalledWith(
          'test',
          123,
          undefined,
          false
        );

        expect(mockTxObjectsService.getSelectedObject).not.toHaveBeenCalled();
      });
    }));

    afterEach(() => {
      jest.clearAllMocks();
    });
  });

  describe('connect', () => {
    it('should filter tree data correctly based on expanded nodes', fakeAsync(() => {
      const collectionViewer: CollectionViewer = {
        viewChange: new BehaviorSubject({ start: 0, end: 2 }),
      };

      runInInjectionContext(TestBed.inject(TestBed), () => {
        dataSource.toggleNode(mockNodes[0].id);

        dataSource.connect(collectionViewer).subscribe((data) => {
          expect(data.length).toBeGreaterThan(0);
        });
      });
      tick();
    }));
  });

  describe('childSelection', () => {
    it('should fetch and process child objects', fakeAsync(() => {
      const mockChildObjects = {
        objects: [
          {
            id: 29,
            isFolder: true,
            name: 'Folder Child',
            idObjectParent: 1,
            order: 1,
            isParent: true,
          },
          {
            id: 174,
            isFolder: false,
            name: 'Regular Child',
            idObjectParent: 1,
            order: 2,
            isParent: false,
          },
        ],
      };

      mockTxObjectsService.getObjectsFromParent.mockReturnValue(of(mockChildObjects));

      runInInjectionContext(TestBed.inject(TestBed), () => {
        dataSource.childSelection([1], false);
        tick();

        expect(mockTxObjectsService.getObjectsFromParent).toHaveBeenCalledWith(123, 28);

        const selectedElements = Array.from(dataSource.getSelectedElements());

        expect(selectedElements).toContain(1);
        expect(selectedElements).not.toContain(29);
      });
    }));

    afterEach(() => {
      jest.clearAllMocks();
    });
  });

  describe('getIconPath', () => {
    it('should return correct icon path for folder', () => {
      const mockPath = 'folder/icon/path';
      mockTxObjectsService.getIconPath.mockReturnValue(mockPath);

      runInInjectionContext(TestBed.inject(TestBed), () => {
        const result = dataSource['getIconPath']({ ...mockTxObject, isFolder: true });
        expect(result).toBe(mockPath);
        expect(mockTxObjectsService.getIconPath).toHaveBeenCalled();
      });
    });

    it('should return correct icon path for non-folder object', () => {
      const mockPath = 'object/icon/path';
      mockTxObjectsService.getIconPath.mockReturnValue(mockPath);

      runInInjectionContext(TestBed.inject(TestBed), () => {
        const result = dataSource['getIconPath']({ ...mockTxObject, isFolder: false });
        expect(result).toBe(mockPath);
        expect(mockTxObjectsService.getIconPath).toHaveBeenCalled();
      });
    });
  });
});
