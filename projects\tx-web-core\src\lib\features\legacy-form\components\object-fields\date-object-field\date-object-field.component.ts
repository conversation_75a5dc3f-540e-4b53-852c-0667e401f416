import { TxInputObjectFieldComponent } from './../_system/input-object-field/input-object-field.component';
import { AfterViewInit, Component, Input, ViewChild } from '@angular/core';
import { TxAttributeField } from '../../../models/formConfiguration/businessClass/attribute-field';
import { DatePicker, DateTimePicker } from '@syncfusion/ej2-angular-calendars';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { LegacyTxDataString, LegacyTxDataType } from '../../../services/structure/models/data';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'tx-date-object-field',
  templateUrl: './date-object-field.component.html',
  styleUrls: ['./date-object-field.component.scss'],
  providers: [DatePipe],
})
export class TxDateObjectFieldComponent
  extends TxInputObjectFieldComponent
  implements AfterViewInit
{
  @Input() declare field: TxAttributeField;
  @Input() declare data: LegacyTxDataString;
  @Input() dateFormat = 'dd/MM/yyyy';
  @Input() dateTimeFormat = 'dd/MM/yyyy HH:mm:ss';
  @Input() timePicker = false;
  @Input() timeStep = 15;
  @Input() timeFormat = 'HH:mm';
  @Input() hideCalendarIcon = false;
  @Input() labelTooltip!: string;

  @ViewChild('datePicker') public datePickerComponent!: DatePicker;
  @ViewChild('dateTimePicker') public dateTimePickerComponent!: DateTimePicker;

  pickerElement!: HTMLElement;
  labelSpan!: Element;

  constructor(private datePipe: DatePipe, public attributeService: LegacyTxAttributesService) {
    super(attributeService);
  }

  initProperties() {
    super.initProperties();
    if (this.attribute) {
      this.timePicker = this.attribute.dataType === LegacyTxDataType.DateTime;
    }
  }

  initValue() {
    if (this.data) {
      this.value = this.data.value;
    }
  }

  ngAfterViewInit() {
    super.ngAfterViewInit();
    if (!this.readMode) {
      if (!this.timePicker) {
        this.pickerElement = this.datePickerComponent.element;
      } else {
        this.pickerElement = this.dateTimePickerComponent.element;
      }
      const spanIcon = this.pickerElement.getElementsByClassName('e-clear-icon')[0];
      this.labelSpan = this.pickerElement.getElementsByClassName('e-float-text')[0];
      this.pickerElement.onmouseenter = () => {
        if (this.control.value) {
          if (spanIcon.classList.contains('e-clear-icon-hide')) {
            spanIcon.classList.remove('e-clear-icon-hide');
          }
        }
      };

      this.pickerElement.onmouseleave = () => {
        if (!spanIcon.classList.contains('e-clear-icon-hide')) {
          spanIcon.classList.add('e-clear-icon-hide');
        }
      };

      if (this.dateTimePickerComponent && this.hideCalendarIcon) {
        this.dateTimePickerComponent.cssClass = 'date-time-no-date-icon';
      }
    }
  }

  getData(): LegacyTxDataString {
    let value = this.control.value;
    if (value instanceof Date) {
      const date = this.control.value as Date;
      value = date.toISOString();
    }
    return new LegacyTxDataString(this.idObject, this.idAttribute, value);
  }

  setTodayDate() {
    if (!this.control.value) {
      const today = new Date();
      this.control.setValue(today);
    }
  }

  getValueWithGoodFormat() {
    return this.datePipe.transform(
      this.control.value,
      this.timePicker ? this.dateTimeFormat : this.dateFormat
    );
  }
}
