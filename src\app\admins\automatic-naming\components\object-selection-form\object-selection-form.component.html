<app-form-pane-template
  #objectSelectionForm
  [settings]="{ isEditMode: false }"
  [iconForm]="'check-circle'"
  [title]="'Object selection'"
  [allowValidation]="validForm"
  [buttonAddCaption]="selectButtonCaption"
  (cancel)="onCancel()"
  (addForm)="beforeConfirm()">
  <div class="form-pane-container">
    <tx-objects-type-dropdown
      [showClearButton]="false"
      [disabled]="disableObjectTypeChange"
      [multipleSelection]="false"
      width="300px"
      floatLabelType="Auto"
      [objectTypes]="objectTypes"
      [label]="'concepts.objectType' | translate"
      [filtered]="true"
      [onlyVisible]="false"
      [required]="true"
      [checkedIds]="[idObjectTypeSelected]"
      (valueChange)="objectTypeChange($event)"></tx-objects-type-dropdown>
    <tx-object-tree-grid
      class="object-tree-grid form-field"
      *ngIf="idObjectTypeSelected"
      [checkedObjects]="objectsSelected"
      [multipleSelection]="multipleSelection"
      [idObjectType]="getIdObjectTypeSelected()"
      [enableCheckbox]="true"
      [memorizeSelection]="false"
      (checkChange)="onTreeGridCheckChange($event)"></tx-object-tree-grid>
  </div>
</app-form-pane-template>
