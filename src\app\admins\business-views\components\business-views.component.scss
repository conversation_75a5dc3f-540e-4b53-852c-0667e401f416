.ttypes-container {
  margin-top: 16px;
  height: Calc(100% - 54px);
  display: flex;
  column-gap: 32px;

  .ttypes-container-left {
    position: relative;
    width: 35%;
    display: flex;
    flex-direction: column;
    row-gap: 8px;

    .header {
      display: flex;
      justify-content: space-between;

      .button-section {
        margin-top: -2px;
      }
    }

    .business-view-toolbar {
      padding: 4px 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: none;
      overflow-x: auto;
      overflow-y: hidden;
      margin-bottom: -8px;
    }

    app-concepts-list {
      flex: 1;
      overflow: hidden;
    }
  }
  .ttypes-container-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    row-gap: 8px;
    width: calc(100% - 350px);

    .no-record-grid {
      flex: 1;
      overflow: hidden;
      position: relative;
    }

    .ttypes-grid {
      flex: 1;
      overflow: hidden;
      border-top-width: 0px;
      border-right-width: 0px;
      border-left-width: 0px;

      .dm-ttreegrid {
        height: calc(100% - 6px) !important;
      }

      .dm-container-right {
        width: calc(65%);
        margin-left: 32px;
        position: relative;

        .dm-buttons {
          display: flex;

          .toggle-attributes-selection {
            margin-top: 10px;
          }
        }

        fa-icon {
          margin-right: 8px;
        }
      }
    }

    .ttypes-buttons-bar {
      display: flex;
      column-gap: 8px;
    }
  }
}

.mat-mdc-standard-chip::after {
  background: none !important;
}

app-loader {
  margin-top: 4px;
}
