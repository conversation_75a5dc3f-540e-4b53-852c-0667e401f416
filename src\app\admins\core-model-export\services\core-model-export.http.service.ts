import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { TxConfigService } from '@bassetti-group/tx-web-core';
import { HttpClient } from '@angular/common/http';
import {
  CoreModelExportHistoryDTO,
  CoreModelExportHistoryObjectDTO,
} from '../models/core-model-export-history-object.dto';
import { CoreModelsExportGatewayService } from './core-model-export-gateway.service';
import { CoreModelExportConceptDTO } from '../models/core-model-export-concept.dto';
import { ErrorService } from 'src/app/core/services/errors/error.service';

@Injectable({
  providedIn: 'root',
})
export class CoreModelsExportHttpService implements CoreModelsExportGatewayService {
  private readonly apiUrl: string;
  private readonly exportUrl: string;
  constructor(
    private readonly configService: TxConfigService,
    private readonly http: HttpClient,
    private readonly errorService: ErrorService
  ) {
    this.apiUrl = `${this.configService.getApiUrl()}api/CoreModel`;
    this.exportUrl = `${this.apiUrl}/export`;
    this.errorService.registerUnhandledRequestURL(this.exportUrl);
  }
  loadCoreModelsExportHistoryList(): Observable<CoreModelExportHistoryDTO[]> {
    return this.http
      .get<CoreModelExportHistoryObjectDTO>(`${this.apiUrl}/export/history`)
      .pipe(map((response) => response.history));
  }
  loadCoreModelsConceptData(): Observable<CoreModelExportConceptDTO[]> {
    return this.http.get<CoreModelExportConceptDTO[]>(`${this.apiUrl}`);
  }

  exportCoreModels(history: CoreModelExportHistoryDTO): Observable<Blob> {
    return this.http.post(this.exportUrl, history, {
      responseType: 'blob',
    });
  }
}
