export interface TableType {
  id: number;
  name: string;
  isUsed?: boolean;
  explanation?: string;
  tags: string[];
  series?: SeriesType[];
}

export interface SeriesType {
  id: number;
  name: string;
  type: SeriesTypesType;
  isMultiple: boolean;
  valuesCount?: number;
  idUnit?: number;
  order: number;
  idTableType: number;
  tags: string[];
}

export enum SeriesTypesType {
  sttNumerical = 'sttNumerical',
  sttText = 'sttText',
  sttUndefined = 'sttUndefined',
}
