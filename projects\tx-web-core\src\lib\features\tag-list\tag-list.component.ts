import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  Input,
  OnChanges,
  ChangeDetectionStrategy,
} from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  ToastComponent,
  ToastService,
  ToastType,
} from '@bassetti-group/tx-web-core/src/lib/ui/toast';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  TxEscapeHtmlPipe,
  TxHighlightSearchPipe,
} from '@bassetti-group/tx-web-core/src/lib/utilities';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { CommonModule } from '@angular/common';

@Component({
  standalone: true,
  imports: [
    FontAwesomeModule,
    MatTooltipModule,
    TxEscapeHtmlPipe,
    ClipboardModule,
    TxHighlightSearchPipe,
    TranslateModule,
    CommonModule,
  ],
  selector: 'tx-tag-list',
  templateUrl: './tag-list.component.html',
  styleUrls: ['./tag-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagListComponent implements OnInit, OnChanges {
  @Input() tags?: string[];
  @Input() tagListWidth?: number;
  @Input() tagListSearchValue?: string;
  @ViewChild('tagList', { read: ElementRef })
  tagList?: ElementRef<HTMLDivElement>;

  arrowsNeeded = false;
  isTagListNeeded = false;

  constructor(private toastService: ToastService, private translate: TranslateService) {}

  ngOnInit() {
    this.getTagListWidth();
  }

  ngOnChanges() {
    this.getTagListWidth();
  }

  copyTag() {
    this.showToast(
      'information',
      this.translate.instant(_('txWebCore.generic.tagCopy')),
      4000,
      false
    );
  }

  scrollLeft(): void {
    this.tagList?.nativeElement.scrollTo({
      left: this.tagList?.nativeElement.scrollLeft - 120,
      behavior: 'smooth',
    });
  }

  scrollRight(): void {
    this.tagList?.nativeElement.scrollTo({
      left: this.tagList?.nativeElement.scrollLeft + 120,
      behavior: 'smooth',
    });
  }
  private getTagListWidth(): void {
    if (!this.tags) {
      return;
    }
    if (this.tagListWidth && this.tagListWidth > 0) {
      let sum = 0;
      this.tags.forEach((tag) => {
        sum += document.getElementById(tag)?.clientWidth ?? 0;
      });
      if (sum > this.tagListWidth) {
        this.arrowsNeeded = true;
      } else {
        this.arrowsNeeded = false;
      }
    }
    this.isTagListNeeded = this.tags?.length > 0;
  }

  private showToast(
    state: string,
    message: string,
    duration: number,
    isPersistent: boolean
  ): ToastComponent {
    return this.toastService.show({
      templateContext: { test: { state, message, progress: 0 } },
      type: state as ToastType,
      description: message,
      isPersistent,
      interval: duration,
    });
  }
}
