import { Injectable, ComponentFactoryResolver, ApplicationRef, Injector } from '@angular/core';
import { ComponentPortal, DomPortalOutlet } from '@angular/cdk/portal';
import { TxEscfPopupComponent } from './escf-popup.component';

@Injectable({
  providedIn: 'root',
})
export class TxEscfPopupService {
  // 1. Reference to our Portal.
  //    This is the portal we'll use to attach our TxEscfPopupComponent.
  private escPopup: ComponentPortal<TxEscfPopupComponent>;

  // 2. Reference to our Portal Host.
  //    We use DOMPortalHost as we'll be using document.body as our anchor.
  private bodyPortalHost: DomPortalOutlet;
  private visible = false;

  // 3. Inject the dependencies needed by the DOMPortalHost constructor
  constructor(
    private componentFactoryResolver: ComponentFactoryResolver,
    private appRef: ApplicationRef,
    private injector: Injector
  ) {
    // 4. Create a Portal based on the LoadingSpinnerComponent
    this.escPopup = new ComponentPortal(TxEscfPopupComponent);

    // 5. Create a PortalHost with document.body as its anchor element
    this.bodyPortalHost = new DomPortalOutlet(
      document.body,
      this.componentFactoryResolver,
      this.appRef,
      this.injector
    );
  }

  reveal() {
    // 6. Attach the Portal to the PortalHost.
    this.bodyPortalHost.attach(this.escPopup);
    this.visible = true;
  }

  hide() {
    // 7. Detach the Portal from the PortalHost
    this.bodyPortalHost.detach();
    this.visible = false;
  }

  toggle() {
    if (this.visible) {
      this.hide();
    } else {
      this.reveal();
    }
  }
}
