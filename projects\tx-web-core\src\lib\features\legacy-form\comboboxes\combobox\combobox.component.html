<ejs-multiselect
  *ngIf="multiple"
  #combobox
  [allowCustomValue]="allowCustomValue"
  [allowFiltering]="allowFiltering"
  [closePopupOnSelect]="closePopupOnSelect"
  [cssClass]="cssClass"
  [dataSource]="dataSource"
  [delimiterChar]="delimiterChar"
  [enableGroupCheckBox]="enableGroupCheckBox"
  [enablePersistence]="enablePersistence"
  [enableRtl]="enableRtl"
  [enableSelectionOrder]="enableSelectionOrder"
  [enabled]="enabled"
  [fields]="fields"
  [filterBarPlaceholder]="filterBarPlaceholder"
  [floatLabelType]="floatLabelType"
  [footerTemplate]="footerTemplate"
  [groupTemplate]="groupTemplate"
  [headerTemplate]="headerTemplate"
  [hideSelectedItem]="hideSelectedItem"
  [htmlAttributes]="htmlAttributes"
  [ignoreAccent]="ignoreAccent"
  [ignoreCase]="ignoreCase"
  [itemTemplate]="itemTemplate"
  [locale]="locale"
  [maximumSelectionLength]="maximumSelectionLength"
  [mode]="mode"
  [openOnClick]="openOnClick"
  [placeholder]="placeholder"
  [popupHeight]="popupHeight"
  [popupWidth]="popupWidth"
  [query]="query"
  [readonly]="readonly"
  [selectAllText]="selectAllText"
  [showClearButton]="showClearButton"
  [showDropDownIcon]="showDropDownIcon"
  [showSelectAll]="showSelectAll"
  [sortOrder]="sortOrder"
  [text]="text"
  [unSelectAllText]="unSelectAllText"
  [value]="value"
  [valueTemplate]="valueTemplate"
  [width]="width"
  [zIndex]="zIndex"
  (actionBegin)="onActionBegin($event)"
  (actionComplete)="onActionComplete($event)"
  (actionFailure)="onActionFailure($event)"
  (beforeOpen)="onBeforeOpen($event)"
  (blur)="onBlur($event)"
  (change)="onChange($event)"
  (chipSelection)="onChipSelection($event)"
  (close)="onClose($event)"
  (created)="onCreated($event)"
  (customValueSelection)="onCustomValueSelection($event)"
  (dataBound)="onDataBound($event)"
  (destroyed)="onDestroyed($event)"
  (filtering)="onFiltering($event)"
  (focus)="onFocus($event)"
  (open)="onOpen($event)"
  (removed)="onRemoved($event)"
  (removing)="onRemoving($event)"
  (select)="onSelect($event)"
  (selectedAll)="onSelectedAll($event)"
  (tagging)="onTagging($event)"></ejs-multiselect>

<ejs-dropdownlist
  *ngIf="!multiple"
  #combobox
  [allowFiltering]="allowFiltering"
  [cssClass]="cssClass"
  [dataSource]="dataSource"
  [enablePersistence]="enablePersistence"
  [enableRtl]="enableRtl"
  [enabled]="enabled"
  [fields]="fields"
  [filterBarPlaceholder]="filterBarPlaceholder"
  [floatLabelType]="floatLabelType"
  [footerTemplate]="footerTemplate"
  [groupTemplate]="groupTemplate"
  [headerTemplate]="headerTemplate"
  [htmlAttributes]="htmlAttributes"
  [ignoreAccent]="ignoreAccent"
  [itemTemplate]="itemTemplate"
  [locale]="locale"
  [placeholder]="placeholder"
  [popupHeight]="popupHeight"
  [popupWidth]="popupWidth"
  [query]="query"
  [readonly]="readonly"
  [showClearButton]="showClearButton"
  [sortOrder]="sortOrder"
  [text]="text"
  [value]="value"
  [valueTemplate]="valueTemplate"
  [width]="width"
  [zIndex]="zIndex"
  (actionBegin)="onActionBegin($event)"
  (actionComplete)="onActionComplete($event)"
  (actionFailure)="onActionFailure($event)"
  (beforeOpen)="onBeforeOpen($event)"
  (blur)="onBlur($event)"
  (change)="onChange($event)"
  (close)="onClose($event)"
  (created)="onCreated($event)"
  (dataBound)="onDataBound($event)"
  (destroyed)="onDestroyed($event)"
  (filtering)="onFiltering($event)"
  (focus)="onFocus($event)"
  (open)="onOpen($event)"
  (select)="onSelect($event)"></ejs-dropdownlist>
