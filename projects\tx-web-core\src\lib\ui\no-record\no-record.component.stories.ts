import { componentWrapperDecorator, type Meta, type StoryObj } from '@storybook/angular';
import { NoRecordComponent } from './no-record.component';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';

const argTypes = {
  noRecordText: {
    description: 'text affiché par la composant',
    table: {
      type: {
        summary: 'description détaillée',
        detail: 'description plus complète de ce à quoi sert l input',
      },
    },
  },
  icon: {
    description: 'icone affichée par la composant',
  },
  displayAction: {
    description: "permet l affichage ou non d'un bouton",
  },
  clickAction: {
    description: 'output event',
  },
};
const meta: Meta<NoRecordComponent> = {
  component: NoRecordComponent,
  title: 'NoRecordComponent',
  parameters: {
    docs: {
      source: {
        state: 'open',
      },
    },
  },
  argTypes: argTypes,
};
export default meta;
type Story = StoryObj<NoRecordComponent>;

export const Primary: Story = {
  args: {
    noRecordText: _('txWebCore.generic.noRecordToDisplay'),
    icon: 'empty-set',
    displayAction: false,
  },
};

export const Secondary: Story = {
  decorators: [
    componentWrapperDecorator((story) => `<div class="blue-theme-dark" >  ${story} </div>`),
  ],
  args: {
    noRecordText: 'txWebCore.generic.noRecordToDisplay',
    icon: 'empty-set',
    displayAction: false,
  },
};
