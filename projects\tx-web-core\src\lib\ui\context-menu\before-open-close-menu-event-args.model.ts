import { BaseEventArgs } from './base-event-args.model';
import { TxContextMenuItem } from './tx-context-menu-item.model';

export interface BeforeOpenCloseMenuEventArgs extends BaseEventArgs {
  /**
   * The target element associated with the context menu.
   */
  element: HTMLElement;

  /**
   * An array of context menu items.
   */
  items: TxContextMenuItem[];

  /**
   * The event object related to the menu interaction.
   */
  event: Event;

  /**
   * Optional property to specify the top position of the menu.
   */
  top?: number;

  /**
   * Optional property to specify the left position of the menu.
   */
  left?: number;
}
