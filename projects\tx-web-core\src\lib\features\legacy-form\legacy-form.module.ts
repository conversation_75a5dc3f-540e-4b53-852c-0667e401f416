import { TxLofComboComponent } from './components/object-fields/link-object-field/components/lof-combo/lof-combo.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TxComboboxesModule } from './comboboxes/comboboxes.module';
import { FontAwesomeModule, FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { fal } from '@fortawesome/pro-light-svg-icons';

// angular material
import { MatStepperModule } from '@angular/material/stepper';
import { MatTabsModule } from '@angular/material/tabs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressBarModule } from '@angular/material/progress-bar';

// syncfusion
import { DatePickerModule, DateTimePickerModule } from '@syncfusion/ej2-angular-calendars';
import { ListBoxModule } from '@syncfusion/ej2-angular-dropdowns';
import { CheckBoxModule, ChipListModule, RadioButtonModule } from '@syncfusion/ej2-angular-buttons';
import { TabModule, TreeViewAllModule } from '@syncfusion/ej2-angular-navigations';
import { DropDownTreeModule } from '@syncfusion/ej2-angular-dropdowns';

// object form
import { LegacyTxObjectFormComponent } from './components/object-form/object-form.component';

// stepper
import { LegacyTxObjectFormStepperComponent } from './components/object-form-stepper/object-form-stepper.component';

// fields
import { LegacyTxObjectTabFieldComponent } from './components/object-tab-field/object-tab-field.component';
import { LegacyTxObjectGroupFieldComponent } from './components/object-group-field/object-group-field.component';
import { TxContainerFieldComponent } from './components/container-field/container-field.component';
import { TxBooleanObjectFieldComponent } from './components/object-fields/boolean-object-field/boolean-object-field.component';
import { TxDateObjectFieldComponent } from './components/object-fields/date-object-field/date-object-field.component';
import { TxLinkObjectFieldComponent } from './components/object-fields/link-object-field/link-object-field.component';
import { TxLongTextObjectFieldComponent } from './components/object-fields/long-text-object-field/long-text-object-field.component';
import { TxMailObjectFieldComponent } from './components/object-fields/mail-object-field/mail-object-field.component';
import { TxShortStringObjectFieldComponent } from './components/object-fields/short-string-object-field/short-string-object-field.component';
import { TxTableObjectFieldComponent } from './components/object-fields/table-object-field/table-object-field.component';
import { TxInputObjectFieldComponent } from './components/object-fields/_system/input-object-field/input-object-field.component';
import { TxObjectFieldComponent } from './components/object-fields/_system/object-field/object-field.component';
import { TxElementSelectionBaseFieldComponent } from './components/object-fields/elements-selection/_element-selection-base-field/element-selection-base-field.component';
import { TxElementsSelectionAltFieldComponent } from './components/object-fields/elements-selection/elements-selection-alt-field/elements-selection-alt-field.component';
import { TxElementsSelectionComboFieldComponent } from './components/object-fields/elements-selection/elements-selection-combo-field/elements-selection-combo-field.component';
import { TxElementsSelectionFieldComponent } from './components/object-fields/elements-selection/elements-selection-field/elements-selection-field.component';
import { TxEscfFilteredElementsComponent } from './components/object-fields/elements-selection/elements-selection-combo-field/sub-components/escf-filtered-elements/escf-filtered-elements.component';
import { TxEscfPopupComponent } from './components/object-fields/elements-selection/elements-selection-combo-field/sub-components/escf-popup/escf-popup.component';
import { LegacyTxEscfTreeComponent } from './components/object-fields/elements-selection/elements-selection-combo-field/sub-components/escf-tree/escf-tree.component';
import { TxPointObjectFieldComponent } from './components/object-fields/point-object-field/point-object-field.component';
import { TxTextInputObjectFieldComponent } from './components/object-fields/text-input-object-field/text-input-object-field.component';
import { TxUrlObjectFieldComponent } from './components/object-fields/url-object-field/url-object-field.component';
import { TxObjectFormDisplayerComponent } from './components/object-form-displayer/object-form-displayer.component';
import { TxFileObjectFieldComponent } from './components/object-fields/file-object-field/file-object-field.component';
import { LegacyTxGenericFieldsModule } from './generic-fields.module';
import { TxExtendedChipFieldComponent } from './components/object-fields/extended-chip-field/extended-chip-field.component';
import { TxLofMatrixComponent } from './components/object-fields/link-object-field/components/lof-matrix/lof-matrix.component';
import { GridModule } from '@syncfusion/ej2-angular-grids';
import { TxRightPaneModule } from './right-pane/right-pane.module';
import { TxTreesModule } from './trees/trees.module';
import { MultiSwitchCaseModule } from './utilities/multi-switch-case.module';
import { fas } from '@fortawesome/pro-solid-svg-icons';
@NgModule({
  imports: [
    CommonModule,
    CheckBoxModule,
    RadioButtonModule,
    ChipListModule,
    FontAwesomeModule,
    FormsModule,
    ReactiveFormsModule,
    TxComboboxesModule,
    LegacyTxGenericFieldsModule,
    // angular material
    MatStepperModule,
    MatTabsModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSlideToggleModule,
    MatButtonToggleModule,
    MatChipsModule,
    MatButtonModule,
    MatSelectModule,
    MatDividerModule,
    MatTooltipModule,
    MatProgressBarModule,

    // syncfusion
    DatePickerModule,
    DateTimePickerModule,
    ListBoxModule,
    DropDownTreeModule,
    TabModule,
    TreeViewAllModule,
    TxRightPaneModule,
    GridModule,
    MultiSwitchCaseModule,
    TxTreesModule,
  ],
  declarations: [
    // form object
    LegacyTxObjectFormComponent,
    // // stepper
    LegacyTxObjectFormStepperComponent,
    // // fields
    LegacyTxObjectTabFieldComponent,
    LegacyTxObjectGroupFieldComponent,
    TxContainerFieldComponent,
    TxBooleanObjectFieldComponent,
    TxDateObjectFieldComponent,
    TxLinkObjectFieldComponent,
    TxLofComboComponent,
    TxLofMatrixComponent,
    TxFileObjectFieldComponent,
    TxLongTextObjectFieldComponent,
    TxMailObjectFieldComponent,
    TxShortStringObjectFieldComponent,
    TxTableObjectFieldComponent,
    TxInputObjectFieldComponent,
    TxObjectFieldComponent,
    TxElementSelectionBaseFieldComponent,
    TxElementsSelectionAltFieldComponent,
    TxElementsSelectionComboFieldComponent,
    TxElementsSelectionFieldComponent,
    TxEscfFilteredElementsComponent,
    TxEscfPopupComponent,
    LegacyTxEscfTreeComponent,
    TxPointObjectFieldComponent,
    TxTextInputObjectFieldComponent,
    TxUrlObjectFieldComponent,
    TxObjectFormDisplayerComponent,
    TxExtendedChipFieldComponent,
  ],
  exports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TxComboboxesModule,
    FontAwesomeModule,
    LegacyTxGenericFieldsModule,
    // angular material
    MatStepperModule,
    MatTabsModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSlideToggleModule,
    MatButtonToggleModule,
    MatChipsModule,
    MatButtonModule,
    MatSelectModule,
    MatDividerModule,
    MatTooltipModule,
    MatProgressBarModule,
    // syncfusion
    DatePickerModule,
    DateTimePickerModule,
    ListBoxModule,
    CheckBoxModule,
    ChipListModule,
    RadioButtonModule,
    TabModule,
    TreeViewAllModule,
    // form object
    LegacyTxObjectFormComponent,
    // stepper
    LegacyTxObjectFormStepperComponent,
    // fields
    LegacyTxObjectTabFieldComponent,
    LegacyTxObjectGroupFieldComponent,
    TxContainerFieldComponent,
    TxBooleanObjectFieldComponent,
    TxDateObjectFieldComponent,
    TxLinkObjectFieldComponent,
    TxLofComboComponent,
    TxLofMatrixComponent,
    TxFileObjectFieldComponent,
    TxLongTextObjectFieldComponent,
    TxMailObjectFieldComponent,
    TxShortStringObjectFieldComponent,
    TxTableObjectFieldComponent,
    TxInputObjectFieldComponent,
    TxObjectFieldComponent,
    TxElementSelectionBaseFieldComponent,
    TxElementsSelectionAltFieldComponent,
    TxElementsSelectionComboFieldComponent,
    TxElementsSelectionFieldComponent,
    TxEscfFilteredElementsComponent,
    TxEscfPopupComponent,
    LegacyTxEscfTreeComponent,
    TxPointObjectFieldComponent,
    TxTextInputObjectFieldComponent,
    TxUrlObjectFieldComponent,
    TxObjectFormDisplayerComponent,
    TxRightPaneModule,
  ],
})
export class LegacyTxFormModule {
  constructor(library: FaIconLibrary) {
    library.addIconPacks(fas);
    library.addIconPacks(fal);
  }
}
