{"$schema": "../../node_modules/ng-packagr/ng-package.schema.json", "dest": "../../dist/tx-web-core", "lib": {"entryFile": "src/public-api.ts"}, "assets": [{"input": "src/lib/", "glob": "**/*.theme.scss", "output": "styles"}, {"input": "src/lib/styles", "glob": "**/*.scss", "output": "styles"}, {"input": "src/assets/i18n", "glob": "**/*.json", "output": "i18n"}, {"input": "src/assets/form", "glob": "**/*", "output": "form"}, {"input": "src/assets/img", "glob": "**/*", "output": "img"}, {"input": "src/assets/themes", "glob": "**/*.scss", "output": "themes"}]}