import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxObjectFormDisplayerComponent } from './object-form-displayer.component';
import { LegacyTxObjectFormStepperComponent } from '../object-form-stepper/object-form-stepper.component';
import { MockComponent } from 'ng-mocks';

describe('TxObjectFormDisplayerComponent', () => {
  let component: TxObjectFormDisplayerComponent;
  let fixture: ComponentFixture<TxObjectFormDisplayerComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        TxObjectFormDisplayerComponent,
        MockComponent(LegacyTxObjectFormStepperComponent),
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxObjectFormDisplayerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
