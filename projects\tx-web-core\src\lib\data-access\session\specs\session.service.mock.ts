import { Observable, of } from 'rxjs';
import { ConnectedUser } from '../user.models';
import { AdminRights } from '../admin-rights.model';
import { Lang } from '../lang.model';

export class MockSessionService {
  currentLang: Lang = {
    id: 1,
    name: 'English',
    code: 'en',
    languageUsedCode: 'en',
  };
  preferences$ = of({});
  retrieveToken(): Observable<string> {
    return of('1234');
  }
  isFolderStorageType(): boolean {
    return true;
  }
  getLoadingState(): Observable<boolean> {
    return of(true);
  }
  getStrongPwdPolicy() {
    return false;
  }
  getDateFormatPreference() {
    return 'dd/MM/yyyy';
  }
  getDateAndTimeFormatPreference() {
    return 'dd/MM/yyyy HH:mm';
  }
  getDelegateAuthentication() {
    return of(false);
  }
  getConnectedUser(): Observable<ConnectedUser> {
    return of({
      name: 'string',
      lastConnectionDate: new Date(),
      adminRights: [
        AdminRights.CanAdministrateRights,
        AdminRights.CanAdministrateStructure,
        AdminRights.CanDoDataMining,
        AdminRights.CanDoMassImportation,
        AdminRights.IsAdmin,
        AdminRights.CoreModelsExport,
        AdminRights.CanExportAndExtract,
        AdminRights.CanExecuteMCSAndChoiceguide,
      ],
      isLoaded: true,
    });
  }
  getVersionsInformation() {
    return of();
  }
  getSessionTimeout(): Observable<number> {
    return of(2);
  }
  hasRightsOn(mandatoryRights: AdminRights | AdminRights[], userRights: AdminRights[]) {
    return true;
  }
  getSpecificIconsZip(): Observable<Blob> {
    const mockZipBlob = new File(['301.svg', '302.png'], '302.png', {
      type: 'application/zip',
    });
    return of(mockZipBlob);
  }
}
