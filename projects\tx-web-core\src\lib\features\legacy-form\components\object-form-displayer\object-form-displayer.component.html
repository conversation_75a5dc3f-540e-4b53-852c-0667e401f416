<ng-container *ngIf="inRightPane; then inPane; else inContent"></ng-container>

<ng-template #inPane>
  <tx-right-pane #rightPane [templateContent]="inContent" [visible]="true"></tx-right-pane>
</ng-template>

<ng-template #inContent>
  <tx-object-form-stepper
    #objectFormStepper
    [idObject]="idObject"
    [idObjectType]="idObjectType"
    [attributesIds]="attributesIds"
    [attributesTags]="attributesTags"
    [formSettings]="formSettings"
    [inRightPane]="inRightPane"
    [showBarAndButton]="showBarAndButton"
    [editionMode]="editionMode"
    [showBarAndButton]="showBarAndButton"
    [indexTabToFocusFirst]="indexTabToFocusFirst"></tx-object-form-stepper>
</ng-template>
