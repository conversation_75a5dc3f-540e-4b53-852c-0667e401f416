import { Component, Input, OnInit, ViewChild, OnChanges, SimpleChanges } from '@angular/core';
import { MatStepper } from '@angular/material/stepper';
import { LegacyTxAttribute } from '../../services/structure/models/attribute';
import { TxEditionMode } from '../../models/formConfiguration/businessClass/form-enum';
import { TxFormSettings } from '../../models/formConfiguration/businessClass/form-settings';
import { TxObjectConfiguration } from '../../models/object-configuration';
import { TxStep } from '../../models/step.model';
import { LegacyTxFormsService } from '../../services/forms.service';
import { LegacyTxObjectFormComponent } from '../object-form/object-form.component';

@Component({
  selector: 'tx-object-form-stepper',
  templateUrl: './object-form-stepper.component.html',
  styleUrls: ['./object-form-stepper.component.scss'],
})
export class LegacyTxObjectFormStepperComponent implements OnInit, OnChanges {
  @Input() isLinear = true;
  @Input() steps!: TxStep[];
  @Input() attributesIds!: number[];
  @Input() attributesTags!: string[];
  @Input() attributes: LegacyTxAttribute[] = [];
  @Input() idObject!: number;
  @Input() idObjectType!: number;
  @Input() editionMode = TxEditionMode.read;
  @Input() tag = '';
  @Input() inRightPane = false;
  @Input() formSettings = new TxFormSettings();
  @Input() config!: TxObjectConfiguration;
  @Input() showBarAndButton: boolean = true;
  @Input() indexTabToFocusFirst = 0;

  @ViewChild('stepper') stepper!: MatStepper;
  @ViewChild('formObject') formObject!: LegacyTxObjectFormComponent;

  isLoaderActive = false;
  mandatoriesIdsAtt: number[] = [];

  constructor(public formsService: LegacyTxFormsService) {}

  ngOnInit() {
    this.loadConfig();
  }

  loadConfig() {
    this.formsService
      .loadConfig(
        this.idObject,
        this.tag,
        new Map(),
        this.formSettings,
        this.editionMode,
        this.idObjectType,
        this.attributesTags && this.attributesTags.length ? this.attributesTags : this.attributesIds
      )
      .subscribe((config) => {
        this.config = config;
        this.steps = config.steps;
        this.mandatoriesIdsAtt = this.getListMandatoriesIdsAtt(this.formSettings);
      });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.idObject && !changes.idObject.firstChange) {
      this.idObject = changes.idObject.currentValue;
      this.loadConfig();
    } else if (changes.attributesTags && !changes.attributesTags.firstChange) {
      this.attributesTags = changes.attributesTags.currentValue;
      this.loadConfig();
    }
  }

  isBackVisible() {
    if (this.stepper) {
      return this.stepper.selectedIndex > 0;
    }
  }

  isNextVisible() {
    if (this.stepper) {
      return this.stepper.selectedIndex < this.steps.length - 1;
    }
  }

  getListMandatoriesIdsAtt(formSettings: TxFormSettings): number[] {
    let mandatoriesIdsAtt: number[] = [];
    if (formSettings.mandatoriesIdsAtt) {
      mandatoriesIdsAtt = formSettings.mandatoriesIdsAtt;
    }
    return mandatoriesIdsAtt;
  }

  onDataLoaded(event: boolean) {
    // to fix the ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      this.isLoaderActive = event;
    });
  }
}
