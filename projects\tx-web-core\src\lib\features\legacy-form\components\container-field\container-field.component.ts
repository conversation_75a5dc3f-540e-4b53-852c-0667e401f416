import { LegacyTxDataType, LegacyTxData } from '../../services/structure/models/data';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  Output,
  QueryList,
  ViewChildren,
} from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { TxVirtualAttributeField } from '../../models/formConfiguration/businessClass/virtual-attribute-field';
import { TxBooleanObjectFieldComponent } from '../object-fields/boolean-object-field/boolean-object-field.component';
import { TxObjectFieldComponent } from '../object-fields/_system/object-field/object-field.component';
import { LegacyTxObjectGroupFieldComponent } from '../object-group-field/object-group-field.component';
import { TxFieldSize } from '../../models/formConfiguration/businessClass/form-enum';

@Component({
  selector: 'tx-container-field',
  templateUrl: './container-field.component.html',
  styleUrls: ['./container-field.component.scss'],
})
export class TxContainerFieldComponent implements AfterViewInit {
  @Input() fields!: TxVirtualAttributeField[];
  @Input() field!: TxVirtualAttributeField;
  @Input() idObject!: number;
  @Input() form!: FormGroup;
  @Input() readMode!: boolean;
  @Output() groupMouseEnterEvent = new EventEmitter();
  @Output() groupMouseLeaveEvent = new EventEmitter();
  @Output() displayPaneEvent = new EventEmitter<any>();

  @ViewChildren('fieldComponent') fieldComponents!: QueryList<TxObjectFieldComponent>;
  @ViewChildren('groupComponent') groupComponents!: QueryList<LegacyTxObjectGroupFieldComponent>;

  fieldType = LegacyTxDataType;

  booleanFields!: TxBooleanObjectFieldComponent[];

  constructor() {}

  ngAfterViewInit(): void {
    this.booleanFields = [];
    for (const field of this.fieldComponents) {
      if (field.field.attribute.dataType === LegacyTxDataType.Boolean) {
        this.booleanFields.push(field as TxBooleanObjectFieldComponent);
      }
    }
  }

  onSubmit(): LegacyTxData[] {
    let data: LegacyTxData[] = [];
    for (const field of this.fieldComponents) {
      const dataToSave = field.getDataToSave();

      if (dataToSave) {
        data.push(dataToSave);
      }
    }
    for (const group of this.groupComponents) {
      data = data.concat(group.onSubmit());
    }

    return data;
  }

  getNbError(): number {
    let res = 0;
    if (this.form) {
      for (const field of this.fields) {
        if (
          this.form.contains(field.id.toString()) &&
          (this.form?.get(field.id.toString()) as FormControl).invalid
        ) {
          res += 1;
        }
      }
      for (const group of this.groupComponents) {
        res += group.getNbError();
      }
    }
    return res;
  }

  reset() {
    for (const fieldComponent of this.fieldComponents) {
      fieldComponent.clear();
    }
  }

  loadData(dataList: LegacyTxData[]) {
    // if(this.readMode){
    //   this.fieldsToDisplay = this.getFieldsToDisplay(dataList);
    //   console.log(this.fieldsToDisplay, this.fieldComponents, this.groupComponents );
    // }
    for (const data of dataList) {
      for (const fieldComponent of this.fieldComponents) {
        if (data.idAttribute === fieldComponent.idAttribute) {
          fieldComponent.setData(data);
          // if (this.readMode){
          //   this.fieldsToDisplay.push(fieldComponent.field);
          // }
          const index = dataList.indexOf(data);
          dataList.slice(index, 1);
        }
      }
    }
    for (const group of this.groupComponents) {
      group.loadData(dataList);
      // if (this.readMode){
      //   this.fieldsToDisplay.push(group.field);
      // }
    }
  }

  getFieldsToDisplay(dataList: LegacyTxData[]): TxVirtualAttributeField[] {
    const res = [];
    for (const field of this.fields) {
      if (field.attribute.dataType === LegacyTxDataType.Group) {
        res.push(field);
      } else {
        for (const data of dataList) {
          if (data.idAttribute === field.attribute.id) {
            res.push(field);
          }
        }
      }
    }
    return res;
  }

  selectedTabChange() {
    for (const group of this.groupComponents) {
      group.selectedTabChange();
    }
    if (this.booleanFields) {
      for (const booleanField of this.booleanFields) {
        booleanField.computeMinWidth();
      }
    }
  }

  isFieldFullWidth(field: TxVirtualAttributeField) {
    if (field.attribute.dataType === LegacyTxDataType.Text) {
      return true;
    }
    if (field.properties.fieldWidth) {
      return field.properties.fieldWidth === TxFieldSize.Full;
    }
    // return field.attribute.id === 96; for test, to remove
    return false;
  }

  getFieldCmpntIndexFromGlobalIndex(globalIndex: number) {
    let resIndex = globalIndex;
    for (let i = 0; i < globalIndex; i++) {
      if (this.fields[i].attribute.dataType === this.fieldType.Group) {
        resIndex--;
      }
    }
    return resIndex;
  }

  haveToBeHidden(index: number) {
    const i = this.getFieldCmpntIndexFromGlobalIndex(index);
    if (this.fieldComponents) {
      const fieldComponent = this.fieldComponents.get(i);
      if (fieldComponent) {
        return fieldComponent.haveToBeHidden();
      }
    } else {
      return false;
    }
  }

  displayRightPane(event: any) {
    this.displayPaneEvent.emit(event);
  }
}
