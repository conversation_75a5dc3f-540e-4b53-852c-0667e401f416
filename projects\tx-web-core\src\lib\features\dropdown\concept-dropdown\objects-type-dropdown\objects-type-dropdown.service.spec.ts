import { TestBed } from '@angular/core/testing';
import { TxObjectsTypeDropdownService } from './objects-type-dropdown.service';
import {
  TxObjectsTypeService,
  TxCommonService,
  MOCK_OBJECTS_TYPE_SERVICE,
  MOCK_COMMON_SERVICE,
} from '../../../../data-access/structure';

describe('Service: ObjectsTypeDropdown', () => {
  let service: TxObjectsTypeDropdownService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        { provide: TxObjectsTypeService, useValue: MOCK_OBJECTS_TYPE_SERVICE },
        { provide: TxCommonService, useValue: MOCK_COMMON_SERVICE },
        TxObjectsTypeDropdownService,
      ],
    });

    service = TestBed.inject(TxObjectsTypeDropdownService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
