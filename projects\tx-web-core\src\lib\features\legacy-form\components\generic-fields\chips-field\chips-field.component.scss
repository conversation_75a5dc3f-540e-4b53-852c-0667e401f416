.mat-form-label {
  pointer-events: auto;
  font-size: 12px;
}

.input-field {
  width: 100%;
}

.input-text {
  font-size: 13px;
}

.data-chip-container {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  float: left;
}

.data-chip {
  display: block !important;
  height: auto;
  padding: 0px !important;
  max-width: 100%;
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 4px !important;
  min-height: 24px;
  margin-left: 0px !important;
  margin-right: 16px !important;
  margin-top: 6px !important;
  font-weight: normal !important;
}

.full-width-chip {
  width: 272px !important;
}

.chip-icon-left {
  margin-right: 16px;
}

.chip-icon-right {
  margin-left: 16px;
}

.action-icon {
  float: right;
  cursor: pointer;
}

.chip-text-container {
  padding: 4px 16px 3px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
}

.chip-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:host ::ng-deep .mat-form-field-label-wrapper {
  width: 270px;
  padding-bottom: 0px !important;
}

:host ::ng-deep .mat-input-element {
  margin: 8px 0px 0px !important;
}

:host ::ng-deep .mat-chip-list-wrapper {
  display: inline-block !important;
  width: calc(100% + 20px);
  margin: 0px !important;
}

:host ::ng-deep .form-field-no-underline {
  // max-height: 60px;
  .mat-form-field-infix {
    padding-bottom: 0px !important;
  }

  .mat-form-field-wrapper {
    padding-bottom: 0px !important;
  }

  .mat-form-field-underline {
    background-color: transparent !important;
    display: none;

    .mat-form-field-ripple {
      background-color: transparent !important;
    }
  }
}

.img-thumbnail-container {
  position: relative;
  width: 272px;
  height: 136px;
  overflow: hidden;
}

.img-thumbnail-without-filename {
  height: 160px !important;
}

.img-thumbnail {
  object-fit: cover;
  width: 272px;
  height: 136px;
  margin: auto;
}

.read-form-field {
  padding-top: 4px;
}

// .remove-chip-icon{
//     width: 17px !important;
//     height: 17px !important;
//     font-size: 17px !important;
// }

.error {
  font-size: 10.5px;
}

.icon-container {
  border-radius: 15px;
  height: 24px;
  width: 24px;
  transition: background-color 0.3s;
  position: absolute;
  top: -1px;
  right: 4px;
}

.chip-icon-right {
  margin-left: 6px;
  vertical-align: middle;
  position: absolute;
  top: 4px;
}

.removable-chip {
  margin-right: 12px;
}
