<app-breadcrumd></app-breadcrumd>
<ng-container *ngIf="coreModelImportVO$ | async as coreModelImportVO">
  <div class="core-models-import">
    <header class="core-models-import__admin-header admin-header">
      <h1 class="admin-header__h1-title h1-title">
        {{ 'admins.wording.coreModelsImport' | translate }}
      </h1>
      <mat-divider class="admin-header__divider"> </mat-divider>
    </header>
    <section class="core-models-import__main">
      <mat-tab-group class="core-models-import__tab-group" mat-stretch-tabs="false">
        <mat-tab>
          <ng-template mat-tab-label>
            {{ 'admins.coreModelsImport.import' | translate }}
          </ng-template>
          <ng-template matTabContent>
            <div class="core-models-import__import-tab content-tab">
              <tx-file-information
                class="core-models-import__file-information"
                (selectedFiles)="validateArchive($event)"
                (deleteFile)="cancelArchive()"
                (filterOnFileVersions)="filterOnNotImportedVersions($event)"
                [fileHistory]="coreModelImportVO.archiveHistory"
                [isLoading]="coreModelImportVO.fileIsLoading"
                [files]="coreModelImportVO.file"
                [fileAction]="fileAction"
                [config]="fileConfig"
                [showVersionsFiltered]="coreModelImportVO.showOnlyNotImportedVersions">
                <ng-template #otherInfoRef>
                  <ul
                    class="core-models-import__archive-details"
                    *ngIf="coreModelImportVO.archiveInfo as archiveInfo">
                    <li
                      class="core-models-import__archive-details-items"
                      *ngFor="let info of archiveInfo">
                      <fa-icon
                        size="lg"
                        class="core-models-import__archive-details-items--icon"
                        [icon]="['fal', info.icon]">
                      </fa-icon>
                      <span class="core-models-import__archive-details-items--label"
                        >{{ info.label | translate }}:
                      </span>
                      <span class="core-models-import__archive-details-items--info">
                        {{ info.info }}</span
                      >
                    </li>
                  </ul>
                </ng-template>
              </tx-file-information>
              <tx-check
                class="core-models-import__check"
                [config]="importContentsConfig"
                (strokedBtnIsClicked)="testImport()"
                (flatBtnIsClicked)="import()"
                [status]="coreModelImportVO.status"
                [isLoading]="coreModelImportVO.checkCmpIsLoading"
                [flatBtnIsDisabled]="importIsDisabled"
                [strokedBtnIsDisabled]="testIsDisabled">
                <ng-template #secondActionFailure>
                  <app-tested-core-model-concepts
                    [flatConcepts]="coreModelImportVO.flatConcepts"
                    [nbErrors]="coreModelImportVO.coreModelConceptsNbErrors"
                    [showOnlyErrors]="coreModelImportVO.showOnlyErrors"
                    (filterOnConceptsInConflicts)="
                      filterOnConceptsInConflicts($event)
                    "></app-tested-core-model-concepts>
                </ng-template>
                <ng-template #secondActionSuccess>
                  <app-tested-core-model-concepts
                    [flatConcepts]="coreModelImportVO.flatConcepts"
                    [nbErrors]="coreModelImportVO.coreModelConceptsNbErrors"
                    [showOnlyErrors]="coreModelImportVO.showOnlyErrors"
                    (filterOnConceptsInConflicts)="
                      filterOnConceptsInConflicts($event)
                    "></app-tested-core-model-concepts>
                </ng-template>
                <ng-template #mainActionSuccess>
                  <app-imported-core-model-concepts
                    [flatConcepts]="
                      coreModelImportVO.flatConcepts
                    "></app-imported-core-model-concepts>
                </ng-template>
                <ng-template #mainActionFailure>
                  <app-tested-core-model-concepts
                    [flatConcepts]="coreModelImportVO.flatConcepts"
                    [nbErrors]="coreModelImportVO.coreModelConceptsNbErrors"
                    [showOnlyErrors]="coreModelImportVO.showOnlyErrors"
                    (filterOnConceptsInConflicts)="
                      filterOnConceptsInConflicts($event)
                    "></app-tested-core-model-concepts>
                </ng-template>
              </tx-check>
            </div>
          </ng-template>
        </mat-tab>
        <mat-tab [disabled]="coreModelImportVO.importHistory.length === 0">
          <ng-template mat-tab-label>
            {{ 'generic.history' | translate }}
          </ng-template>
          <ng-template matTabContent>
            <div class="core-models-import__history-tab content-tab">
              <app-core-models-import-history
                class="core-models-import__history-list"
                [history]="coreModelImportVO.importHistory"
                (filterOnLatestImports)="filterOnLatestImports($event)"
                [showOnlyLatestImports]="coreModelImportVO.showOnlyLatestImports">
              </app-core-models-import-history>
            </div>
          </ng-template>
        </mat-tab>
      </mat-tab-group>
    </section>
  </div>
</ng-container>
