@import 'ui/no-record/no-record.theme.scss';
@import 'features/dropdown/dropdown-tree/dropdown-tree.theme.scss';
@import 'features/redirection/themes/view.theme.scss';
@import 'features/grid/grid.theme.scss';
@import 'features/tree-grid/tree-grid.theme.scss';
@import 'features/trees/tree-view/tree-view.theme.scss';
@import 'features/sidebar/_sidebar.theme.scss';
@import 'features/trees/object-type-tree-view.theme.scss';

@mixin twc-components-theme($theme) {
  --mdc-text-button-label-text-size: 0.833rem;
  @include dropdown-tree-theme($theme);
  @include no-record-theme($theme);
  @include view-theme($theme);
  @include grid-theme($theme);
  @include tree-grid-theme($theme);
  @include tree-view-theme($theme);
  @include object-type-tree-view-theme($theme);
  @include sidebar-theme($theme);
}
tx-attributes-tree-grid,
tx-object-tree-grid,
tx-object-types-tree-grid {
  .mat-expansion-panel-body {
    padding: 0px !important;
    margin: -1px;
  }
  .e-grid .e-row .e-input-group {
    height: 16px;
  }
  .e-grid .e-spinner-pane {
    display: none;
  }

  .e-emptyrow {
    display: none;
  }
  .e-updatedtd:before {
    display: none;
  }
  .row-opacity {
    opacity: 0.6;
  }

  .e-grid .e-rowcell {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    height: 26px !important;
    border: none !important;

    .e-treecolumn-container:has(.e-treegridexpand),
    .e-treecolumn-container:has(.e-treegridcollapse) {
      height: 100%;

      .e-treegridcollapse,
      .e-treegridexpand {
        margin-left: 10px;
      }

      .e-icons.e-none {
        width: 25px !important;
      }

      .e-treegridexpand,
      .e-treegridexpand ~ .e-icons.e-none,
      .e-treegridcollapse,
      .e-treegridcollapse ~ .e-icons.e-none {
        width: 10px !important;
      }

      .e-treegridexpand {
        margin-bottom: 7px;
      }
    }
  }
  .e-treegrid .e-treegridexpand,
  .e-treegrid .e-treegridcollapse {
    height: auto !important;
  }
}

.w-100 {
  width: 100%;
}
.d-inline {
  display: inline;
}

.mat-mdc-form-field-infix {
  padding-top: 14px;
  padding-bottom: 0px;
  display: flex;
}

.no-label-input {
  display: inline-block;
  width: 280px;
  vertical-align: top;

  @include mat.form-field-density(-5);

  fa-icon {
    margin-right: 16px;
  }

  .input-icon-remove {
    cursor: pointer;
  }
  input.mat-mdc-input-element {
    margin-top: 0px;
  }
  .mat-mdc-form-field-infix {
    padding-top: 0px;
  }
  .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }
}
.not-allowed-cursor {
  cursor: not-allowed;
}

.auto-cursor {
  cursor: auto;
}
