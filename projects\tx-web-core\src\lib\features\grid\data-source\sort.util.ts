import { SortDirection, Sort as MatSortInterface } from '@angular/material/sort';
import { InjectionToken } from '@angular/core';

export interface Sort<T> {
  active: keyof T;
  direction: SortDirection;
}
export type SortRequest<T> = Sort<T> | MatSortInterface | undefined | null;
export const INITIAL_SORT_TOKEN = new InjectionToken<Sort<unknown>>('InitialSortToken');

export function sortData<T>(
  sort: MatSortInterface | Sort<T>,
  data: T[],
  groupBy?: keyof T,
  disableInitialSort?: boolean
): T[] {
  if (disableInitialSort) {
    return data;
  }
  if (groupBy) {
    return sortGroup<T>(sort, data, groupBy);
  } else {
    return data.sort((leftItem, rightItem) => {
      const leftItemActiveSort = leftItem[sort.active as keyof T];
      const rightItemActiveSort = rightItem[sort?.active as keyof T];
      let result;
      if (typeof leftItemActiveSort === 'string') {
        result = leftItemActiveSort
          .toLowerCase()
          .localeCompare(((rightItemActiveSort as any) ?? '').toString().toLowerCase());
      } else {
        result = (leftItemActiveSort as any) - (rightItemActiveSort as any);
      }
      const factor = sort.direction === 'asc' || sort.direction === '' ? 1 : -1;
      return result * factor;
    });
  }
}

//group sorting
function sortGroup<T>(sort: MatSortInterface | Sort<T>, data: T[], groupBy: keyof T) {
  return data.sort((a: any, b: any) => {
    if (a[groupBy] !== b[groupBy]) {
      return a[groupBy].localeCompare(b[groupBy]);
    }
    if (a.isTxGroup && !b.isTxGroup) return -1;
    if (!a.isTxGroup && b.isTxGroup) return 1;
    if (
      typeof a[sort.active as keyof T] === 'number' &&
      typeof b[sort.active as keyof T] === 'number'
    ) {
      return sort.direction === 'asc' || sort.direction === ''
        ? a[sort.active as keyof T] - b[sort.active as keyof T]
        : b[sort.active as keyof T] - a[sort.active as keyof T];
    }
    if (a[sort.active as keyof T] && b[sort.active as keyof T]) {
      return sort.direction === 'asc' || sort.direction === ''
        ? a[sort.active as keyof T].localeCompare(b[sort.active as keyof T])
        : b[sort.active as keyof T].localeCompare(a[sort.active as keyof T]);
    }
    return sortbyUndefine(sort, a, b);
  });
}

function sortbyUndefine<T>(sort: MatSortInterface | Sort<T>, compare: T, reference: T) {
  if (sort.direction === 'asc') {
    if (compare[sort.active as keyof T] && !reference[sort.active as keyof T]) return -1;
    if (!compare[sort.active as keyof T] && reference[sort.active as keyof T]) return 1;
  } else {
    if (compare[sort.active as keyof T] && !reference[sort.active as keyof T]) return 1;
    if (!compare[sort.active as keyof T] && reference[sort.active as keyof T]) return -1;
  }
  return 0;
}
