import { HttpRequest } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { TxObject } from '../../../business-models';
import { MOCK_TX_CONFIG_SERVICE, TxConfigService } from '../../config';
import { TxObjectsService } from '../objects.service';
import { MockProvider } from 'ng-mocks';
import { MOCK_OBJECT_TYPE_ICON_SERVICE, TxObjectTypeIconService } from '../../session';
const objectRest = {
  idObjectType: 1,
  searchName: '',
  isParent: false,
  isFolder: true,
  idOwnerObject: 1,
  creationDate: new Date(),
  tags: [],
};

describe('ObjectsService', () => {
  const apiUrl = 'https://localhost:44336/';
  let service: TxObjectsService;
  let http: HttpTestingController;
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        MockProvider(TxConfigService, MOCK_TX_CONFIG_SERVICE, 'useValue'),
        MockProvider(TxObjectTypeIconService, MOCK_OBJECT_TYPE_ICON_SERVICE, 'useValue'),
      ],
    });
    service = TestBed.inject(TxObjectsService);
    http = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    http.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Get Objects from Object Type', () => {
    let objectTests: TxObject[];
    let idObjectType: number;
    beforeEach(() => {
      objectTests = [
        {
          order: 1,
          id: 1,
          name: 'Folder',
          ...objectRest,
        },
        {
          order: 2,
          id: 2,
          name: 'Child 1',
          ...objectRest,
        },
        {
          order: 3,
          id: 3,
          name: 'Chil 1.1',
          ...objectRest,
        },
      ];
      idObjectType = 1;
    });

    it('should get objects to the root from Object Type (ID=1)', () => {
      let response: TxObject[] = [];
      service
        .getObjectsFromParent(idObjectType, 0)
        .subscribe((res: { cachedIds: number[]; objects: TxObject[] }) => {
          response = res.objects;
        });

      http
        .expectOne((req: HttpRequest<any>) => true)
        .flush({
          cachedIds: [],
          objects: objectTests,
        });
      expect(response).toBe(objectTests);
    });

    it('should considerer http params idObjectType', () => {
      service.getObjectsFromParent(33, 1).subscribe();

      const req = http.expectOne((r: HttpRequest<any>) => true);
      expect(req.request.params.get('idObjectType')).toBe('33');
    });

    it('should considerer http params idParent', () => {
      service.getObjectsFromParent(1, 22).subscribe();

      const req = http.expectOne((r: HttpRequest<any>) => true);
      expect(req.request.params.get('idParent')).toBe('22');
    });
  });

  describe('Search Objects', () => {
    let objectTests: TxObject[];
    let idObjectType: number;
    beforeEach(() => {
      objectTests = [
        {
          order: 1,
          id: 1,
          name: 'Test 1',
          ...objectRest,
        },
        {
          order: 2,
          id: 2,
          name: 'Test 2',
          ...objectRest,
        },
        {
          order: 3,
          id: 3,
          name: 'Test 3',
          ...objectRest,
        },
      ];
      idObjectType = 1;
    });

    it('should get objects from search as response', () => {
      let response: TxObject[] = [];
      service.searchObjects('test', idObjectType).subscribe((res: TxObject[]) => {
        response = res;
      });

      http
        .expectOne(`${apiUrl}api/Objects/search?searchString=test&idObjectType=1`)
        .flush(objectTests);
      expect(response).toBe(objectTests);
    });

    it('should considerer http params idObjectType', () => {
      service.searchObjects('test', 1).subscribe();

      const req = http.expectOne((r: HttpRequest<any>) => true);
      expect(req.request.params.get('idObjectType')).toBe('1');
    });

    it('should considerer http params searchString', () => {
      service.searchObjects('test').subscribe();

      const req = http.expectOne((r: HttpRequest<any>) => true);
      expect(req.request.params.get('searchString')).toBe('test');
    });
  });
});
