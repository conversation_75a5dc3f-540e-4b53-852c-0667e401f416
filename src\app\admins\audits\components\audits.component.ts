import { Component, OnInit, ViewChild, ViewEncapsulation, HostListener } from '@angular/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { AuditsService } from 'src/app/admins/audits/services/audits.service';
import {
  Audit,
  DatabaseInformation,
  Section,
  InformationAudit,
} from 'src/app/admins/audits/models/audit';
import { animate, state, style, transition, trigger } from '@angular/animations';
import {
  ToastComponent,
  ToastType,
  ToastService,
  TxConfigService,
  TxGridColumn,
  TxTreeGridComponent,
  TxTreeExpandState,
  TxTreeGridChildren,
  defaultTreeGridDataSource,
} from '@bassetti-group/tx-web-core';
import { SessionService } from 'src/app/core/services/session/session.service';
import { FilesUtils } from 'src/app/core/utils/files';
import { ErrorService } from 'src/app/core/services/errors/error.service';
import { About } from 'src/app/core/about/about';
import { HelpBoxService } from 'src/app/core/services/help-box/help-box.service';
interface AuditsTreeGridColumn {
  name: string;
  id: string;
  explanation: string;
  status: string;
  result: string;
}
@Component({
  selector: 'app-audits',
  templateUrl: './audits.component.html',
  styleUrls: ['./audits.component.scss'],
  animations: [
    trigger('transitShowHide', [
      state(
        'true',
        style({
          height: '*',
        })
      ),
      state(
        'false',
        style({
          height: '0px',
        })
      ),
      transition('true => false', animate('0.3s')),
      transition('false => true', animate('0.3s')),
    ]),
  ],
  encapsulation: ViewEncapsulation.None,
  providers: defaultTreeGridDataSource(),
})
export class AuditsComponent implements OnInit {
  @ViewChild('treegrid') treegrid?: TxTreeGridComponent<Audit & TxTreeGridChildren<Audit>>;

  readonly extensions = ['exml'];

  public numberAuditPoints = 0;
  public version = '';
  public revision = 0;
  public sections: Section[] = [];
  public isDropzoneHovered = false;
  public isFileUpload = false;
  public fileNameUpload: string | undefined;
  public isExecuteMode = false;
  public isExecutingAtStart = false;
  public isLoadingBar = false;
  public isExplanationDisplayed = false;
  public progress = 0;
  public isLangLoading = true;
  public isCurrentlyExporting = false;
  public defaultFileTranslation = _('admins.audit.defaultFile');
  txColumns: TxGridColumn<AuditsTreeGridColumn>[] = [
    { headerText: _('admins.columns.name'), field: 'name', width: '30%', visible: true },
    { headerText: _('admins.columns.id'), field: 'id', width: '10%', visible: false },
    {
      headerText: _('admins.columns.explanation'),
      field: 'explanation',
      visible: !this.isExecutingAtStart,
    },
    {
      headerText: _('admins.columns.status'),
      field: 'status',
      width: '120px',
      textAlign: 'center',
      visible: this.isExecutingAtStart,
    },
    { headerText: _('admins.columns.message'), field: 'result', visible: this.isExecutingAtStart },
  ];

  constructor(
    private readonly sessionService: SessionService,
    private readonly auditsService: AuditsService,
    private readonly toastService: ToastService,
    private readonly errorService: ErrorService,
    private readonly helpboxService: HelpBoxService,
    private readonly configService: TxConfigService
  ) {}

  @HostListener('window:beforeunload', ['$event']) unloadHandler(event: Event) {
    this.auditsService.deleteAuditFiles().subscribe();
  }

  ngOnInit() {
    this.isExecuteMode = this.auditsService.isAlreadyExecuted();
    this.isExecutingAtStart = this.isExecuteMode;
    this.auditsService.getSections().subscribe((sections) => {
      this.sections = sections.slice();
    });

    this.auditsService.getInfomation().subscribe((informations: DatabaseInformation) => {
      this.revision = informations.revision;
    });
    this.auditsService
      .getInformationAuditPoint()
      .subscribe((informationAudit: InformationAudit) => {
        this.numberAuditPoints = informationAudit.numberAuditPoints;
        this.isFileUpload = informationAudit.isFileUploaded;
        this.fileNameUpload = informationAudit.fileName;
      });
    this.sessionService.getVersionsInformation().subscribe((about: About) => {
      this.version = `${about.teexma.version}.${about.teexma.revision}`;
    });
    this.auditsService.getLoadingBar().subscribe((stats) => {
      this.isLoadingBar = stats;
    });
    this.sessionService.getLoadingState().subscribe((loadingState) => {
      this.isLangLoading = !loadingState;
    });
    this.helpboxService.getHelpboxState().subscribe((value) => {
      if (value === false) {
        this.isExplanationDisplayed = false;
      }
    });
    this.helpboxService.getMultipleStates().subscribe(([hsState, mhsState, exp]) => {
      if (!hsState && !mhsState) {
        this.isExplanationDisplayed = false;
      } else if (exp.id === 'expAudit') {
        this.isExplanationDisplayed = true;
      } else {
        this.isExplanationDisplayed = false;
      }
    });
    this.errorService.registerUnhandledRequestURL(
      this.configService.getApiUrl() + 'api/TxAudit/queryresult',
      true
    );
  }

  public get txTreeExpandState(): typeof TxTreeExpandState {
    return TxTreeExpandState;
  }
  executeAudit(): void {
    if (this.treegrid) {
      this.treegrid.hideColumns(['explanation']);
      this.treegrid.showColumns(['name', 'status', 'result']);
    }
    this.isExecuteMode = true;
    const step = 100 / this.numberAuditPoints;
    this.auditsService.toastProgress = this.createNotification(
      'loading',
      _('admins.audit.executionInProgress'),
      0,
      0
    );
    this.auditsService.executeQueries().subscribe(() => {
      this.progress = this.progress + step; // progressBar
      if (this.progress > 99.99) {
        this.updateNotification(
          this.auditsService.toastProgress,
          'success',
          _('admins.audit.executionCompleted'),
          this.progress
        );
        this.auditsService.toastProgress = null;
      } else {
        this.updateProgress(this.auditsService.toastProgress, this.progress);
      }
    });
  }

  updateProgress(toast: ToastComponent | null, progress?: number) {
    if (toast !== null) {
      toast.data.progress = progress;
    }
  }

  showAllLine(data: Audit): void {
    data.isShowAllLine = true;
  }

  hideLine(data: Audit): void {
    data.isShowAllLine = false;
  }

  cannotExport(): boolean {
    return this.auditsService.toastProgress !== null || this.isCurrentlyExporting;
  }

  cannotReset(): boolean {
    return !this.isExecuteMode;
  }

  reset(): void {
    this.setResetMode();
  }

  setResetMode() {
    this.auditsService.updateAuditPoints(); // reset results
    if (this.auditsService.toastProgress) {
      this.updateNotification(
        this.auditsService.toastProgress,
        'error',
        _('admins.audit.executionInterrupted')
      );
      this.auditsService.toastProgress = null;
    }
    if (this.treegrid) {
      this.treegrid.hideColumns(['status', 'result']);
      this.treegrid.showColumns(['name', 'explanation']);
    }
    this.isExecuteMode = false;
    this.progress = 0;
  }

  containsFiles(evt: DragEvent) {
    if (evt.dataTransfer?.types.some((type: string) => type === 'Files')) {
      this.isDropzoneHovered = true;
    } else {
      this.isDropzoneHovered = false;
    }
  }

  handleFileInput(event: { isFileUploaded: boolean; file: File }): void {
    this.auditsService.deleteAuditFiles().subscribe();
    this.uploadFileToActivity(event.file);
  }

  uploadFileToActivity(file: File): void {
    this.setResetMode();
    this.auditsService.postFile(file).subscribe(() => {
      this.createNotification('success', _('admins.audit.fileUploaded'), 8000);
    });
  }

  deleteFile(): void {
    this.setResetMode();
    this.auditsService.deleteAuditFiles().subscribe();
    this.auditsService.callTxAudit();
  }

  public hideDropZone() {
    this.isDropzoneHovered = false;
  }

  export(): void {
    this.isCurrentlyExporting = true;
    this.auditsService.exportResults().subscribe(
      (blob) => {
        FilesUtils.downloadBlobFile(blob, 'AuditResult.xlsx');
      },
      (err) => {
        console.error('An error occured while exporting the file: ', err);
      },
      () => (this.isCurrentlyExporting = false)
    );
  }

  createNotification(
    type: string,
    message: string,
    duration: number = 0,
    progress?: number
  ): ToastComponent {
    return this.toastService.show({
      templateContext: { test: { state: type, message } },
      type: type as ToastType,
      interval: duration,
      date: new Date(),
      title: '',
      description: message,
      isPersistent: false,
      isUnread: true,
      progress,
      displayPercent: progress !== undefined && progress >= 0,
    });
  }

  updateNotification(
    toast: ToastComponent | null,
    type: string,
    message: string,
    progress?: number
  ): void {
    if (toast !== null) {
      toast.data.templateContext = { test: { state: type, message } };
      toast.data.type = type === 'loading' ? 'information' : (type as ToastType);
      toast.data.description = message;
      toast.data.progress = progress;
      setTimeout(() => {
        toast.animationState = 'closing';
      }, 3000);
    }
  }

  getExplanation(globalExpId: string, expId: string, active: boolean): void {
    this.helpboxService.setExplanationsFromId(globalExpId, expId, active);
    this.isExplanationDisplayed = true;
  }

  closeHelpbox() {
    this.helpboxService.closeHelpbox();
  }
}
