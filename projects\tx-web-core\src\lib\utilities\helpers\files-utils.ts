import { IconName } from '@fortawesome/fontawesome-svg-core';
import { _LegacyStringUtils } from '../../features/legacy-form/utilities/legacy-string-utils';

export class FilesUtils {
  /**
   * Return the file name extension
   * @param fileName the file name
   * @param withDot return the dot ?
   */
  static extractFileExt(fileName: string, withDot = false): string {
    const regEx: RegExp = /(?:\.([^.]+))?$/;

    if ((regEx.exec(fileName) as RegExpExecArray)[1]) {
      const dot = withDot ? '.' : '';
      return dot + (regEx.exec(fileName) as RegExpExecArray)[1].toLowerCase();
    }

    return '';
  }

  /**
   * Return the file name from a path
   * @param filePath the file path
   */
  static extractFileName(filePath: string) {
    if (!filePath) {
      return '';
    }

    const x = filePath.lastIndexOf('/');
    const y = filePath.lastIndexOf('\\');
    const z = x > y ? x : y;

    if (z >= 0) {
      // Windows-based path
      return filePath.substring(z + 1);
    }

    return filePath; // just the filename
  }

  /**
   * Return the file directory from a path
   * @param filePath the file path
   */
  static extractFileDir(filePath: string): string {
    const fileName = FilesUtils.extractFileName(filePath);

    return _LegacyStringUtils.left(fileName, filePath.length - fileName.length);
  }

  static extractFileNameWithoutExt(fileName: string) {
    const ext = FilesUtils.extractFileExt(fileName);
    fileName = FilesUtils.extractFileName(fileName);

    if (_LegacyStringUtils.isEmpty(ext)) {
      return fileName;
    }

    return _LegacyStringUtils.left(fileName, fileName.lastIndexOf('.' + ext));
  }

  /**
   * Return the icon name according to the extension in the file name.
   * @memberof _FileUtils
   * @param {string} aFileName
   * @returns {string}
   */
  static getFileIcon(aFileName: string): ['fas' | 'fab', IconName] {
    let sExt = FilesUtils.extractFileExt(aFileName);
    let sIcon: ['fas' | 'fab', IconName] = ['fas', 'file'];

    switch (sExt) {
      case 'docx':
      case 'doc':
      case 'rtf':
      case 'dot':
      case 'dotm':
      case 'docm':
        sIcon = ['fas', 'file-word'];
        break;
      case 'xls':
      case 'xlt':
      case 'xlsm':
      case 'xlsx':
        sIcon = ['fas', 'file-spreadsheet'];
        break;
      case 'txt':
        sIcon = ['fas', 'file-alt'];
        break;
      case 'rss':
      case 'atom':
        sIcon = ['fas', 'rss-square'];
        break;
      case 'phps':
        sIcon = ['fab', 'php'];
        break;
      case 'vcard':
        sIcon = ['fas', 'address-card'];
        break;
      case 'exe':
      case 'dmg':
      case 'app':
        sIcon = ['fas', 'window'];
        break;
      case 'pps':
      case 'ppsx':
      case 'ppt':
      case 'pptx':
        sIcon = ['fas', 'file-powerpoint'];
        break;
      case 'pdf':
        sIcon = ['fas', 'file-pdf'];
        break;
      case 'xpi':
        sIcon = ['fas', 'puzzle-piece'];
        break;
      // case "fla":
      // case "swf":
      //     sIcon = "icon_flash.gif";
      //    break;
      case 'zip':
      case 'rar':
      case 'gzip':
      case 'bzip':
      case 'ace':
        sIcon = ['fas', 'file-archive'];
        break;
      case 'ical':
        sIcon = ['fas', 'calendar'];
        break;
      case 'css':
        sIcon = ['fas', 'file-code'];
        break;
      case 'ttf':
        sIcon = ['fas', 'text'];
        break;
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'png':
      case 'bmp':
      case 'svg':
      case 'eps':
        sIcon = ['fas', 'file-image'];
        break;
      case 'mov':
      case 'wmv':
      case 'mp4':
      case 'avi':
      case 'mpg':
        sIcon = ['fas', 'file-video'];
        break;
      case 'mp3':
      case 'wav':
      case 'ogg':
      case 'wma':
      case 'm4a':
        sIcon = ['fas', 'file-audio'];
        break;
    }

    return sIcon;
  }

  static isAFilePicture(fileName: string) {
    let ext = FilesUtils.extractFileExt(fileName);
    return ['png', 'jpg', 'jpeg', 'svg', 'eps'].includes(ext);
  }

  static fileSizeToDisplay(size: number): string {
    const units = [' o', ' Ko', ' Mo', ' Go'];
    let i = 0;
    while (size > 1024) {
      size /= 1024;
      i += 1;
    }
    const sizeToDisplay =
      (size >= 100 ? Math.round(size) : Math.round(size * 10) / 10).toString() + units[i];
    return sizeToDisplay;
  }
}
