import { LegacyTxTreeObjectsService } from './tree-objects.service';
import { Observable } from 'rxjs';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  BeforeOpenCloseMenuEventArgs,
  DrawNodeEventArgs,
  MenuEventArgs,
  NodeCheckEventArgs,
  NodeClickEventArgs,
  NodeExpandEventArgs,
  NodeSelectEventArgs,
} from '@syncfusion/ej2-angular-navigations';
import { TxTreeComponent } from '../tree/tree.component';
import { LegacyTxObject } from '../../services/structure/models/object';
import { LegacyTxObjectTypeService } from '../../services/structure/services/object-type.service';
import { LegacyTxObjectsService } from '../../services/structure/services/objects.service';

export interface TxObjectCheckEventArgs {
  /**
   * Return the name of action like check or un-check.
   */
  checked: boolean;
  /**
   * Return the currently checked txObject as JSON object from data source.
   */
  txObject: LegacyTxObject;
}

@Component({
  selector: 'tx-tree-objects',
  templateUrl: '../tree/tree.component.html',
  styleUrls: ['../tree/tree.component.scss', './tree-objects.component.scss'],
})
export class TxTreeObjectsComponent extends TxTreeComponent implements OnInit {
  @Input() idObjectType!: number;
  @Input() idParentObjectFiltering = 0;
  @Input() idAttribute!: number;
  @Input() recursive = false;
  @Input() folderCheckable = true;
  @Input() includeFolder = true;
  @Input() searchInData = false;
  @Input() searchInLinkedObjects = false;
  @Input() searchInFullObjectType = true;
  @Input() searchWithStrongFilter = false;
  @Input() displayContextMenu = false;
  @Input() txObjects!: LegacyTxObject[];
  @Input() imagePath: any;
  @Input() checkedNodes: number[] = [];

  @Output() txObjectChecked: EventEmitter<TxObjectCheckEventArgs> = new EventEmitter();
  @Output() txObjectSelected: EventEmitter<TxObjectCheckEventArgs> = new EventEmitter();

  nodesToCheck: LegacyTxObject[] = [];

  constructor(
    public objectService: LegacyTxObjectsService,
    public objectTypeService: LegacyTxObjectTypeService,
    public treeObjectsService: LegacyTxTreeObjectsService
  ) {
    super();
  }

  ngOnInit() {
    super.ngOnInit();
    this.fields.parentID = 'idObjectParent';
    this.fields.hasChildren = 'isParent';
  }

  onCreated(event: object) {
    super.onCreated(event);

    if (!this.txObjects && !this.silent) {
      this.loadBranch(this.idParentObjectFiltering);
    }

    if (this.displayContextMenu) {
      this.menuItems = [
        { text: 'Add New Object', id: 'addObject' },
        { text: 'Rename Object', id: 'renameObject' },
        { text: 'Remove Object', id: 'removeObject' },
      ];
    }
  }

  onDrawNode(event: DrawNodeEventArgs) {
    super.onDrawNode(event);
    const node = this.getNode(parseInt(event.nodeData.id as string, 10));

    if ((this.showCheckBox && node && node.isFolder && !this.folderCheckable) || node.noCheckBox) {
      // hide the checkbox
      const el = event.node.querySelector('.e-checkbox-wrapper') as HTMLElement;
      el.style.display = 'none';

      // then remove the margin of the icon
      const img = event.node.querySelector('.e-list-img') as HTMLElement;
      if (img) {
        img.style.margin = '0px';
        (img.style as any)['margin-left'] = '4px';
      }
    }

    if (node && node.showMoreResults) {
      const span = event.node.querySelector('.e-list-text') as HTMLElement;
      span.style.textAlign = 'center';
      span.style.color = 'blue';
      span.style.textTransform = 'underline';
    }
  }

  onNodeExpanding(event: NodeExpandEventArgs) {
    super.onNodeExpanding(event);

    const node = this.getNode(parseInt(event.nodeData.id as string, 10));

    if (!node || node.options.branchLoaded) {
      return false;
    }

    this.loadBranch(node.id, node);
  }

  onNodeExpanded(event: NodeExpandEventArgs) {
    // permit to keep checkbox checked if this parent node is checked when we load dynamically it's children
    super.onNodeExpanded(event);

    const node = this.getNode(event.nodeData.id);
    const ischecked = event.nodeData.isChecked === 'true';
    if (node && node.options.firstCall) {
      delete node.options.firstCall;

      return;
    }
  }

  loadBranch(idParentObjectFiltering?: number, parentNode?: LegacyTxObject) {
    this.objectService
      .listObjects(
        this.idObjectType,
        idParentObjectFiltering,
        this.recursive,
        this.includeFolder,
        true
      )
      .subscribe((txObjects: LegacyTxObject[]) => {
        if (!txObjects.length) {
          return;
        }

        if (parentNode) {
          parentNode.options.branchLoaded = true;
          parentNode.options.firstCall = true;
        }

        this.addTxObjects(txObjects, idParentObjectFiltering);
      });
  }

  onSearchingObjects(value: string, limit?: number): Observable<LegacyTxObject[]> {
    return this.objectService.searchObjects(
      value,
      undefined,
      undefined,
      this.idObjectType,
      true,
      false,
      limit
    );
  }

  filter(txObjects: LegacyTxObject[]) {
    this.reloadNodes(this.treeObjectsService.fillTxObjectsNodes(txObjects, this.imagePath, true));
  }

  cancelFilter() {
    this.reloadPreviousNodes();
  }

  addTxObjects(txObjects: LegacyTxObject[], idParentObjectFiltering: number = 0) {
    this.addNodes(this.treeObjectsService.fillTxObjectsNodes(txObjects), idParentObjectFiltering);
  }

  onNodeClicked(event: NodeClickEventArgs) {
    super.onNodeClicked(event);

    const node = this.getNode(parseInt(event.node.dataset.uid as string, 10));
    if (node.showMoreResults) {
      this.removeNode(node.id);

      let nodesToDisplay: any[];
      if (this.seachedNodes.length > this.searchLimit) {
        nodesToDisplay = this.seachedNodes.slice(0, this.searchLimit);
        this.seachedNodes = this.seachedNodes.slice(this.searchLimit, this.seachedNodes.length);
        nodesToDisplay.push({
          noCheckBox: true,
          id: -666,
          name: `${nodesToDisplay.length + this.nodes.length} on ${
            this.seachedNodes.length + nodesToDisplay.length + this.nodes.length
          } are displayed, click to show more...`,
          showMoreResults: true,
        });
      } else {
        nodesToDisplay = this.seachedNodes;
        this.seachedNodes = [];
      }

      this.addTxObjects(nodesToDisplay);
    }
  }

  // context menu events
  onContextMenuClick(args: MenuEventArgs) {
    const idClickedOption = args.item.id;
    const targetNodeId: string = this.treeview.selectedNodes[0];

    const node = this.getNode(parseInt(targetNodeId, 10));

    switch (
      idClickedOption
      // case 'addObject':
      //   const txNewObject = TxObject.new(this.idObjectType, 'new object', this.txObjectTypeService.findObjectTypeIcon(this.idObjectType) + '.png');
      //   this.objectService.addObject(txNewObject);
      //   this.addTxObjects([txNewObject], parseInt(targetNodeId));
      //   break;
      // case 'renameObject':
      //   this.editNode(targetNodeId);
      //   break;
      // case 'removeObject':
      //   this.removeNode(node.id);
      //   break;
    ) {
    }
  }

  onBeforeContextMenuOpen(args: BeforeOpenCloseMenuEventArgs) {}

  onNodeChecked(args: NodeCheckEventArgs) {
    super.onNodeChecked(args);

    const txObject = this.getNode(args.data[0].id);

    this.txObjectChecked.emit({
      checked: args.action === 'check',
      txObject,
    });
  }

  onNodeSelected(args: NodeSelectEventArgs) {
    super.onNodeSelected(args);

    const txObject = this.getNode(args.nodeData.id);

    this.txObjectSelected.emit({
      checked: args.action === 'select',
      txObject,
    });
  }

  isNodeCheckable(node: LegacyTxObject): boolean {
    let ok = super.isNodeCheckable(node);

    if (!this.folderCheckable && ok) {
      ok = !node.isFolder;
    }

    return ok;
  }
}
