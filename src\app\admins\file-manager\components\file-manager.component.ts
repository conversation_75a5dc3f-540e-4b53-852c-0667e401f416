import { animate, style, transition, trigger } from '@angular/animations';
import { Component, ElementRef, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { AdminRights, DataBaseRights, TxDialogService } from '@bassetti-group/tx-web-core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable, Subscription, combineLatest, delay, take, tap } from 'rxjs';
import { ResourcesService } from 'src/app/admins/file-manager/services/resources.service';
import { RightPaneRef } from 'src/app/core/right-pane/right-pane.ref';
import { RightPaneService } from 'src/app/core/right-pane/right-pane.service';
import { ErrorService } from 'src/app/core/services/errors/error.service';
import { HelpBoxService } from 'src/app/core/services/help-box/help-box.service';
import { SessionService } from 'src/app/core/services/session/session.service';
import { FilesUtils } from 'src/app/core/utils/files';
import {
  FileDescription,
  FileHistory,
  FileItem,
  FileItemType,
  FileTree,
  FilesManager,
  FilesState,
} from '../models/file-models';
import { FileManagerService } from '../services/file-manager.service';
import { FileGridComponent } from './file-grid/file-grid.component';
import { FileHistoryComponent } from './file-history/file-history.component';
import { FileRenamingComponent } from './file-renaming/file-renaming.component';
import { FileReplacementComponent } from './file-replacement/file-replacement.component';
import { FileTreeComponent } from './file-tree/file-tree.component';

@Component({
  selector: 'app-file-manager',
  templateUrl: './file-manager.component.html',
  styleUrls: ['./file-manager.component.scss'],
  animations: [
    trigger('insertTrigger', [
      transition(':leave', [style({ opacity: 1 }), animate('300ms', style({ opacity: 0 }))]),
    ]),
    trigger('insertDrop', [transition(':leave', [animate('300ms', style({ opacity: 0 }))])]),
  ],
  providers: [FileManagerService],
})
export class FileManagerComponent implements OnInit {
  @ViewChild('fileUpload') fileUpload: ElementRef<HTMLInputElement> | undefined;
  @ViewChild(FileReplacementComponent) public fileReplacementComponent:
    | FileReplacementComponent
    | undefined;
  @ViewChild(FileRenamingComponent) public fileRenamingComponent: FileRenamingComponent | undefined;
  @ViewChild(FileGridComponent) public fileGridComponent: FileGridComponent | undefined;
  @ViewChild('templateHistoryFile') public templateHistoryFile: TemplateRef<any> | undefined;
  @ViewChild(FileHistoryComponent) public fileHistoryComponent: FileHistoryComponent | undefined;
  @ViewChild(FileTreeComponent) public fileTreeComponent: FileTreeComponent | undefined;

  public hasRights = true;
  public isAdmin = false;
  public isLoaderActive = true;
  public isLoaderHistoryActive = true;
  public isExplanationDisplayed = false;
  public isLangLoading = true;
  public isGridUpdatable = true;
  public isGridLoading = false;
  public isGridLoadingStart$ = new BehaviorSubject(false);
  public isGridLoadingStop$ = new BehaviorSubject(false);
  public loaderGridDelayBeginning = 300;
  public rightPaneRef?: RightPaneRef;
  // defined the array of data
  public treeData: FileTree[] = [];
  public dragExternalFile: any = {
    active: false,
    type: 0,
    message: '',
    node: '',
    emplacement: '',
  };
  public maxFileSize = 28400000; // Max file size to upload : 28.4 MB
  public histories: FileHistory[] = [];
  public currentFileHistory: any;
  public filesInFolder: FileDescription[] = [];
  public subscription: Subscription | undefined;

  constructor(
    private readonly fileManagerService: FileManagerService,
    private readonly elem: ElementRef,
    private readonly resourcesService: ResourcesService,
    private readonly sessionService: SessionService,
    private readonly translate: TranslateService,
    private readonly errorService: ErrorService,
    private readonly helpboxService: HelpBoxService,
    private readonly dialogConfirmService: TxDialogService,
    private readonly rightPaneService: RightPaneService
  ) {}

  ngOnInit(): void {
    combineLatest([
      this.sessionService.getConnectedUser(),
      this.resourcesService.onInitialize(),
    ]).subscribe(([connectedUser, result]) => {
      if (result.length > 0) {
        this.treeData = this.fileManagerService.formatTreeData(
          result.map((r) => ({
            alias: r.alias,
            right: r.right,
            isFolder: r.isFolder,
            lastModification: r.lastModification,
            hasSubFolders: true,
            expanded: false,
            size: r.size,
          }))
        );
        this.fileManagerService.setTreeData(this.treeData);

        const errorMessageIncorrectConfiguration = result
          .reduce((acc: any, r) => {
            if (r.originalRight) {
              acc.push(r.alias);
            }
            return acc;
          }, [])
          .join(', ');

        if (errorMessageIncorrectConfiguration !== '') {
          this.errorService.addError({
            error: {
              errorKey: 'WRONG_MODULE_CONFIGURATION',
              errorDetails: errorMessageIncorrectConfiguration,
            },
          });
        }
      } else {
        this.isAdmin = connectedUser.adminRights.some((right) => right === AdminRights.IsAdmin);
        this.hasRights = false;
      }
      this.isLoaderActive = false;
    });

    this.sessionService.getLoadingState().subscribe((stateL) => (this.isLangLoading = !stateL));

    this.helpboxService.getHelpboxState().subscribe((value) => {
      if (value === false) {
        this.isExplanationDisplayed = false;
      }
    });
    this.helpboxService.getMultipleStates().subscribe(([hsState, mhsState, exp]) => {
      if (!hsState && !mhsState) {
        this.isExplanationDisplayed = false;
      } else if (exp.id === 'expFileManager') {
        this.isExplanationDisplayed = true;
      } else {
        this.isExplanationDisplayed = false;
      }
    });

    // LoaderGrid :
    combineLatest([this.isGridLoadingStart$, this.isGridLoadingStop$]).subscribe(
      ([gridLoadingStart, gridLoadingStop]) => {
        // selector function
        if (gridLoadingStop) {
          setTimeout(() => {
            this.isGridLoading = false;
          }, 100);
        } else if (gridLoadingStart) {
          this.isGridLoading = true;
        }
      }
    );
  }
  updateFilesInFolder(data: FileDescription[]) {
    this.filesInFolder = data;
  }
  getCurrentFileHistoryFromChilds(currentFileHistoryFromChilds: any) {
    this.currentFileHistory = currentFileHistoryFromChilds;
  }
  updateSelectedNodes(targetCell: FileDescription) {
    this.fileManagerService.selectedNode = this.fileManagerService.selectedNode?.children?.find(
      (child) => child.name === targetCell.name
    );
    //To do fix
    // if (this.fileTreeComponent?.fileTree) {
    //   this.fileTreeComponent.fileTree.selectedNodes = [
    //     this.fileManagerService.selectedNode?.id ?? [],
    //   ].flat();
    // }
    if (this.fileManagerService.selectedNode) {
      this.selectNode(this.fileManagerService.selectedNode);
    }
  }
  showRenamingInformations(args: {
    fileName: string;
    newName: string;
    path: string;
    alias: string;
    node?: FileTree;
    selectedRowGrid?: FileDescription;
  }) {
    this.fileRenamingComponent?.show(
      args.fileName,
      args.newName,
      args.path,
      args.alias,
      args.node,
      args.selectedRowGrid
    );
  }
  handleFileInput(files: FileList) {
    const node = this.fileManagerService.selectedNode;
    if (node) {
      this.getFilesInFolder(node).subscribe((result) => {
        const filesControled = this.controlFilesToUpload(files, node);
        this.addFilesInGrid(filesControled, node);
        if (this.fileUpload) {
          this.fileUpload.nativeElement.value = '';
        }
      });
    }
  }
  dialogDisplayafterDelete(objectToDisplay: {
    message?: string;
    annotation?: string;
    object?: any;
    showAdditionalInput?: boolean;
  }) {
    if (objectToDisplay.message && objectToDisplay.object) {
      this.dialogConfirmService
        .open({
          message: objectToDisplay.message,
          okCaption: _('button.delete'),
        })
        .subscribe((deleteClicked) => {
          if (deleteClicked) {
            this.confirmDelete(objectToDisplay.object);
          }
        });
    }
  }

  displayDialogReplaceFile(objectToDisplay: {
    files?: FilesState;
    isMovingItems?: boolean;
    isPasteItems?: boolean;
    sourceParentNode?: FileTree;
    destinationParentNode?: FileTree;
    data?: FileDescription[];
    nodeMoved?: FileTree;
  }) {
    if (
      objectToDisplay.files &&
      objectToDisplay.sourceParentNode &&
      objectToDisplay.destinationParentNode &&
      objectToDisplay.data
    ) {
      this.fileReplacementComponent?.show(
        objectToDisplay.files,
        objectToDisplay.isMovingItems ?? true,
        objectToDisplay.isPasteItems ?? false,
        objectToDisplay.sourceParentNode,
        objectToDisplay.destinationParentNode,
        objectToDisplay.data,
        objectToDisplay.nodeMoved
      );
    }
  }
  addFile(): void {
    this.fileUpload?.nativeElement.click();
  }
  setFileDescription(filesInFolder: FileDescription[]) {
    this.filesInFolder = filesInFolder;
  }
  getFilesInFolder(node: FileTree): Observable<FileDescription[]> {
    const nodesIDDOM = Array.from(document.querySelectorAll('.tree-id'));
    let elementLoaderClone: HTMLElement;

    this.activateLoaderGrid();

    let nodeIDDOM = nodesIDDOM.find((nodeIDElement) => nodeIDElement.textContent === node.id);

    // To delete : when the bug of the API has been fixed :
    // (Bug : when the first node is expanded, the text of all nodes disappears and therefore, the id of the nodes too)
    if (nodeIDDOM === undefined && node.id === this.treeData[0].id) {
      nodeIDDOM = nodesIDDOM[0];
    }
    // -------------------------------------------------------------------------------------------------------------
    const nodeDOM = nodeIDDOM?.closest('.e-text-content');
    if (nodeDOM) {
      // Delete the chevron sign if it is still here :
      if (nodeDOM.firstElementChild?.classList.contains('e-icons')) {
        nodeDOM.firstElementChild.remove();
      }
      const elementLoader = document.querySelector('#loaderNodeExpanded');
      elementLoaderClone = elementLoader?.cloneNode(true) as HTMLElement;
      elementLoaderClone.style.display = 'inline-block';
      nodeDOM.insertBefore(elementLoaderClone, nodeDOM.firstChild);
    }

    return this.resourcesService
      .onExpandingFolder(
        this.fileManagerService.getAlias(node.id),
        this.fileManagerService.findPath(node)
      )
      .pipe(
        tap((result: FileDescription[]) => {
          console.log('objects in folder', node, result);
          this.handleExpandedFolderResult(node, result);
          this.fileTreeComponent?.fileTree?.addChildrenToNode(node.children ?? [], node);
          // this.fileTreeComponent?.fileTree?.nodeExpanded.emit(node); // expand the node in the tree
          // this.fileTreeComponent?.refreshTree(true, () => {
          //   this.fileTreeComponent?.fileTree?.expand([node.id]); // expand node to see children
          // });
          if (elementLoaderClone) {
            elementLoaderClone.remove();
          }
          this.disableLoaderGrid();
        }),
        delay(10) // need a delay, because of setTimeout in refreshTree, a select good item in tree
      );
  }
  moveFilesInTreeManager(args: {
    filesControlled: FilesState;
    sourceParentNode: FileTree;
    destinationNode: FileTree;
    nodes?: FileTree[];
  }) {
    this.fileTreeComponent?.moveFilesInTree(
      args.filesControlled,
      args.sourceParentNode,
      args.destinationNode,
      args.nodes
    );
  }
  refreshTreeInFileManager() {
    this.fileTreeComponent?.refreshTree();
  }
  showFromFileManager(args: {
    fileName: string;
    newName: string;
    path: string;
    alias: string;
    node?: FileTree;
    selectedRowGrid?: FileDescription;
  }) {
    this.fileRenamingComponent?.show(args.fileName, args.newName, args.path, args.alias, args.node);
  }
  setChildren(node: FileTree, children?: FileDescription[]) {
    if (!children || children === null) {
      node.children = undefined;
      node.expanded = false;
      node.hasChildren = false;
    } else {
      node.children = this.fileManagerService.formatTreeData(
        children
          .filter((r) => r.type === FileItemType.directory)
          .map((r) => ({
            alias: r.name,
            right: this.fileManagerService.getRight(node.id),
            isFolder: r.type === FileItemType.directory,
            lastModification: r.lastWriteTime,
            hasSubFolders: r.hasSubFolders,
            expanded: false,
            size: r.length,
          }))
      );
    }
  }
  activateLoaderGrid() {
    this.isGridLoadingStart$.next(false);
    this.isGridLoadingStop$.next(false);
    setTimeout(() => {
      this.isGridLoadingStart$.next(true);
    }, this.loaderGridDelayBeginning);
  }

  renameWithName(data: {
    name: string;
    folder?: FileTree;
    rowGrid: FileDescription | undefined;
    oldName: string;
  }): void {
    if (!data.rowGrid) {
      if (data.folder) {
        data.folder.name = data.name;
        data.folder.resource.alias = data.name;
        this.fileTreeComponent?.renameWithNameInTree({ name: data.name, node: data.folder });
      }
    } else {
      this.fileGridComponent?.renameWithNameInGrid({ name: data.name, rowGrid: data.rowGrid });
      if (data.folder) {
        data.folder.name = data.name;
        data.folder.resource.alias = data.name;
        this.fileTreeComponent?.renameWithNameInTree({ name: data.name, node: data.folder });
      }
    }
  }

  renameItem(data: {
    oldName: string;
    newName: string;
    folder?: FileTree;
    path: string;
    alias: string;
    rowGrid?: FileDescription;
  }): void {
    const folderToChange = data.folder;
    this.resourcesService
      .onRenamingFile(data.alias, data.oldName, data.newName, data.path)
      .subscribe(
        (d) => {
          this.renameWithName({
            name: data.newName,
            folder: folderToChange,
            rowGrid: data.rowGrid,
            oldName: data.oldName,
          });
          if (data.rowGrid && data.rowGrid.type !== FileItemType.directory) {
            this.fileManagerService.createNotification(
              'success',
              this.translate.instant(_('admins.resources.fileRename'), {
                new: data.newName,
                old: data.oldName,
              }),
              true,
              8000
            );
          } else {
            this.fileManagerService.createNotification(
              'success',
              this.translate.instant(_('admins.resources.folderRename'), {
                new: data.newName,
                old: data.oldName,
              }),
              true,
              8000
            );
          }
        },
        (error) => {
          // rename with old name in case of server error
          this.renameWithName({
            name: data.oldName,
            folder: folderToChange,
            rowGrid: data.rowGrid,
            oldName: data.oldName,
          });
        }
      );
  }

  openHistory(args: { alias: string; fileName: string; path: string }): void {
    this.isLoaderHistoryActive = true;
    this.resourcesService
      .onOpeningHistory(args.alias, args.fileName, args.path)
      .subscribe((data) => {
        this.sortHistory(data);
        this.histories = data;
        this.isLoaderHistoryActive = false;
      });
    this.rightPaneRef = this.rightPaneService.showNewPaneWithTemplate({
      template: this.templateHistoryFile,
    });
    this.rightPaneRef.afterClosed.pipe(take(1)).subscribe(() => {
      this.histories = [];
    });
  }

  sortHistory(history: FileHistory[]) {
    history.sort((a, b) => {
      if (a.dateOfAction === b.dateOfAction) {
        if (a.authorContactName > b.authorContactName) {
          return -1;
        } else {
          return 1;
        }
      } else if (a.dateOfAction > b.dateOfAction) {
        return -1;
      } else {
        return 1;
      }
    });
  }

  updateVersionFile(file: FileHistory) {
    let index = this.filesInFolder.findIndex((f) => f.name === file.itemName);
    if (index !== -1) {
      this.filesInFolder.splice(index, 1);
    } else {
      index = this.fileManagerService.getIndexFirstFileInGrid(this.filesInFolder);
    }
    this.filesInFolder.splice(index, 0, {
      name: file.itemName,
      type: file.itemType,
      lastWriteTime: file.dateOfAction,
      extension: FilesUtils.extractFileExt(file.itemName, true),
      length: file.length,
    });
    this.filesInFolder = [...this.filesInFolder];
  }
  hideIndicator() {
    this.dragExternalFile.active = false;
    this.dragExternalFile.emplacement = '';
    const elements = this.elem.nativeElement.querySelectorAll('.border-accent-dashed');
    elements.forEach((element: any) => {
      element.classList.remove('border-accent-dashed');
    });
  }
  selectNode(node: FileTree) {
    if (this.isGridUpdatable && node) {
      if (this.subscription) {
        this.subscription.unsubscribe();
      }
      this.activateLoaderGrid();

      this.subscription = this.resourcesService
        .onExpandingFolder(
          this.fileManagerService.getAlias(node.id),
          this.fileManagerService.findPath(node)
        )
        .subscribe((data) => {
          this.setChildren(node, data); // set the children to the node
          this.fileTreeComponent?.refreshTree();
          this.fileManagerService.sortGrid(data);
          this.filesInFolder = data;
          this.disableLoaderGrid();
        });
    }
  }
  controlFilesToUpload(files: FileList, selectedNode: FileTree): FilesState {
    const result = {
      goodFiles: [] as any,
      sameFiles: [] as any,
      existingFiles: [] as any,
    };

    // iterate over each files selected by user to upload
    Array.from(files).forEach((file) => {
      if (selectedNode.resource.right !== DataBaseRights.DbrStructure) {
        this.fileManagerService.createNotification(
          'error',
          this.translate.instant(_('admins.resources.fileAddAndModifNotAllow')),
          false,
          8000
        );
        return;
      }

      if (file.size <= this.maxFileSize) {
        let existingRowGrid: FileDescription | undefined;
        if (selectedNode.children) {
          existingRowGrid = this.filesInFolder.find((child) => child.name === file.name);
        }
        if (existingRowGrid) {
          result.sameFiles.push(this.convertFileToFileDescription(file, true));
          result.existingFiles.push(existingRowGrid);
        } else {
          result.goodFiles.push(this.convertFileToFileDescription(file, true));
        }
      } else {
        this.fileManagerService.createNotification(
          'error',
          this.translate.instant(_('admins.resources.maxFileSizeExceeded'), {
            fileName: file.name,
          }),
          false,
          8000
        );
      }
    });
    return result;
  }
  convertFileToFileDescription(file: File, setDateNow: boolean): FileDescription {
    let date;
    if (setDateNow) {
      date = new Date(Date.now()).toISOString();
    } else {
      date = new Date(file.lastModified).toISOString();
    }

    return {
      name: file.name,
      type: 'File',
      lastWriteTime: date,
      extension: FilesUtils.extractFileExt(file.name, true),
      length: file.size,
      file,
    };
  }
  disableLoaderGrid() {
    this.isGridLoadingStop$.next(true);
  }
  showIndicator(event: any) {
    // this.hideIndicator();
    // let target: Element | null | undefined = event.target;
    // target = target?.closest('.e-list-item');
    // // In tree :
    // if (target) {
    //   this.dragExternalFile.emplacement = 'tree';
    //   this.dragExternalFile.active = true;
    //   event.dataTransfer.dropEffect = 'copy';
    //   this.dragExternalFile.message = _('admins.resources.dropFilesInFolder');
    //   this.dragExternalFile.type = 1;
    //   let node = this.fileTreeComponent?.fileTree?.getNode(target);
    //   const treeNode = node
    //     ? this.fileManagerService.findNode(node.id as string, this.treeData)
    //     : undefined;
    //   if (!treeNode?.isFolder) {
    //     this.dragExternalFile.type = 2;
    //     target = target.closest('.e-has-child');
    //     node = target ? this.fileTreeComponent?.fileTree?.getNode(target) : undefined;
    //   }
    //   target?.children?.item(1)?.classList.add('border-accent-dashed');
    //   this.dragExternalFile.node = node?.text;
    // } else {
    //   this.dragExternalFile.type = 0;
    //   event.dataTransfer.dropEffect = 'none';
    // }
  }

  confirmDelete(data: {
    files: FileDescription[] | FileTree;
    isDeletingFromTree: boolean;
    alias?: string;
    path?: string;
    parentNode?: FileTree;
  }): void {
    if (data.isDeletingFromTree && data.parentNode) {
      this.fileTreeComponent?.deleteFromTree({
        file: data.files as FileTree,
        parentNode: data.parentNode,
      });
    } else if (data.alias && data.path !== undefined && data.parentNode) {
      this.deleteFromGrid({
        files: data.files as FileDescription[],
        alias: data.alias,
        path: data.path,
        parentNode: data.parentNode,
      });
    }
  }

  deleteFromGrid(dataDelete: {
    files: FileDescription[];
    alias: string;
    path: string;
    parentNode: FileTree;
  }): void {
    const itemsToDelete: FileItem[] = [];
    dataDelete.files.forEach((file) => {
      itemsToDelete.push({
        alias: dataDelete.alias,
        itemName: file.name,
        path: dataDelete.path,
      } as FileItem);
    });
    const toast = this.fileManagerService.createNotification(
      'loading',
      this.translate.instant(_('admins.resources.itemsDeletingNoProgression'), {
        number: itemsToDelete.length,
      }),
      false,
      0
    );

    this.resourcesService.onDeletingFiles(itemsToDelete).subscribe(
      (result) => {
        // If result of the request is empty (== all the files have been successfully deleted)
        if (!result || result.length === 0) {
          this.fileManagerService.afterDeleteFiles(itemsToDelete.length, toast);
        } else {
          // Else : some of the files/folders have not been successfully deleted : var "result" contains the files/folders that are not deleted
          dataDelete.files = dataDelete.files.filter(
            (o1) => !result.some((o2: any) => o1.name === o2.itemName)
          ); // Remove from the dataDelete.files the items that could not be deleted
          this.fileManagerService.afterErrorDeleteFiles(
            toast,
            true,
            result.reduce((acc: string[], r: any) => {
              acc.push(r.itemName as string);
              return acc;
            }, [] as string[])
          );
        }

        dataDelete.files.forEach((file) => {
          this.deleteItemInGrid(file.name);

          if (file.type === FileItemType.directory && dataDelete.parentNode.children) {
            this.deleteFolderInTree({
              parentNode: dataDelete.parentNode,
              childNodeToDelete: dataDelete.parentNode.children.filter(
                (item) => item.name === file.name
              )[0],
            });
          }
        });
      },
      (error) => {
        this.fileManagerService.afterErrorDeleteFiles(toast, false);
      }
    );
  }

  deleteItemInGrid(itemName: string) {
    const directoryData =
      this.fileGridComponent?.filesGrid?.dataSource.dataList ?? this.filesInFolder;
    this.isGridUpdatable = false;
    const index = directoryData.findIndex((f) => f.name === itemName);
    if (index !== -1) {
      directoryData.splice(index, 1);
    }
    this.filesInFolder = [...directoryData];
    this.isGridUpdatable = true;
  }

  deleteFolderInTree(args: { parentNode: FileTree; childNodeToDelete: FileTree }) {
    // args.parentNode.children = args.parentNode.children?.filter(
    //   (child) => child.id !== args.childNodeToDelete.id
    // );
    // this.fileTreeComponent?.fileTree?.removeNodes([args.childNodeToDelete.id]);
    // if (!args.parentNode.children || args.parentNode.children.length === 0) {
    //   this.setChildren(args.parentNode, undefined);
    //   this.fileTreeComponent?.refreshTree();
    // }
  }

  doActionsOnFiles(args: {
    filesToAdd: FileDescription[];
    filesToReplace: FileDescription[];
    node: FileTree | FileDescription;
    isMovingItems: boolean;
    isPasteItems: boolean;
    sourceParentNode: FileTree;
    destinationParentNode: FileTree;
  }) {
    if (args.isMovingItems) {
      this.fileTreeComponent?.doMoveFilesInTree({
        filesToAdd: args.filesToAdd,
        filesToReplace: args.filesToReplace,
        sourceParentNode: args.sourceParentNode,
        destinationParentNode: args.destinationParentNode,
        node: [args.node as FileTree],
      });
    } else if (args.isPasteItems) {
      this.doCopyFilesInGrid({
        filesToAdd: args.filesToAdd,
        filesToReplace: args.filesToReplace,
        node: args.node as FileDescription,
      });
    } else {
      this.fileGridComponent?.doAddFilesInGrid({
        filesToAdd: args.filesToAdd,
        filesToReplace: args.filesToReplace,
        node: args.node as FileTree,
      });
    }
  }

  setFileAfterSuccessMoving(args: {
    destinationFolder: FileTree;
    file: FileDescription;
    parentNodeMoved: FileTree;
    nodeMoved: FileTree;
  }) {
    // -----------------------------------------
    // Update grid :
    // -----------------------------------------
    this.fileGridComponent?.setFileAfterSuccessMovingGrid(
      args.destinationFolder,
      args.file,
      args.parentNodeMoved
    );

    // -----------------------------------------
    // Update tree :
    // -----------------------------------------
    this.fileTreeComponent?.setFileAfterSuccessMovingTree(
      args.destinationFolder,
      args.file,
      args.parentNodeMoved,
      args.nodeMoved
    );

    this.fileTreeComponent?.refreshTree();
  }

  createFolder(filesInFolder: FileDescription[]) {
    this.filesInFolder = filesInFolder;
    if (this.fileManagerService?.selectedNode?.resource.right !== DataBaseRights.DbrStructure) {
      this.fileManagerService.createNotification(
        'error',
        this.translate.instant(_('admins.resources.fileAddAndModifNotAllow')),
        false,
        8000
      );
      return;
    }
    const toast = this.fileManagerService.createNotification(
      'loading',
      this.translate.instant(_('admins.resources.creatingFolder')),
      false,
      0
    );

    let nameFolder = this.translate.instant(_('admins.resources.newFolder'));

    if (this.filesInFolder.find((f) => f.name.toLowerCase() === nameFolder.toLowerCase())) {
      nameFolder = FilesManager.createUniqueFileNameGrid(
        this.translate.instant(_('admins.resources.newFolder')),
        this.filesInFolder,
        2
      );
    }

    this.resourcesService
      .onCreatingFolder(
        this.fileManagerService.getAlias(this.fileManagerService.selectedNode.id),
        nameFolder,
        this.fileManagerService.findPath(this.fileManagerService.selectedNode)
      )
      .subscribe(
        (data) => {
          // In grid :
          const newFolderInGrid: FileDescription = {
            name: nameFolder,
            type: FileItemType.directory,
            lastWriteTime: new Date(Date.now()).toISOString(),
            extension: '',
            length: -1,
          };
          this.filesInFolder = [newFolderInGrid, ...this.filesInFolder];
          setTimeout(
            () => this.fileGridComponent?.filesGrid?.dataSource.select(newFolderInGrid),
            0
          );

          // In tree :
          const newFolder: any = {
            alias: nameFolder,
            right: this.fileManagerService?.selectedNode?.resource.right,
            isFolder: true,
            hasSubFolders: false,
            expanded: false,
            lastModification: new Date(Date.now()).toISOString(),
            size: -1,
          };
          if (this.fileManagerService.selectedNode) {
            this.fileManagerService.selectedNode.hasChildren = true;
          }
          if (this.fileManagerService?.selectedNode?.children) {
            this.fileManagerService.selectedNode.children =
              this.fileManagerService.selectedNode.children.concat(
                this.fileManagerService.formatTreeData([newFolder], false)
              );
          }

          this.fileTreeComponent?.refreshTree();

          this.fileManagerService.updateNotification(
            toast,
            'success',
            this.translate.instant(_('admins.resources.folderCreated'))
          );
        },
        (error) => {
          this.fileManagerService.updateNotification(toast, 'loading', '', undefined, true, 1);
        }
      );
  }

  // To delete when the bug of the API has been fixed :
  // (Bug : we can only expand the nodes of the tree if the first one is expanded (= if the first has children))
  public deleteFakeNode(node: FileTree): void {
    if (node.children && node.children.length === 1 && node.children[0].id === '-1') {
      delete node.children;
      this.fileTreeComponent?.refreshTree();
    }
  }
  getExplanation(globalExpId: string, expId: string, active: boolean): void {
    this.helpboxService.setExplanationsFromId(globalExpId, expId, active);
    this.isExplanationDisplayed = true;
  }

  closeHelpbox() {
    this.helpboxService.closeHelpbox();
  }

  addFilesInGrid(files: FilesState, selectedNode: FileTree) {
    if (files.sameFiles.length > 0) {
      // ask for replacing same files
      this.fileReplacementComponent?.show(
        files,
        false,
        false,
        selectedNode,
        selectedNode,
        this.filesInFolder,
        selectedNode
      );
    } else {
      this.fileGridComponent?.doAddFilesInGrid({
        filesToAdd: files.goodFiles,
        filesToReplace: [],
        node: selectedNode,
      });
    }
  }
  getFilesInFolderInTree(args: { treeNode: FileTree; files: any }) {
    this.getFilesInFolder(args.treeNode).subscribe((result) => {
      const filesControled = this.controlFilesToUpload(args.files, args.treeNode);
      this.addFilesInGrid(filesControled, args.treeNode);
    });
  }

  private doCopyFilesInGrid(args: {
    filesToAdd: FileDescription[];
    filesToReplace: FileDescription[];
    node: FileDescription;
  }) {
    let files: FileItem[] = [];
    const folderPath = this.fileManagerService.createFolderPath(args.node);
    const selectedNode = this.fileManagerService.selectedNode;
    if (args.filesToReplace.length) {
      // replace files
      files = args.filesToReplace.map((file) => ({
        alias: this.fileManagerService.getAlias(selectedNode?.id),
        itemName: file.name,
        path: selectedNode ? this.fileManagerService.findPath(selectedNode) : '',
        itemNameCopy: file.name,
        itemPathCopy: folderPath,
        shouldReplaceExistingItem: true,
      }));
    }
    if (args.filesToAdd.length) {
      files = files.concat(
        args.filesToAdd.map((addedFile) => ({
          alias: this.fileManagerService.getAlias(selectedNode?.id),
          itemName: addedFile.name,
          path: selectedNode ? this.fileManagerService.findPath(selectedNode) : '',
          itemNameCopy: addedFile.newName,
          itemPathCopy: folderPath,
          shouldReplaceExistingItem: false,
        }))
      );
    }

    if (files.length) {
      this.resourcesService.onCopingFiles(files).subscribe((result) => {
        const filesCopied: FileItem[] = files.filter(
          (o1) => !result.some((o2: any) => o1.itemName === o2.itemName)
        );
        if (result.length > 0) {
          this.fileManagerService.afterErrorCopyingFiles(
            true,
            result.map((r: any) => r.itemName)
          );
        }
        const filesReplaced = filesCopied.filter((fc) => fc.shouldReplaceExistingItem);
        const filesAdded = filesCopied.filter((fc) => !fc.shouldReplaceExistingItem);
        if (filesReplaced.length > 0) {
          this.fileManagerService.createNotification(
            'success',
            this.translate.instant(_('admins.resources.filesReplaced'), {
              numberFiles: filesReplaced.length,
              filesName: filesReplaced.map((f) => f.itemName).join(', '),
            }),
            false,
            4000
          );
        }
        if (filesAdded.length > 0) {
          this.fileManagerService.createNotification(
            'success',
            this.translate.instant(_('admins.resources.filesAdded'), {
              numberFiles: filesAdded.length,
              filesName: filesAdded.map((f) => f.itemNameCopy).join(', '),
            }),
            false,
            4000
          );
        }
      });
    }
  }

  //these two private methods will be used in getFilesinFolder
  private handleExpandedFolderResult(node: FileTree, result: FileDescription[]) {
    this.fileManagerService.sortGrid(result);
    this.filesInFolder = result;

    if (result.length === 0) {
      node.hasChildren = false;
    } else {
      this.setChildren(node, result);
      if (node.children && node.children.length > 0) {
        node.expanded = true;
      } else {
        node.hasChildren = false;
      }

      // check if selected node exists again
      this.checkIfSelectedNodeExist();
    }
  }

  private checkIfSelectedNodeExist() {
    if (this.fileManagerService.selectedNode) {
      const selectedNode = this.fileManagerService.findNode(
        this.fileManagerService.selectedNode.id,
        this.treeData
      );
      if (!selectedNode) {
        this.fileManagerService.selectedNode = undefined;
      }
    }
  }
  dropToTree(event: any) {
    this.fileGridComponent?.onCellDragStop({ target: event.event.target, data: [event.item.data] });
  }
}
