<div
  *ngIf="!(showSpinner$ | async) && !currObjectType()"
  class="ttreegrid-no-grid h2-section-subtitle">
  <tx-no-record [noRecordText]="noObjectTypeSelected"></tx-no-record>
</div>
<div *ngIf="!(showSpinner$ | async) && isPortalOT()" class="ttreegrid-no-grid h2-section-subtitle">
  <tx-no-record [noRecordText]="noAttributesForPortal"></tx-no-record>
</div>
<div
  *ngIf="!(showSpinner$ | async) && isEnumerationOT()"
  class="ttreegrid-no-grid h2-section-subtitle">
  <tx-no-record [noRecordText]="noAttributesForListings"></tx-no-record>
</div>
<div
  [ngStyle]="{
    display: currObjectType() && !isPortalOT() && !isEnumerationOT() ? 'block' : 'none'
  }"
  class="att-tree-grid">
  <tx-tree-grid
    class="tree-grid"
    #treeGrid
    [columns]="txColumns"
    primaryKey="uniqueId"
    [showSpinner]="showSpinner$ | async"
    [isRowSelectable]="true"
    (rowSelected)="handleNativeSelect($event)"
    [enableSearching]="enableSearching"
    [disableSelectionHandling]="true"
    [enableColumnResize]="true"
    [disableColumnShift]="true"
    [inputPlaceholder]="'txWebCore.input.searchByNameTagId' | translate"
    [allowCellEditOnDblClick]="allowCellEditOnDblClick"
    [disableEditOnCellClick]="disableEditOnCellClick"
    (searchInputChange)="handleSearch($event)"
    (searchClear)="handleSearchClear()"
    (actionCellComplete)="actionCellComplete.emit($event)"
    (cellEdit)="cellEdit.emit($event)"
    class="attribute-tree-grid"
    id="attributeTreeGrid">
    <tx-grid-toolbar>
      <ng-template>
        <div class="toolbar-align">
          <mat-slide-toggle
            #toggle
            class="toggle-show-selection"
            *ngIf="enableCheckbox"
            (change)="showSelectionToggle($event)">
            {{ 'txWebCore.generic.showSelection' | translate }}</mat-slide-toggle
          >
          <button
            type="button"
            mat-icon-button
            (click)="selectFolderChildren()"
            *ngIf="enableCheckbox && multipleSelection && displayCheckAllButton">
            <fa-icon [icon]="['fal', 'check']" matSuffix></fa-icon>
          </button>
          <button
            type="button"
            mat-icon-button
            (click)="selectFolderChildren(true)"
            *ngIf="enableCheckbox && displayUnCheckAllButton">
            <fa-icon [icon]="['fal', 'times']"></fa-icon>
          </button>
        </div>
      </ng-template>
    </tx-grid-toolbar>

    <tx-grid-column fieldName="name">
      <ng-template #template let-data>
        <mat-checkbox
          *ngIf="isCheckBoxDisplayed(data)"
          [class]="data.uniqueId + ' attribute-node-checkbox m-checkbox'"
          [checked]="isSelected(data)"
          [disabled]="data.isDisabled"
          (change)="handleSelection(data, $event)"></mat-checkbox>
        <tx-column-name-template
          class="user-selection-none text-ellipsis"
          [showMoreTooltips]="true"
          [hasMainParentObject]="false"
          [hideTooltipId]="hideTooltipId"
          [data]="data"
          [searchId]="searchById"
          [inputSearchValue]="inputSearchValue"></tx-column-name-template>
      </ng-template>
      <ng-template #headerTemplate let-data>
        <mat-checkbox
          class="m-checkbox"
          [checked]="isAllSelected"
          [indeterminate]="hasSelectedItems() && !isAllSelected"
          (change)="handleMasterCheckbox()"
          *ngIf="enableCheckbox && multipleSelection && displayMasterCheckBox"></mat-checkbox>
        <span>{{ data }} {{ selectionCount }}</span>
      </ng-template>
    </tx-grid-column>

    <tx-grid-column fieldName="type">
      <ng-template #template let-data>
        <div
          class="user-selection-none text-ellipsis"
          [innerHTML]="data.dataType | dataType | translate"></div>
      </ng-template>
    </tx-grid-column>

    <tx-grid-column fieldName="tags">
      <ng-template #template let-data>
        <ng-container *ngIf="data.tags.length > 0">
          <tx-texts-to-copy
            class="user-selection-none text-ellipsis"
            [texts]="data.tags"
            [searchValue]="inputSearchValue"></tx-texts-to-copy>
        </ng-container>
      </ng-template>
    </tx-grid-column>

    <tx-grid-column *ngFor="let column of additionalColumns" [fieldName]="column.field">
      <ng-template #template let-data *ngIf="column.template">
        <ng-container
          [ngTemplateOutlet]="column.template"
          [ngTemplateOutletContext]="{ data: data, column: column }"></ng-container>
      </ng-template>
      <ng-template #headerTemplate let-data>
        <div class="header-column">
          {{ data.headerText }}
        </div>
      </ng-template>
    </tx-grid-column>
  </tx-tree-grid>
  <tx-context-menu
    trigger="#grid-table"
    [items]="menuItems"
    (beforeOpen)="beforeContextMenu($event)"
    (select)="selectOnContextMenu($event)"></tx-context-menu>
</div>
