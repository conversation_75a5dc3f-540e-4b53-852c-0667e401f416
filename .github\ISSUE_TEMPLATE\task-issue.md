---
name: Task issue
about: Template issue for Tasks
title: "[Txxx] Title"
labels: Task
assignees: ''

---

## Description
<Short description of the task>
 
## Development Tasks
- [ ] Development (to define in details with sub tasks): xx day(s)
- [ ] Add / update Unit test(s) 
- [ ] Execute script `npm run extract-translations` succeed
- [ ] Execute script `npm run test` succeed
- [ ] Debug
- [ ] Pull Request validated
- [ ] Ticket(s) solved (TEEXMA BASSETTI)

## Quality Tasks
- [ ] Specialist Tests (if needed)
- [ ] Test plan updated (if needed)
- [ ] Quality validation
- [ ] Tag merged

## Documentation
- [ ] Specification updated
- [ ] Version of the feature incremented into (TEEXMA BASSETTI)
