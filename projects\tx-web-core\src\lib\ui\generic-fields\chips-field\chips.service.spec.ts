import { TestBed } from '@angular/core/testing';
import { ChipsService } from './chips.service';
import {
  TxDataBaseAction,
  TxDataFile,
  TxDataLink,
  TxDataTab,
} from '@bassetti-group/tx-web-core/src/lib/business-models';
import { MockService } from 'ng-mocks';
import { TxFileAttributeService } from '../../../data-access/files/file-attribute.service';

describe('ChipsService', () => {
  let service: ChipsService;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      providers: [
        ChipsService,
        { provide: TxFileAttributeService, useValue: MockService(TxFileAttributeService) },
      ],
    });
    service = TestBed.inject(ChipsService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get chips list from names', () => {
    const chips = service.chipsFromNames(
      { selectable: true, removable: true, selected: false, icon: 'user' },
      ['test1', 'test2', 'test3']
    );
    expect(chips).toStrictEqual([
      {
        type: 'standard',
        name: 'test1',
        selectable: true,
        selected: false,
        removable: true,
        icon: ['fal', 'user'],
      },
      {
        type: 'standard',
        name: 'test2',
        selectable: true,
        selected: false,
        removable: true,
        icon: ['fal', 'user'],
      },
      {
        type: 'standard',
        name: 'test3',
        selectable: true,
        selected: false,
        removable: true,
        icon: ['fal', 'user'],
      },
    ]);
  });

  it('should create chips from data tab', async () => {
    const data = new TxDataTab(0, 1, ['test1', 'test2', 'test3'], TxDataBaseAction.None);
    const chips = await service.chipsFromData(
      { selectable: true, removable: true, selected: false, icon: 'user' },
      data
    );
    expect(chips).toStrictEqual([
      {
        type: 'standard',
        name: 'test1',
        selectable: true,
        selected: false,
        removable: true,
        icon: ['fal', 'user'],
      },
      {
        type: 'standard',
        name: 'test2',
        selectable: true,
        selected: false,
        removable: true,
        icon: ['fal', 'user'],
      },
      {
        type: 'standard',
        name: 'test3',
        selectable: true,
        selected: false,
        removable: true,
        icon: ['fal', 'user'],
      },
    ]);
  });

  it('should create chips from link', async () => {
    const data = new TxDataLink(
      0,
      1,
      [2],
      [
        {
          order: 1,
          isParent: true,
          isFolder: false,
          idObjectType: 1,
          idOwnerObject: 0,
          creationDate: null,
          id: 12,
          name: 'test4',
          tags: [],
        },
      ],
      TxDataBaseAction.None
    );
    const chips = await service.chipsFromData(
      { selectable: true, removable: true, selected: false, icon: 'user' },
      data
    );
    expect(chips).toStrictEqual([
      {
        type: 'standard',
        name: 'test4',
        selectable: true,
        selected: false,
        removable: true,
        icon: ['fal', 'user'],
      },
    ]);
  });

  it('should create chips from file', async () => {
    const action = TxDataBaseAction.None;
    const dataImage = new TxDataFile(
      0,
      1,
      [{ name: 'x.png', size: 12, view: true, action: action }],
      action
    );
    const chipsImage = await service.chipsFromData(
      { selectable: true, removable: true, selected: false, icon: 'user' },
      dataImage
    );
    expect(chipsImage).toStrictEqual([
      {
        type: 'file',
        name: 'x.png',
        selectable: true,
        selected: false,
        removable: true,
        icon: ['fas', 'file-image'],
        viewableFile: true,
        fileUrl: undefined,
        idFile: undefined,
      },
    ]);

    const dataFile = new TxDataFile(
      0,
      1,
      [{ name: 'y.docx', size: 12, view: true, action: action }],
      action
    );
    const chipsFile = await service.chipsFromData(
      { selectable: true, removable: true, selected: false, icon: 'user' },
      dataFile
    );
    expect(chipsFile).toStrictEqual([
      {
        type: 'file',
        name: 'y.docx',
        selectable: true,
        selected: false,
        removable: true,
        icon: ['fas', 'file-word'],
        viewableFile: false,
        fileUrl: undefined,
        idFile: undefined,
      },
    ]);
  });
});
