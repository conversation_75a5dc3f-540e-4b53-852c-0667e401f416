import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialogModule } from '@angular/material/dialog';
import {
  LocalizedDateMockPipe,
  LocalizedNumberPipe,
  TranslatePipe,
} from 'src/app/app.testing.mock';
import { FileManagerServiceMock } from '../../tests/file-manager.mock';
import { FileDescription, FileGridSelection, FilesManager } from '../../models/file-models';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';

import { FileReplacementComponent } from './file-replacement.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDividerModule } from '@angular/material/divider';
import { FormsModule } from '@angular/forms';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { FileManagerService } from '../../services/file-manager.service';

const TRANSLATIONS = {
  en: {},
  fr: {},
};

const filesToUploadMock: FileGridSelection[] = [
  {
    selected: true,
    file: {
      name: 'fileMock1',
      type: 'Directory',
      lastWriteTime: '2021-09-10T13:10:31.6050849Z',
      extension: '',
      length: -1,
    },
  },
  {
    selected: false,
    file: {
      name: 'fileMock2.png',
      type: 'File',
      lastWriteTime: '2021-11-02T15:38:27.0232213Z',
      extension: '.png',
      length: 100,
    },
  },
];

const filesInDestinationMock: FileGridSelection[] = [
  {
    selected: false,
    file: {
      name: 'fileMock2',
      type: 'Directory',
      lastWriteTime: '2021-09-10T13:10:31.6050849Z',
      extension: '',
      length: -1,
    },
  },
  {
    selected: true,
    file: {
      name: 'fileMock3.png',
      type: 'File',
      lastWriteTime: '2021-11-02T15:38:27.0232213Z',
      extension: '.png',
      length: 100,
    },
  },
];

const sameFilesMock: FileDescription[] = [
  {
    name: 'fileMock1',
    type: 'Directory',
    lastWriteTime: '2021-09-10T13:10:31.6050849Z',
    extension: '',
    length: -1,
  },
  {
    name: 'fileMock3.png',
    type: 'File',
    lastWriteTime: '2021-11-02T15:38:27.0232213Z',
    extension: '.png',
    length: 100,
  },
];

const filesDescInDestinationMock: FileDescription[] = [
  {
    name: 'fileMock3.png',
    type: 'File',
    lastWriteTime: '2021-11-02T15:38:27.0232213Z',
    extension: '.png',
    length: 100,
  },
];

describe('FileReplacementComponent', () => {
  let component: FileReplacementComponent;
  let fixture: ComponentFixture<FileReplacementComponent>;
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        FileReplacementComponent,
        TranslatePipe,
        LocalizedDateMockPipe,
        LocalizedNumberPipe,
      ],
      imports: [
        NoopAnimationsModule,
        MatDialogModule,
        MatCheckboxModule,
        MatDividerModule,
        FormsModule,
        FontAwesomeTestingModule,
        TranslateTestingModule.withTranslations(TRANSLATIONS),
      ],
      providers: [{ provide: FileManagerService, useClass: FileManagerServiceMock }],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FileReplacementComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  beforeEach(() => {
    component.filesToUpload = JSON.parse(JSON.stringify(filesToUploadMock));
    component.allFTUSelected = false;

    component.filesInDestination = JSON.parse(JSON.stringify(filesInDestinationMock));
    component.allFIDSelected = false;

    component.useDetailedChoice = true;

    component.files.sameFiles = [];
    component.files.goodFiles = [];
    component.files.existingFiles = [];

    component.filesDescInDestination = filesDescInDestinationMock;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Some FTU Select', () => {
    beforeEach(() => {
      component.allFTUSelected = false;
    });

    it('should return "false" with this.allFTUSelected=true', () => {
      component.allFTUSelected = true;
      expect(component.someFTUSelect()).toBe(false);
    });

    it('should return "false" with "this.filesToUpload.filter.length=0"', () => {
      component.filesToUpload.forEach((f) => (f.selected = false));
      expect(component.someFTUSelect()).toBe(false);
    });

    it('should return "true" with "this.filesToUpload.filter.length>0" && !this.allFTUSelected', () => {
      expect(component.someFTUSelect()).toBe(true);
    });
  });

  describe('Set All FTU', () => {
    let argsBool: boolean;

    beforeEach(() => {
      argsBool = true;
    });

    it('should assign "true" to this.allFTUSelected with argsBool=true', () => {
      component.setAllFTU(argsBool);
      expect(component.allFTUSelected).toBe(argsBool);
    });

    it('should assign "true" for each filesToUpload.selected with argsBool=true', () => {
      component.setAllFTU(argsBool);
      expect(component.filesToUpload.every((f) => f.selected === argsBool)).toBe(true);
    });

    it('should assign "false" to this.allFTUSelected with argsBool=false', () => {
      argsBool = false;
      component.setAllFTU(argsBool);
      expect(component.allFTUSelected).toBe(argsBool);
    });

    it('should assign "false" for each filesToUpload.selected with argsBool=false', () => {
      argsBool = false;
      component.setAllFTU(argsBool);
      expect(component.filesToUpload.every((f) => f.selected === argsBool)).toBe(true);
    });
  });

  describe('Update All FTU Select', () => {
    it('should assign "true" to this.allFTUSelected with every filesToUpload.selected=true', () => {
      component.filesToUpload.forEach((f) => (f.selected = true));
      component.updateAllFTUSelect();
      expect(component.allFTUSelected).toBe(true);
    });

    it('should assign "false" to this.allFTUSelected with not every filesToUpload.selected=true', () => {
      component.updateAllFTUSelect();
      expect(component.allFTUSelected).toBe(false);
    });

    it('should assign "false" to this.allFTUSelected with every filesToUpload.selected=false', () => {
      component.filesToUpload.forEach((f) => (f.selected = false));
      component.updateAllFTUSelect();
      expect(component.allFTUSelected).toBe(false);
    });
  });

  describe('Some FID Select', () => {
    beforeEach(() => {
      component.allFIDSelected = false;
    });

    it('should return "false" with this.allFIDSelected=true', () => {
      component.allFIDSelected = true;
      expect(component.someFIDSelect()).toBe(false);
    });

    it('should return "false" with "this.filesInDestination.filter.length=0"', () => {
      component.filesInDestination.forEach((f) => (f.selected = false));
      expect(component.someFIDSelect()).toBe(false);
    });

    it('should return "true" with "this.filesInDestination.filter.length>0" && !this.allFTUSelected', () => {
      expect(component.someFIDSelect()).toBe(true);
    });
  });

  describe('Set All FID', () => {
    let argsBool: boolean;

    beforeEach(() => {
      argsBool = true;
    });

    it('should assign "true" to this.allFIDSelected with argsBool=true', () => {
      component.setAllFID(argsBool);
      expect(component.allFIDSelected).toBe(argsBool);
    });

    it('should assign "true" for each filesInDestination.selected with argsBool=true', () => {
      component.setAllFID(argsBool);
      expect(component.filesInDestination.every((f) => f.selected === argsBool)).toBe(true);
    });

    it('should assign "false" to this.allFIDSelected with argsBool=false', () => {
      argsBool = false;
      component.setAllFID(argsBool);
      expect(component.allFIDSelected).toBe(argsBool);
    });

    it('should assign "false" for each filesInDestination.selected with argsBool=false', () => {
      argsBool = false;
      component.setAllFID(argsBool);
      expect(component.filesInDestination.every((f) => f.selected === argsBool)).toBe(true);
    });
  });

  describe('Update All FID Select', () => {
    it('should assign "true" to this.allFIDSelected with every filesInDestination.selected=true', () => {
      component.filesInDestination.forEach((f) => (f.selected = true));
      component.updateAllFIDSelect();
      expect(component.allFIDSelected).toBe(true);
    });

    it('should assign "false" to this.allFIDSelected with not every filesInDestination.selected=true', () => {
      component.updateAllFIDSelect();
      expect(component.allFIDSelected).toBe(false);
    });

    it('should assign "false" to this.allFIDSelected with every filesInDestination.selected=false', () => {
      component.filesInDestination.forEach((f) => (f.selected = false));
      component.updateAllFIDSelect();
      expect(component.allFIDSelected).toBe(false);
    });
  });

  describe('Format File Size', () => {
    it('should return "28400" with "28400000"', () => {
      const fileSize = 28400000;
      expect(component.formatFileSizeInReplacement(fileSize)).toBe(28400);
    });
  });

  describe('Replace All Files', () => {
    it('should call onConfim', () => {
      const spyOnConfirm = jest.spyOn(component, 'onConfirm');
      component.replaceAllFiles();
      expect(spyOnConfirm).toBeCalled();
    });
  });

  describe('Ignore All Files', () => {
    beforeEach(() => {
      component.files.sameFiles = [];
    });
    it('should assign [] to files.sameFiles', () => {
      component.ignoreAllFiles();
      expect(component.files.sameFiles).toStrictEqual([]);
    });
    it('should call onConfim', () => {
      const spyOnConfirm = jest.spyOn(component, 'onConfirm');
      component.ignoreAllFiles();
      expect(spyOnConfirm).toBeCalled();
    });
  });

  describe('Switch To Detailed Choice', () => {
    beforeEach(() => {
      component.useDetailedChoice = false;
    });
    it('should assign "true" to this.useDetailedChoice', () => {
      component.switchToDetailedChoice();
      expect(component.useDetailedChoice).toBe(true);
    });
  });

  describe('Valid Detailed Choice', () => {
    beforeEach(() => {
      component.files.sameFiles = JSON.parse(JSON.stringify(sameFilesMock));
      FilesManager.createUniqueFileNameGrid = jest.fn().mockReturnValue('newNameMock');
    });

    it('should call onConfirm', () => {
      const spyOnConfirm = jest.spyOn(component, 'onConfirm');
      component.validDetailedChoice();
      expect(spyOnConfirm).toBeCalled();
    });

    it('should push file to files.sameFiles with !(isFTU && isFID)', () => {
      component.validDetailedChoice();
      expect(component.files.sameFiles).toStrictEqual([sameFilesMock[0]]);
    });

    it('should call createUniqueFileNameGrid', () => {
      component.filesToUpload = [
        {
          selected: true,
          file: {
            name: 'fileMock1',
            type: 'Directory',
            lastWriteTime: '2021-09-10T13:10:31.6050849Z',
            extension: '',
            length: -1,
          },
        },
        {
          selected: false,
          file: {
            name: 'fileMock2.png',
            type: 'File',
            lastWriteTime: '2021-11-02T15:38:27.0232213Z',
            extension: '.png',
            length: 100,
          },
        },
      ];

      component.filesInDestination = [
        {
          selected: false,
          file: {
            name: 'fileMock2',
            type: 'Directory',
            lastWriteTime: '2021-09-10T13:10:31.6050849Z',
            extension: '',
            length: -1,
          },
        },
        {
          selected: true,
          file: {
            name: 'fileMock1',
            type: 'File',
            lastWriteTime: '2021-11-02T15:38:27.0232213Z',
            extension: '.png',
            length: 100,
          },
        },
      ];

      const spyCreateUniqueFileNameGrid = jest.spyOn(FilesManager, 'createUniqueFileNameGrid');
      component.validDetailedChoice();
      expect(spyCreateUniqueFileNameGrid).toBeCalledWith(
        'fileMock1',
        filesDescInDestinationMock,
        2
      );
    });

    it('should push file to files.goodFiles with (isFTU && isFID)', () => {
      const firstSameFilesMock = sameFilesMock[0];
      firstSameFilesMock.newName = 'newNameMock';

      component.filesToUpload = [
        {
          selected: true,
          file: {
            name: 'fileMock1',
            type: 'Directory',
            lastWriteTime: '2021-09-10T13:10:31.6050849Z',
            extension: '',
            length: -1,
          },
        },
        {
          selected: false,
          file: {
            name: 'fileMock2.png',
            type: 'File',
            lastWriteTime: '2021-11-02T15:38:27.0232213Z',
            extension: '.png',
            length: 100,
          },
        },
      ];

      component.filesInDestination = [
        {
          selected: false,
          file: {
            name: 'fileMock2',
            type: 'Directory',
            lastWriteTime: '2021-09-10T13:10:31.6050849Z',
            extension: '',
            length: -1,
          },
        },
        {
          selected: true,
          file: {
            name: 'fileMock1',
            type: 'File',
            lastWriteTime: '2021-11-02T15:38:27.0232213Z',
            extension: '.png',
            length: 100,
          },
        },
      ];

      component.validDetailedChoice();
      expect(component.files.goodFiles).toStrictEqual([firstSameFilesMock]);
    });
  });

  describe('On Confirm', () => {
    let goodFilesTestMock: any[];
    let sameFilesTestMock: any[];
    let isMovingItemsMock: boolean;
    let sourceParentNodeMock: any;
    let destinationParentNodeMock: any;
    let selectedNodeMock: any;

    beforeEach(() => {
      goodFilesTestMock = [{ id: '1' }];
      sameFilesTestMock = [{ id: '2' }];
      isMovingItemsMock = true;
      sourceParentNodeMock = { id: '5' };
      destinationParentNodeMock = { id: '6' };
      selectedNodeMock = { id: '8' };

      component.files.goodFiles = goodFilesTestMock;
      component.files.sameFiles = sameFilesTestMock;
      component.selectedNode = selectedNodeMock;
      component.sourceParentNode = sourceParentNodeMock;
      component.destinationParentNode = destinationParentNodeMock;
      component.isMovingItems = isMovingItemsMock;
    });
    it('should call reset', () => {
      const spyReset = jest.spyOn(component, 'reset');
      component.onConfirm();
      expect(spyReset).toBeCalled();
    });

    it('should call dialog.closeAll', () => {
      const spyCloseAll = jest.spyOn(component.dialog, 'closeAll');
      component.onConfirm();
      expect(spyCloseAll).toBeCalled();
    });

    it('should call confirm.emit', () => {
      const spyEmit = jest.spyOn(component.confirm, 'emit');
      component.onConfirm();
      expect(spyEmit).toBeCalledWith({
        filesToAdd: goodFilesTestMock,
        filesToReplace: sameFilesTestMock,
        node: selectedNodeMock,
        sourceParentNode: sourceParentNodeMock,
        destinationParentNode: destinationParentNodeMock,
        isMovingItems: isMovingItemsMock,
        isPasteItems: false,
      });
    });
  });

  describe('Reset', () => {
    beforeEach(() => {
      component.useDetailedChoice = true;
      component.allFTUSelected = true;
      component.allFIDSelected = true;
    });
    it('should assign "false" to this.useDetailedChoice', () => {
      component.reset();
      expect(component.useDetailedChoice).toBe(false);
    });

    it('should assign "false" to this.allFTUSelected', () => {
      component.reset();
      expect(component.allFTUSelected).toBe(false);
    });

    it('should assign "false" to this.allFIDSelected', () => {
      component.reset();
      expect(component.allFIDSelected).toBe(false);
    });
  });

  describe('Show', () => {
    let filesMock: any;

    let goodFilesTestMock: any[];
    let sameFilesTestMock: any[];
    let existingFilesTestMock: any[];
    let isMovingItemsMock: boolean;
    let sourceParentNodeMock: any;
    let destinationParentNodeMock: any;
    let filesInDestinationTestMock: any[];
    let selectedNodeMock: any;

    beforeEach(() => {
      component.dialog.open = jest.fn();

      goodFilesTestMock = [{ id: '1' }];
      sameFilesTestMock = [{ id: '2' }];
      existingFilesTestMock = [{ id: '3' }];
      isMovingItemsMock = true;
      sourceParentNodeMock = { id: '5' };
      destinationParentNodeMock = { id: '6' };
      filesInDestinationTestMock = [{ id: '7' }];
      selectedNodeMock = { id: '8' };

      filesMock = {
        goodFiles: goodFilesTestMock,
        sameFiles: sameFilesTestMock,
        existingFiles: existingFilesTestMock,
      };
    });

    it('should assign filesMock to this.files', () => {
      component.show(
        filesMock,
        isMovingItemsMock,
        false,
        sourceParentNodeMock,
        destinationParentNodeMock,
        filesInDestinationTestMock,
        selectedNodeMock
      );
      expect(component.files).toBe(filesMock);
    });

    it('should assign isMovingItemsMock to this.isMovingItems', () => {
      component.show(
        filesMock,
        isMovingItemsMock,
        false,
        sourceParentNodeMock,
        destinationParentNodeMock,
        filesInDestinationTestMock,
        selectedNodeMock
      );
      expect(component.isMovingItems).toBe(isMovingItemsMock);
    });

    it('should assign sameFilesTestMock to this.filesToUpload', () => {
      component.show(
        filesMock,
        isMovingItemsMock,
        false,
        sourceParentNodeMock,
        destinationParentNodeMock,
        filesInDestinationTestMock,
        selectedNodeMock
      );
      expect(component.filesToUpload).toStrictEqual([
        { selected: false, file: sameFilesTestMock[0] },
      ]);
    });

    it('should assign existingFilesTestMock to this.filesInDestination', () => {
      component.show(
        filesMock,
        isMovingItemsMock,
        false,
        sourceParentNodeMock,
        destinationParentNodeMock,
        filesInDestinationTestMock,
        selectedNodeMock
      );
      expect(component.filesInDestination).toStrictEqual([
        { selected: false, file: existingFilesTestMock[0] },
      ]);
    });

    it('should assign sourceParentNodeMock to this.sourceParentNode', () => {
      component.show(
        filesMock,
        isMovingItemsMock,
        false,
        sourceParentNodeMock,
        destinationParentNodeMock,
        filesInDestinationTestMock,
        selectedNodeMock
      );
      expect(component.sourceParentNode).toBe(sourceParentNodeMock);
    });

    it('should assign destinationParentNodeMock to this.destinationParentNode', () => {
      component.show(
        filesMock,
        isMovingItemsMock,
        false,
        sourceParentNodeMock,
        destinationParentNodeMock,
        filesInDestinationTestMock,
        selectedNodeMock
      );
      expect(component.destinationParentNode).toBe(destinationParentNodeMock);
    });

    it('should assign filesInDestinationTestMock to this.filesDescInDestination', () => {
      component.show(
        filesMock,
        isMovingItemsMock,
        false,
        sourceParentNodeMock,
        destinationParentNodeMock,
        filesInDestinationTestMock,
        selectedNodeMock
      );
      expect(component.filesDescInDestination).toBe(filesInDestinationTestMock);
    });

    it('should assign totalFiles with files.sameFiles.length + files.goodFiles.length', () => {
      component.show(
        filesMock,
        isMovingItemsMock,
        false,
        sourceParentNodeMock,
        destinationParentNodeMock,
        filesInDestinationTestMock,
        selectedNodeMock
      );
      expect(component.totalFiles).toBe(filesMock.sameFiles.length + filesMock.goodFiles.length);
    });

    it('should assign selectedNodeMock to this.selectedNode', () => {
      component.show(
        filesMock,
        isMovingItemsMock,
        false,
        sourceParentNodeMock,
        destinationParentNodeMock,
        filesInDestinationTestMock,
        selectedNodeMock
      );
      expect(component.selectedNode).toBe(selectedNodeMock);
    });

    it('should call this.dialog.open', () => {
      const spyOpen = jest.spyOn(component.dialog, 'open');
      component.show(
        filesMock,
        isMovingItemsMock,
        false,
        sourceParentNodeMock,
        destinationParentNodeMock,
        filesInDestinationTestMock,
        selectedNodeMock
      );
      expect(spyOpen).toBeCalled();
    });
  });
});
