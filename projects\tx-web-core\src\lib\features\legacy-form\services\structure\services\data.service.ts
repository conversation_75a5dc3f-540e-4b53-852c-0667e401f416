import { TxAttributeLink } from './../models/attribute';
import { Observable } from 'rxjs';
import { TxApiService } from './api.service';
import {
  LegacyTxData,
  LegacyTxDataBoolean,
  LegacyTxDataLink,
  LegacyTxDataNumeric,
  LegacyTxDataString,
  LegacyTxDataTable,
  LegacyTxDataFile,
  LegacyTxDataUrl,
  LegacyTxDataTab,
  LegacyTxDataWithLinkedObjects,
  LegacyTxDataType,
} from './../models/data';
import { Injectable } from '@angular/core';
import { LegacyTxAttributesService } from './attributes.service';
import { LegacyTxObject } from '../models/object';
import { LegacyTxObjectsService } from './objects.service';

@Injectable({
  providedIn: 'root',
})
export class TxDataService {
  // permit to create a txData
  private createData(result: any): LegacyTxData[] {
    const _createData = (data: any): LegacyTxData => {
      const attribute = this.attributeService.findAttribute(data.idAttribute);

      switch (attribute?.dataType) {
        case LegacyTxDataType.Boolean:
          return LegacyTxDataBoolean.assign(data as unknown as Partial<LegacyTxDataBoolean>);
        case LegacyTxDataType.DecRangeMean:
        case LegacyTxDataType.DecRange:
        case LegacyTxDataType.DecUnique:
          return LegacyTxDataNumeric.assign(data as unknown as Partial<LegacyTxDataNumeric>);
        case LegacyTxDataType.LnkDirect:
        case LegacyTxDataType.Enum:
        case LegacyTxDataType.LnkInv:
        case LegacyTxDataType.LnkBi:
          data.linkedObjects = data.linkedIds.map((id: any) => _findObject(id));
          return LegacyTxDataLink.assign(data as unknown as Partial<LegacyTxDataNumeric>);
        case LegacyTxDataType.URL:
          return LegacyTxDataUrl.assign(data as unknown as Partial<LegacyTxDataNumeric>);
        case LegacyTxDataType.Text:
        case LegacyTxDataType.String:
        case LegacyTxDataType.Date:
        case LegacyTxDataType.DateTime:
          return LegacyTxDataString.assign(data as unknown as Partial<LegacyTxDataNumeric>);
        case LegacyTxDataType.Email:
          return LegacyTxDataTab.assign(data as unknown as Partial<LegacyTxDataNumeric>);
        case LegacyTxDataType.Table:
          return LegacyTxDataTable.assign(data as unknown as Partial<LegacyTxDataNumeric>);
        case LegacyTxDataType.File:
          return LegacyTxDataFile.assign(data as unknown as Partial<LegacyTxDataNumeric>);
        default:
          return LegacyTxData.assign(data);
      }
    };

    const _findObject = function (idObject: number): LegacyTxObject {
      return objects.find((o: any) => o.id === idObject) as LegacyTxObject;
    };

    const dataList = result.data;
    const objects = this.objectsService.createObjects(result.objects);
    const attributesId = dataList.map((d: any) => d.idAttribute);

    // check first if all attribute are available in the attribute service
    this.attributeService.listAttributes(attributesId).subscribe();

    return dataList.map((d: any) => _createData(d));
  }

  constructor(
    public apiService: TxApiService,
    public attributeService: LegacyTxAttributesService,
    public objectsService: LegacyTxObjectsService
  ) {}

  private toAttributeIds(paramAttribute: number | number[] | string | string[]): number[] {
    const isAttributeArray = Array.isArray(paramAttribute);
    const byId = isAttributeArray
      ? paramAttribute[0]
        ? typeof paramAttribute[0] === 'number'
        : null
      : typeof paramAttribute === 'number';
    let idsAttribute: number[] = [];

    if (byId) {
      if (isAttributeArray) {
        idsAttribute = paramAttribute as number[];
      } else {
        idsAttribute = [paramAttribute as number];
      }
    } else {
      const tagsAttribute: string[] = isAttributeArray
        ? (paramAttribute as string[])
        : [paramAttribute as string];
      idsAttribute = this.attributeService.tagToId(tagsAttribute);
    }

    return idsAttribute;
  }

  private getDataRequest(
    paramObject: number | number[],
    paramAttribute: number | number[] | string | string[]
  ): any {
    const idsObject: number[] = Array.isArray(paramObject) ? paramObject : [paramObject];
    const idsAttribute = this.toAttributeIds(paramAttribute);

    return {
      attributeSet: {
        levels: idsAttribute.map((idAttribute) => {
          const level: any = { idAttribute };

          const attribute = this.attributeService.findAttribute(idAttribute);

          if (attribute?.idLinkType) {
            const linkAttribute = attribute as TxAttributeLink;
            if (linkAttribute.linkType?.isAssociative) {
              const assoAttributes = this.attributeService.findAttributeFromAssociativeAttribute(
                linkAttribute.id
              );
              level.linkedLevels = assoAttributes.map((a) => {
                idAttribute: a.id;
              });
            }
          }

          return level;
        }),
      },
      idObjects: idsObject,
    };
  }

  read(
    paramObject: number | number[],
    paramAttribute: number | number[] | string | string[],
    maxReturnedResult: number = 200,
    attributeSetLevel?: any
  ): Observable<LegacyTxData[]> {
    if (maxReturnedResult === null) {
      maxReturnedResult = 200;
    }
    return new Observable((observer) => {
      // check if attribute are readed in the attributes service before sending data
      this.attributeService.listAttributes(paramAttribute).subscribe((attributes) => {
        let requestParam = attributeSetLevel;
        if (!requestParam) {
          requestParam = this.getDataRequest(paramObject, paramAttribute);
        }
        this.apiService.readData(requestParam, maxReturnedResult).subscribe((result) => {
          observer.next(this.createData(result));
          observer.complete();
        });
      });
    });
  }

  write(paramData: LegacyTxData | LegacyTxData[]): void {
    const dataToSave: LegacyTxData[] = Array.isArray(paramData) ? paramData : [paramData];

    this.apiService.writeData(dataToSave).subscribe((res) => {
      // console.log(res);
    });

    // plug api rest

    // dataToSave.forEach(d => {
    //   this.removeOldData(d.idObject, d.idAttribute);
    //   this.data.push(d);
    // });
  }

  writeObjects(idObject: number, idObjectType: number, name: string, data: LegacyTxData[]) {
    const arg = {
      id: idObject,
      idObjectType: idObjectType,
      name: name,
      data: data,
    };
    this.apiService.writeObjectWithData([arg]).subscribe((res) => {
      // console.log(res);
    });
  }
}
