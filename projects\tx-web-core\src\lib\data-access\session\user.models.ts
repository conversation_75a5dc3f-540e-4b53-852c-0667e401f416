import { AdminRights } from './admin-rights.model';

export interface User {
  id: number;
  idObject?: number;
  name?: string;
  login: string;
  password?: string;
  passwordValidityDuration?: number;
  lastPasswordChangeDate?: Date;
  isSynchronizedWithLdap: boolean;
  lastConnectionDate?: Date;
  accountExpirationDate?: Date;
  isBlocked?: boolean;
  groups?: UserGroup[];
  // specific for grid
  isSelected?: boolean;
}

export interface ConnectedUserDTO {
  name: string;
  lastConnectionDate: Date;
  canAdministrateStructure: boolean;
  canAdministrateRights: boolean;
  canDoMassImportation: boolean;
  canExportAndExtract: boolean;
  canExecuteMCSAndChoiceguide: boolean;
  canDoDataMining: boolean;
  isAdmin: boolean;
  isLoaded: boolean;
}
export interface ConnectedUser {
  name: string;
  adminRights: AdminRights[];
  isLoaded: boolean;
  lastConnectionDate: Date;
}
export type ConnectedUserState = ConnectedUser | undefined;
export interface UserGroup {
  id: number;
  name: string;
  type: UserGroupType;
  directoryInteractionType: LdapInteractionType;
  directoryGroups?: string[];
  explanation?: string;
  tags?: string[];
  users?: User[];
  numberUsers?: number;
}

export enum UserGroupType {
  Standard = 'ugtStandard',
  WhiteList = 'ugtWhiteList',
  Personal = 'ugtPersonal',
}

export enum LdapInteractionType {
  NoInteraction = 'litNoInteraction',
  AssignToAllUsers = 'litAssignToAllUsers',
  AssignToUsersInCertainLDAPGroups = 'litAssignToUsersInCertainLDAPGroups',
}

export type FilterValueType = string | number | Date;
