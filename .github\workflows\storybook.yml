name: Deploy website

on:
  workflow_dispatch:
    inputs:
      release_version:
        type: string
        description: 'Release version'
        required: true
      release_date:
        type: string
        description: 'Release date'
        required: true

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  build:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "npm"

      - name: Setup FontAwesome Registry
        run: |
          npm config set "//npm.fontawesome.com/:_authToken" ${{ secrets.ORGA_FONT_AWESOME_TOKEN }}
          npm config set "@fortawesome:registry" https://npm.fontawesome.com/

      - name: Install dependencies
        run: npm ci

      - name: Install Angular CLI
        run: npm install -g @angular/cli@latest

      - name: Build Storybook
        env:
          NODE_OPTIONS: "--max_old_space_size=4096"
        run: RELEASE_VERSION=${{ github.event.inputs.release_version }} ng run tx-web-core:build-storybook
      
      - name: Archive Build
        uses: actions/upload-artifact@v4
        with:
          name: build
          path: dist/storybook/tx-web-core

  deploy:
    runs-on: ubuntu-latest
    needs: build
    steps: 
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Download build artifact
        uses: actions/download-artifact@v4
        with: 
          name: build
          path: dist

      - name: Deploy to GitHub Pages
        uses: JamesIves/github-pages-deploy-action@releases/v4
        with:
          token: ${{ secrets.ORGA_GITHUB_ACTION_PAT }}
          folder: dist
          target-folder: ${{ github.event.inputs.release_version }}

  update-json:
    runs-on: ubuntu-latest
    needs: deploy
    steps:
    - name: Checkout Branch
      uses: actions/checkout@v4
    
    - name: Create the script artifact
      uses: actions/upload-artifact@v4
      with:
        name: node-script
        path: .github/workflows/utils/updateVersions.js

    - name: Checkout gh-pages
      uses: actions/checkout@v4
      with:
        ref: gh-pages
    
    - name: Download the script artifact
      uses: actions/download-artifact@v4
      with: 
        name: node-script

    - name: Run the script
      run: |
        node updateVersions.js ${{ github.event.inputs.release_version }} ${{ github.event.inputs.release_date }}
    
    - name: Move versions.json file to build folder
      run: |
        mkdir build
        mv versions.json build/versions.json

    - name: Deploy to GitHub Pages
      uses: JamesIves/github-pages-deploy-action@releases/v4
      with:
        token: ${{ secrets.ORGA_GITHUB_ACTION_PAT }}
        folder: build
        clean: false
