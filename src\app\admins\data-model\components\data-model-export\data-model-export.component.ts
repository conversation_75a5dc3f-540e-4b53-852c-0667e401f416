import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { DataModelService } from '../../service/data-model.service';
import { ExportDataMapSettings } from '../../models/export-data-map-settings';
import {
  EmitObjectType,
  EmitObjectTypeList,
  EmitAttributeType,
  CTxAttributeSetLevel,
  TxAttribute,
  TxAttributesService,
  TxObjectType,
  TxObjectsTypeService,
  TxDataType,
} from '@bassetti-group/tx-web-core';

@Component({
  selector: 'app-data-model-export',
  templateUrl: './data-model-export.component.html',
  styleUrls: ['./data-model-export.component.scss'],
})
export class DataModelExportComponent implements OnInit {
  @Output() changeSelection = new EventEmitter<TxObjectType>();
  @Output() closePanel = new EventEmitter<any>();
  @ViewChild(MatTabGroup) tabGroup!: MatTabGroup;

  public objectType?: TxObjectType;
  public attribute?: TxAttribute;
  public filtredDataType: TxDataType[] = [
    TxDataType.Boolean,
    TxDataType.ShortText,
    TxDataType.Listing,
    TxDataType.Decimal,
    TxDataType.Table,
    TxDataType.LongText,
    TxDataType.Group,
    TxDataType.SingleValue,
    TxDataType.Range,
    TxDataType.RangeMeanValue,
    TxDataType.Series,
    TxDataType.TableValue,
    TxDataType.ArchivedGraphic,
    TxDataType.Date,
    TxDataType.DateAndTime,
    TxDataType.File,
    TxDataType.Email,
    TxDataType.Url,
    TxDataType.Tab,
  ];
  public checkAllAttribute = false;
  public mapAllChecked = new Map<number, ExportDataMapSettings>();
  public oTsChecked = new Array<number>();
  public attributeSetLevels: CTxAttributeSetLevel[] = []; // allow to defined which nodes are checked

  constructor(
    public attributeService: TxAttributesService,
    public oTService: TxObjectsTypeService,
    private dataModelService: DataModelService
  ) {}

  ngOnInit(): void {
    if (this.tabGroup) {
      this.tabGroup.selectedIndexChange.subscribe((index: number) => {
        this.onChangeTab(index);
      });
    }
    this.oTsChecked = [];
    const cpOTs: number[] = [];
    this.oTService.listAll().subscribe((ots) => {
      ots.forEach((ot) => {
        this.mapAllChecked.set(ot.id, this.CreateExportDataMapSettings(true, true, [], []));
        cpOTs.push(ot.id);
      });
      this.oTsChecked = cpOTs;
    });
  }

  public changeObjectType(ot: TxObjectType): void {
    const dataMap = this.mapAllChecked.get(ot.id);
    if (dataMap) {
      this.checkAllAttribute = dataMap.allAttributesChecked;
      this.attributeSetLevels = dataMap.attributeSetLevels;
    }
    this.objectType = ot;
  }

  public changecheckAllAttribute(att: TxAttribute): void {
    this.checkAllAttribute = false;
  }

  onChangeTab(index: number) {}

  onOTChecked(event: EmitObjectType): void {
    const idOT = event.ot.id;
    const ischecked = event.ischeked;
    if (ischecked) {
      this.addOt(idOT);
    } else {
      this.removeOT(idOT);
    }
  }

  onOTSelectOrUnselectAll(event: EmitObjectTypeList): void {
    const listIdOT = Array.from(event.ot.map((elt) => elt.id));
    const ischecked = event.ischeked;
    if (ischecked) {
      this.addOTList(listIdOT);
    } else {
      this.removeOTList(listIdOT);
    }
  }

  onAttributeChecked(event: EmitAttributeType): void {
    const id = event.attribute.id;
    const idOT = event.attribute.idObjectType;
    const ischecked = event.ischeked;
    if (ischecked) {
      this.addAttrToOT(idOT, id);
    } else {
      this.removeAttrFromOT(idOT, id);
    }
  }

  addAttrToOT(idOT: number, idAtt: number): void {
    const exportData = this.mapAllChecked.get(idOT);
    if (exportData) {
      if (!exportData.objectTypeChecked) {
        exportData.objectTypeChecked = true;
      }
      const listCtxAtt = exportData?.attributeSetLevels;
      const newCtxAtt = new CTxAttributeSetLevel({ idAttribute: idAtt });
      if (listCtxAtt && !listCtxAtt.find((att) => att.idAttribute === idAtt)) {
        listCtxAtt.push(newCtxAtt);
      }
      if (listCtxAtt?.length === exportData?.attributes.length) {
        exportData.allAttributesChecked = true;
      }
      this.mapAllChecked.set(idOT, exportData);
    }
  }

  removeAttrFromOT(idOT: number, idAtt: number): void {
    const exportData = this.mapAllChecked.get(idOT);
    if (exportData) {
      exportData.attributeSetLevels = exportData.attributeSetLevels.filter(
        (CtxAttr) => CtxAttr.idAttribute !== idAtt
      );
      if (exportData.attributeSetLevels.length !== exportData.attributes.length) {
        exportData.allAttributesChecked = false;
      }
      this.mapAllChecked.set(idOT, exportData);
    }
  }

  addOt(idOT: number): void {
    const exportSetting = this.mapAllChecked.get(idOT);
    if (
      exportSetting?.allAttributesChecked !== undefined &&
      exportSetting?.attributes !== undefined
    ) {
      exportSetting.objectTypeChecked = true;
      exportSetting.allAttributesChecked = true;
      exportSetting.attributeSetLevels = [];
      exportSetting.attributes.forEach((att) => {
        exportSetting.attributeSetLevels.push(new CTxAttributeSetLevel({ idAttribute: att.id }));
      });
      this.mapAllChecked.set(idOT, exportSetting);
      this.updateChildrenComponents(idOT);
    }
  }

  removeOT(idOT: number): void {
    const exportSetting = this.mapAllChecked.get(idOT);
    if (exportSetting) {
      exportSetting.attributeSetLevels = [];
      exportSetting.allAttributesChecked = false;
      exportSetting.objectTypeChecked = false;
      this.mapAllChecked.set(idOT, exportSetting);
      this.updateChildrenComponents(idOT);
    }
  }

  addOTList(lisIdOt: number[]): void {
    lisIdOt.forEach((idOT) => {
      const exportSetting = this.mapAllChecked.get(idOT);
      if (
        exportSetting?.allAttributesChecked !== undefined &&
        exportSetting?.attributes !== undefined
      ) {
        exportSetting.objectTypeChecked = true;
        exportSetting.allAttributesChecked = true;
        exportSetting.attributeSetLevels = [];
        exportSetting.attributes.forEach((att) => {
          exportSetting.attributeSetLevels.push(new CTxAttributeSetLevel({ idAttribute: att.id }));
        });
        this.mapAllChecked.set(idOT, exportSetting);
      }
    });
    if (this.objectType?.id) {
      this.attributeSetLevels =
        this.mapAllChecked.get(this.objectType.id)?.attributeSetLevels || [];
    }
    this.oTsChecked = lisIdOt;
  }

  removeOTList(listIdOT: number[]): void {
    listIdOT.forEach((idOT) => {
      const exportSetting = this.mapAllChecked.get(idOT);
      if (exportSetting) {
        exportSetting.attributeSetLevels = [];
        exportSetting.objectTypeChecked = false;
        exportSetting.allAttributesChecked = false;
        if (this.objectType && idOT === this.objectType.id) {
          this.attributeSetLevels = exportSetting.attributeSetLevels;
        }
      }
    });
  }

  updateChildrenComponents(idOT: number): void {
    const newOTtoCheck: number[] = [];
    const iterator = this.mapAllChecked.keys();
    let id: number = iterator.next().value;
    while (id !== undefined) {
      const exportData = this.mapAllChecked.get(id);
      if (exportData?.objectTypeChecked) {
        newOTtoCheck.push(id);
      }
      id = iterator.next().value;
    }
    this.oTsChecked = newOTtoCheck;
    if (this.objectType && idOT === this.objectType.id) {
      this.attributeSetLevels = this.mapAllChecked.get(idOT)?.attributeSetLevels || [];
    }
  }

  onAttributeLoaded(attributes: TxAttribute[]): void {
    this.checkAllAttribute = false;
    if (this.objectType) {
      const dataModel = this.mapAllChecked.get(this.objectType.id);
      if (dataModel && dataModel.attributes.length <= 0) {
        if (dataModel.allAttributesChecked) {
          dataModel.allAttributesChecked = false;
        }
        attributes.forEach((att) => {
          if (att.dataType !== TxDataType.Tab && att.dataType !== TxDataType.Group) {
            dataModel.attributes.push(att);
            if (this.filtredDataType.includes(att.dataType)) {
              dataModel.attributeSetLevels.push(new CTxAttributeSetLevel({ idAttribute: att.id }));
            }
          }
        });
        this.mapAllChecked.set(this.objectType.id, dataModel);
      }
    }
  }

  onCancel(): void {
    this.closePanel.emit();
  }

  onExport(): void {
    this.dataModelService.mapToJson(this.mapAllChecked);
    this.closePanel.emit();
  }

  private CreateExportDataMapSettings(
    objectTypeChecked_: boolean,
    allAttributesChecked_: boolean,
    attributes_: TxAttribute[],
    attributeSetLevels_: CTxAttributeSetLevel[]
  ): ExportDataMapSettings {
    return {
      objectTypeChecked: objectTypeChecked_,
      allAttributesChecked: allAttributesChecked_,
      attributes: attributes_,
      attributeSetLevels: attributeSetLevels_,
    };
  }
}
