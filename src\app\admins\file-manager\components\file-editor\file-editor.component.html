<div class="panel-container">
  <div class="panel-header">
    <fa-icon [icon]="['fas', 'code']" class="panel-icon-title"></fa-icon>
    <span>
      <div class="panel-title h2-section-title">{{ fileToEdit?.name }}</div>
    </span>
    <app-last-save-chip [date]="lastSaveDate" [isSaving]="isSaving"></app-last-save-chip>
    <ng-container *ngIf="theme === 'vs-light'; else lightModeBtn">
      <fa-icon
        class="editor-theme-btn-icon"
        [icon]="['fas', 'moon']"
        size="lg"
        [matTooltip]="'admins.resources.editor.darkmode' | translate"
        (click)="switchTheme()">
      </fa-icon>
    </ng-container>
    <ng-template #lightModeBtn>
      <fa-icon
        class="editor-theme-btn-icon"
        [icon]="['fal', 'sun']"
        size="lg"
        [matTooltip]="'admins.resources.editor.lightmode' | translate"
        (click)="switchTheme()">
      </fa-icon>
    </ng-template>
  </div>
  <div class="file-editor">
    <div class="editor-container">
      <app-loader *ngIf="!file || isEditorLoading"></app-loader>
      <ngx-monaco-editor
        *ngIf="file"
        #editor
        [options]="editorOptions"
        [(ngModel)]="file.content"
        (onInit)="editorInit($event)"
        language="xml"
        style="height: 100%"></ngx-monaco-editor>
    </div>
  </div>

  <div class="panel-button-bar mat-elevation-z4">
    <button mat-stroked-button (click)="closePane()">
      {{ 'window.close' | translate }}
    </button>
    <button
      id="btnSave"
      mat-flat-button
      color="accent"
      [disabled]="!(isSaving | canSaveEditor : fileHasModifications : currentErrors)"
      (click)="saveFile()">
      {{ (isSaving ? 'generic.saving' : 'generic.save') | translate }}
    </button>
  </div>
</div>
