name: Angular Test

on: [pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
      - run: npm config set "//npm.pkg.github.com/:_authToken" ${{ secrets.ORGA_GITHUB_PACKAGES_PAT }}
      - run: npm config set "@bassetti-group:registry" https://npm.pkg.github.com/bassetti-group
      - run: npm config set "//npm.fontawesome.com/:_authToken" ${{ secrets.ORGA_FONT_AWESOME_TOKEN }}
      - run: npm config set "@fortawesome:registry" https://npm.fontawesome.com/
      - uses: mayurrawte/github-angular-actions@latest
      - run: node -v
      - run: npm ci
      - run: npm run test-all
