<app-form-pane-template
  [settings]="settings"
  [isUnderValidation]="isUnderValidation"
  [form]="form"
  [iconForm]="settings?.isEditMode ? 'pencil-alt' : 'plus-circle'"
  [title]="'admins.dataModel.addObjectType' | translate"
  (cancel)="cancel()"
  (saveForm)="saveForm()"
  (addForm)="addForm()">
  <div class="object-type-edition-global-container">
    <form class="ttform-container" [formGroup]="form">
      <mat-tab-group
        #mainTabForm
        id="main-tab-users"
        mat-stretch-tabs="false"
        color="accent"
        [selectedIndex]="0">
        <mat-tab>
          <ng-template mat-tab-label>
            {{ 'generic.settings' | translate }}
          </ng-template>

          <!-- Nature -->
          <div class="form-section">
            <div class="h2-section-subtitle" style="padding-bottom: 8px">
              {{ 'admins.dataModel.nature' | translate }}
              <fa-icon
                *ngIf="!isNatureExplanationDisplayed"
                [matTooltip]="'tooltip.showExplanation' | translate"
                [icon]="['fal', 'question-circle']"
                size="lg"
                style="padding: 0px 16px; font-size: 15px; cursor: pointer"
                (click)="getExplanation('dataModel', 'expNature', false)"></fa-icon>
            </div>

            <div class="form-sub-section">
              <!-- Name -->
              <div class="form-div-form-field">
                <mat-form-field color="accent" class="form-field form-input-field">
                  <mat-label color="accent">{{ 'admins.columns.name' | translate }}</mat-label>
                  <input
                    #nameInput
                    matInput
                    floatLabelType="Auto"
                    [formControl]="name"
                    autocomplete="off"
                    required />
                  <mat-error *ngIf="name?.errors?.required" class="e-error">
                    {{ 'input.fieldRequired' | translate }}
                  </mat-error>
                  <mat-error *ngIf="name?.errors?.nameExist" class="e-error">
                    {{ 'input.nameAlreadyUsed' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Type -->
              <div class="form-div-form-field-right">
                <mat-form-field color="accent" class="form-field form-input-field">
                  <mat-label>{{ 'admins.dataModel.type' | translate }}</mat-label>
                  <mat-select [formControl]="idType" required>
                    <mat-option *ngFor="let type of types" [value]="type.id">
                      {{ type.viewValue }}
                    </mat-option>
                  </mat-select>
                  <mat-error class="e-error">
                    {{ 'input.fieldRequired' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <div class="form-sub-section">
              <!-- Unique Object names for this Object Type -->
              <div
                class
                class="form-div-form-field"
                *ngIf="
                  this.idType?.value === 0 || this.idType?.value === 1 || this.idType?.value === 6
                ">
                <mat-slide-toggle color="accent" [formControl]="hasDistinctName">
                  {{
                    hasDistinctName?.value
                      ? ['admins.dataModel.OTdistinctNames' | translate]
                      : ['admins.dataModel.OTnotDistinctNames' | translate]
                  }}<br />
                </mat-slide-toggle>
                <span
                  style="margin-left: 45px"
                  [ngClass]="{
                    error: hasDistinctName?.value,
                    'color-grey40': !hasDistinctName?.value
                  }"
                  class="legend"
                  >{{ 'admins.dataModel.explanationOTNatureShort' | translate }}</span
                >
              </div>
            </div>
          </div>

          <!-- Visibility -->
          <div
            class="form-section"
            *ngIf="
              this.idType?.value !== 2 &&
              this.idType?.value !== 4 &&
              this.idType?.value !== 5 &&
              this.idType?.value !== 6
            ">
            <div class="h2-section-subtitle" style="padding-top: 16px; padding-bottom: 8px">
              {{ 'admins.dataModel.visibility' | translate }}
              <fa-icon
                *ngIf="!isVisibilityExplanationDisplayed"
                [matTooltip]="'tooltip.showExplanation' | translate"
                [icon]="['fal', 'question-circle']"
                size="lg"
                style="padding: 0px 16px; font-size: 15px; cursor: pointer"
                (click)="getExplanation('dataModel', 'expVisibility', false)"></fa-icon>
            </div>

            <div class="form-sub-section">
              <!-- Object Type visible in the list -->
              <div class="form-div-form-field">
                <mat-slide-toggle color="accent" [formControl]="isVisible">{{
                  isVisible?.value
                    ? ['admins.dataModel.OTvisibleInList' | translate]
                    : ['admins.dataModel.OTNotVisibleInList' | translate]
                }}</mat-slide-toggle>
              </div>

              <!-- Object type visible in the list of search results -->
              <div
                class="form-div-form-field-right"
                *ngIf="this.idType?.value === 0 || this.idType?.value === 3">
                <mat-slide-toggle color="accent" [formControl]="displayResultInTextSearch">{{
                  displayResultInTextSearch?.value
                    ? ['admins.dataModel.OTVisibleInListSearchResults' | translate]
                    : ['admins.dataModel.OTNotVisibleInListSearchResults' | translate]
                }}</mat-slide-toggle>
              </div>
            </div>
          </div>

          <!-- Locking -->
          <div class="form-section" *ngIf="this.idType?.value === 0 || this.idType?.value === 3">
            <div class="h2-section-subtitle" style="padding-top: 16px; padding-bottom: 8px">
              {{ 'admins.dataModel.locking' | translate }}
              <fa-icon
                *ngIf="!isLockingExplanationDisplayed"
                [matTooltip]="'tooltip.showExplanation' | translate"
                [icon]="['fal', 'question-circle']"
                size="lg"
                style="padding: 0px 16px; font-size: 15px; cursor: pointer"
                (click)="getExplanation('dataModel', 'expLocking', false)"></fa-icon>
            </div>

            <div class="form-sub-section">
              <!-- Type of Lock -->
              <div class="form-div-form-field">
                <mat-form-field color="accent">
                  <mat-label>{{ 'admins.dataModel.typeLock' | translate }}</mat-label>
                  <mat-select [formControl]="lockingType" required>
                    <mat-option *ngFor="let lockingType of lockingTypes" [value]="lockingType.type">
                      {{ lockingType.viewValue }}
                    </mat-option>
                  </mat-select>
                  <mat-error class="e-error">
                    {{ 'input.fieldRequired' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Max time before expiration -->
              <div
                *ngIf="!isLockingTypeNone(this.lockingType?.value)"
                class="form-div-form-field-right">
                <mat-form-field color="accent" class="form-field">
                  <mat-label color="accent">{{
                    'admins.dataModel.maxTimeBeforeExpiration' | translate
                  }}</mat-label>
                  <input
                    #nameInput
                    matInput
                    floatLabelType="Auto"
                    [formControl]="lockingDuration"
                    autocomplete="off"
                    type="number"
                    required />
                  <mat-error *ngIf="lockingDuration?.errors.required" class="e-error">
                    {{ 'input.fieldRequired' | translate }}
                  </mat-error>
                  <mat-error *ngIf="lockingDuration?.errors.greaterThanZero" class="e-error">
                    {{ 'input.mustBeGreaterThanZero' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
          </div>

          <!-- Associativity -->
          <div class="form-section" *ngIf="this.idType?.value === 2">
            <div class="h2-section-subtitle" style="padding-top: 16px; padding-bottom: 8px">
              {{ 'admins.dataModel.associativity' | translate }}
            </div>

            <div class="form-sub-section">
              <div class="treegrids-parent" id="treeGridsParent">
                <div class="treegrid-objecttype-attributes form-div-form-field border-grey">
                  <!-- To replace to txTreeGrid when the admins will be refound -->
                  <!-- <oldejs old-treegrid #treeGrid0 [dataSource]="objectTypesStandardTree"  idMapping="id" parentIdMapping="idParent" expandStateMapping="expanded" [treeColumnIndex]='0' class="treegrid-associativity"
                                            (collapsed)="onNodeCollapsed($event, objectTypesStandardTree)" (expanded)="onNodeExpanded($event, objectTypesStandardTree)" (rowSelected)="changeObjectType($event, 0)"
                                            (dataBound)='dataBound($event, 0)' (rowSelecting)="storeIndexRow($event, 0)" (rowDeselected)="selectingRowAgain($event, 0)">
                                            <e-columns>
                                                <e-column field='name' [headerText]="'admins.wording.objectTypes' | translate">
                                                    <ng-template #template let-data>
                                                        <img *ngIf="data.icon !== null" class="otgrid-tree-icon" [src]="data.icon" alt="Object type icon"/>
                                                        <div style="display: inline; margin-left: 8px;">{{data.name}}</div>
                                                    </ng-template>
                                                </e-column>
                                            </e-columns>
                                    </ejs> -->
                </div>
                <div class="treegrid-objecttype-attributes form-div-form-field-right border-grey">
                  <!-- To replace to txTreeGrid when the admins will be refound -->
                  <!-- <oldejs old-treegrid #treeGrid1 [dataSource]="objectTypesStandardTree2"  idMapping="id" parentIdMapping="idParent" expandStateMapping="expanded" [treeColumnIndex]='0' class="treegrid-associativity"
                                            (collapsed)="onNodeCollapsed($event, objectTypesStandardTree2)" (expanded)="onNodeExpanded($event, objectTypesStandardTree2)" (rowSelected)="changeObjectType($event, 1)"
                                            (dataBound)='dataBound($event, 1)' (rowSelecting)="storeIndexRow($event, 1)" (rowDeselected)="selectingRowAgain($event, 1)">
                                            <e-columns>
                                                <e-column field='name' [headerText]="'admins.wording.objectTypes' | translate">
                                                    <ng-template #template let-data>
                                                        <img *ngIf="data.icon !== null" class="otgrid-tree-icon" [src]="data.icon" alt="Object type icon"/>
                                                        <div style="display: inline; margin-left: 8px;">{{data.name}}</div>
                                                    </ng-template>
                                                </e-column>
                                            </e-columns>
                                    </ejs> -->
                </div>
              </div>
            </div>
          </div>

          <!-- Icon -->
          <div
            class="form-section form-section-icon"
            *ngIf="this.isFolder?.value === false && this.idType?.value !== 6">
            <div class="h2-section-subtitle">{{ 'admins.dataModel.icon' | translate }}</div>
            <div style="padding: 8px 3px 4px 3px">
              <mat-accordion>
                <mat-expansion-panel #panel expanded="true">
                  <mat-expansion-panel-header>
                    <mat-panel-title>
                      <fa-icon
                        [icon]="['fal', 'folder']"
                        class="otgrid-accordion-icon"
                        size="lg"></fa-icon>
                      {{ 'generic.standards' | translate }}
                      <span class="otgrid-accordion-counter background-grey20">{{
                        tabIcons?.length ?? 0
                      }}</span>
                    </mat-panel-title>
                  </mat-expansion-panel-header>
                  <div class="form-ot-icons">
                    <div
                      class="form-ot-icon-group hover-grey20"
                      *ngFor="
                        let icon of tabIcons;
                        let index = index;
                        let isFirst = first;
                        let isOdd = odd
                      "
                      (click)="setIcon(icon.id)"
                      [ngClass]="selectedIcon === icon.id ? 'background-grey20' : ''">
                      <span><img class="form-ot-icon" [src]="icon.path" alt="Form icon" /></span>
                      <span>{{ icon.id }}</span>
                    </div>
                  </div>
                </mat-expansion-panel>
              </mat-accordion>
            </div>
          </div>
        </mat-tab>
        <mat-tab>
          <ng-template mat-tab-label>
            {{ 'generic.tags&Descriptions' | translate }}
          </ng-template>
          <!-- Tags & Description -->
          <div class="form-section">
            <div>
              <app-tags-descriptions-form
                [proposeNewTag]="true"
                [tagValue]="name?.value"
                formGroupName="descriptions"
                [concept]="tagConcept"></app-tags-descriptions-form>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </form>
  </div>
</app-form-pane-template>
