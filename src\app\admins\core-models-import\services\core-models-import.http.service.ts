import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { TxConfigService } from '@bassetti-group/tx-web-core';
import { HttpClient } from '@angular/common/http';
import {
  CoreModelsArchiveSummaryDTO,
  CoreModelImportHistoryDTO,
  TestedImportDTO,
  ImportedDTO,
} from '../models/core-models-import.dto';
import { ErrorService } from 'src/app/core/services/errors/error.service';

@Injectable({
  providedIn: 'root',
})
export class CoreModelsImportHttpService {
  activeArchiveError = false;
  archiveTestError = false;
  archiveUploadError = false;
  private readonly apiUrl: string;
  private readonly validateArchiveUrl: string;

  constructor(
    private configService: TxConfigService,
    private http: HttpClient,
    private errorService: ErrorService
  ) {
    this.apiUrl = `${this.configService.getApiUrl()}api/CoreModel/import`;
    this.validateArchiveUrl = this.apiUrl + '/getArchiveSummary';
    this.errorService.registerUnhandledRequestURL(this.apiUrl);
    this.errorService.registerUnhandledRequestURL(this.validateArchiveUrl);
  }

  loadHistory(): Observable<CoreModelImportHistoryDTO['importHistory']> {
    return this.http
      .get<CoreModelImportHistoryDTO>(`${this.apiUrl}/history`)
      .pipe(map((summaries) => summaries.importHistory));
  }

  validateArchive(file: File): Observable<CoreModelsArchiveSummaryDTO> {
    const formData = new FormData();
    formData.append('coreModelArchive', file, file.name);
    return this.http.post<CoreModelsArchiveSummaryDTO>(this.validateArchiveUrl, formData);
  }

  testImport(file: File): Observable<TestedImportDTO> {
    const formData = new FormData();
    formData.append('coreModelArchive', file, file.name);
    return this.http.post<TestedImportDTO>(`${this.apiUrl}/validate`, formData);
  }
  import(file: File): Observable<ImportedDTO> {
    const formData = new FormData();
    formData.append('coreModelArchive', file, file.name);
    return this.http.post<ImportedDTO>(`${this.apiUrl}`, formData);
  }
}
