import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { AbstractSessionService } from '@bassetti-group/tx-web-core/src/lib/data-access/session';
import { GridFilter, GridFilterValue } from './grid-filter-menu.component';
import { LocalizedDatePipe } from '@bassetti-group/tx-web-core/src/lib/features/localized';
import { TxObjectType } from '@bassetti-group/tx-web-core/src/lib/business-models';

type SimpleGridFilterValue = string | number | boolean | TxObjectType | Date;

@Pipe({
  name: 'formatTooltipFilter',
  standalone: true,
})
export class TxFormatTooltipFilterPipe implements PipeTransform {
  private datePipe: LocalizedDatePipe;

  constructor(public translate: TranslateService, public sessionService: AbstractSessionService) {
    this.datePipe = new LocalizedDatePipe(sessionService, translate);
  }

  transform(filter: GridFilter, value: GridFilterValue): string | undefined {
    if (filter.column?.type === 'date') {
      return this.datePipe.transform(filter.value as Date, 'shortDate');
    }
    if (filter.column?.filterOptions) {
      let values: (string | number | boolean | TxObjectType | Date)[];
      if (!Array.isArray(filter.value)) {
        values = [filter.value];
      } else {
        values = filter.value;
      }
      let options = filter.column?.filterOptions?.filter(
        (option: { [value: string]: SimpleGridFilterValue }) => values.includes(option.value)
      );
      let key = 'text';
      if (filter.column?.filterType === 'objectType') {
        // in case of object type, filterOptions is empty
        options = [...values];
        key = 'name';
      }
      const texts: string[] = options.map((o: { [value: string]: string }) =>
        this.translate.instant(o[key])
      );
      return texts.length
        ? texts.join(', ')
        : this.translate.instant('txWebCore.syncFusion.grid.all');
    }
    return filter.value.toString();
  }
}
