<mat-tree [dataSource]="dataSource" [treeControl]="treeControl" class="mat-treeview">
  <mat-tree-node *matTreeNodeDef="let node" (click)="onNodeClick(node)">
    <div class="tree-node" [ngClass]="selectedRowPKValue == node[primaryKey] ? 'node-select' : ''">
      <span *ngIf="!node[hasChildren]" class="spacer margined-row"></span>
      <div class="tree-content" matTreeNodePadding [matTreeNodePaddingIndent]="'34px'">
        <ng-container
          [ngTemplateOutlet]="nodeContent"
          [ngTemplateOutletContext]="{ $implicit: node }"></ng-container>
      </div>
    </div>
  </mat-tree-node>

  <mat-nested-tree-node
    *matTreeNodeDef="let node; when: hasChild"
    [cdkTreeNodeTypeaheadLabel]="node.name">
    <div class="mat-tree-node" (click)="onNodeClick(node)" (dblclick)="onNodeDblClick(node)">
      <div
        class="tree-node"
        [ngClass]="selectedRowPKValue == node[primaryKey] ? 'node-select' : ''">
        <div class="tree-content" matTreeNodePadding [matTreeNodePaddingIndent]="'34px'">
          <ng-container
            [ngTemplateOutlet]="nodeContent"
            [ngTemplateOutletContext]="{ $implicit: node }"></ng-container>
        </div>
      </div>
    </div>

    <div
      [@expandCollapse]="{
        value: treeControl.isExpanded(node) ? 'expanded' : 'collapsed',
        params: { expandDuration, collapseDuration }
      }"
      [@.disabled]="!showAnimations"
      role="group">
      <ng-container matTreeNodeOutlet></ng-container>
    </div>
  </mat-nested-tree-node>
</mat-tree>

<!-- Reusable Node Content Template -->
<ng-template #nodeContent let-node>
  <fa-icon
    *ngIf="node[hasChildren] || node.children?.length > 0"
    class="tree-chevron-icon margined-row"
    [icon]="['fas', 'chevron-right']"
    [@rotateChevron]="treeControl.isExpanded(node) ? 'expanded' : 'collapsed'"
    [@.disabled]="!showAnimations"
    (click)="$event.stopPropagation(); onNodeToggle(node)"></fa-icon>
  <mat-checkbox
    *ngIf="isCheckBoxDisplayed(node)"
    [checked]="isNodeChecked(node)"
    (click)="$event.stopPropagation()"
    (change)="handleSelection(node, $event)"></mat-checkbox>
  <ng-container
    [ngTemplateOutlet]="nodeTemplate?.template || noTemplate"
    [ngTemplateOutletContext]="{ $implicit: node }"></ng-container>
</ng-template>

<!-- Fallback Template -->
<ng-template #noTemplate let-data>
  <span>{{ data.name | translate }}</span>
</ng-template>
