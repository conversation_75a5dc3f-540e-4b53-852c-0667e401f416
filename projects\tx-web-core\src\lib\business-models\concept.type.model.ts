export enum ConceptType {
  Attribute = 'Attribute',
  AttributeSet = 'AttributeSet',
  ChoiceGuide = 'ChoiceGuide',
  EquivalenceSet = 'EquivalenceSet',
  Exportation = 'Exportation',
  FileType = 'FileType',
  Language = 'Language',
  LinkType = 'LinkType',
  Log = 'Log',
  Model = 'Model',
  ModelApplication = 'ModelApplication',
  Object = 'Object',
  ObjectType = 'ObjectType',
  PortalFile = 'PortalFile',
  RequirementList = 'RequirementList',
  SeriesType = 'SeriesType',
  SettingFile = 'SettingFile',
  TableType = 'TableType',
  Unit = 'Unit',
  UserGroup = 'UserGroup',

  // subconcepts
  Question = 'Question',
  unitSystem = 'systemsOfUnits',
  BusinessView = 'BusinessView',
  AdvancedComparison = 'AdvancedComparison',
  AdvancedCreation = 'AdvancedCreation',
  AdvancedDeletion = 'AdvancedDeletion',
  AdvancedDuplication = 'AdvancedDuplication',
  AdvancedExportation = 'AdvancedExportation',
  AppliedInputOutput = 'AppliedInputOutput',
  AsLinkType = 'ASLinkType',
  AsLinkTypeInverse = 'ASLinkTypeInverse',
  AsStandard = 'Standard',
  MCS = 'MCS',
}
