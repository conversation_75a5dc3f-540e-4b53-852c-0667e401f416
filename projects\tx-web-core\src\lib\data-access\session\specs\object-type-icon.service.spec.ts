import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MockProvider } from 'ng-mocks';
import { TxObjectTypeIconService } from '../object-type-icon.service';
import { AbstractSessionService, MockSessionService } from '../../session';
import { of } from 'rxjs';
import { DestroyRef } from '@angular/core';

describe('Service: Abstract Concept', () => {
  let service: TxObjectTypeIconService;
  let sessionService: AbstractSessionService;
  let destroyRef: DestroyRef;
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [MockProvider(AbstractSessionService, MockSessionService, 'useClass'), DestroyRef],
    });
    sessionService = TestBed.inject(AbstractSessionService);
    service = TestBed.inject(TxObjectTypeIconService);
    destroyRef = TestBed.inject(DestroyRef);
  });

  it('should return new icon path', () => {
    const pathResult = service.getIconPath(200);
    expect(pathResult).toEqual('./assets/tx-web-core/img/icons/svg/200.svg');
  });

  it('should return old icon path', () => {
    sessionService.preferences$ = of({ bUseNewIconSet: 'false' });
    const newService = new TxObjectTypeIconService(sessionService, destroyRef);
    const pathResult = newService.getIconPath(200);
    expect(pathResult).toEqual('./assets/tx-web-core/img/icons/png/200.png');
  });
});
