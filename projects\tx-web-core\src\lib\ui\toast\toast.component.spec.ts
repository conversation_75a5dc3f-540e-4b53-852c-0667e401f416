import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { ToastRefMock } from '../testing';
import { ToastComponent } from './toast.component';
import { ToastRef } from './toast.reference';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { TOAST_DATA_TOKEN } from './toast-data-token';
import { TranslateTestingModule } from 'ngx-translate-testing';

describe('ToastComponent', () => {
  let component: ToastComponent;
  let fixture: ComponentFixture<ToastComponent>;
  let toastRef: ToastRef;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        ToastComponent,
        NoopAnimationsModule,
        MatProgressBarModule,
        FontAwesomeTestingModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
      ],
      providers: [
        {
          provide: TOAST_DATA_TOKEN,
          useValue: {
            type: 'success',
            title: 'Test Title',
            description: `test description`,
            date: new Date(),
            isPersistent: false,
            interval: 8000,
          },
        },
        { provide: ToastRef, useClass: ToastRefMock },
      ],
    }).compileComponents();

    toastRef = TestBed.inject(ToastRef);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ToastComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Initialize component', () => {
    it('should set isUnread property to true if undefined', () => {
      expect(component.data.isUnread).toBe(true);
    });

    it('should initialize timer', () => {
      // eslint-disable-next-line @typescript-eslint/dot-notation
      expect(component['intervalId']).toBeDefined();
    });
  });

  describe('After display', () => {
    it('should call "afterDisplay" method of ToastRef', () => {
      const spyDisplay = jest.spyOn(toastRef, 'afterDisplay');
      component.afterDisplay();
      expect(spyDisplay).toBeCalled();
    });
  });

  describe('Close', () => {
    it('should call "close" method of ToastRef', () => {
      const spyClose = jest.spyOn(toastRef, 'close');
      component.close();
      expect(spyClose).toBeCalled();
    });
  });

  describe('On close', () => {
    it('should launch "onClose" method', () => {
      const spyClose = jest.spyOn(component, 'onClose');
      fixture.debugElement.query(By.css('.close-button')).triggerEventHandler('click', null);
      fixture.detectChanges();
      expect(spyClose).toBeCalled();
    });

    it('should change animation state to "closing"', () => {
      expect(component.animationState).toBe('default');
      component.onClose();
      expect(component.animationState).toBe('closing');
    });
  });

  describe('On fade finished', () => {
    it('should launch "onFadeFinished" method', () => {
      const spyFade = jest.spyOn(component, 'onFadeFinished');
      fixture.debugElement
        .query(By.css('.toast'))
        .triggerEventHandler('@fadeAnimation.done', 'closing');
      fixture.detectChanges();
      expect(spyFade).toBeCalled();
    });

    it('should call "afterDisplay" method after fade', () => {
      const spyDisplay = jest.spyOn(component, 'afterDisplay');
      component.animationState = 'default';
      component.onFadeFinished('default');
      expect(spyDisplay).toBeCalled();
    });

    it('should call "close" method after fade', () => {
      const spyClose = jest.spyOn(component, 'close');
      component.animationState = 'closing';
      component.onFadeFinished('closing');
      expect(spyClose).toBeCalled();
    });

    it('should call nothing after fade', () => {
      const spyDisplay = jest.spyOn(component, 'afterDisplay');
      const spyClose = jest.spyOn(component, 'close');
      component.animationState = 'closing';
      component.onFadeFinished('default');
      expect(spyDisplay).toBeCalledTimes(0);
      expect(spyClose).toBeCalledTimes(0);
    });
  });
});
