import { PaginatedTreeGridDataSource } from '@bassetti-group/tx-web-core';
import { BehaviorSubject, of } from 'rxjs';
import { CollectionViewer } from '@angular/cdk/collections';

describe('PaginatedTreeGridDataSource', () => {
  let dataSource: PaginatedTreeGridDataSource<any>;
  const mockNode = { id: '1', children: [] };
  const mockNodes = [
    { id: '1', children: [{ id: '1.1', children: [] }] },
    { id: '2', children: [] },
  ];

  beforeEach(() => {
    dataSource = new PaginatedTreeGridDataSource(of(mockNodes), 'id');
  });

  describe('toggleNode', () => {
    it('should expand a collapsed node and trigger page update', () => {
      const spyTriggerPageUpdate = jest.spyOn(dataSource, 'triggerUpdatePageRequest');
      dataSource.toggleNode(mockNode);
      expect(spyTriggerPageUpdate).toHaveBeenCalled();
      expect(dataSource.isExpanded(mockNode)).toBe(true);
    });

    it('should collapse an expanded node and trigger page update', () => {
      const spyTriggerPageUpdate = jest.spyOn(dataSource, 'triggerUpdatePageRequest');
      dataSource.toggleNode(mockNode); // Expanding first
      dataSource.toggleNode(mockNode); // Now collapsing
      expect(spyTriggerPageUpdate).toHaveBeenCalledTimes(2);
      expect(dataSource.isExpanded(mockNode)).toBe(false);
    });

    it('should throw an error if primaryKey is not defined', () => {
      dataSource['primaryKey'] = undefined as any;
      expect(() => dataSource.toggleNode(mockNode)).toThrowError('primaryKey not defined');
    });
  });

  describe('isExpanded', () => {
    it('should return true if the node is expanded', () => {
      dataSource.toggleNode(mockNode); // Expanding node
      expect(dataSource.isExpanded(mockNode)).toBe(true);
    });

    it('should return false if the node is not expanded', () => {
      expect(dataSource.isExpanded(mockNode)).toBe(false);
    });

    it('should handle undefined primaryKey by logging an error', () => {
      console.error = jest.fn();
      dataSource['primaryKey'] = undefined as any;
      expect(dataSource.isExpanded(mockNode)).toBe(false);
      expect(console.error).toHaveBeenCalledWith('primaryKey not defined');
    });
  });

  describe('connect', () => {
    it('should filter tree data correctly based on expanded nodes', (done) => {
      const collectionViewer: CollectionViewer = {
        viewChange: new BehaviorSubject({ start: 0, end: 2 }),
      };

      dataSource.toggleNode(mockNodes[0]); // Expanding first node

      dataSource.connect(collectionViewer).subscribe((data: string | any[]) => {
        expect(data.length).toBe(3); // First node and its expanded child
        expect(data[0].id).toBe('1');
        expect(data[1].id).toBe('1.1');
        done();
      });
    });
  });
});
