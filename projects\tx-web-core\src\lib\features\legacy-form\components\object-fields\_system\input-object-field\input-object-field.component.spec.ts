import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxInputObjectFieldComponent } from './input-object-field.component';
import { AttributesMockService } from '../../../../testing.mock';
import {
  LegacyTxAttributeRight,
  TxAttributePoint,
} from '../../../../services/structure/models/attribute';
import { LegacyTxAttributesService } from '../../../../services/structure/services/attributes.service';
import { TxAttributeField } from '../../../../models/formConfiguration/businessClass/attribute-field';
import { LegacyTxDataType } from '../../../../services/structure/models/data';

const mockAttribute = new TxAttributePoint({
  id: 1,
  name: 'otherTest',
  option: { minLength: 2, maxLength: 10 },
  lowerBound: 0,
  upperBound: 100,
  dataType: LegacyTxDataType.ArchivedGraphic,
  idObjectType: 0,
  idAttributeParent: 0,
  tags: [],
  right: LegacyTxAttributeRight.None,
  isInherited: false,
  order: 0,
  idInheritedAttribute: 0,
  idLinkType: 0,
});
const mockField = new TxAttributeField();
const mockFieldNoAttr = new TxAttributeField();
const mockFieldMandatProp = new TxAttributeField();
mockField.assign({
  id: 1,
  attribute: mockAttribute,
  properties: {
    pattern: /^([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)$/,
    placeHolder: 'test',
  },
});
mockFieldNoAttr.assign({ id: 2 });
mockFieldMandatProp.assign({ id: 3, properties: { mandatory: true } });

describe('TxInputObjectFieldComponent', () => {
  let component: TxInputObjectFieldComponent;
  let fixture: ComponentFixture<TxInputObjectFieldComponent>;
  let element;
  let attributesService: LegacyTxAttributesService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxInputObjectFieldComponent],
      providers: [{ provide: LegacyTxAttributesService, useClass: AttributesMockService }],
    }).compileComponents();
    attributesService = TestBed.inject(LegacyTxAttributesService);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxInputObjectFieldComponent);
    component = fixture.componentInstance;
    element = fixture.nativeElement;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('testing initPropertiesFromField', () => {
    beforeEach(() => {
      component.field = mockField;
      component.initPropertiesFromField();
    });

    it('should assign pattern value from field', () => {
      expect(component.pattern).toEqual(/^([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)$/);
    });

    it('should assign placeHolder value from field', () => {
      expect(component.placeHolder).toBe('test');
    });

    it('should assign lowerBoundValue value from field', () => {
      expect(component.lowerBoundValue).toBe(0);
    });

    it('should assign upperBoundValue value from field', () => {
      expect(component.upperBoundValue).toBe(100);
    });
  });
});
