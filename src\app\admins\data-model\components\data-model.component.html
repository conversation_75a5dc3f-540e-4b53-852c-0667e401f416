<div class="admin-container">
  <mat-progress-bar
    *ngIf="isLoaderActive"
    mode="indeterminate"
    color="accent"
    style="position: absolute"></mat-progress-bar>
  <app-breadcrumd *ngIf="!isInsideRightPane"></app-breadcrumd>
  <div class="admin-content">
    <div class="h1-title">{{ 'admins.wording.dataModel' | translate }}</div>
    <fa-icon
      *ngIf="!isExplanationDisplayed"
      [matTooltip]="'tooltip.showExplanation' | translate"
      [icon]="['fal', 'question-circle']"
      size="lg"
      class="icon-explanation"
      (click)="getExplanation('dataModel', 'expDataModel', false)"
      txClickableElement></fa-icon>
    <button
      *ngIf="displayExportFunctionality"
      class="button-export"
      id="export-button"
      mat-flat-button
      color="accent"
      (click)="openRightPaneExport()">
      <div class="main-button-icon">
        {{ 'button.export' | translate }}
      </div>
    </button>
    <div class="dm-container-body">
      <div class="dm-container-left">
        <app-data-model-object-types
          [objectTypesFilteredIds]="objectTypesFilteredIds"
          (changeSelection)="changeObjectType($event)"></app-data-model-object-types>
      </div>
      <div class="dm-container-right">
        <tx-attributes-tree-grid
          [objectType]="objectType"
          (changeSelection)="changeAttribute($event)">
        </tx-attributes-tree-grid>
      </div>
    </div>
  </div>
  <ng-template #templateDataModel>
    <app-data-model-export (closePanel)="closePanel()"></app-data-model-export>
  </ng-template>
</div>
