import { waitForAsync } from '@angular/core/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxTreeComponent } from './tree.component';
import { ContextMenuAllModule, TreeViewAllModule } from '@syncfusion/ej2-angular-navigations';

describe('TxTreeComponent', () => {
  let component: TxTreeComponent;
  let fixture: ComponentFixture<TxTreeComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxTreeComponent],
      imports: [TreeViewAllModule, ContextMenuAllModule],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxTreeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
