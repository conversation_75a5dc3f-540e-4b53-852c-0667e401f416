import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TxChipsFieldComponent } from './chips-field.component';
import { MatInputModule } from '@angular/material/input';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatChipsModule } from '@angular/material/chips';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { CommonModule } from '@angular/common';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { Component } from '@angular/core';
import {
  MatChipEditInputHarness,
  MatChipInputHarness,
  MatChipRowHarness,
} from '@angular/material/chips/testing';
import { TestbedHarnessEnvironment } from '@angular/cdk/testing/testbed';
import { HarnessLoader } from '@angular/cdk/testing';
import { TxChip } from './models';
import { FaIconComponent, FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { MockComponent } from 'ng-mocks';
import { TxFileAttributeService } from '../../../data-access/files/file-attribute.service';
import { FileAttributeMockService } from '../../../data-access/files/file-attribute-testing.mock';

@Component({
  template: `<tx-chips-field
    [formControl]="control"
    [required]="true"
    [removable]="true"
    [editable]="true"
    [selectable]="true"
    [maxChipDisplay]="3"
    [chipsIcon]="'user'">
  </tx-chips-field>`,
})
class TestChipsFieldComponent {
  control = new FormControl<TxChip[]>([{ type: 'standard', name: 'test', removable: true }]);
}

describe('TxChipsFieldComponent', () => {
  let component: TxChipsFieldComponent;
  let fixture: ComponentFixture<TxChipsFieldComponent>;
  let HostComponent: TestChipsFieldComponent;
  let hostFixture: ComponentFixture<TestChipsFieldComponent>;
  let loaderTest: HarnessLoader;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      providers: [{ provide: TxFileAttributeService, useClass: FileAttributeMockService }],
      imports: [
        TxChipsFieldComponent,
        MatChipsModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatInputModule,
        CommonModule,
        FontAwesomeModule,
        NoopAnimationsModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
      ],
      declarations: [TestChipsFieldComponent, MockComponent(FaIconComponent)],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxChipsFieldComponent);
    component = fixture.componentInstance;
    hostFixture = TestBed.createComponent(TestChipsFieldComponent);
    HostComponent = hostFixture.componentInstance;
    fixture.detectChanges();
    hostFixture.detectChanges();
    loaderTest = TestbedHarnessEnvironment.loader(hostFixture);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
    expect(HostComponent).toBeTruthy();
  });

  it('input is required', async () => {
    const chip = await loaderTest.getHarness(MatChipRowHarness);
    await chip.remove();
    expect(HostComponent.control.errors).toEqual({ required: true });
  });

  it('should remove chip', async () => {
    const chip = await loaderTest.getHarness(MatChipRowHarness);
    await chip.remove();
    expect(HostComponent.control.value).toStrictEqual([]);
  });

  it('should edit chip', async () => {
    const chip = await loaderTest.getHarness(MatChipRowHarness);
    await chip.startEditing();
    const chipEdit = await loaderTest.getHarness(MatChipEditInputHarness);
    await chipEdit.setValue('test1');
    await chip.finishEditing();
    expect(HostComponent.control.value).toStrictEqual([
      { type: 'standard', name: 'test1', removable: true },
    ]);
  });

  it('should add chip', async () => {
    const input = await loaderTest.getHarness(MatChipInputHarness);
    await input.setValue('test2');
    await input.blur();
    expect(HostComponent.control.value).toStrictEqual([
      { type: 'standard', name: 'test', removable: true },
      {
        type: 'standard',
        name: 'test2',
        removable: true,
        selectable: true,
        selected: false,
        icon: ['fal', 'user'],
      },
    ]);
  });

  it('should not add chip when max chips reached', async () => {
    const input = await loaderTest.getHarness(MatChipInputHarness);
    await input.setValue('test3');
    await input.blur();
    await input.setValue('test4');
    await input.blur();
    await input.setValue('test5');
    await input.blur();
    expect(HostComponent.control.value).toStrictEqual([
      { type: 'standard', name: 'test', removable: true },
      {
        type: 'standard',
        name: 'test3',
        removable: true,
        selectable: true,
        selected: false,
        icon: ['fal', 'user'],
      },
      {
        type: 'standard',
        name: 'test4',
        removable: true,
        selectable: true,
        selected: false,
        icon: ['fal', 'user'],
      },
    ]);
  });
});
