.audit-container {
  margin-top: 16px;
  height: calc(100% - 54px);

  .audit-section-title {
    display: inline-block;
    width: calc(100% - 28px);
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 5px;
  }

  .audit-container-left {
    display: inline-block;
    height: 100%;
    width: Calc(30% - 16px);
    padding-right: 16px;
  }

  .audit-container-right {
    display: inline-block;
    height: 100%;
    width: Calc(70% - 16px);
    padding-left: 16px;
    vertical-align: top;

    .audit-treegrid {
      height: Calc(100% - 77px);
      overflow: auto;
      .tree-grid .tree-grid__grid {
        .mat-mdc-row {
          height: auto;
          min-height: 2rem;
          .mat-mdc-cell {
            position: relative;
          }
        }
      }
    }

    .e-grid .e-rowcell {
      padding: 3px 21px !important;
      line-height: 16px !important;
      height: 10px !important;
      position: relative;
    }

    td.e-rowcell {
      border-color: transparent !important;
    }
    .e-headercelldiv {
      padding-bottom: 10px !important;
      padding-top: 10px !important;
      line-height: 8px !important;
    }
    .e-treerowcell {
      font-weight: 600 !important;
      font-size: 15px !important;
    }
    .e-headertext {
      font-size: 13px !important;
    }
  }
}

.audit-settings-label {
  width: Calc(100% - 20px);
  line-height: 20px;
  padding-left: 8px;
  flex: 1;
}

.audit-border {
  position: relative;
  display: flex;
  row-gap: 16px;
  padding: 16px;
  margin-bottom: 32px;
  flex-direction: column;

  .audit-settings-line {
    display: flex;
    align-items: baseline;
    margin-bottom: 8px;

    fa-icon.audit-icon-line {
      width: 20px !important;
      text-align: center;
    }

    .audit-settings-title {
      padding-left: 0px;
      font-size: 18px;
    }
    .label-nowrap {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .audit-settings-version {
      margin-right: 3px;
    }
    .audit-settings-revision {
      font-size: 10px;
      font-weight: 400;
    }

    .audit-trash-button {
      width: 20px;
      padding: 5px;
      text-align: center;
      border-radius: 20px;
      cursor: pointer;

      .audit-trash-icon {
        font-size: 12px;
      }
    }
  }
}

.audit-button-right {
  margin: 8px 0px 0px 8px !important;
  float: right;
}

.audit-cell-status {
  position: absolute;
  inset: 0px;
  font-weight: 600;
  text-transform: capitalize;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
}

.audit-result {
  text-overflow: ellipsis;
  overflow: hidden;
}
.audit-result-link {
  font-size: 12px;
}

/* Ellipsis loading */
.audit-loading-ellipsis {
  display: flex;
  font-weight: 600;
}
.audit-loading-ellipsis:after {
  overflow: hidden;
  display: inline-block;
  vertical-align: bottom;
  -webkit-animation: ellipsis steps(4, end) 900ms infinite;
  animation: ellipsis steps(4, end) 900ms infinite;
  content: '\2026'; /* ascii code for the ellipsis character */
  width: 0px;
}
@keyframes ellipsis {
  to {
    width: 1.25em;
  }
}
@-webkit-keyframes ellipsis {
  to {
    width: 1.25em;
  }
}
