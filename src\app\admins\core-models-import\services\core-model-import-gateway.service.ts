import { Observable } from 'rxjs';
import {
  CoreModelImportHistoryDTO,
  CoreModelsArchiveSummaryDTO,
  ImportedDTO,
  TestedImportDTO,
} from '../models/core-models-import.dto';

export abstract class CoreModelImportGatewayService {
  abstract loadHistory(): Observable<CoreModelImportHistoryDTO['importHistory']>;
  abstract validateArchive(file: File): Observable<CoreModelsArchiveSummaryDTO>;
  abstract testImport(file: File): Observable<TestedImportDTO>;
  abstract import(file: File): Observable<ImportedDTO>;
}
