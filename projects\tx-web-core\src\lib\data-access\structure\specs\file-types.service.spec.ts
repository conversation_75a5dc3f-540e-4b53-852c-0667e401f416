import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MOCK_TX_CONFIG_SERVICE, TxConfigService } from '../../config';
import { TxFileTypesService } from '../file-types.service';
import { MockProvider } from 'ng-mocks';
import { TxCommonService } from '../common.service';
import { MOCK_OBJECT_TYPE_ICON_SERVICE, TxObjectTypeIconService } from '../../session';

describe('Service: FileTypes', () => {
  let service: TxFileTypesService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        TxFileTypesService,
        MockProvider(TxConfigService, MOCK_TX_CONFIG_SERVICE, 'useValue'),
        Mock<PERSON>rovider(TxObjectTypeIconService, MOCK_OBJECT_TYPE_ICON_SERVICE, 'useValue'),
      ],
    });
    service = TestBed.inject(TxFileTypesService);
    TestBed.inject(TxConfigService);
    TestBed.inject(HttpTestingController);
    TestBed.inject(TxCommonService);
  });

  beforeEach(() => {});

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
