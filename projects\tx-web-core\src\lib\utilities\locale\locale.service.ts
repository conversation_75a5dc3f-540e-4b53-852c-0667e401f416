import { FormatWidth, getLocaleDateFormat } from '@angular/common';
import { Injectable } from '@angular/core';
import { MatDateFormats } from '@angular/material/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LocaleService {
  public $localeChanged: Observable<string>;

  private dateFormat: string | null = null;
  private locale = 'en'; // Default locale
  private localeChangedSub: BehaviorSubject<string> = new BehaviorSubject('en');

  constructor() {
    this.$localeChanged = this.localeChangedSub.asObservable();
  }

  setDateFormat(format: string | null) {
    this.dateFormat = format;
  }

  getDateFormat(): string {
    if (this.dateFormat) {
      return this.dateFormat;
    } else {
      return getLocaleDateFormat(this.locale, FormatWidth.Short);
    }
  }

  getCustomDateFormats(): MatDateFormats {
    return {
      parse: {
        dateInput: 'input',
      },
      display: {
        dateInput: this.dateFormat ?? 'YYYY-MM-DD',
        monthYearLabel: 'MMM YYYY',
        dateA11yLabel: 'LL',
        monthYearA11yLabel: 'MMMM YYYY',
      },
    };
  }

  setLocale(locale: string) {
    this.locale = locale;
    this.localeChangedSub.next(this.locale);
  }

  getLocale(): string {
    return this.locale;
  }
}
