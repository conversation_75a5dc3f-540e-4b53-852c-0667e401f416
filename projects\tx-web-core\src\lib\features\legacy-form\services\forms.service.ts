import { LegacyTxDataLink } from './structure/models/data';
import { TxAttributeField } from './../models/formConfiguration/businessClass/attribute-field';
import { TxVirtualAttributeField } from './../models/formConfiguration/businessClass/virtual-attribute-field';
import { Renderer2 } from '@angular/core';
import { Injectable } from '@angular/core';
import { TxFormConfiguration } from '../models/formConfiguration/businessClass/form-configuration';
import { BehaviorSubject, Observable } from 'rxjs';
import { TxObjectConfiguration } from '../models/object-configuration';
import { TxStep } from '../models/step.model';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { TxTabAttributeField } from '../models/formConfiguration/businessClass/tab-attribute-field';
import { TxGroupAttributeField } from '../models/formConfiguration/businessClass/group-attribute-field';
import { TxEditionMode } from '../models/formConfiguration/businessClass/form-enum';
import { TxFormConfigFiller } from '../models/formConfiguration/executionClass/form-config-filler';
import { _isNumberValue } from '@angular/cdk/coercion';
import { LegacyTxObjectTypeService } from './structure/services/object-type.service';
import { LegacyTxAttributesService } from './structure/services/attributes.service';
import { TxDataService } from './structure/services/data.service';
import { LegacyTxAttribute, LegacyTxLinkDisplayMode } from './structure/models/attribute';
import { LegacyTxData, LegacyTxDataType } from './structure/models/data';
import { TxFormSettings } from '../models/formConfiguration/businessClass/form-settings';
import { TxLinkTypeService } from './structure/services/link-type.service';

@Injectable({
  providedIn: 'root',
})
export class LegacyTxFormsService {
  public formConfigs: Map<string, TxFormConfiguration>;
  public objectConfigs: Map<string, TxObjectConfiguration>;
  public formConfigFiller: TxFormConfigFiller;
  public formRulesToUse: any;
  structureReady = false;

  // defaultFormRules =  {
  //   'groupsClosedByDefault': false,
  //   'hideTitleImageThumbnail': false
  // }

  private loadConfigFromObjectType(
    idObjectType: number,
    formSettings: TxFormSettings = new TxFormSettings(),
    editionMode: TxEditionMode = TxEditionMode.write
  ): Observable<TxObjectConfiguration> {
    return new Observable<TxObjectConfiguration>((observer) => {
      this.searchObjectConfig(idObjectType, editionMode, formSettings).subscribe((config) => {
        observer.next(config);
        observer.complete();
      });
    });
  }

  private loadConfigFromTag(
    tag: string,
    formSettings: TxFormSettings,
    editionMode: TxEditionMode = TxEditionMode.write
  ): Observable<TxObjectConfiguration> {
    return new Observable<TxObjectConfiguration>((observer) => {});
  }

  private loadConfigFromAttributes(
    attributesParam: string[] | number[],
    formSettings: TxFormSettings = new TxFormSettings(),
    editionMode: TxEditionMode = TxEditionMode.write,
    keepStructureOrder = false
  ): Observable<TxObjectConfiguration> {
    return new Observable<TxObjectConfiguration>((observer) => {
      this.attributesService
        .listAttributes(attributesParam)
        .subscribe((attributes: LegacyTxAttribute[]) => {
          this.searchObjectConfig(
            attributes,
            editionMode,
            formSettings,
            keepStructureOrder
          ).subscribe((objectConfig: TxObjectConfiguration) => {
            observer.next(objectConfig);
            observer.complete();
          });
        });
    });
  }

  private addForm(tag: string, editionMode: TxEditionMode): TxFormConfiguration {
    const formConfig = new TxFormConfiguration(tag, editionMode);
    this.formConfigs.set(tag, formConfig);
    return formConfig;
  }

  private addObjectConfig(tag: string): TxObjectConfiguration {
    const objectConfig = new TxObjectConfiguration(tag);
    this.objectConfigs.set(tag, objectConfig);
    return objectConfig;
  }

  private searchObjectConfig(
    param: number | LegacyTxAttribute[],
    readOrWriteMode: TxEditionMode,
    formSettings: TxFormSettings,
    keepStructureOrder = false,
    searchForAParameterizedConfiguration = true
  ): Observable<TxObjectConfiguration> {
    let tag: string;
    let objectConfig: TxObjectConfiguration | undefined;
    const fromIdObjectType = _isNumberValue(param);
    const attributes = fromIdObjectType ? [] : (param as LegacyTxAttribute[]);

    return new Observable<TxObjectConfiguration>((observer) => {
      if (!attributes.length && !fromIdObjectType) {
        observer.error('Atributes are missing in the form configuration');
        return;
      }

      const idObjectType = fromIdObjectType ? (param as number) : attributes[0].idObjectType;
      const key = fromIdObjectType ? idObjectType : attributes.map((a) => a.id).join('-');
      const formConfigListObject: any = { txFormsConfigurations: [] };

      if (searchForAParameterizedConfiguration) {
        // todo
      } else {
        objectConfig = undefined;
      }
      tag = 'objectConfig_' + JSON.stringify(readOrWriteMode) + '_' + key;
      if (formSettings) {
        if (formSettings.mandatoriesAttTags) {
          tag = tag + '_mandatories=' + formSettings.mandatoriesAttTags;
        }
        if (formSettings.formRightManagement) {
          tag = tag + '_fwm=' + formSettings.formRightManagement;
        }
      }
      if (!this.objectConfigs.has(tag)) {
        objectConfig = this.addObjectConfig(tag);
        const step = new TxStep();
        this.searchForm(param, readOrWriteMode, formSettings, keepStructureOrder).subscribe(
          (form) => {
            step.formConfig = form;
            objectConfig?.steps.push(step);
            observer.next(objectConfig);
            observer.complete();
          }
        );
      } else {
        observer.next(this.getConfig(tag));
        observer.complete();
      }
    });
  }

  private searchForm(
    param: LegacyTxAttribute[] | number,
    editionMode: TxEditionMode,
    formSettings: TxFormSettings,
    keepStructureOrder = false,
    searchForAParameterizedConfiguration = true
  ): Observable<TxFormConfiguration> {
    let tag: string;
    let formConfig: TxFormConfiguration | undefined;
    const formConfigListObject: any = { txFormsConfigurations: [] };
    const fromIdObjectType = _isNumberValue(param);
    const attributes = fromIdObjectType ? [] : (param as LegacyTxAttribute[]);
    const idObjectType = fromIdObjectType ? (param as number) : attributes[0].idObjectType;
    const key = fromIdObjectType ? idObjectType : attributes.map((a) => a.id).join('-');

    return new Observable<TxFormConfiguration>((observer) => {
      if (searchForAParameterizedConfiguration) {
        // get all existing form configuration
        // formConfigListObject = this.configService.getFormsConfigsList();
        for (const formConfigObj of formConfigListObject.txFormsConfigurations) {
          if (!formConfigObj.replaceDefault) {
            continue;
          }
          if (formConfigObj.editionMode !== editionMode) {
            continue;
          }
          // TODO: if !isValid continue
          if (formConfigObj.objectType.id !== idObjectType) {
            continue;
          }
          // TODO: continuer cette partie si le formulaire existe dans les config existantes
        }
      } else {
        formConfig = undefined;
      }
      // if a form config was found
      if (formConfig) {
        // Generating a unique key. This form will be deleted when not usefull anymore.
        // TODO: Generer la clé
      } else {
        // Searching for the autogenerated default configuration for this Object Type.
        tag = 'form_' + JSON.stringify(editionMode) + '_' + key;
        if (formSettings) {
          if (formSettings.mandatoriesAttTags) {
            tag = tag + '_mandatories=' + formSettings.mandatoriesAttTags;
          }
          if (formSettings.formRightManagement) {
            tag = tag + '_fwm=' + formSettings.formRightManagement;
          }
        }
        if (!this.formConfigs.has(tag)) {
          formConfig = this.addForm(tag, editionMode);
          formConfig.objectType = this.objectTypeService.getObjectType(idObjectType);
          this.formConfigFiller.formSettings = formSettings;
          this.formConfigFiller.globalRules = this.formRulesToUse;

          if (fromIdObjectType) {
            this.formConfigFiller
              .loadAttributesAndFillForm(
                formConfig,
                idObjectType,
                editionMode === TxEditionMode.read
              )
              .subscribe((formConfigFilled) => {
                this.initializeFormMetadata(formConfigFilled, formSettings);
                observer.next(formConfigFilled);
                observer.complete();
              });
          } else {
            this.formConfigFiller.fillForm(
              formConfig,
              attributes,
              true,
              true,
              editionMode === TxEditionMode.read,
              keepStructureOrder
            );
            this.initializeFormMetadata(formConfig, formSettings);
            observer.next(formConfig);
            observer.complete();
          }
        } else {
          observer.next(this.formConfigs.get(tag));
          observer.complete();
        }
      }
    });
  }

  constructor(
    private objectTypeService: LegacyTxObjectTypeService,
    public attributesService: LegacyTxAttributesService,
    private linkTypeService: TxLinkTypeService,
    private dataService: TxDataService
  ) {
    this.formConfigs = new Map<string, TxFormConfiguration>();
    this.objectConfigs = new Map<string, TxObjectConfiguration>();
    this.formConfigFiller = new TxFormConfigFiller(attributesService, linkTypeService);
    this.linkTypeService.linkTypesLoaded.subscribe((value) => {
      this.structureReady = value;
    });
  }

  start(formRules: any) {
    this.formRulesToUse = formRules;
  }

  loadConfig(
    idObject: number,
    tag: string,
    executionSettings: Map<string, string>,
    formSettings: TxFormSettings = new TxFormSettings(),
    editionMode: TxEditionMode,
    idObjectType: number,
    attributesParam: string[] | number[],
    keepStructureOrder: boolean = false
  ): Observable<TxObjectConfiguration> {
    return new Observable<TxObjectConfiguration>((observer) => {
      // while (!this.structureReady) {
      //   console.log('structure not loaded yet');
      //   setTimeout(() => {
      //     return this.loadConfig(idObject, tag, executionSettings, formSettings, editionMode, idObjectType, attributesParam);
      //   }, 100);
      // }

      if (tag) {
        this.loadConfigFromTag(tag, formSettings, editionMode).subscribe((config) => {
          observer.next(config);
          observer.complete();
        });
      } else if (attributesParam?.length) {
        this.loadConfigFromAttributes(attributesParam, formSettings, editionMode).subscribe(
          (config) => {
            observer.next(config);
            observer.complete();
          }
        );
      } else if (idObjectType) {
        this.loadConfigFromObjectType(idObjectType, formSettings, editionMode).subscribe(
          (config) => {
            observer.next(config);
            observer.complete();
          }
        );
      }

      // this.searchObjectConfig(idObjectType, editionMode, formSettings).subscribe((config) => {

      //   observer.next(config);
      //   observer.complete();
      // });
    });
    // if (!this.structureReady) {
    //   console.log('structure not loaded yet');
    //   setTimeout(() => {
    //     this.loadConfig(idObject, tag, executionSettings, formSettings, editionMode, idObjectType, attributesParam);
    //   }, 100);
    // } else {
    //   if (tag) {
    //     return this.loadConfigFromTag(tag, formSettings, editionMode);
    //   }

    //   if (attributesParam.length) {
    //     return this.loadConfigFromAttributes(attributesParam, formSettings, editionMode);
    //   }

    //   if (idObjectType) {
    //     return this.loadConfigFromObjectType(idObjectType, formSettings, editionMode);
    //   }
    // }
  }

  loadData(
    formConfigTag: string,
    idObject: number,
    tabIndex: number,
    maxReturnedResult: number = 0
  ): Observable<LegacyTxData[]> {
    return new Observable<LegacyTxData[]>((observer) => {
      const _fillAttributeSetLevel = (fields: TxVirtualAttributeField[], levels: any[]) => {
        fields.forEach((f) => {
          if (f.attribute) {
            if (f.attribute.dataType === LegacyTxDataType.Tab) {
              return;
            }

            if (f.attribute.dataType === LegacyTxDataType.Group) {
              _fillAttributeSetLevel((f as TxGroupAttributeField).children, levels);
            } else {
              const level: any = {
                idAttribute: f.attribute.idInheritedAttribute || f.attribute.id,
              };
              if (this.attributesService.isLink(f.attribute.dataType)) {
                const attributeField = f as TxAttributeField;
                if (attributeField.linkField?.linkViewMode === LegacyTxLinkDisplayMode.Matrix) {
                  level.linkedLevels = [];
                  _fillAttributeSetLevel(attributeField.linkField.linkedFields, level.linkedLevels);
                } else if (
                  attributeField.linkField?.linkViewMode === LegacyTxLinkDisplayMode.OneFieldPerRaw
                ) {
                  // find the root level
                  let rootLevel: any = _findNestedLevel(rootLevels, level.idAttribute);

                  if (!rootLevel) {
                    rootLevel = level;
                    levels.push(rootLevel);
                    attributesIds.push(rootLevel?.idAttribute);
                  }

                  // then add the linked attribute
                  rootLevel.linkedLevels = [];
                  _fillAttributeSetLevel(
                    attributeField.linkField.linkedFields,
                    rootLevel.linkedLevels
                  );

                  return;
                }
              }
              levels.push(level);
              attributesIds.push(level.idAttribute);
            }
          }
        });
      };

      const _findNestedLevel = function (levels: any[], idAttribute: number) {
        let level: any;

        levels.find((l) => {
          if (l.idAttribute === idAttribute) {
            level = l;
          } else if (l.linkedLevels) {
            level = _findNestedLevel(l.linkedLevels, idAttribute);
          }

          if (level) {
            return true;
          }
        });

        return level;
      };

      const _findData = function (data: LegacyTxData[], idAttribute: number, idObject: number) {
        return data.find((d) => d.idAttribute === idAttribute && d.idObject === idObject);
      };

      const formConfig = this.getFormConfig(formConfigTag);
      const attributesIds: any[] = [];

      const attributeSetLevel = {
        attributeSet: {
          levels: [],
        },
        idObjects: [idObject],
      };

      const rootLevels: any[] = attributeSetLevel.attributeSet.levels;

      if (formConfig?.tabs) {
        const tab = formConfig?.tabs[tabIndex];

        if (tab) {
          // generate attribute set level of attributes
          if (tab.children) {
            _fillAttributeSetLevel(tab.children, rootLevels);
          }
        }
      }

      this.dataService
        .read(idObject, attributesIds, maxReturnedResult, attributeSetLevel)
        .subscribe((data) => {
          // console.log('data', data);
          const modifiedData: LegacyTxData[] = [];

          rootLevels.forEach((rootLevel) => {
            const dataLevel = _findData(data, rootLevel?.idAttribute as number, idObject);
            const attribute = this.attributesService.findAttribute(rootLevel.idAttribute);

            if (dataLevel) {
              if (rootLevel.linkedLevels?.length) {
                const dataLinkLevel = dataLevel as LegacyTxDataLink;

                dataLinkLevel.linkedObjects?.forEach((lkdObject) => {
                  (rootLevel.linkedLevels as any[]).forEach((lindedLevel) => {
                    const linkedData = _findData(
                      data,
                      lindedLevel.idAttribute,
                      lkdObject.id as number
                    );
                    if (linkedData) {
                      if (!lkdObject.data) {
                        lkdObject.data = [];
                      }
                      lkdObject.data.push(linkedData);
                    }
                  });
                });
              }
              modifiedData.push(dataLevel);
            }
          });

          observer.next(modifiedData);
          observer.complete();
        });
    });
  }

  updateTheme(renderer: Renderer2, themeClass: string, isStyleLoaded: BehaviorSubject<boolean>) {
    // class on body
    renderer.addClass(document.getElementsByTagName('body').item(0), themeClass);
    // inject style
    const link = document.createElement('link');
    const head = document.getElementsByTagName('head')[0];
    link.type = 'text/css';
    link.rel = 'stylesheet';
    link.className = 'syncTheme';
    link.href = `./assets/tx-web-core/form/syncfusion-theme/material-${themeClass}.css`;
    link.onload = () => {
      isStyleLoaded.next(true);
    };
    head.appendChild(link);
  }

  initializeFormMetadata(formConfig: TxFormConfiguration, formSettings: TxFormSettings): void {
    let mandatoriesByTags: any[] = [];
    let mandatoriesByIds: any[] = [];
    if (formSettings.mandatoriesAttTags) {
      mandatoriesByTags = formSettings.mandatoriesAttTags;
    }
    if (formSettings.mandatoriesIdsAtt) {
      mandatoriesByIds = formSettings.mandatoriesIdsAtt;
    }

    if (formConfig.editionMode === TxEditionMode.write) {
      this.formConfigFiller.setMandatories(formConfig, mandatoriesByIds, mandatoriesByTags);
    }
    if (formSettings.formRightManagement) {
      this.formConfigFiller.setRightManagement(formConfig, formSettings.formRightManagement);
    }
  }

  getConfig(tag: string): TxObjectConfiguration {
    return this.objectConfigs.get(tag) as unknown as TxObjectConfiguration;
  }

  getFormConfig(tag: string): TxFormConfiguration {
    return this.formConfigs.get(tag) as TxFormConfiguration;
  }

  toFormGroup(tabField: TxTabAttributeField): FormGroup {
    const group: any = {};
    function fillGroup(groupField: TxGroupAttributeField, parentMandatory: boolean = false) {
      for (const attrField of groupField.children) {
        let mandatory = false;
        const validators = [];

        for (const property of attrField.properties) {
          if (property.name === 'mandatory' && property.value === 'true') {
            mandatory = true;
            continue;
          }
        }

        if (parentMandatory || mandatory) {
          validators.push(Validators.required);
        }

        if (attrField.attribute.dataType === LegacyTxDataType.Group) {
          fillGroup(attrField as TxGroupAttributeField, mandatory);
          continue;
        }

        group[attrField.id] = new FormControl('', validators);

        if (attrField.attribute.dataType === LegacyTxDataType.DecRange) {
          group[attrField.id.toString() + 'max'] = new FormControl('', validators);
        }

        if (attrField.attribute.dataType === LegacyTxDataType.DecRangeMean) {
          group[attrField.id.toString() + 'max'] = new FormControl('', validators);
          group[attrField.id.toString() + 'mean'] = new FormControl('', validators);
        }
      }
    }
    fillGroup(tabField);
    return new FormGroup(group);
  }
}
