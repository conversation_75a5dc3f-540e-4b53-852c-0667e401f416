@use 'src/app/styles/variables.scss' as variables;
:host {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 3.125rem minmax(50vh, calc(95vh - 3.125rem));
  width: 100%;
  height: 94.4vh;
  overflow: auto;
  box-sizing: border-box;
  * {
    box-sizing: border-box;
  }
  .breadcrumd {
    grid-column: 1;
    grid-row: 1;
  }
  .spinner-container:nth-child(2) {
    grid-column: 1;
    grid-row: 1 / 3;
    z-index: 100;
    height: 94.4vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .content {
    grid-column: 1;
    grid-row: 2;
    padding: 0 2rem;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows:
      3.125rem minmax(calc(52vh - 3.125rem), calc(100% - 3.125rem))
      variables.$responsive-bottom-margin;
    header {
      .title-button-actions {
        display: flex;
        justify-content: space-between;
        h1 {
          margin: 0;
        }

        .actions-container {
          display: flex;
          > button {
            margin-inline-start: 0.313rem;
          }
        }
      }
      mat-divider {
        margin-top: 0.5rem;
      }
      margin-bottom: 0.625rem;
    }
    .tab-container {
      padding-top: 1rem;
      height: 100%;
    }
  }
}
