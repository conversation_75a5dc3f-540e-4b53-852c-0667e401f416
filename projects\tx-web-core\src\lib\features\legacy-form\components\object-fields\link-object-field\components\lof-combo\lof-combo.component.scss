.search-tree-link-object .e-treeview {
  display: block !important;
}

.search-tree-link-object {
  display: block !important;
}

.hide-item {
  display: none;
}

.e-dropdowntree .e-icons {
  background-color: #f5f4f4 !important;
}

.filtered-tree-object {
  border: 0px solid lightgrey !important;
}

.mandatory-label-error-field .e-float-text::after {
  content: ' *';
}

.mandatory-label .e-float-text::after {
  content: ' *';
}

.field-error-container {
  height: 13px;
  .field-error {
    font-size: 10.5px;
  }
}
