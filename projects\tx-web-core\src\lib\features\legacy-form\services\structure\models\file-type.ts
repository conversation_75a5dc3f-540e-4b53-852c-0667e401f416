export enum LegacyTxFileIndexType {
  // No index
  None,
  // An index specific to the file type
  FileType,
  // A global index
  Global,
}

export class LegacyTxFileType {
  public id: number;
  public basename: string;
  public isBasenameModifiable: boolean;
  public isVersioned: boolean;
  public includeDate: boolean;
  public relativeDirectory: string;
  public isReadOnly: boolean;
  public indexTitle: string;
  public isAlphabeticalIndex: boolean;
  public versionTitle: string;
  public isAlphabeticalVersion: boolean;
  public isIndexModifiable: boolean;
  public fileIndexType: LegacyTxFileIndexType;
  public name: string;
  public tags: string[];
  public explanation: string;

  constructor(fileType: LegacyTxFileType) {
    this.id = fileType.id;
    this.basename = fileType.basename;
    this.isBasenameModifiable = fileType.isBasenameModifiable;
    this.isVersioned = fileType.isVersioned;
    this.includeDate = fileType.includeDate;
    this.relativeDirectory = fileType.relativeDirectory;
    this.isReadOnly = fileType.isReadOnly;
    this.indexTitle = fileType.indexTitle;
    this.isAlphabeticalIndex = fileType.isAlphabeticalIndex;
    this.versionTitle = fileType.versionTitle;
    this.isAlphabeticalVersion = fileType.isAlphabeticalVersion;
    this.isIndexModifiable = fileType.isIndexModifiable;
    this.fileIndexType = fileType.fileIndexType;
    this.name = fileType.name;
    this.tags = fileType.tags;
    this.explanation = fileType.explanation;
  }
}
