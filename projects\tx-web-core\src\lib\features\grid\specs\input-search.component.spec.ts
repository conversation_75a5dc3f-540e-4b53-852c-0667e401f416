import {
  ComponentFixture,
  fakeAsync,
  flush,
  TestBed,
  tick,
  waitForAsync,
} from '@angular/core/testing';

import { InputSearchComponent, InputSearchEventInfo } from '../input-search/input-search.component';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { By } from '@angular/platform-browser';
import { Component } from '@angular/core';

@Component({
  selector: 'app-host-component',
  template: `<tx-input-search
    [isFiltered]="isFiltered"
    [focusInput]="focusInput"
    [fieldWidth]="'100%'"
    [placeholderValue]="'placeholder value test'"
    (searchChanged)="search($event)"
    (filterChanged)="addFilter($event)"
    (searchOrFilterCleared)="clearSearchOrFilter()">
  </tx-input-search>`,
})
class TestHostComponent {
  public isFiltered = false;
  public focusInput = false;

  search(inputSearchEventInfo: InputSearchEventInfo) {
    return;
  }
  addFilter(text: string) {
    return;
  }
  clearSearchOrFilter() {
    return;
  }
}

describe('InputSearchComponent', () => {
  let component: InputSearchComponent;
  let hostComponent: TestHostComponent;
  let hostFixture: ComponentFixture<TestHostComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TestHostComponent],
      imports: [
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
        MatFormFieldModule,
        MatInputModule,
        FontAwesomeTestingModule,
        NoopAnimationsModule,
        InputSearchComponent,
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    hostFixture = TestBed.createComponent(TestHostComponent);
    hostComponent = hostFixture.componentInstance;
    hostFixture.detectChanges();
    component = hostFixture.debugElement.children[0].componentInstance;
  });

  it('should create', () => {
    expect(hostComponent).toBeTruthy();
    expect(component).toBeTruthy();
  });

  describe('template display', () => {
    it('should receive the correct width ', () => {
      const width = hostFixture.debugElement.query(By.css('mat-form-field')).nativeElement.style
        .width;
      expect(width).toBe('100%');
    });

    it('should receive the correct placeholder value ', () => {
      expect(component.inputSearch?.nativeElement.placeholder).toBe('placeholder value test');
    });

    it('should not show remove icon if the input value is empty', () => {
      if (component.inputSearch) {
        component.inputSearch.nativeElement.value = '';
      }
      hostFixture.detectChanges();
      expect(hostFixture.debugElement.query(By.css('.input-icon-remove'))).toBeFalsy();
    });

    it('should show remove icon if the input value is not empty', () => {
      const inputEl = hostFixture.debugElement.query(By.css('input'));
      inputEl.nativeElement.value = 'test';
      inputEl.triggerEventHandler('input', { target: inputEl.nativeElement });
      hostFixture.detectChanges();
      expect(hostFixture.debugElement.query(By.css('.input-icon-remove'))).toBeTruthy();
    });
  });

  describe('ngOnChanges', () => {
    let spySearch: jest.SpyInstance<void, [inputSearchEventInfo: InputSearchEventInfo]>;
    beforeEach(() => {
      component.inputValue = 'value';
      hostFixture.detectChanges();
      spySearch = jest.spyOn(hostComponent, 'search');
    });

    it('should not start any search if the input value didnt change ', () => {
      const changes = {
        inputValue: {
          currentValue: 'value',
          previousValue: 'value',
          firstChange: false,
          isFirstChange: () => true,
        },
      };
      component.ngOnChanges(changes);
      expect(spySearch).not.toBeCalled();
    });

    it('should start a new search if the input value do change ', () => {
      const changes = {
        inputValue: {
          currentValue: 'new value',
          previousValue: 'value',
          firstChange: false,
          isFirstChange: () => true,
        },
      };
      component.ngOnChanges(changes);
      expect(spySearch).toBeCalled();
    });
  });

  describe('onKeyUpSearch by triggering keyup event', () => {
    let enterEvent: KeyboardEvent;
    let notEnterEvent: KeyboardEvent;
    let spySearch: jest.SpyInstance<void, [inputSearchEventInfo: InputSearchEventInfo]>;
    beforeEach(() => {
      enterEvent = new KeyboardEvent('keyup', { code: 'Enter' });
      notEnterEvent = new KeyboardEvent('keyup', { key: 'm' });
      spySearch = jest.spyOn(hostComponent, 'search');
    });

    it('should call onKeyUpSearch for any keyup event', () => {
      const spyOnKeyUpSearch = jest.spyOn(component, 'onKeyUpSearch');
      hostFixture.debugElement.query(By.css('input')).nativeElement.dispatchEvent(notEnterEvent);
      expect(spyOnKeyUpSearch).toBeCalledWith(notEnterEvent);
    });

    it('should only call the search method if enter key is pressed', () => {
      hostFixture.debugElement.query(By.css('input')).nativeElement.dispatchEvent(enterEvent);
      expect(spySearch).toBeCalledWith({
        event: enterEvent,
        inputSearch: component.inputSearch,
      });
    });

    it('should not call the search method when other key is pressed', () => {
      hostFixture.debugElement.query(By.css('input')).nativeElement.dispatchEvent(notEnterEvent);
      expect(spySearch).not.toBeCalled();
    });
  });

  describe('onInputSearchChanged by triggering input event', () => {
    it('should call the search method with the correct argument', () => {
      const spySearch = jest.spyOn(hostComponent, 'search');
      hostFixture.debugElement
        .query(By.css('input'))
        .nativeElement.dispatchEvent(new Event('input'));
      expect(spySearch).toBeCalledWith({
        event: new Event('input'),
        inputSearch: component.inputSearch,
      });
    });
  });

  describe('keyupStream if isFiltered is set to false', () => {
    let spyFilter: jest.SpyInstance<void, [text: string]>;
    beforeEach(() => {
      spyFilter = jest.spyOn(hostComponent, 'addFilter');
    });

    it('should call keyupstream.next() when input is triggered ', () => {
      const spyKeyUpStream = jest.spyOn(component.keyupStream$, 'next');
      hostFixture.debugElement
        .query(By.css('input'))
        .nativeElement.dispatchEvent(new Event('input'));
      expect(spyKeyUpStream).toBeCalled();
    });

    it('should not call the filter method if isFiltered is set to false', fakeAsync(() => {
      if (component.inputSearch) {
        component.inputSearch.nativeElement.value = 'rôLEs';
      }
      hostFixture.debugElement
        .query(By.css('input'))
        .nativeElement.dispatchEvent(new Event('input'));
      tick(700);

      expect(spyFilter).not.toBeCalled();
      flush();
    }));
  });

  describe('keyupStream if isFiltered is set to true', () => {
    let spyFilter: jest.SpyInstance<void, [text: string]>;
    beforeEach(() => {
      hostComponent.isFiltered = true;
      hostFixture.detectChanges();
      component.ngOnInit();
      spyFilter = jest.spyOn(hostComponent, 'addFilter');
    });

    it('should call filter method and send the cleaned value of the input search ', fakeAsync(() => {
      if (component.inputSearch) {
        component.inputSearch.nativeElement.value = 'rôLEs';
      }
      hostFixture.debugElement
        .query(By.css('input'))
        .nativeElement.dispatchEvent(new Event('input'));
      tick(700);
      expect(spyFilter).toBeCalledWith('roles');
      flush();
    }));

    it('should only call filter method ONCE if inputs are triggered few times in 700ms duration ', fakeAsync(() => {
      if (component.inputSearch) {
        component.inputSearch.nativeElement.value = 'rôL';
      }
      hostFixture.debugElement
        .query(By.css('input'))
        .nativeElement.dispatchEvent(new Event('input'));
      tick(300); //first call lesser than 700ms
      if (component.inputSearch) {
        component.inputSearch.nativeElement.value = 'rôLEs';
      }
      hostFixture.debugElement
        .query(By.css('input'))
        .nativeElement.dispatchEvent(new Event('input'));
      tick(700); //second call

      expect(spyFilter).toBeCalledTimes(1);
      expect(spyFilter).toBeCalledWith('roles'); //check if it is called with the last value
      flush();
    }));

    it('should only call filter method ONCE if the input value didnt change ', fakeAsync(() => {
      if (component.inputSearch) {
        component.inputSearch.nativeElement.value = 'rôle';
      }
      hostFixture.debugElement
        .query(By.css('input'))
        .nativeElement.dispatchEvent(new Event('input'));
      tick(700); //first call
      if (component.inputSearch) {
        component.inputSearch.nativeElement.value = 'rôle';
      }
      hostFixture.debugElement
        .query(By.css('input'))
        .nativeElement.dispatchEvent(new Event('input'));
      tick(700); //second call
      hostFixture.detectChanges();
      flush();
      expect(spyFilter).toBeCalledTimes(1);
    }));

    it('should call filter method TWICE  if the input value do change ', fakeAsync(() => {
      if (component.inputSearch) {
        component.inputSearch.nativeElement.value = 'rôle';
      }
      hostFixture.debugElement
        .query(By.css('input'))
        .nativeElement.dispatchEvent(new Event('input'));
      tick(700); //first call
      if (component.inputSearch) {
        component.inputSearch.nativeElement.value = 'rôles';
      }
      hostFixture.debugElement
        .query(By.css('input'))
        .nativeElement.dispatchEvent(new Event('input'));
      tick(700); //second call

      expect(spyFilter).toBeCalledTimes(2);
      flush();
    }));
  });

  describe('clearInputValue by triggering click event on remove icon', () => {
    beforeEach(() => {
      const inputEl = hostFixture.debugElement.query(By.css('input'));
      inputEl.nativeElement.value = 'test';
      inputEl.triggerEventHandler('input', { target: inputEl.nativeElement });
      hostFixture.detectChanges();
    });

    it('should clear input value', fakeAsync(() => {
      const inputEl = hostFixture.debugElement.query(By.css('.input-icon-remove'));
      inputEl.nativeElement.click();
      hostFixture.detectChanges();
      flush();
      expect(component.inputSearch?.nativeElement.value).toBe('');
    }));

    it('should call the clear method in the host component', () => {
      const spyClear = jest.spyOn(hostComponent, 'clearSearchOrFilter');
      hostFixture.debugElement
        .query(By.css('.input-icon-remove'))
        .nativeElement.dispatchEvent(new Event('click'));
      expect(spyClear).toBeCalled();
    });
  });
});
