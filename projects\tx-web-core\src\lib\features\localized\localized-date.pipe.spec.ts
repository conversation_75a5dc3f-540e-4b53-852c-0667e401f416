import { LocalizedDatePipe } from './localized-date.pipe';
import { TestBed, waitForAsync } from '@angular/core/testing';
import { TranslateService } from '@ngx-translate/core';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { AbstractSessionService, MockSessionService } from '../../data-access/session';

describe('LocalizedDatePipe', () => {
  let pipe: LocalizedDatePipe;
  let sessionService: AbstractSessionService;
  let translateService: TranslateService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [TranslateTestingModule.withTranslations({ en: {}, fr: {} })],
      providers: [{ provide: AbstractSessionService, useClass: MockSessionService }],
    }).compileComponents();

    sessionService = TestBed.inject(AbstractSessionService);
    translateService = TestBed.inject(TranslateService);
    pipe = new LocalizedDatePipe(sessionService, translateService);
  }));

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });
});
