.fm-container-right {
  display: inline-block;
  height: calc(100% - 16px);
  vertical-align: top;
  width: 100%;

  .file-container-text {
    border-bottom: 0px !important;
    padding: 4px 8px;
    overflow-x: auto;
    overflow-y: hidden;
    text-align: center;
    position: relative;
    display: flex;
    column-gap: 8px;
    flex-wrap: nowrap;

    .file-drop-text {
      margin: 0px 8px 0px 16px;
    }
  }

  .e-gridheader {
    padding: 0% !important;
  }

  .fm-grid-container {
    height: Calc(100% - 29px);
    position: relative;
    fa-icon {
      margin-right: 8px;
    }
    .fm-row-isTemp {
      opacity: 0.5;
    }
    table {
      border-collapse: collapse !important;

      .border-accent-dashed td {
        border: none !important;
      }

      tr.e-row.border-accent-dashed + tr td {
        border: none !important;
      }
    }
    .e-spinner-pane {
      display: none !important;
    }
  }
}
.dbr-dragzone {
  opacity: 0.97 !important;
  height: 100%;
  position: absolute;
  width: calc(100% - 2px);
  display: table;
  z-index: 4;

  .dbr-drag {
    vertical-align: middle;
    display: table-cell;
    text-align: center;

    .dbr-file {
      font-size: 50px;
    }

    .dbr-file-message {
      font-size: 20px;
      line-height: 50px;
      font-weight: 600;
    }
    .dbr-file-extension {
      font-size: 12px;
    }
  }
}

.fm-no-record-placeholder {
  height: calc(100% - 2px);
  position: absolute;
  width: calc(100% - 2px);
  display: flex;
  z-index: 2;

  fa-icon {
    margin: 0px !important;
    font-size: 80px;
  }
}

.fm-spinner-loadingGrid {
  height: calc(100% - 2px);
  position: absolute;
  width: calc(100% - 2px);
  z-index: 3;
}

.tx-no-record {
  width: 100%;
}

:host {
  width: 100%;
  overflow: auto;
}
