<div
  *ngIf="!readMode"
  class="form-field"
  [ngClass]="{
    'form-field-error': control && !control.valid && !disabled,
    'read-field': readMode
  }">
  <ng-container *ngIf="timePicker; else elseTemplate">
    <ejs-datetimepicker
      #dateTimePicker
      [required]="required"
      width="228"
      [formControl]="control"
      [placeholder]="label"
      [matTooltip]="labelTooltip"
      matTooltipPosition="above"
      [step]="timeStep"
      [timeFormat]="timeFormat"
      matTooltipShowDelay="500"
      [format]="dateTimeFormat"
      [ngClass]="{
        'mandatory-label': required && control.valid,
        'date-time-picker-error': !control.valid && !disabled,
        'mandatory-label-error-field': control.hasError('required') && required
      }"
      class="date-time-picker-field"
      floatLabelType="Auto"
      (focus)="setTodayDate()">
    </ejs-datetimepicker>
  </ng-container>
  <ng-template #elseTemplate>
    <ejs-datepicker
      #datePicker
      [required]="required"
      width="228"
      [placeholder]="label"
      [matTooltip]="labelTooltip"
      matTooltipPosition="above"
      matTooltipShowDelay="500"
      [formControl]="control"
      [format]="dateFormat"
      [ngClass]="{
        'mandatory-label': required && control.valid,
        'date-time-picker-error': !control.valid && !disabled,
        'mandatory-label-error-field': !control.valid && required
      }"
      class="date-picker-field"
      floatLabelType="Auto"
      (focus)="setTodayDate()">
    </ejs-datepicker>
  </ng-template>
  <div class="field-error-container">
    <mat-error class="field-error" *ngIf="control && control.hasError('required')"
      ><strong>Required</strong></mat-error
    >
  </div>
</div>

<div *ngIf="readMode" class="form-field read-field">
  <mat-label
    class="read-form-label text-read-form-content form-label mat-form-label"
    [matTooltip]="labelTooltip"
    matTooltipClass="mat-label-tooltip"
    matTooltipShowDelay="500"
    matTooltipPosition="above">
    {{ label }}
  </mat-label>
  <div class="read-form-field">
    <span *ngIf="readMode">{{ getValueWithGoodFormat() }}</span>
  </div>
</div>
