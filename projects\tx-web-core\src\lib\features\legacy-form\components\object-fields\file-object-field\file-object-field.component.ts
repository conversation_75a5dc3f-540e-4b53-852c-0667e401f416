import { <PERSON>PropModel, RenderingEventArgs } from '@syncfusion/ej2-angular-inputs';
import { Component, Input, ViewChild, OnInit } from '@angular/core';
import { TxObjectFieldComponent } from '../_system/object-field/object-field.component';
import { LegacyTxAttributeFile, LegacyTxFile } from '../../../services/structure/models/attribute';
import { LegacyTxDataBaseAction, LegacyTxDataFile } from '../../../services/structure/models/data';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { TxChipsFieldComponent } from '../../generic-fields/chips-field/chips-field.component';
import { TxChip } from '../../../models/formFields/setting-model';
import { TxFileService } from '../../../services/structure/services/file.service';
import { TxUploadFileFieldComponent } from '../../generic-fields/file-field-alt/upload-file-field.component';
import { LegacyFileUtils } from '../../../utilities/legacy-file-utils';

@Component({
  selector: 'tx-file-object-field',
  templateUrl: './file-object-field.component.html',
  styleUrls: [
    '../../generic-fields/base-field/base-field.component.scss',
    './file-object-field.component.scss',
  ],
})
export class TxFileObjectFieldComponent extends TxObjectFieldComponent implements OnInit {
  @Input() attribute!: LegacyTxAttributeFile;
  @Input() data!: LegacyTxDataFile;
  @Input() value!: FilesPropModel[];
  @Input() multiple!: boolean;

  @ViewChild('chipsField') chipsField!: TxChipsFieldComponent;

  @ViewChild('uploadeFileField') uploadeFileField!: TxUploadFileFieldComponent;

  txFiles: LegacyTxFile[] = [];
  fileToRendered: RenderingEventArgs[] = [];
  newFileCounter = -1;
  hideTitleImageThumbnail = false;
  hideVisualisationToggle = false;

  constructor(
    public attributeService: LegacyTxAttributesService,
    public fileService: TxFileService
  ) {
    super(attributeService);
  }

  ngOnInit(): void {
    super.ngOnInit();
    if (this.field && this.field.properties) {
      if (this.field.properties.hideTitleImageThumbnail !== undefined) {
        this.hideTitleImageThumbnail = this.field.properties.hideTitleImageThumbnail;
      }
      if (this.field.properties.hideVisualisationToggle !== undefined) {
        this.hideVisualisationToggle = this.field.properties.hideVisualisationToggle;
      }
    }
  }

  initProperties() {
    super.initProperties();
    if (this.attribute) {
      this.multiple = this.attribute.isList;
    }
    if (!this.readMode && this.multiple) {
      this.addFieldClasses('double-field');
    }
  }

  initValue() {
    if (this.data && this.data.files) {
      this.value = this.data.files.map((f) => {
        f.action = LegacyTxDataBaseAction.None;
        return {
          name: LegacyFileUtils.extractFileNameWithoutExt(f.name),
          type: LegacyFileUtils.extractFileExt(f.name),
          size: f.size,
        };
      });

      this.txFiles = this.data.files;
    }
  }

  getFile(name: string): LegacyTxFile {
    return this.txFiles.find(
      (f) => f.name === name && f.action !== LegacyTxDataBaseAction.Delete
    ) as LegacyTxFile;
  }

  isViewableFile(fileName: string) {
    const viewableExts = [
      'jpg',
      'bmp',
      'jpeg',
      'raw',
      'skc',
      'png',
      'gif',
      'tif',
      'tiff',
      'ico',
      'icon',
      'svg',
      'pdf',
    ];
    const ext = LegacyFileUtils.extractFileExt(fileName);
    return viewableExts.indexOf(ext) > -1;
  }

  onSuccess(args: any) {
    const txFile = this.getFile(args.file.name);

    switch (args.operation) {
      case 'remove':
        if (txFile) {
          this.removeFile(txFile);
        }
        break;
      case 'upload':
        if (txFile) {
          this.removeFile(txFile);
        }

        this.txFiles.push(
          new LegacyTxFile(
            args.file.name,
            args.file.size,
            false,
            this.newFileCounter,
            LegacyTxDataBaseAction.Add
          )
        );
        this.newFileCounter--;
        break;
    }
  }

  removeFile(txFile: LegacyTxFile) {
    switch (txFile.action) {
      case LegacyTxDataBaseAction.None:
      case LegacyTxDataBaseAction.Modify:
        txFile.action = LegacyTxDataBaseAction.Delete;

        break;
      case LegacyTxDataBaseAction.Add:
        this.txFiles = this.txFiles.filter((f) => f.idArchivedFile !== txFile.idArchivedFile);

        break;
    }
  }

  log(arg: any) {}

  setData(data: LegacyTxDataFile) {
    // console.log(data);
    data.files?.forEach((f) => {
      if (!f.size) {
        f.size = -1;
      }
    });
    this.txFiles = data.files as LegacyTxFile[];
    if (this.chipsField) {
      this.chipsField.data = data;
      this.chipsField.setData();
    } else {
      this.control.setValue(data.files);
    }
    if (this.uploadeFileField) {
      this.uploadeFileField.addFiles(data.files as LegacyTxFile[]);
    }
  }

  getDataToSave(): LegacyTxDataFile | undefined {
    if (this.control.value) {
      const txFiles = this.control.value;
      const data = new LegacyTxDataFile(this.idObject, this.idAttribute, txFiles);

      this.txFiles = this.txFiles.filter((f) => f.action < LegacyTxDataBaseAction.Delete);

      this.txFiles.forEach((f) => {
        switch (f.action) {
          case LegacyTxDataBaseAction.Add:
          case LegacyTxDataBaseAction.Modify:
            f.action = LegacyTxDataBaseAction.None;
            break;
        }
      });
      return data;
    }
  }

  downloadFile(chip: TxChip) {
    this.fileService.download(chip.idFile as number).subscribe((blob) => {
      const a = document.createElement('a');
      const objectUrl = URL.createObjectURL(blob);
      a.href = objectUrl;
      a.download = chip.name;
      a.click();
      URL.revokeObjectURL(objectUrl);
    });
  }

  downloadFileList(chips: TxChip[]) {
    // console.log('download');
  }
}
