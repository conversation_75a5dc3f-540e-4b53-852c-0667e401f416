import { Injectable } from '@angular/core';
import { FilesDirective } from '@syncfusion/ej2-angular-inputs';
import { Observable, of } from 'rxjs';
import { TxApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class TxFileService {
  filesSaved: Map<number, Blob>;

  constructor(public apiService: TxApiService) {
    this.filesSaved = new Map<number, Blob>();
  }

  download(id: number): Observable<any> {
    if (this.filesSaved.has(id)) {
      return of(this.filesSaved.get(id));
    } else {
      return new Observable((observer) => {
        this.apiService.downloadFile(id).subscribe((blob) => {
          this.filesSaved.set(id, blob);
          observer.next(blob);
          observer.complete;
        });
      });
    }
  }

  upload(file: File, idAttribute: number) {
    const formData = new FormData();
    formData.append('UploadFiles', file);
    return this.apiService.uploadFile(formData, idAttribute);
  }

  deleteCache(idsFile: number[]) {
    return this.apiService.deleteCacheFile(idsFile);
  }
}
