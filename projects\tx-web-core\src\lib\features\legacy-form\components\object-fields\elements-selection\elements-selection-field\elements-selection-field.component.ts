import { Component, EventEmitter, Output, OnInit } from '@angular/core';
import { ChangeEventArgs } from '@syncfusion/ej2-angular-buttons';
import { LegacyTxAttributesService } from '../../../../services/structure/services/attributes.service';
import { TxElementSelectionBaseFieldComponent } from '../_element-selection-base-field/element-selection-base-field.component';

@Component({
  selector: 'tx-elements-selection-field',
  templateUrl: './elements-selection-field.component.html',
  styleUrls: ['./elements-selection-field.component.scss'],
})
export class TxElementsSelectionFieldComponent
  extends TxElementSelectionBaseFieldComponent
  implements OnInit
{
  @Output() clickEvent = new EventEmitter<any>();

  elementSize!: number;

  constructor(public attributeService: LegacyTxAttributesService) {
    super(attributeService);
  }
  ngOnInit() {
    super.ngOnInit();

    this.elementSize = this.maxElementSize + 140;
  }

  onSelectionChange(element: any, event: ChangeEventArgs) {
    this.clickEvent.emit({ element, event });
  }
}
