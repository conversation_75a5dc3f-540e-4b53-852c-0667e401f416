import { ComponentFixture, TestBed } from '@angular/core/testing';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { ViewComponent } from './view.component';
import { PathService } from '../path/path.service';
import { PathServiceMock } from '../path/path.service.mock';

describe('ViewComponent', () => {
  let component: ViewComponent;
  let fixture: ComponentFixture<ViewComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ViewComponent],
      providers: [{ provide: PathService, useClass: PathServiceMock }],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ViewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should viewDisabled view', () => {
    component.toFocus = {
      toFocus: 'Attribute',
    };
    expect(component.viewDisabled).toBe(false);
  });
  it('should not not disable view', () => {
    component.toFocus = {
      toFocus: 'AdvancedComparison',
    };
    expect(component.viewDisabled).toBe(true);
  });
  it('can view', () => {
    component.toFocus = {
      toFocus: 'Attribute',
    };
    expect(component.tooltip).toBe(_('txWebCore.admins.columns.view'));
  });
  it('no sufficient Right', () => {
    component.toFocus = {
      toFocus: 'Concept',
    };
    expect(component.tooltip).toBe(_('txWebCore.tooltip.noSufficientRight'));
  });
  it('conceptNotAccessible', () => {
    component.toFocus = {
      toFocus: 'AdvancedComparison',
    };
    expect(component.tooltip).toBe(_('txWebCore.tooltip.notAccessible'));
  });
});
