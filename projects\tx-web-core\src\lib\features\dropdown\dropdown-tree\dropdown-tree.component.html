<mat-form-field
  class="dropdown-tree-form-field"
  *ngIf="readyToLoad"
  [ngClass]="{
    'field-without-hint': !displayHint,
    'dropdown-tree-with-chips': useChipsForLabels,
    'dropdown-tree-small': isSmallDropdown
  }"
  [style]="{ width: width }"
  [matTooltip]="isSmallDropdown ? ('txWebCore.tooltip.changeFilter' | translate) : ''"
  color="accent">
  <div *ngIf="isSmallDropdown">
    <fa-icon
      [icon]="['fal', 'filter']"
      class="selection-counter-icon"
      [ngClass]="disabled ? 'color-grey40' : ''"></fa-icon>
    <span
      class="selection-counter-background"
      [ngClass]="disabled ? 'background-grey20' : 'background-text'">
      <span class="selection-counter-content">{{ selectedNodesLength }}</span>
    </span>
  </div>
  <mat-label>{{ label | translate }}</mat-label>
  <mat-select
    #treeSelect
    multiple
    panelClass="dropdown-tree-global-panel"
    [value]="selectedNodesNames"
    (click)="clickOnSelect()"
    (focus)="treeSelect.open(); clickOnSelect()"
    [formControl]="control"
    txClickableElement>
    <mat-select-trigger
      *ngIf="useChipsForLabels"
      [ngClass]="{ 'dropdown-tree-select-small': isSmallDropdown }">
      <mat-chip-listbox aria-label="Object Types Selection">
        <mat-chip
          *ngFor="let node of selectedNodes | slice : 0 : 3"
          [matTooltip]="node.name | translate"
          class="chip-select"
          [removable]="true">
          <div class="chip-select-content">
            <img [src]="node.iconPath" alt="Object Type icon" />
            <span class="chip-select-text">{{ node.name | translate }}</span>
            <fa-icon
              matChipRemove
              [icon]="['fas', 'times-circle']"
              class="chip-select-icon"
              (click)="removeChip($event, node)"
              txClickableElement></fa-icon>
          </div>
        </mat-chip>
        <mat-chip
          *ngIf="selectedNodes.length > 3"
          [matTooltip]="(selectedNodesNames | slice : 3).join(', ')"
          class="chip-select">
          {{
            (selectedNodes.length === 4 ? 'txWebCore.generic.other' : 'txWebCore.generic.others')
              | translate : { count: selectedNodes.length - 3 }
          }}
        </mat-chip>
      </mat-chip-listbox>
    </mat-select-trigger>
    <mat-option style="display: none"></mat-option>
    <!-- Because if no mat-option, the dropdown menu does not open -->
    <ng-container *ngFor="let node of selectedNodesNames">
      <mat-option style="display: none" [value]="node">{{ node }}</mat-option>
    </ng-container>
    <div class="dropdown-input-search-container">
      <input
        #treeSearchInput
        class="dropdown-tree-input-search"
        type="text"
        aria-label="SearchForItems"
        (input)="filterDataSource(treeSearchInput.value)"
        (keydown)="checkForSelection($event)" />
      <fa-icon
        *ngIf="treeSearchInput.value.length > 0"
        [icon]="['fal', 'times']"
        size="lg"
        class="dropdown-tree-search-clear-input"
        (click)="clearSearchInput()"
        txClickableElement></fa-icon>
    </div>
    <mat-divider></mat-divider>
    <div class="dropdown-tree-container">
      <tx-object-type-tree-view
        #treeView
        [treeData]="dataSource"
        [filteredTreeData]="filteredDataSource"
        [treeDataOptions]="dataOptions"
        [nodeTemplate]="treeNodeTemplate"
        [checkedIds]="checkedIds"
        [disabledIds]="disabledIds"
        [showCheckBox]="showCheckBox"
        [multipleSelection]="multipleSelection"
        [hierarchicalSelection]="hierarchicalSelection"
        (selectionChange)="onSelectionChange($event)"></tx-object-type-tree-view>
    </div>
  </mat-select>
  <fa-icon
    *ngIf="
      !useChipsForLabels &&
      enableRemoveSelection &&
      selectedNodesNames.length > 0 &&
      !control?.disabled
    "
    [icon]="['fal', 'times']"
    class="dropdown-clear-input"
    (click)="clearSelection($event)"
    txClickableElement></fa-icon>
  <mat-error *ngIf="required && displayHint && control?.hasError('required')" class="e-error">
    {{ 'txWebCore.input.fieldRequired' | translate }}
  </mat-error>
  <mat-hint class="accent explanation-message" *ngIf="message">{{ message | translate }}</mat-hint>
</mat-form-field>
