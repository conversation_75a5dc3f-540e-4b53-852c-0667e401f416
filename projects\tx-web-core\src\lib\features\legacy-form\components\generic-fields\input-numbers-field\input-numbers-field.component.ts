import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { AbstractControl, FormControl, Validators } from '@angular/forms';
import { TxUnit } from '../../../services/structure/models/unit';
import { TxBaseFieldComponent } from '../base-field/base-field.component';
import { TxInputNumbersControlComponent } from './input-numbers-control/input-numbers-control.component';

@Component({
  selector: 'tx-input-numbers-field',
  templateUrl: './input-numbers-field.component.html',
  styleUrls: ['./input-numbers-field.component.scss'],
})
export class TxInputNumbersFieldComponent
  extends TxBaseFieldComponent
  implements AfterViewInit, OnInit, OnChanges
{
  @Input() withSecondInput = false;
  @Input() withThirdInput = false;

  @Input() value!: number | number[];

  @Input() lowerBoundValue!: number;
  @Input() upperBoundValue!: number;
  @Input() lowerBoundInclude!: boolean;
  @Input() upperBoundInclude!: boolean;

  @Input() minPlaceHolder = '';
  @Input() maxPlaceHolder = 'max';
  @Input() meanPlaceHolder = 'mean';

  @Input() minLength!: number;
  @Input() maxLength!: number;

  @Input() maxFormControl!: AbstractControl;
  @Input() meanFormControl!: AbstractControl;

  @Input() units!: TxUnit[];
  @Input() idUnitSelected!: number;

  @Input() inputWidthPx: number = 54;

  @Output() keyPress = new EventEmitter();

  @ViewChild('inputsNumber') inputsNumberComponent!: TxInputNumbersControlComponent;

  focused = false;

  unitFocused = false;

  hintBound!: string;

  value2UpperThanValue1 = true;
  meanBetweenMinAndMax = true;
  valueRequiredUnassigned = false;

  public get isValid() {
    const minFieldValid = this.control.valid;
    const maxFieldValid = this.maxFormControl ? this.maxFormControl.valid : true;
    const meanFieldValid = this.meanFormControl ? this.meanFormControl.valid : true;
    return minFieldValid && maxFieldValid && meanFieldValid;
  }

  get notEmpty() {
    const minFieldHasValue =
      this.control && this.control.value !== null && this.control.value !== undefined;
    const maxFieldHasValue =
      this.maxFormControl &&
      this.maxFormControl.value !== null &&
      this.maxFormControl.value !== undefined;
    const meanFieldHasValue =
      this.meanFormControl &&
      this.meanFormControl.value !== null &&
      this.meanFormControl.value !== undefined;
    return minFieldHasValue || maxFieldHasValue || meanFieldHasValue;
  }

  changeFocus(result: boolean) {
    setTimeout(() => {
      this.focused = result;
    }, 10);
  }

  blurUnit() {
    setTimeout(() => {
      this.unitFocused = false;
    }, 1000);
  }

  constructor() {
    super();
  }

  ngOnChanges(changes: SimpleChanges) {
    super.ngOnChanges(changes);
    if (changes.required && this.maxFormControl) {
      if (changes.required.currentValue) {
        this.maxFormControl.addValidators(Validators.required);
      } else {
        this.maxFormControl.removeValidators(Validators.required);
      }
      this.maxFormControl.updateValueAndValidity();
    }

    if (changes.disabled && this.maxFormControl) {
      if (changes.disabled.currentValue) {
        this.maxFormControl.disable();
      } else {
        this.maxFormControl.enable();
      }
      this.maxFormControl.updateValueAndValidity();
    }

    if (changes.required && this.meanFormControl) {
      if (changes.required.currentValue) {
        this.meanFormControl.addValidators(Validators.required);
      } else {
        this.meanFormControl.removeValidators(Validators.required);
      }
      this.meanFormControl.updateValueAndValidity();
    }

    if (changes.disabled && this.meanFormControl) {
      if (changes.disabled.currentValue) {
        this.meanFormControl.disable();
      } else {
        this.meanFormControl.enable();
      }
      this.meanFormControl.updateValueAndValidity();
    }
  }

  ngAfterViewInit(): void {
    if (this.lowerBoundValue || this.upperBoundValue) {
      this.updateBoundValue();
    }
  }

  initValue(): void {
    if (!Array.isArray(this.value) && (this.withSecondInput || this.withThirdInput)) {
      this.value = [this.value, null, null] as number[];
    }
    if (!Array.isArray(this.value)) {
      if (!this.value && !this.control.value) {
        this.value = null as unknown as number[];
        this.control.setValue(null);
      } else if (!this.value && this.control.value) {
        this.value = this.control.value;
      } else {
        this.control.setValue(this.value);
      }
    } else {
      if (!this.value[0] && !this.control.value) {
        this.value[0] = null as unknown as number;
        this.control.setValue(null);
      } else if (!this.value[0] && this.control.value) {
        this.value[0] = this.control.value;
      } else {
        this.control.setValue(this.value[0]);
      }
      if ((this.withSecondInput || this.withThirdInput) && this.maxFormControl) {
        if (!this.value[1] && !this.maxFormControl.value) {
          this.value[1] = null as unknown as number;
          this.maxFormControl.setValue(null);
        } else if (!this.value[1] && this.maxFormControl.value) {
          this.value[1] = this.maxFormControl.value;
        } else {
          this.maxFormControl.setValue(this.value[1]);
        }
      }
      if (this.withThirdInput && this.meanFormControl) {
        if (!this.value[2] && !this.meanFormControl.value) {
          this.value[2] = null as unknown as number;
          this.meanFormControl.setValue(null);
        } else if (!this.value[2] && this.meanFormControl.value) {
          this.value[2] = this.meanFormControl.value;
        } else {
          this.meanFormControl.setValue(this.value[2]);
        }
      }
    }
  }

  initValueChangeEmitter() {
    this.control.valueChanges.subscribe(() => {
      if (!Array.isArray(this.value)) {
        this.value = this.control.value;
      } else {
        this.value[0] = this.control.value;
      }
      this.valueChange.emit(this.value);
    });
    if (this.maxFormControl) {
      this.maxFormControl.valueChanges.subscribe(() => {
        (this.value as number[])[1] = this.maxFormControl.value;
        this.valueChange.emit(this.value);
      });
    }
    if (this.meanFormControl) {
      this.meanFormControl.valueChanges.subscribe(() => {
        (this.value as number[])[2] = this.meanFormControl.value;
        this.valueChange.emit(this.value);
      });
    }
  }

  onFieldChange(param?: any): void {
    super.onFieldChange(param);
    if (!Array.isArray(this.value)) {
      this.value = this.control.value;
    } else if (this.withSecondInput) {
      this.value = [this.control.value, this.maxFormControl.value];
    } else if (this.withThirdInput) {
      this.value = [this.control.value, this.maxFormControl.value, this.meanFormControl.value];
    } else {
      this.value = [this.control.value];
    }
  }

  initProperties(): void {
    if (!this.minPlaceHolder && (this.withSecondInput || this.withThirdInput)) {
      this.minPlaceHolder = 'min';
    }
    if ((this.withSecondInput || this.withThirdInput) && !this.maxFormControl) {
      this.maxFormControl = new FormControl({ value: null, disabled: this.disabled });
      if (this.required) {
        this.maxFormControl.setValidators(Validators.required);
      }
      this.form.addControl(this.label + 'max', this.maxFormControl);
    }
    if (this.withThirdInput && !this.meanFormControl) {
      this.meanFormControl = new FormControl({ value: null, disabled: this.disabled });
      if (this.required) {
        this.meanFormControl.setValidators(Validators.required);
      }
      this.form.addControl(this.label + 'mean', this.meanFormControl);
    }
    super.initProperties();
  }

  updateBoundValue() {
    // update information message about bounds under the field
    const unitSelected = this.idUnitSelected
      ? this.units.find((u) => u.id === this.idUnitSelected)
      : null;
    if (!this.readMode) {
      this.hintBound = '';
      if (this.lowerBoundValue) {
        if (unitSelected) {
          // const newLowerBoundValue = this.unitService.Convert(this.lowerBoundValue, this.mainUnit.id, this.unitIdSelected);
          this.hintBound += this.lowerBoundValue.toString();

          this.hintBound += unitSelected.name;
          // this.inputsNumberComponent.changeLowerBoundValue(newLowerBoundValue);
        } else {
          this.hintBound += this.lowerBoundValue.toString();
        }
        this.hintBound += ' < _';
      }
      if (this.upperBoundValue) {
        if (!this.lowerBoundValue) {
          this.hintBound += '_';
        }
        this.hintBound += ' < ';
        if (unitSelected) {
          // const newUpperBoundValue = this.unitService.Convert(this.upperBoundValue, this.mainUnit.id, this.unitIdSelected);
          this.hintBound += this.upperBoundValue.toString();
          this.hintBound += unitSelected.name;
          // this.inputsNumberComponent.changeUpperBoundValue(newUpperBoundValue);
        } else {
          this.hintBound += this.upperBoundValue.toString();
        }
      }
    }
  }
}
