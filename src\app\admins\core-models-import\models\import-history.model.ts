import { CoreModelsHistory } from '../../core-model-common';
import { ImportedCoreModelConcept } from './imported-core-model-concept.model';
import { TestedCoreModelConcept } from './tested-core-model-concept.model';

export type MapToSuccessHistory<T> = {
  [Property in keyof T]: Property extends 'concepts'
  ? ImportedCoreModelConcept[]
  : Property extends 'status'
  ? Failed
  : T[Property];
};
export type MapToFailedHistory<T> = {
  [Property in keyof T]: Property extends 'concepts'
  ? TestedCoreModelConcept[]
  : Property extends 'status'
  ? Success
  : T[Property];
};
export type SuccessImportHistory = MapToSuccessHistory<FailedImportHistory>;
export type FailedImportHistory = MapToFailedHistory<CoreModelImportHistory>;
export interface CoreModelImportHistory extends CoreModelsHistory {
  readonly id: string;
  readonly status: CoreModelsImportHistoryStatus;
  readonly concepts: TestedCoreModelConcept[] | ImportedCoreModelConcept[];
}

export enum CoreModelsImportHistoryFieldEnum {
  Date = 'date',
  Username = 'username',
  Name = 'name',
  Version = 'version',
  Explanation = 'explanation',
  Comment = 'comment',
  Status = 'status',
  View = 'view',
}

export type CoreModelsImportHistoryStatus = Failed | Success;

export type Failed = 'Failed';
export type Success = 'Success';
