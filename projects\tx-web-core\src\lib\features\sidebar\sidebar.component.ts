import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  ContentChild,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { TxTreeViewComponent, TxTreeNodeTemplate,
  TxTreeViewChildren,
  TxTreeViewSelect } from '@bassetti-group/tx-web-core/src/lib/features/trees';
import { TxSidebarContentTemplate } from './directives/sidebar-content.directive';
import { TxSidebarHeaderTemplate } from './directives/sidebar-header.directive';

@Component({
  selector: 'tx-sidebar',
  standalone: true,
  imports: [
    MatSidenavModule,
    CommonModule,
    FontAwesomeModule,
    TranslateModule,
    MatTooltipModule,
    TxTreeViewComponent,
    TxTreeNodeTemplate,
  ],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
})
export class TxSidebarComponent<T extends TxTreeViewChildren<T>>
  implements AfterViewInit, OnChanges
{
  @Input({ required: true }) isSideNavExpanded = true;
  @Input({ required: true }) srcLogo!: string;
  @Input({ required: true }) fields!: T[];
  @Input() showTreeView: boolean = false;
  @Input() actualYear?: number;
  @Input() selectedNode?: T[keyof T];

  @Output() dataBound: EventEmitter<void> = new EventEmitter();
  @Output() nodeSelecting: EventEmitter<TxTreeViewSelect<T>> = new EventEmitter();
  @Output() nodeSelected: EventEmitter<TxTreeViewSelect<T>> = new EventEmitter();

  @ContentChild(TxSidebarContentTemplate) contentTemplate!: TxSidebarContentTemplate;
  @ContentChild(TxSidebarHeaderTemplate) headerTemplate!: TxSidebarHeaderTemplate;
  @ViewChild('treeViewInstance') tree?: TxTreeViewComponent<T>;

  constructor() {}
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.showTreeView?.currentValue) {
      this.setSelectedNodes();
    }
    if (changes.selectedNode?.currentValue) {
      this.setSelectedNodes();
    }
  }
  ngAfterViewInit(): void {
    this.setSelectedNodes();
  }
  setSelectedNodes() {
    setTimeout(() => {
      if (this.tree) {
        this.tree.selectedRowPKValue = this.selectedNode;
      }
    }, 0);
  }
}
