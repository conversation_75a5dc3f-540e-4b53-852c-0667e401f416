import { CollectionViewer } from '@angular/cdk/collections';
import {
  PaginatedTableDataSource,
  PaginationEndpointFn,
  Sort,
  FilterQuery,
} from '@bassetti-group/tx-web-core/src/lib/features/grid';
import { Observable, map } from 'rxjs';
import {
  addLevelsToTree,
  flattenTree,
  assignTxId,
  generateTree,
  addLevelsToTreeSourceData,
  flattenTreeWithChild,
  parentToRootIds,
} from '../tree-utils';
import {
  DEFAULT_CHILD_ID,
  DEFAULT_CHILD_MAPPING,
  TxTreeExpandState,
  TxTreeGridChildren,
} from '../tree-grid.interface';

export class PaginatedTreeGridDataSource<
  T extends TxTreeGridChildren<T>
> extends PaginatedTableDataSource<T> {
  expandedNodes = new Set<T[keyof T]>();
  expandState: TxTreeExpandState = TxTreeExpandState.ExpandedFirst;
  buildTree: boolean = false;
  autoReloadCollapse = true;
  childMapping: keyof TxTreeGridChildren<T> = DEFAULT_CHILD_MAPPING;
  idMapping?: keyof T;
  parentMapping?: keyof T;

  /**
   * Adds or updates the data in the data source, handling tree structure generation if required.
   * Manages level assignment and tree hierarchy maintenance.
   *
   * @param {Observable<T[]> | T[]} value - New data to be added, either as an Observable or direct array
   * @throws {Error} When buildTree is true but idMapping or parentMapping is undefined
   * @memberof PaginatedTreeGridDataSource
   */
  override addData(value: Observable<T[]> | T[]) {
    if (this.buildTree && (this.idMapping === undefined || this.parentMapping === undefined)) {
      throw this.generateTreeError();
    }
    let treeData: Observable<T[]> | T[];
    if (this.buildTree && this.idMapping !== undefined && this.parentMapping !== undefined) {
      treeData =
        value instanceof Observable
          ? value.pipe(
              map((data) => {
                if (this.idMapping === undefined || this.parentMapping === undefined) {
                  throw this.generateTreeError();
                }
                return generateTree(data, this.idMapping, this.parentMapping);
              })
            )
          : generateTree(value, this.idMapping, this.parentMapping);
    } else {
      treeData = value;
    }

    const updatedData = addLevelsToTreeSourceData(treeData, this.childMapping);
    let doseNotDataChanged = this.areArraysEqualByPrimaryKey(
      updatedData as T[],
      this.dataList,
      this.primaryKey
    );
    super.addData(
      this.primaryKey !== DEFAULT_CHILD_ID
        ? updatedData
        : assignTxId(updatedData, this.childMapping, this.primaryKey)
    );
    if (!doseNotDataChanged || this.autoReloadCollapse) {
      this.handleExpandCollapseNode(this.expandState);
    }
  }

  constructor(
    endPoint: PaginationEndpointFn<T> | Observable<T[]>,
    primaryKey: keyof T | undefined = undefined,
    initialSort?: Sort<T> | undefined | null,
    initialQuery?: FilterQuery<T>,
    pageSize = 100
  ) {
    super(
      enhanceEndPoint(endPoint, DEFAULT_CHILD_MAPPING),
      primaryKey,
      initialSort,
      initialQuery,
      pageSize
    );
  }

  /**
   * Toggles the expansion state of a tree node.
   * If the node is expanded, it will be collapsed and vice versa.
   *
   * @param {T} node - The tree node to toggle
   * @throws {Error} When primaryKey is undefined
   * @memberof PaginatedTreeGridDataSource
   */
  toggleNode(node: T): void {
    if (this.primaryKey !== undefined) {
      if (this.expandedNodes.has(node[this.primaryKey])) {
        this.expandedNodes.delete(node[this.primaryKey]);
      } else {
        this.expandedNodes.add(node[this.primaryKey]);
      }
      this.triggerUpdatePageRequest();
    } else {
      throw new Error('primaryKey not defined');
    }
  }

  /**
   * Handles the expansion/collapse state of nodes based on the specified expand state.
   * Can expand all nodes, first node only, or collapse all nodes.
   *
   * @param {TxTreeExpandState} expandState - The desired expansion state for the tree
   * @memberof PaginatedTreeGridDataSource
   */
  handleExpandCollapseNode(expandState: TxTreeExpandState) {
    this.expandedNodes.clear();
    if (expandState === TxTreeExpandState.Expanded) {
      this.expandNodes(super.dataList);
    } else if (expandState === TxTreeExpandState.ExpandedFirst) {
      this.expandNodes([super.dataList[0]]);
    }
    this.triggerUpdatePageRequest();
  }

  /**
   * Checks if a specific node is currently expanded.
   *
   * @param {T} node - The node to check
   * @returns {boolean} True if the node is expanded, false otherwise
   * @memberof PaginatedTreeGridDataSource
   */
  isExpanded(node: T): boolean {
    if (!this.primaryKey) {
      console.error('primaryKey not defined');
      return false;
    }
    return this.expandedNodes.has(node[this.primaryKey]);
  }
  /**
   *
   * @returns
   */
  parseData(): T[] {
    return flattenTree(this.dataList, this.childMapping);
  }

  expandNodes(dataList: T[]): void {
    dataList.forEach((data) => {
      if (data !== undefined) {
        const children = data[this.childMapping as keyof T] as T[] | undefined;
        if (children !== undefined && children?.length > 0) {
          this.expandedNodes.add(data[this.primaryKey as keyof T]);
          this.expandNodes(children);
        }
      }
    });
  }

  /**
   * Searches through the tree data using specified searchable keys.
   * Flattens the tree structure before performing the search.
   *
   * @param {T[]} tableData - The tree data to search through
   * @param {string} lowerCaseInput - Lowercase search input string
   * @param {T[keyof T] | undefined} selectedRowPK - Selected row's primary key
   * @param {(keyof T)[]} searchableKeys - Array of keys to search through
   * @memberof PaginatedTreeGridDataSource
   */
  override searchByData(
    lowerCaseInput: string,
    selectedRowPK: T[keyof T] | undefined,
    searchableKeys: (keyof T)[]
  ) {
    const flattenedData = this.parseData();
    const result = super.searchByData(lowerCaseInput, selectedRowPK, searchableKeys, flattenedData);
    if (
      result.selected &&
      result.selectedPK != null &&
      (isNaN(+result.selectedPK) || +result.selectedPK > -1) &&
      this.parentMapping
    ) {
      const ids = parentToRootIds(
        flattenedData,
        result.selectedPK as string,
        this.primaryKey,
        this.parentMapping
      );
      ids.pop(); // remove the last element,need only parent ids
      console.log(ids);
      ids.forEach((id) => {
        this.expandedNodes.add(id as T[keyof T]);
      });
      this.triggerUpdatePageRequest();
    }

    return result;
  }
  override prepareToSelection(selectionList: T[], selectedRowPKValue: T[keyof T]) {
    const flattenTableData = flattenTreeWithChild(selectionList);
    return super.prepareToSelection(flattenTableData, selectedRowPKValue);
  }
  /**
   * Connects the data source to a collection viewer.
   * Filters the tree based on expanded/collapsed state before returning the data.
   *
   * @param {CollectionViewer} [collectionViewer] - Optional collection viewer to connect to
   * @returns {Observable<T[]>} Observable of filtered tree data
   * @memberof PaginatedTreeGridDataSource
   */
  override connect(collectionViewer?: CollectionViewer): Observable<T[]> {
    return super.connect(collectionViewer).pipe(map((data) => this.filterTree(data)));
  }

  private filterTree(nodes: T[]): T[] {
    return nodes.reduce<T[]>((acc, node) => {
      const filteredChildren =
        this.isExpanded(node) && node[this.childMapping]
          ? this.filterTree(node[this.childMapping] as T[])
          : [];
      return [...acc, node, ...filteredChildren];
    }, []);
  }
  private areArraysEqualByPrimaryKey(
    arr1: any[],
    arr2: any[],
    primaryKey: keyof T | undefined
  ): boolean {
    if (arr1.length !== arr2.length) {
      return false;
    }
    const map1 = new Map(arr1.map((obj) => [obj[primaryKey], obj]));
    for (const obj of arr2) {
      const key = obj[primaryKey];
      if (!map1.has(key)) {
        return false;
      }
    }
    return true;
  }
  private generateTreeError() {
    return new TypeError('idMapping or parentMapping is missing to build tree hierarchies');
  }
}

function enhanceEndPoint<T extends TxTreeGridChildren<T>>(
  endPoint: PaginationEndpointFn<T> | Observable<T[]>,
  childMapping: keyof TxTreeGridChildren<T>
): PaginationEndpointFn<T> | Observable<T[]> {
  if (typeof endPoint !== 'function') {
    return endPoint.pipe(map((data) => addLevelsToTree(data, childMapping)));
  } else {
    return endPoint;
  }
}
