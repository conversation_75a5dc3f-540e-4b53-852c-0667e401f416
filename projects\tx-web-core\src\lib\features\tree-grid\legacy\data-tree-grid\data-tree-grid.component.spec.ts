import { TxDataTreeGridComponent } from './data-tree-grid.component';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { TreeGridComponent, TreeGridModule } from '@syncfusion/ej2-angular-treegrid';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { MockComponent } from 'ng-mocks';
import { TxTreeGrid } from '@bassetti-group/tx-web-core';

const TRANSLATIONS = {
  en: {},
  fr: {},
};

describe('DataTreeGridComponent', () => {
  let component: TxDataTreeGridComponent<{}>;
  let fixture: ComponentFixture<TxDataTreeGridComponent<{}>>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [MockComponent(TreeGridComponent)],
      imports: [
        NoopAnimationsModule,
        MatFormFieldModule,
        MatInputModule,
        MatTooltipModule,
        MatExpansionModule,
        TreeGridModule,
        FontAwesomeTestingModule,
        TranslateTestingModule.withTranslations(TRANSLATIONS),
      ],
      providers: [],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TxDataTreeGridComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  beforeEach(() => {
    fixture.detectChanges();
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Get cell tooltip', () => {
    it('should return only id and name', () => {
      expect(component.getTooltip({ id: 1, name: 'Contact', txObject: {} })).toBe(
        'txWebCore.generic.id 1\nContact'
      );
    });

    it('should return concatenation of id, name & explanation with break line', () => {
      expect(
        component.getTooltip({
          id: 1,
          name: 'Contact',
          txObject: { explanation: 'This is explanation' },
        })
      ).toBe('txWebCore.generic.id 1\nContact\nThis is explanation');
    });
  });

  describe('Search', () => {
    describe('Expand node in Grid', () => {
      let gridData: TxTreeGrid<{}>[];
      beforeEach(() => {
        gridData = [
          { id: 1, expanded: false, name: 'Contact', txObject: {} },
          { id: 2, expanded: true, name: 'Building', txObject: {} },
        ];
      });

      it('should modify the TreeGridObject "expanded" property to true', () => {
        component.onNodeExpanded({ data: { id: 1 } }, gridData);
        expect(gridData[0].expanded).toBe(true);
      });

      it('should modify the TreeGridObject "expanded" property to false', () => {
        component.onNodeCollapsed({ data: { id: 2 } }, gridData);
        expect(gridData[1].expanded).toBe(false);
      });
    });

    describe('Change opacity rows', () => {
      let rowMock: HTMLTableRowElement;
      beforeEach(() => {
        rowMock = document.createElement('tr');
      });

      it('should add an opacity for description rows', () => {
        component.onRowBound({ data: { id: 2.1 }, row: rowMock });
        expect(rowMock.classList.contains('row-opacity')).toBe(true);
      });

      it('should NOT add an opacity for standard rows', () => {
        component.onRowBound({ data: { id: 2 }, row: rowMock });
        expect(rowMock.classList.contains('row-opacity')).toBe(false);
      });
    });
  });
});
