import type { StorybookConfig } from '@storybook/angular';

const config: StorybookConfig = {
  previewBody(config, options) {
    if (!process.env.RELEASE_VERSION) return config;

    return `
      <script>
        document.addEventListener('DOMContentLoaded', () => {
            const observer = new MutationObserver(() => {
                let t = document.querySelector('h1.sbdocs-title, h1#component-norecord')
                if (t && !t.releaseNumberPrepended) {
                    t.textContent = '${process.env.RELEASE_VERSION + ' - '}' + t.textContent 
                    t.releaseNumberPrepended = true;
                }
            });
            observer.observe(document.body, { childList: true });
        })
      </script>
      ${config}
    `;
  },
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-interactions',
    '@storybook/addon-docs',
  ],
  framework: {
    name: '@storybook/angular',
    options: {},
  },
  docs: {
    autodocs: 'tag',
    defaultName: 'Documentation',
  },
  staticDirs: ['../src/assets'],
};
export default config;
