import { Component, Input } from '@angular/core';
import { LegacyTxDataString } from '../../../services/structure/models/data';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { TxAttributeField } from '../../../models/formConfiguration/businessClass/attribute-field';
import { TxTextInputObjectFieldComponent } from '../text-input-object-field/text-input-object-field.component';

@Component({
  selector: 'tx-long-text-object-field',
  templateUrl: '../text-input-object-field/text-input-object-field.component.html',
  styleUrls: [
    './long-text-object-field.component.scss',
    '../text-input-object-field/text-input-object-field.component.scss',
  ],
})
export class TxLongTextObjectFieldComponent extends TxTextInputObjectFieldComponent {
  @Input() declare field: TxAttributeField;
  @Input() declare data: LegacyTxDataString;

  constructor(public attributeService: LegacyTxAttributesService) {
    super(attributeService);
    this.inputType = 'text';
    this.inTextArea = true;
    if (!this.numberOfLine) {
      this.numberOfLine = 5;
    }
    this.textAreaHeight = this.numberOfLine * 14; // fontSize + 1
  }

  initValue() {
    if (this.data) {
      this.value = this.data.value;
    }
  }

  initPropertiesFromField() {
    super.initPropertiesFromField();
    this.maxLength = this.maxLength ?? 850;
  }

  getData(): LegacyTxDataString {
    return new LegacyTxDataString(this.idObject, this.idAttribute, this.control.value);
  }
}
