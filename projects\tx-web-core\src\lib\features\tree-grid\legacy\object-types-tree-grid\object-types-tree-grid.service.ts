import { Injectable } from '@angular/core';
import {
  TxObjectTypeType,
  TxObjectType,
} from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TxObjectsTypeService } from '@bassetti-group/tx-web-core/src/lib/data-access/structure';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TxTreeGrid } from '../../tree-grid.models';

@Injectable()
export class TxObjectTypesTreeGridService {
  constructor(public otService: TxObjectsTypeService) {}

  public initTreeGridData(data: TxObjectType[]): TxTreeGrid<TxObjectType>[] {
    const title: string[] = [
      _('txWebCore.generic.standards'),
      _('txWebCore.generic.listings'),
      _('txWebCore.generic.associatives'),
      _('txWebCore.generic.systems'),
    ];
    let treeGridArray: TxTreeGrid<TxObjectType>[] = this.createObjectParentArray(title);
    treeGridArray = treeGridArray.concat(data.map((o) => this.createOTinGrid(o, treeGridArray)));

    //Associate the child to its parent (only for the objects with no parent)
    treeGridArray.forEach((o) => {
      if (o.txObject.idObjectTypeParent === undefined) {
        if (
          o.txObject.type === TxObjectTypeType.Standard ||
          o.txObject.type === TxObjectTypeType.User ||
          o.txObject.type === TxObjectTypeType.Portal
        ) {
          o.idParent = -1;
        } else if (o.txObject.type === TxObjectTypeType.Listing) {
          o.idParent = -2;
        } else if (o.txObject.type === TxObjectTypeType.Associativity) {
          o.idParent = -3;
        } else if (
          o.txObject.type === TxObjectTypeType.Source ||
          o.txObject.type === TxObjectTypeType.Information
        ) {
          o.idParent = -4;
        }
      }
    });

    return treeGridArray;
  }

  public getDataLengthAccordingType(
    id: number,
    data: TxTreeGrid<TxObjectType>[]
  ): number | undefined {
    if (id === -1) {
      return data.filter(
        (o) =>
          o.txObject.type === TxObjectTypeType.Standard ||
          o.txObject.type === TxObjectTypeType.User ||
          o.txObject.type === TxObjectTypeType.Portal
      ).length;
    } else if (id === -2) {
      return data.filter((o) => o.txObject.type === TxObjectTypeType.Listing).length;
    } else if (id === -3) {
      return data.filter((o) => o.txObject.type === TxObjectTypeType.Associativity).length;
    } else if (id === -4) {
      return data.filter(
        (o) =>
          o.txObject.type === TxObjectTypeType.Source ||
          o.txObject.type === TxObjectTypeType.Information
      ).length;
    }
  }

  public createObjectParentArray(title: string[]): TxTreeGrid<TxObjectType>[] {
    return title.map((parentName, index) => {
      const idObject = -(index + 1); // Calculate the id
      if (idObject === -1) {
        //expand first object parent
        return this.createTreeGridObject(
          idObject,
          './assets/tx-web-core/img/icons/124.svg',
          parentName,
          true
        );
      } else {
        return this.createTreeGridObject(
          idObject,
          './assets/tx-web-core/img/icons/124.svg',
          parentName,
          false
        );
      }
    });
  }

  private createTreeGridObject(
    idObject: number,
    iconPath: string,
    objectName: string,
    expandedObject: boolean
  ): TxTreeGrid<TxObjectType> {
    const emptyString: string[] = [];
    return {
      id: idObject,
      icon: iconPath,
      name: objectName,
      expanded: expandedObject,
      tags: emptyString,
      txObject: {} as TxObjectType,
    };
  }

  private createOTinGrid(
    ot: TxObjectType,
    otGrid: TxTreeGrid<TxObjectType>[]
  ): TxTreeGrid<TxObjectType> {
    return {
      id: ot.id,
      idParent: ot.idObjectTypeParent,
      icon: this.otService.getIconPath(ot.id),
      name: ot.name,
      tags: ot.tags,
      expanded: this.getExpandedState(ot, otGrid),
      txObject: ot,
    };
  }

  private getExpandedState(obj: TxObjectType, objGrid: TxTreeGrid<TxObjectType>[]): boolean {
    if (objGrid) {
      const o = objGrid.find((og) => og.id === obj.id);
      return o?.expanded ? o.expanded : true;
    }
    return true;
  }
}
