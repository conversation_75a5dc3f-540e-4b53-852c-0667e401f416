import { LegacyTxLinkDisplayMode } from '../../../services/structure/models/attribute';
import { TxVirtualAttributeField } from './virtual-attribute-field';
import { LegacyTxAttribute } from '../../../services/structure/models/attribute';
import { TxIdConcept } from '../../generics/Id-concept';
import { TxAttributeField } from './attribute-field';
import { TxLinkAttributeFilteringType } from './form-enum';

export class TxLinkField extends TxIdConcept {
  filteringAttributes!: LegacyTxAttribute[];
  filteredAttributes!: LegacyTxAttribute[];
  handleDynamicGroups!: boolean;
  filterType!: TxLinkAttributeFilteringType;
  idDestinationObjectType!: number;
  idFilteringObject!: number;
  strongFilter!: boolean;
  multiple!: boolean;
  linkViewMode!: LegacyTxLinkDisplayMode;
  transposeTable!: boolean;
  hideRootLinks!: boolean;
  linkedFields: TxVirtualAttributeField[] = [];

  constructor() {
    super();
  }

  reset() {
    super.reset();
    this.handleDynamicGroups = false;
    this.filterType = TxLinkAttributeFilteringType.undefined;
    this.idDestinationObjectType = 0;
    this.idFilteringObject = 0;
    this.strongFilter = false;
    this.multiple = false;
    this.linkViewMode = LegacyTxLinkDisplayMode.ComboTree;
    this.transposeTable = false;
    this.hideRootLinks = false;
    this.filteredAttributes = [];
    this.filteringAttributes = [];
  }

  addLinkedField(field: TxVirtualAttributeField) {
    this.linkedFields.push(TxVirtualAttributeField.assign(field));
  }

  assign(object: Partial<TxLinkField>) {
    super.assign(object);
    this.filteringAttributes = object.filteringAttributes as LegacyTxAttribute[];
    this.filteredAttributes = object.filteredAttributes as LegacyTxAttribute[];
    this.handleDynamicGroups = object.handleDynamicGroups as boolean;
    this.filterType = object.filterType as TxLinkAttributeFilteringType;
    this.idDestinationObjectType = object.idDestinationObjectType as number;
    this.strongFilter = object.strongFilter as boolean;
    this.multiple = object.multiple as boolean;
    this.linkViewMode = object.linkViewMode as LegacyTxLinkDisplayMode;
    this.transposeTable = object.transposeTable as boolean;
    this.hideRootLinks = object.hideRootLinks as boolean;

    if (object.linkedFields) {
      this.linkedFields = object.linkedFields.map((linkedField) =>
        TxVirtualAttributeField.assign(linkedField)
      );
    }
  }
}

export class TxLinkedAttributeField extends TxAttributeField {
  linksAttTags!: string;

  constructor() {
    super();
  }

  assign(lnkedAttField?: Partial<TxLinkedAttributeField>) {
    super.assign(lnkedAttField);
    this.linksAttTags = lnkedAttField?.linksAttTags as string;
  }
}
