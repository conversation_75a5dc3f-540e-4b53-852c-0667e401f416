<tx-tree-grid
  #treeGrid
  primaryKey="id"
  [columns]="txColumns"
  [rowHeight]="rowHeight"
  [showSpinner]="showSpinner$ | async"
  [isRowSelectable]="true"
  [enableSearching]="enableSearching"
  [disableSelectionHandling]="true"
  (rowSelected)="handleNativeSelect($event)"
  (searchInputChange)="handleSearch($event)"
  (searchClear)="handleSearchClear()"
  class="object-tree-grid"
  id="objectTreeGrid">
  <tx-grid-toolbar>
    <ng-template>
      <div class="toolbar-align">
        <mat-slide-toggle
          #toggle
          class="toggle-show-selection"
          (change)="showSelectionToggle($event)">
          {{ 'txWebCore.generic.showSelection' | translate }}</mat-slide-toggle
        >
        <button
          type="button"
          *ngIf="displayCheckAllButton && multipleSelection"
          mat-icon-button
          [disabled]="displayMasterCheckBox && isFolderNotHighlighted"
          (click)="selectFolderChildren()">
          <fa-icon [icon]="['fal', 'check']" matSuffix></fa-icon>
        </button>
        <button
          type="button"
          *ngIf="displayUnCheckAllButton"
          mat-icon-button
          [disabled]="displayMasterCheckBox && isFolderNotHighlighted"
          (click)="selectFolderChildren(true)">
          <fa-icon [icon]="['fal', 'times']"></fa-icon>
        </button>
      </div>
    </ng-template>
  </tx-grid-toolbar>
  <tx-grid-column fieldName="name">
    <ng-template #template let-data>
      <mat-checkbox
        *ngIf="enableCheckbox && (data.txObject.isFolder ? folderCheckable : true)"
        [checked]="isSelected(data)"
        (change)="handleSelection(data, $event)"></mat-checkbox>
      <span class="user-selection">
        <tx-column-name-template
          [showMoreTooltips]="true"
          [hasMainParentObject]="false"
          [data]="data"></tx-column-name-template>
      </span>
    </ng-template>
    <ng-template #headerTemplate let-data>
      <mat-checkbox
        [checked]="isAllSelected"
        [indeterminate]="hasSelectedItems() && !isAllSelected"
        (change)="multipleSelection ? handleMasterCheckbox() : clearSelection($event)"
        [color]="multipleSelection ? 'accent' : 'warn'"
        [disabled]="!multipleSelection && !hasSelectedItems()"
        *ngIf="enableCheckbox && displayMasterCheckBox && multipleSelection"></mat-checkbox>
      <span>{{ data }} {{ selectionCount }}</span>
    </ng-template>
  </tx-grid-column>
</tx-tree-grid>

<tx-context-menu
  trigger="#grid-table"
  [items]="menuItems"
  (beforeOpen)="beforeContextMenu($event)"
  (select)="selectOnContextMenu($event)"></tx-context-menu>
