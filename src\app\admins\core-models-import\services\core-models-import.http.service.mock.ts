import { MockService } from 'ng-mocks';
import { CoreModelsImportHttpService } from './core-models-import.http.service';
import { of, throwError } from 'rxjs';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { formatDate } from '@angular/common';
import { ArchiveData, ArchiveStatus } from '../models/archive-info.model';
import {
  CoreModelImportHistoryDTO,
  CoreModelsArchiveSummaryDTO,
  ImportedDTO,
  TestedImportDTO,
} from '../models/core-models-import.dto';
import { TestedCoreModelConcept } from '../models/tested-core-model-concept.model';
import { ModificationTypeEnum } from '../enums/modification.enum';
import { ImportedCoreModelConcept } from '../models/imported-core-model-concept.model';
import { ConceptType } from '@bassetti-group/tx-web-core';

const arrayBuffer = new ArrayBuffer(2);
const dataView = new DataView(arrayBuffer);
dataView.setInt8(0, 104);
dataView.setInt8(1, 105);
const blob = new Blob([arrayBuffer]);
export const VALID_ARCHIVE_FILE = new File([blob], 'valid-archive.zip', {
  type: 'application/zip',
});
export const INVALID_ARCHIVE_FILE = new File([blob], 'valid-archive.zip', {
  type: 'application/zip',
});
const logFile = new File([blob], 'log.txt', { type: 'text/plain' });
const DATE_FORMAT = 'short';
const DEFAULT_LANGUAGE_USED_CODE = 'en';
export const ARCHIVE_SUMMARY: CoreModelsArchiveSummaryDTO = {
  id: 'string',
  cacheId: 1,
  version: '0.0.1',
  explanation: 'test',
  name: 'test',
  date: new Date().toString(),
  user: 'john',
  history: [],
};

export const CONCEPT_DTO: TestedImportDTO = {
  isValid: true,
  importedConcepts: [
    {
      type: ConceptType.ObjectType,
      id: 25,
      idObjectType: 25,
      name: 'TEST_WF',
      tag: 'otTESTWF_CM8974C727',
      icon: 0,
      conflicts: ['conflict'],
      modificationType: 'creation',
    },
    {
      type: ConceptType.ObjectType,
      id: 25,
      idObjectType: 25,
      name: 'TEST_WF',
      tag: 'otTESTWF_CM8974C727',
      icon: 0,
      conflicts: [],
      modificationType: 'creation',
    },
  ],
};
export const IMPORT_CONCEPT_DTO: ImportedDTO = {
  isValid: true,
  importedConcepts: [
    {
      type: 'objectType',
      id: 1,
      tag: 'string',
      idObjectType: 1,
      icon: 1,
      name: 'name',
      modificationType: 'modification',
    },
  ],
};
export const IMPORTED_CONCEPTS: ImportedCoreModelConcept[] = [
  {
    type: ConceptType.ObjectType,
    id: 1,
    tags: ['string'],
    icon: 1,
    name: 'name',
    modificationType: ModificationTypeEnum.Modification,
    translatedModificationType: 'modification',
  },
];

export const TESTED_CORE_MODELS_CONCEPTS_MOCK: TestedCoreModelConcept[] = [
  {
    type: ConceptType.ObjectType,
    id: 25,
    name: 'TEST_WF',
    tags: ['otTESTWF_CM8974C727'],
    conflicts: ['conflict'],
    explanation: '',
    translatedConflicts: [],
    modificationType: ModificationTypeEnum.Creation,
    translatedModificationType: 'creation',
  },
  {
    type: ConceptType.ObjectType,
    id: 25,
    name: 'TEST_WF',
    tags: ['otTESTWF_CM8974C727'],
    conflicts: [],
    explanation: '',
    translatedConflicts: [],
    modificationType: ModificationTypeEnum.Creation,
    translatedModificationType: 'creation',
  },
];

export const CORE_MODEL_IMPORT_DATA_SERVICE_MOCK = MockService(CoreModelsImportHttpService, {
  loadHistory: () => {
    return of(IMPORT_HISTORY);
  },
  validateArchive: (file: File) => {
    if (file.name === 'valid-archive.zip') {
      return of(ARCHIVE_SUMMARY);
    }
    return throwError(() => {
      const error = new Error(`This is error number`);
      return error;
    });
  },
  testImport: (file: File) => of(CONCEPT_DTO),
  import: (file: File) => of(IMPORT_CONCEPT_DTO),
});
export const IMPORT_HISTORY: CoreModelImportHistoryDTO['importHistory'] = [
  {
    id: 'id_1',
    date: new Date().toString(),
    success: true,
    username: 'user',
    name: 'name',
    version: '0.0.1',
    explanation: 'explanation',
    comment: 'comment',
    importedConcepts: [
      {
        type: 'Attribute',
        id: 1,
        tag: 'tags',
        idObjectType: 1,
        icon: 1,
        name: 'name',
        modificationType: 'Add',
        conflicts: ['conflicts'],
      },
    ],
  },
];
export const INVALID_ARCHIVE_ID = 2;
export const VALID_ARCHIVE_MOCK = VALID_ARCHIVE_FILE;
export const INVALID_ARCHIVE_MOCK = INVALID_ARCHIVE_FILE;
export const BLOB_MOCK = new Blob([]);
export const ERR_MSG_MOCK = '';
export const VALID_ARCHIVE: ArchiveData = {
  id: 'a8dffdsdsfs',
  cacheId: 1,
  file: VALID_ARCHIVE_MOCK,
  archiveInfo: [
    {
      label: _('admins.coreModelsImport.archiveVersion'),
      info: ARCHIVE_SUMMARY.version,
      icon: 'code-commit' as IconName,
    },
    {
      label: _('generic.creationDate'),
      info: formatDate(ARCHIVE_SUMMARY.date, DATE_FORMAT, DEFAULT_LANGUAGE_USED_CODE),
      icon: 'code-commit' as IconName,
    },
    {
      label: _('generic.createdBy'),
      info: ARCHIVE_SUMMARY.user,
      icon: 'user' as IconName,
    },
    {
      label: _('generic.createdBy'),
      info: ARCHIVE_SUMMARY.user,
      icon: 'user' as IconName,
    },
    {
      label: _('generic.createdBy'),
      info: ARCHIVE_SUMMARY.user,
      icon: 'user' as IconName,
    },
  ],
  status: ArchiveStatus.Valid,
  history: [],
};
