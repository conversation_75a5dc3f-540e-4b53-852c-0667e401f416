import { NgModule } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { TreeViewModule } from '@syncfusion/ej2-angular-navigations';
import { AutomaticNamingComponent } from './components/automatic-naming.component';
import { ObjectSelectionFormComponent } from './components/object-selection-form/object-selection-form.component';
import { TxAutomaticNamingService } from './services/automatic-naming.service';
import { AutomaticNamingRoutingModule } from './automatic-naming-routing.module';
import { DatePipe } from '@angular/common';

@NgModule({
  declarations: [AutomaticNamingComponent, ObjectSelectionFormComponent],
  imports: [
    SharedModule,
    AutomaticNamingRoutingModule,
    // Syncfusion
    TreeViewModule,
  ],
  providers: [TxAutomaticNamingService, DatePipe],
})
export class AutomaticNamingModule {
  getComponent() {
    return AutomaticNamingComponent;
  }
}
