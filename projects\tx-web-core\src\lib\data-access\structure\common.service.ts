import { Injectable } from '@angular/core';
import {
  ConceptType,
  TxConceptIconEnum,
} from '@bassetti-group/tx-web-core/src/lib/business-models';

@Injectable({
  providedIn: 'root',
})
export class TxCommonService {
  constructor() {}

  /**
   *
   * @param conceptType
   * @param objectTypeIcon
   * @returns
   */
  getConceptTypeIcon(
    conceptType: ConceptType,
    objectTypeIcon?: number | undefined
  ): number | undefined {
    switch (conceptType) {
      case ConceptType.Attribute:
        return objectTypeIcon;
      case ConceptType.FileType:
        return TxConceptIconEnum.FileType;
      case ConceptType.LinkType:
        return TxConceptIconEnum.LinkType;
      case ConceptType.ChoiceGuide:
        return TxConceptIconEnum.ChoiceGuide;
      case ConceptType.TableType:
        return TxConceptIconEnum.TableType;
      case ConceptType.SeriesType:
        return TxConceptIconEnum.SeriesType;
      case ConceptType.Exportation:
        return TxConceptIconEnum.Exportation;
      case ConceptType.ModelApplication:
        return TxConceptIconEnum.ModelApplication;
      case ConceptType.Model:
        return TxConceptIconEnum.Model;
      case ConceptType.Log:
        return TxConceptIconEnum.LogOrSettingFile;
      case ConceptType.UserGroup:
        return TxConceptIconEnum.UserGroup;
      case ConceptType.Language:
        return TxConceptIconEnum.Language;
      case ConceptType.Unit:
        return TxConceptIconEnum.Unit;
      case ConceptType.Object:
      case ConceptType.BusinessView:
      case ConceptType.AttributeSet:
      case ConceptType.AsStandard:
      case ConceptType.AsLinkType:
      case ConceptType.AsLinkTypeInverse:
      case ConceptType.AdvancedCreation:
      case ConceptType.AdvancedDuplication:
      case ConceptType.AdvancedDeletion:
        return objectTypeIcon;
      case ConceptType.Question:
        return TxConceptIconEnum.Question;
      case ConceptType.ObjectType:
        return objectTypeIcon;
      case ConceptType.SettingFile:
        return TxConceptIconEnum.LogOrSettingFile;
      default:
        return undefined;
    }
  }
}
