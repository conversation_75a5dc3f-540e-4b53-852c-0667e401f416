import { Component, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import {
  CoreModelExportHistory,
  CoreModelExportHistoryForm,
  VERSION_ENUM_LABEL,
  VersionEnum,
} from '../../models/core-model-export-history.model';
import { RightPaneSettings } from 'src/app/core/right-pane/right-pane.models';
import { RightPaneRef } from 'src/app/core/right-pane/right-pane.ref';
import {
  newMajorVersion,
  newMinorVersion,
  newReleaseVersion,
} from 'src/app/core/utils/version.utils';

@Component({
  selector: 'app-core-model-export-form',
  templateUrl: './core-model-export-form.component.html',
  styleUrls: ['./core-model-export-form.component.scss'],
})
export class CoreModelExportFormComponent implements OnInit {
  formGroup: FormGroup<CoreModelExportHistoryForm>;
  versionsDisplay: { versionValue: string; label: string }[] = [];
  VersionEnum = VersionEnum;
  previousVersion: string | undefined;
  readonly nameControlMaxLength = 35;
  constructor(
    private readonly rightPaneSettings: RightPaneSettings<CoreModelExportHistory>,
    private paneRef: RightPaneRef,
    private fb: FormBuilder
  ) {
    this.formGroup = this.fb.group<CoreModelExportHistoryForm>({
      version: this.fb.control<string>('', {
        nonNullable: true,
        validators: Validators.required,
      }),
      name: this.fb.control<string>('', {
        nonNullable: true,
        validators: [Validators.required, Validators.maxLength(30)],
      }),
      explanation: this.fb.control<string>('', {
        nonNullable: true,
        validators: Validators.required,
      }),
      comment: this.fb.control<string>('', {
        nonNullable: true,
        validators: Validators.required,
      }),
    });

    if (this.rightPaneSettings.data !== undefined) {
      const { version, name, explanation } = this.rightPaneSettings.data;
      this.formGroup.patchValue({ name, explanation });
      this.previousVersion = version;
    }
  }

  get versionControl() {
    return this.formGroup.get('version') as FormControl<string>;
  }
  get nameControl() {
    return this.formGroup.get('name') as FormControl<string>;
  }
  get explanationControl() {
    return this.formGroup.get('explanation') as FormControl<string>;
  }

  get commentControl() {
    return this.formGroup.get('comment') as FormControl<string>;
  }

  ngOnInit(): void {
    try {
      this.versionsDisplay = VERSION_ENUM_LABEL.map((version) => {
        switch (version.value) {
          case VersionEnum.Major:
            return {
              label: version.label,
              versionValue: newMajorVersion(this.previousVersion),
            };
          case VersionEnum.Minor:
            return {
              label: version.label,
              versionValue: newMinorVersion(this.previousVersion),
            };
          case VersionEnum.Release:
            return {
              label: version.label,
              versionValue: newReleaseVersion(this.previousVersion),
            };
        }
      });
    } catch (error) {
      //TODO implements snack bar to express to user that there are some trouble with version
    }

    this.formGroup.markAllAsTouched();
  }

  addCoreModelsExportHistoric(): void {
    if (this.formGroup.valid) {
      const rightPaneResult = {
        hasError: false,
        cancel: false,
        data: { ...this.formGroup.getRawValue(), date: new Date() },
      };
      this.paneRef.close(rightPaneResult);
    }
  }
  cancel(): void {
    this.paneRef.close();
  }
}
