@use '@angular/material' as mat;

@mixin color-picker-theme($theme) {
  $background: map-get($theme, background);
  $foreground: map-get($theme, foreground);
  $primary: map-get($theme, primary);

  /* override colors to be compatible with the dark mode */
  .ngx-colors-overlay {
    .opened {
      background-color: mat.m2-get-color-from-palette($foreground, grey5);
      color: mat.m2-get-color-from-palette($foreground, text);
    }
    .round-button svg,
    .add svg {
      fill: mat.m2-get-color-from-palette($foreground, text);
    }
    .nav-wrapper button {
      color: mat.m2-get-color-from-palette($foreground, text) !important;
    }
    .nav-wrapper button:hover,
    .round-button:hover {
      background-color: mat.m2-get-color-from-palette($foreground, grey10) !important;
      cursor: pointer;
    }
    .manual-input-wrapper {
      div {
        border-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
      }
      input {
        background-color: mat.m2-get-color-from-palette($background, base);
        color: mat.m2-get-color-from-palette($foreground, grey60) !important;
      }
    }
  }
}

/* Define global rules to override some CSS from ngx-colors library */
.ngx-colors-overlay {
  .opened {
    width: 354px !important;

    .manual-input-wrapper {
      display: none !important;
    }
  }
  .colors {
    align-items: flex-start !important;

    .circle:first-child {
      margin-bottom: 20px !important;
    }
    .circle {
      height: 26px !important;
      width: 26px !important;
      flex: 26px 0 0 !important;

      .selected {
        height: 20px !important;
        width: 20px !important;
      }
    }
  }
  .color-picker {
    width: 100% !important;
    display: flex !important;
    flex-direction: column;
    align-items: center;

    .saturation-lightness {
      width: 220px !important;
    }
    .box {
      width: 100% !important;
    }
  }
}

.tx-color-light-mode {
  .color-text-recommended {
    display: block;
    width: 100%;
    margin-bottom: 8px;
    margin-left: 5px;
  }
  .color-text-advanced {
    position: absolute;
    top: 167px;
    margin-left: 5px;
  }
  .circle:nth-child(3) {
    margin-bottom: 20px !important;
  }
  .circle:nth-child(27) {
    margin-bottom: 50px !important;
  }
}
