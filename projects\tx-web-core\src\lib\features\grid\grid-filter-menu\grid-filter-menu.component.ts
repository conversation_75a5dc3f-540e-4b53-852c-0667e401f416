// @ts-strict-ignore
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Column } from '@syncfusion/ej2-angular-grids';
import { BehaviorSubject, Subject, combineLatest, filter, takeUntil } from 'rxjs';
import { TxGridFilterType } from './grid-filter-menu.model';
import { MatChipsModule } from '@angular/material/chips';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { CommonModule } from '@angular/common';
import { TxDatepickerComponent } from '@bassetti-group/tx-web-core/src/lib/ui/generic-fields/datepicker';
import { FormsModule } from '@angular/forms';
import {
  TxConceptDropdownComponent,
  TxObjectsTypeDropdownComponent,
} from '@bassetti-group/tx-web-core/src/lib/features/dropdown';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { TxFormatTooltipFilterPipe } from './format-tooltip-filter.pipe';
import { TxNotEmptyFilterPipe } from './not-empty-filter.pipe';
import {
  TxObjectType,
  TxObjectTypeType,
} from '@bassetti-group/tx-web-core/src/lib/business-models';

@Component({
  standalone: true,
  imports: [
    FormsModule,
    MatChipsModule,
    FontAwesomeModule,
    MatTooltipModule,
    MatMenuModule,
    TranslateModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    CommonModule,
    TxDatepickerComponent,
    TxObjectsTypeDropdownComponent,
    TxConceptDropdownComponent,
    TxFormatTooltipFilterPipe,
    TxNotEmptyFilterPipe,
  ],
  selector: 'tx-grid-filter-menu',
  templateUrl: './grid-filter-menu.component.html',
  styleUrls: ['./grid-filter-menu.component.scss'],
})
export class TxGridFilterMenuComponent implements OnInit, OnDestroy {
  @Input() disabled?: boolean;
  @Input() blackListedColsNames?: string[];

  /**
   * Input to set new filters.
   * The existing filters will be replaced.
   */
  @Input() set newFilters(value: GridFilter[] | undefined) {
    if (value !== undefined) {
      this.removeAllFilters();
      value.forEach((filter) => this.createNewFilter(filter));
    }
  }

  @Output() filter = new EventEmitter<any>();
  @Output() deleteFilter = new EventEmitter<any>();
  @Output() deleteAllFilters = new EventEmitter<any>();

  public filterList: GridFilter[] = [];
  public filterableCols: any[] = [];

  private readonly filterTypes: { [key: string]: string[] } = {
    string: ['equal', 'notEqual', 'startsWith', 'endsWith', 'contains'],
    number: [
      'equal',
      'notEqual',
      'greaterThan',
      'greaterThanOrEqual',
      'lessThan',
      'lessThanOrEqual',
    ],
    boolean: ['equal', 'notEqual'],
    date: ['equal', 'greaterThanOrEqual', 'lessThan'],
    objectType: ['contains', 'equal', 'notEqual'],
    conceptDropDown: ['equal', 'notEqual'],
  };

  private readonly filterNamesForTranslation = [
    _('txWebCore.gridFilters.filterTypes.equal'),
    _('txWebCore.gridFilters.filterTypes.notEqual'),
    _('txWebCore.gridFilters.filterTypes.startsWith'),
    _('txWebCore.gridFilters.filterTypes.endsWith'),
    _('txWebCore.gridFilters.filterTypes.contains'),
    _('txWebCore.gridFilters.filterTypes.greaterThan'),
    _('txWebCore.gridFilters.filterTypes.greaterThanOrEqual'),
    _('txWebCore.gridFilters.filterTypes.lessThan'),
    _('txWebCore.gridFilters.filterTypes.lessThanOrEqual'),
  ];

  private _columns: Column[] = [];
  private readonly _columnsSub = new BehaviorSubject<Column[]>([]);
  private readonly _columns$ = this._columnsSub.asObservable();
  private readonly _optionsForListOptionColSub = new BehaviorSubject<FilterOptionsList[]>([]);
  private readonly _optionsForListOptionCol$ = this._optionsForListOptionColSub.asObservable();
  private readonly _destroying = new Subject<void>();
  private _optionsForListOptionCol: FilterOptionsList[] = [];
  private _objectTypeFilterOptions: FilterOptions[] | null = null;
  constructor(public translate: TranslateService) {}

  get columns() {
    return this._columns;
  }
  @Input() set columns(value: Column[]) {
    if (value !== undefined) {
      this._columns = value;
      this._columnsSub.next(value);
    }
  }
  // eslint-disable-next-line @typescript-eslint/member-ordering
  get optionsForListOptionCol(): FilterOptionsList[] {
    return this._optionsForListOptionCol;
  }
  @Input() set optionsForListOptionCol(value: FilterOptionsList[]) {
    if (value !== undefined) {
      this._optionsForListOptionCol = value;
      this._optionsForListOptionColSub.next(value);
    }
  }

  ngOnInit(): void {
    combineLatest([
      this._columns$.pipe(filter((columns) => columns.length > 0)),
      this._optionsForListOptionCol$.pipe(
        filter((filterOptionList) => filterOptionList.length > 0)
      ),
    ])
      .pipe(takeUntil(this._destroying))
      .subscribe(() => {
        this.formatColsWithOptions();
      });
  }

  setFilterableCols(): void {
    if (this.filterableCols.length === 0) {
      this.columns?.forEach((col) => {
        if (
          this.isFilterableColType((col as any).type) &&
          !this.blackListedColsNames?.some((colName) => (col as any).field === colName)
        ) {
          this.filterableCols.push(col);
        }
      });
    }
  }

  public formatColsWithOptions(): void {
    this.optionsForListOptionCol?.forEach((filterOption) => {
      const concernedCol = this.columns?.find((col) => filterOption.column === col.field) as any;
      if (concernedCol) {
        concernedCol.filterOptions =
          filterOption.filterType === TxGridFilterType.ObjectType &&
          this._objectTypeFilterOptions !== null
            ? this._objectTypeFilterOptions
            : filterOption.options;
        concernedCol.filterType = filterOption.filterType;
      }
    });
  }

  public addNewFilter(): void {
    this.filterList.push({ column: undefined, operator: '', value: '' });
  }

  public createNewFilter(filter: GridFilter) {
    const existingFilter = this.filterList.find((f) => f.column?.field === filter.column?.field);
    if (existingFilter) {
      existingFilter.operator = filter.operator;
      existingFilter.value = filter.value;
      this.filter.emit(this.formatValue(existingFilter));
    } else {
      filter.column = this.columns?.find((col) => filter.column?.field === (col as any).field);
      this.filterList.push(filter);
      this.filter.emit(this.formatValue(filter));
    }
  }

  public removeFilter(filterIndex: number): void {
    const field = this.filterList[filterIndex]?.column?.field
    this.filterList.splice(filterIndex, 1);
    if (field !== undefined) {
      this.deleteFilter.emit(field);
    }
  }

  public removeAllFilters(): void {
    this.filterList = [];
    this.deleteAllFilters.emit();
  }

  public isFilterableColType(colType: string): boolean {
    return Object.keys(this.filterTypes).some((type) => type === colType);
  }

  public isColumnAlreadyFiltered(colName: string): boolean {
    return this.filterList.some((filter) => filter.column?.field === colName);
  }

  public getFilterTypesByColType(colType: string, isFilterOptions: boolean): string[] {
    if (isFilterOptions) {
      return this.filterTypes.boolean;
    }
    return this.filterTypes[colType];
  }

  public changeFilterColumn(event: any, filterIndex: number): void {
    if (this.filterList[filterIndex]?.column?.field) {
      this.deleteFilter.emit(this.filterList[filterIndex].column?.field);
    }
    this.filterList[filterIndex].column = this.columns.find((col) => event.value === col.field);
    const filterOption = this.optionsForListOptionCol.find((col) => event.value === col.column);
    if (filterOption) {
      this.filterList[filterIndex].hideFilterType = filterOption.hideFilterType;
      if (this.filterList[filterIndex].hideFilterType) {
        this.filterList[filterIndex].operator = filterOption.filterOperator ?? 'equal';
      }
    } else {
      this.filterList[filterIndex].hideFilterType = false;
    }
    this.filterList[filterIndex].value = '';

    if (
      !this.filterList[filterIndex].hideFilterType &&
      this.filterList[filterIndex].operator === ''
    ) {
      if (this.filterList[filterIndex].column?.type === 'string') {
        this.filterList[filterIndex].operator = 'contains';
      } else {
        this.filterList[filterIndex].operator = 'equal';
      }
    }
    this.applyFilter(filterIndex);
  }

  public changeFilterType(event: any, filterIndex: number): void {
    this.filterList[filterIndex].operator = event.value;
    this.applyFilter(filterIndex);
  }

  public onDropDownObjectTypeOptionsReloaded(filterIndex: number, options: any[]) {
    const column = this.filterList[filterIndex].column;
    if (column) {
      const filterOptions = options.map((o) => {
        o.value = o.id;
        o.text = o.name;
        return o;
      });
      column.filterOptions = filterOptions;
      this._objectTypeFilterOptions = filterOptions;
    }
  }

  public getObjectTypeTypes(filterIndex: number) {
    const filter = this.filterList[filterIndex];

    if (filter?.column) {
      const option = this.optionsForListOptionCol?.find((o) => o.column === filter.column?.field);
      if (option?.settings) {
        return option.settings.types;
      }
    }

    return [TxObjectTypeType.Standard, TxObjectTypeType.User];
  }

  public getOnlyVisibleObjectType(filterIndex: number) {
    const filter = this.filterList[filterIndex];

    if (filter?.column) {
      const option = this.optionsForListOptionCol?.find((o) => o.column === filter.column?.field);
      if (option?.settings) {
        return option.settings.onlyVisible;
      }
    }

    return true;
  }

  public applyDropDownTreeFilter(filterIndex: number, selectedOTs: TxObjectType[]) {
    this.applyFilter(filterIndex, selectedOTs);
  }

  public applyFilter(filterIndex: number, value?: any): void {
    setTimeout(() => {
      if (value) {
        this.filterList[filterIndex].value = value;
      }
      this.filter.emit(this.formatValue(this.filterList[filterIndex]));
    }, 50);
  }

  public onFilterDateChange(date: string, filter: GridFilter, filterIndex: number) {
    filter.value = date;
    this.applyFilter(filterIndex);
  }

  ngOnDestroy(): void {
    this._destroying.next();
  }
  /* Private Methods */
  private formatValue(filter: GridFilter): GridFilter {
    const result = { ...filter };
    if (result.column?.type === 'number' && result.value !== '') {
      result.value = parseFloat(result.value as string);
    }
    return result;
  }
}
export interface FilterOptionsList {
  column: string;
  options: FilterOptions[];
  hideFilterType?: boolean;
  filterType?: string;
  filterOperator?: string;
  settings?: {
    types: TxObjectTypeType[];
    onlyVisible: boolean;
  };
}

export interface GridFilter {
  column:
    | {
        field: string;
        type?: string;
        filterType?: string;
        headerText?: string;
        filterOptions?: any;
      }
    | undefined;
  operator: string;
  hideFilterType?: boolean;
  value: GridFilterValue;
}

export type FilterOptions = {
  [key: string]: string | boolean | number;
};

export type BaseGridFilterValue =
  | string
  | number
  | boolean
  | string[]
  | Date
  | number[]
  | Date[]
  | boolean[];

export type GridFilterValue = BaseGridFilterValue | TxObjectType[];

export enum GridFilterType {
  String = 'string',
  Number = 'number',
  Boolean = 'boolean',
  Date = 'date',
  ObjectType = 'objectType',
  ConceptDropDown = 'conceptDropDown',
  FilterSelectLarge = 'filter-select-large',
}
export enum GridFilterOperator {
  Equal = 'equal',
  NotEqual = 'notEqual',
  GreaterThan = 'greaterThan',
  GreaterThanOrEqual = 'greaterThanOrEqual',
  LessThan = 'lessThan',
  StartsWith = 'StartWith',
  EndsWith = 'endsWith',
  Contains = 'contains',
}
