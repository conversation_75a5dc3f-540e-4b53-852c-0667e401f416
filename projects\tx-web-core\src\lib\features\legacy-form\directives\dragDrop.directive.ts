import { Directive, EventEmitter, HostListener, Output } from '@angular/core';

@Directive({
  selector: '[LegacyTxDragDrop]',
})
export class LegacyTxDragDropDirective {
  constructor() {}
  @Output() fileDropped = new EventEmitter<any>();
  @Output() showIndicator = new EventEmitter<any>();
  @Output() hideIndicator = new EventEmitter<any>();

  containsFiles(evt: any) {
    if (evt.dataTransfer.types) {
      return evt.dataTransfer.types.some((type: string) => type === 'Files');
    }
  }

  // Dragover listener
  @HostListener('dragover', ['$event']) public onDragOver(evt: any) {
    evt.preventDefault();
    evt.stopPropagation();
    this.showIndicator.emit(evt);
  }

  // Dragleave listener
  @HostListener('dragleave', ['$event']) public onDragLeave(evt: any) {
    evt.preventDefault();
    evt.stopPropagation();
    this.hideIndicator.emit(evt);
  }

  // Drop listener
  @HostListener('drop', ['$event']) public ondrop(evt: any) {
    evt.preventDefault();
    evt.stopPropagation();
    this.hideIndicator.emit(evt);
    if (this.containsFiles(evt)) {
      this.fileDropped.emit(evt);
    }
  }
}
