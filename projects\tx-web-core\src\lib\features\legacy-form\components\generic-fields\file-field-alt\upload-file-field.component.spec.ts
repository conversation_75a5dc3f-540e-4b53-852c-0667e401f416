import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxUploadFileFieldComponent } from './upload-file-field.component';
import { TxFileService } from '../../../services/structure/services/file.service';
import { FilesMockService } from '../../../testing.mock';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import {
  LegacyTxAttributeFile,
  LegacyTxAttributeRight,
  LegacyTxFile,
} from '../../../services/structure/models/attribute';
import { TxAttributeField } from '../../../models/formConfiguration/businessClass/attribute-field';
import { Subscription } from 'rxjs';
import { LegacyTxDataBaseAction, LegacyTxDataType } from '../../../services/structure/models/data';

const mockAttribute = new LegacyTxAttributeFile({
  id: 1,
  idFileType: 0,
  name: 'otherTest',
  isList: true,
  dataType: LegacyTxDataType.ArchivedGraphic,
  idObjectType: 0,
  idAttributeParent: 0,
  tags: [],
  right: LegacyTxAttributeRight.None,
  order: 0,
  isInherited: false,
  idInheritedAttribute: 0,
  idLinkType: 0,
  option: {},
});

describe('TxFileFieldAltComponent', () => {
  let component: TxUploadFileFieldComponent;
  let fixture: ComponentFixture<TxUploadFileFieldComponent>;
  let fileService: TxFileService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxUploadFileFieldComponent],
      providers: [{ provide: TxFileService, useClass: FilesMockService }],
      imports: [FormsModule, MatFormFieldModule, FontAwesomeTestingModule, ReactiveFormsModule],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxUploadFileFieldComponent);
    fileService = TestBed.inject(TxFileService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('testing onFileSelected...', () => {
    let event1: any;
    let event2: any;
    let file1: any;
    let file2: any;

    beforeEach(() => {
      fixture = TestBed.createComponent(TxUploadFileFieldComponent);
      file1 = new File([], 'fileTest1');
      file2 = new File([], 'fileTest2');
      // file1.size = 500;
      // file2.size = 1024*1025;
      event1 = { dataTransfer: { files: [file1, file2] } };
      event2 = { target: { files: [file1, file2] } };
      component.control = new FormControl();
      component.field = new TxAttributeField();
      component.field.attribute = mockAttribute;
      component.multiple = true;
    });

    it('Should call isValidFile for each file in the event', () => {
      const isValidFileSpy = jest.spyOn(component, 'isValidFile');
      component.onFileSelected(event1);
      expect(isValidFileSpy).toHaveBeenNthCalledWith(1, file1);
      expect(isValidFileSpy).toHaveBeenNthCalledWith(2, file2);
    });

    it('should call addNewFile for aller valid files', () => {
      const addNewFileSpy = jest.spyOn(component, 'addNewFile');
      component.isValidFile = jest.fn().mockReturnValueOnce(false).mockReturnValueOnce(true);
      component.onFileSelected(event1);
      expect(addNewFileSpy).toHaveBeenCalledTimes(1);
      expect(addNewFileSpy).toHaveBeenCalledWith(file2);
    });

    it('should call upload file service function for each file to added', () => {
      component.addNewFile = jest
        .fn()
        .mockReturnValueOnce(new LegacyTxFile('fileTest1'))
        .mockReturnValueOnce(new LegacyTxFile('fileTest2'));
      const uploadSpy = jest.spyOn(fileService, 'upload');
      component.onFileSelected(event1);
      expect(uploadSpy).toHaveBeenNthCalledWith(1, file1, 1);
      expect(uploadSpy).toHaveBeenNthCalledWith(2, file2, 1);
    });

    //   it('should set upload subject to each corresponding TxFile', waitForAsync(() => {
    //     const txFile1 = new TxFile('fileTest1');
    //     const txFile2 = new TxFile('fileTest2');
    //     component.addNewFile = jest.fn().mockReturnValueOnce(txFile1).mockReturnValueOnce(txFile2);
    //     const subj1 = of({body: [{id: 5}]});
    //     const subj2 = of({body: [{id: 6}]});
    //     fileService.upload = jest.fn().mockResolvedValueOnce(subj1).mockReturnValueOnce(subj2);
    //     setTimeout( () => {
    //       expect(txFile1.idArchivedFile).toBe(5);
    //       expect(txFile2.idArchivedFile).toBe(6);
    //     }, 500);
    //   }));
  });

  describe('testing containsFiles', () => {
    let event;

    it('should set isDropzoneHovered to true if event contains file', () => {
      event = { dataTransfer: { types: ['Files', 'otherTypes'] } };
      component.containsFiles(event);
      expect(component.isDropzoneHovered).toBeTruthy();
    });

    it('should set isDropzoneHovered to false if event does not contains file', () => {
      event = { dataTransfer: { types: ['otherTypes'] } };
      component.isDropzoneHovered = true;
      component.containsFiles(event);
      expect(component.isDropzoneHovered).toBeFalsy();
    });
  });

  describe('testing isScrollable', () => {
    it('should return true if enough files in field', () => {
      component.nbChipsPerColumn = 4;
      component.getNbFiles = jest.fn().mockReturnValue(8);
      expect(component.isScrollable()).toBeTruthy();
    });

    it('should return false if not enough files in field', () => {
      component.nbChipsPerColumn = 4;
      component.getNbFiles = jest.fn().mockReturnValue(6);
      expect(component.isScrollable()).toBeFalsy();
    });
  });

  describe('testing getFileIndex', () => {
    let txFile1: LegacyTxFile;
    let txFile2: LegacyTxFile;
    let txFile3: LegacyTxFile;
    let txFile4: LegacyTxFile;
    beforeEach(() => {
      txFile1 = new LegacyTxFile('file1');
      txFile2 = new LegacyTxFile('file2');
      txFile3 = new LegacyTxFile('file3');
      txFile4 = new LegacyTxFile('file4');
      component.files = [txFile1, txFile2, txFile3, txFile4];
    });

    it('should return corresponding TxFile index given TxFile', () => {
      expect(component.getFileIndex(txFile2)).toBe(1);
    });

    it('should return corresponding TxFile index given File', () => {
      const file3 = new File([], 'file3');
      expect(component.getFileIndex(file3)).toBe(2);
    });

    it('should return negative value if file does not exist', () => {
      const txFile5 = new LegacyTxFile('file5');
      expect(component.getFileIndex(txFile5)).toBeLessThan(0);
    });
  });

  describe('testing getNbFiles', () => {
    it('should get the right number of files in the field', () => {
      const txFile1 = new LegacyTxFile('file1');
      const txFile2 = new LegacyTxFile('file2');
      const txFile3 = new LegacyTxFile('file3');
      const txFile4 = new LegacyTxFile('file4');
      component.filesShown = [txFile1, txFile2, txFile3, txFile4];
      expect(component.getNbFiles()).toBe(4);
    });
  });

  describe('testing fileExist...', () => {
    let txFile1: LegacyTxFile;
    let txFile2: LegacyTxFile;
    let txFile3: LegacyTxFile;
    let txFile4: LegacyTxFile;
    beforeEach(() => {
      txFile1 = new LegacyTxFile('file1');
      txFile2 = new LegacyTxFile('file2');
      txFile3 = new LegacyTxFile('file3');
      txFile4 = new LegacyTxFile('file4');
      component.files = [txFile1, txFile2, txFile3, txFile4];
    });

    it('should return true if TxFile is in field', () => {
      expect(component.fileExist(txFile2)).toBeTruthy();
    });

    it('should return true if corresponding File is in field', () => {
      const file3 = new File([], 'file3');
      expect(component.fileExist(file3)).toBeTruthy();
    });

    it('should return false if file not in field', () => {
      const txFile5 = new LegacyTxFile('file5');
      expect(component.fileExist(txFile5)).toBeFalsy();
    });
  });

  describe('testing isHeadColumnFile...', () => {
    let txFile1: LegacyTxFile;
    let txFile2: LegacyTxFile;
    let txFile3: LegacyTxFile;
    let txFile4: LegacyTxFile;
    beforeEach(() => {
      txFile1 = new LegacyTxFile('file1');
      txFile2 = new LegacyTxFile('file2');
      txFile3 = new LegacyTxFile('file3');
      txFile4 = new LegacyTxFile('file4');
      component.filesShown = [txFile1, txFile2, txFile3, txFile4];
      component.nbChipsPerColumn = 3;
    });

    it('should return false if file not at the top of a column', () => {
      expect(component.isHeadColumnFile(txFile2)).toBeFalsy();
    });

    it('should return true if file is at the top of a column', () => {
      expect(component.isHeadColumnFile(txFile3)).toBeTruthy();
    });
  });

  describe('testing addNewfile...', () => {
    let txFile1: LegacyTxFile;
    let txFile2: LegacyTxFile;
    let txFile3: LegacyTxFile;
    let txFile4: LegacyTxFile;

    beforeEach(() => {
      txFile1 = new LegacyTxFile('file1');
      txFile2 = new LegacyTxFile('file2');
      txFile3 = new LegacyTxFile('file3');
      txFile4 = new LegacyTxFile('file4');
      component.files = [txFile1, txFile2, txFile3, txFile4];
      component.filesShown = [txFile1, txFile3, txFile4];
      component.multiple = true;
      component.control = new FormControl();
      component.control.setValue(component.files);
    });

    it('should return null if file already exist in field', () => {
      const file3 = new File([], 'file3');
      expect(component.addNewFile(file3)).toBeFalsy();
    });

    it('should return null if not multiple and one file in field', () => {
      const newFile = new File([], 'file5');
      component.multiple = false;
      component.filesShown = [txFile1];
      expect(component.addNewFile(newFile)).toBeFalsy();
    });

    it('should create the corresponding txFile and add it at the beggining of files', () => {
      const newFile = new File([], 'file5');
      component.addNewFile(newFile);
      expect(component.files[0].name).toBe('file5');
    });

    it('should create the corresponding txFile and add it at the beggining of filesShown', () => {
      const newFile = new File([], 'file5');
      component.addNewFile(newFile);
      expect(component.filesShown[0].name).toBe('file5');
    });
  });

  describe('testing addFiles...', () => {
    let txFile1: LegacyTxFile;
    let txFile2: LegacyTxFile;
    let txFile3: LegacyTxFile;
    let txFile4: LegacyTxFile;

    beforeEach(() => {
      txFile1 = new LegacyTxFile('file1');
      txFile2 = new LegacyTxFile('file2');
      txFile3 = new LegacyTxFile('file3');
      txFile4 = new LegacyTxFile('file4');
      component.files = [txFile1, txFile2, txFile3, txFile4];
      component.filesShown = [txFile1, txFile3, txFile4];
      component.multiple = true;
      component.control = new FormControl();
      component.control.setValue(component.files);
    });

    it('should not add file if it already exists in field', () => {
      const newTxFile = new LegacyTxFile('file3');
      const newTxFile2 = new LegacyTxFile('file4');
      const oldFiles = component.files;
      component.addFiles([newTxFile, newTxFile2]);
      expect(component.files).toBe(oldFiles);
    });

    it('should not add files if not multiple and one file in field', () => {
      const newFile = new LegacyTxFile('file5');
      component.multiple = false;
      component.filesShown = [txFile1];
      const oldFiles = component.filesShown;
      component.addFiles([newFile]);
      expect(oldFiles).toBe(component.filesShown);
    });

    it('should add file in files with none dataBaseAction', () => {
      const newFile = new LegacyTxFile('file5');
      component.addFiles([newFile]);
      expect(component.files[component.files.length - 1].name).toBe('file5');
      expect(component.files[component.files.length - 1].action).toBe(LegacyTxDataBaseAction.None);
    });

    it('should add file in files shown', () => {
      const newFile = new LegacyTxFile('file5');
      component.addFiles([newFile]);
      expect(component.filesShown[component.filesShown.length - 1].name).toBe('file5');
    });

    it('should add file in form control', () => {
      const newFile = new LegacyTxFile('file5');
      component.addFiles([newFile]);
      expect(component.control.value[component.control.value.length - 1].name).toBe('file5');
    });
  });

  describe('testing removeFiles...', () => {
    let txFile1: LegacyTxFile;
    let txFile2: LegacyTxFile;
    let txFile3: LegacyTxFile;
    let txFile4: LegacyTxFile;

    beforeEach(() => {
      txFile1 = new LegacyTxFile('file1', 0, true, 20);
      txFile2 = new LegacyTxFile('file2', 0, true, 25);
      txFile3 = new LegacyTxFile('file3', 0, false, -5);
      txFile4 = new LegacyTxFile('file4', 0, true, -6);
      component.files = [txFile1, txFile2, txFile3, txFile4];
      component.filesShown = [txFile1, txFile3, txFile4];
      component.multiple = true;
      component.control = new FormControl();
      component.control.setValue(component.files);
    });

    it('should call resetUpload with the file to remove', () => {
      const resetUploadSpy = jest.spyOn(component, 'resetUpload');
      component.removeFile(txFile1);
      expect(resetUploadSpy).toHaveBeenCalledWith(txFile1);
    });

    it('should change de DataBase action of the file to delete if it exist in the db', () => {
      component.removeFile(txFile1);
      expect(component.files[0].action).toBe(LegacyTxDataBaseAction.Delete);
    });

    it('should should remove the file from files Shown', () => {
      component.removeFile(txFile1);
      component.removeFile(txFile4);
      expect(component.filesShown.includes(txFile1)).toBeFalsy();
      expect(component.filesShown.includes(txFile4)).toBeFalsy();
      expect(component.filesShown.includes(txFile3)).toBeTruthy();
    });

    it('should remove the file from files if not exist in DB', () => {
      component.removeFile(txFile4);
      component.removeFile(txFile1);
      expect(component.files.includes(txFile4)).toBeFalsy();
      expect(component.files.includes(txFile1)).toBeTruthy();
    });

    it('should call resetInput', () => {
      const resetInputSpy = jest.spyOn(component, 'resetInput');
      component.removeFile(txFile1);
      expect(resetInputSpy).toHaveBeenCalled();
    });

    it('should update the form control value', () => {
      component.removeFile(txFile4);
      expect(component.control.value).toEqual([txFile1, txFile2, txFile3]);
    });
  });

  describe('testing isValidFile...', () => {
    it('should return false if unvalid file size', () => {
      const file1 = new File([], 'file1');
      component.validFileSize = jest.fn().mockReturnValue(false);
      expect(component.isValidFile(file1)).toBeFalsy();
    });

    it('should return false if valid file', () => {
      const file1 = new File([], 'file1');
      component.validFileSize = jest.fn().mockReturnValue(true);
      expect(component.isValidFile(file1)).toBeTruthy();
    });
  });

  describe('testing removeFromUploadedFilesAndCache...', () => {
    beforeEach(() => {
      component.idUploadedFiles = [1, 5, 8, 15];
    });

    it('should remove id from idUploadedFiles', () => {
      component.removeFromUploadedFilesAndCache(8);
      expect(component.idUploadedFiles).toEqual([1, 5, 15]);
    });

    it('should call deleteCache from service with id', () => {
      const deleteCacheSpy = jest.spyOn(fileService, 'deleteCache');
      component.removeFromUploadedFilesAndCache(8);
      expect(deleteCacheSpy).toHaveBeenCalledWith([8]);
    });
  });
});
