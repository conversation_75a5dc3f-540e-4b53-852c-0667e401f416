import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { ActivatedRouteStub, ConfigServiceMock } from '../../ui/testing';
import { TxConfigService } from '../config';
import { AuthenticationService } from './authentication.service';

describe('Service: Authentification', () => {
  let service: AuthenticationService;
  let activatedRoute: ActivatedRouteStub;

  beforeEach(() => {
    activatedRoute = new ActivatedRouteStub();

    TestBed.configureTestingModule({
      imports: [RouterTestingModule.withRoutes([]), HttpClientTestingModule],
      providers: [
        { provide: TxConfigService, useClass: ConfigServiceMock },
        { provide: ActivatedRoute, useValue: activatedRoute },
      ],
    });
    service = TestBed.inject(AuthenticationService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Token is not null', () => {
    beforeAll(() => {
      const item = {
        value: 'testToken',
        expiry: new Date().getTime() + 7200000,
      };
      localStorage.setItem('access_token', JSON.stringify(item));
    });

    it('should update the token with new expiry', () => {
      Storage.prototype.setItem = jest.fn();
      service.updateSession(72000);
      expect(localStorage.setItem).toHaveBeenCalled();
    });

    it('should get the stored JSON token as a string', () => {
      expect(service.getAccessToken()).toEqual('testToken');
    });

    it('should get token activity', () => {
      expect(service.isTokenActive()).toBeTruthy();
    });

    it('should be logged in', () => {
      jest.spyOn(service, 'login').mockImplementation(jest.fn());
      expect(service.checkLoggedIn()).toBeTruthy();
    });
  });

  describe('Token is null', () => {
    beforeAll(() => {
      localStorage.clear();
    });

    it('should get null as there is no stored JSON token', () => {
      expect(service.getAccessToken()).toBeNull();
    });

    it('should not update the session if there is no stored JSON token', () => {
      expect(service.updateSession(72000)).toBeNull();
    });

    it('should not be active', () => {
      expect(service.isTokenActive()).toBeFalsy();
    });

    it('should not be logged in', () => {
      jest.spyOn(service, 'login').mockImplementation(jest.fn());
      expect(service.checkLoggedIn()).toBeFalsy();
    });
  });

  describe('LocalStorage methods calls', () => {
    beforeAll(() => {
      localStorage.clear();
    });

    it('should call "localStorage.setItem" when setting a session', () => {
      Storage.prototype.setItem = jest.fn();
      service.setSession('token');
      expect(localStorage.setItem).toHaveBeenCalled();
    });

    it('should call "localStorage.getItem" when updating a session', () => {
      Storage.prototype.getItem = jest.fn();
      service.updateSession(72000);
      expect(localStorage.getItem).toHaveBeenCalled();
    });

    it('should call "localStorage.removeItem" when clearing a session', () => {
      Storage.prototype.removeItem = jest.fn();
      service.clearSession();
      expect(localStorage.removeItem).toHaveBeenCalled();
    });

    it('should call "localStorage.getItem" when getting the access token', () => {
      Storage.prototype.getItem = jest.fn();
      service.getAccessToken();
      expect(localStorage.getItem).toHaveBeenCalled();
    });
  });

  describe('Login and logout', () => {
    it('should call "login" to check login', () => {
      const spyLogin = jest.spyOn(service, 'login').mockImplementation(jest.fn());
      service.checkLoggedIn();
      expect(spyLogin).toHaveBeenCalled();
    });

    it('should call "login" when logging out', () => {
      const spyLogin = jest.spyOn(service, 'login').mockImplementation(jest.fn());
      service.logout();
      expect(spyLogin).toHaveBeenCalled();
    });

    it('should call "clearSession" when logging out', () => {
      const spyLogin = jest.spyOn(service, 'login').mockImplementation(jest.fn());
      const spyClearSession = jest.spyOn(service, 'clearSession').mockImplementation(jest.fn());
      service.logout();
      expect(spyClearSession).toHaveBeenCalled();
      expect(spyLogin).toHaveBeenCalled();
    });
  });
});
