import { Subscription, filter } from 'rxjs';
import { TxObjectsTypeDropdownService } from './objects-type-dropdown.service';
import {
  TxObjectType,
  TxObjectTypeType,
} from '@bassetti-group/tx-web-core/src/lib/business-models';
import { TxConceptDropdownComponent } from '../concept-dropdown.component';
import { Component, Input, AfterViewInit, OnChanges, OnDestroy } from '@angular/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TxObjectsTypeService } from '@bassetti-group/tx-web-core/src/lib/data-access/structure';
import { TxDropdownTreeComponent } from '../../dropdown-tree/dropdown-tree.component';
import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TxObjectTypeIconService } from '@bassetti-group/tx-web-core/src/lib/data-access/session';

@Component({
  standalone: true,
  imports: [TxDropdownTreeComponent, CommonModule, ReactiveFormsModule],
  selector: 'tx-objects-type-dropdown',
  templateUrl: '../concept-dropdown.component.html',
  styleUrls: ['../concept-dropdown.component.scss'],
  providers: [TxObjectsTypeDropdownService],
})
export class TxObjectsTypeDropdownComponent
  extends TxConceptDropdownComponent
  implements AfterViewInit, OnChanges, OnDestroy
{
  @Input() onlyVisible = true;
  @Input() filtered = false;
  @Input() types: TxObjectTypeType[] = [TxObjectTypeType.Standard, TxObjectTypeType.User];
  @Input() objectTypes: TxObjectType[] = [];

  public initialized = false;
  subscription?: Subscription;
  keyValue = 'id';
  textValue = 'name';

  constructor(
    public objectTypeService: TxObjectsTypeService,
    public otdService: TxObjectsTypeDropdownService,
    objectTypeIcon: TxObjectTypeIconService
  ) {
    super(objectTypeIcon);
  }
  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  reloadOptions(objectTypes: TxObjectType[]) {
    this.objectTypes = objectTypes;
    super.reloadDataSource(this.objectTypes);
    this.updateLabel();
  }

  ngAfterViewInit(): void {
    if (this.filtered || this.objectTypes?.length > 0) {
      this.initDropDown();
    } else {
      this.subscription = this.objectTypeService
        .filter(this.types, this.onlyVisible)
        .pipe(filter((objectTypes) => objectTypes.length > 0))
        .subscribe((objectTypes: TxObjectType[]) => {
          this.objectTypes = objectTypes;
          this.initDropDown();
        });
    }
  }

  protected markAsInit() {
    this.isContentInit = this.initialized;
  }

  protected updateLabel() {
    if (this.dataSource.length === 0) {
      this.displayedLabel = _('txWebCore.components.otDropdown.noObjectTypeAvailable');
    } else {
      this.displayedLabel = this.label ?? '';
    }
  }

  private initDropDown() {
    if (this.initialized) {
      return;
    }
    this.reloadOptions(this.objectTypes);
    this.readyToLoad = true;
    this.initialized = true;
    this.markAsInit();
  }
}
