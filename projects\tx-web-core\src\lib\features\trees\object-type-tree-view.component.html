<div class="spacer margined-row"></div>
<ng-container *ngIf="currentTreeData?.length > 0; else noRecordToDisplay">
  <mat-tree [dataSource]="dataSource" [treeControl]="treeControl">
    <!-- This is the template for simple node (no children node). The checked statut is based only on the boolean attribute of the node -->
    <mat-tree-node
      class="tree-view-component-node-row"
      [ngClass]="{'tree-view-node-row-selected': checklistSelection.isSelected(node) && !hierarchicalSelection}"
      [disabled]="node | isNodeDisabled : checkListDisabled : treeDataOptions"
      *matTreeNodeDef="let node"
      matTreeNodeToggle
      matTreeNodePadding
      matTreeNodePaddingIndent="30px"
      (click)="onSingleSelection(node)"
      [ngClass]="{
        'selected-node':
          multipleSelection == false && showCheckBox && checklistSelection.isSelected(node),
        'single-selection-hover': multipleSelection == false
      }">
      <div class="spacer margined-row"></div>
      <!-- Because the other type of nodes have the toggle (chevron) icon, we need this to keep elements aligned -->
      <mat-checkbox
        *ngIf="multipleSelection && showCheckBox; else simpleNodeTemplate"
        [disabled]="node | isNodeDisabled : checkListDisabled : treeDataOptions"
        [checked]="checklistSelection.isSelected(node)"
        (change)="leafItemSelectionToggle(node)">
        <ng-container *ngTemplateOutlet="treeNodeTemplate; context: { node: node }"></ng-container>
      </mat-checkbox>
      <ng-template #simpleNodeTemplate>
          <ng-container *ngTemplateOutlet="treeNodeTemplate; context: { node: node }"></ng-container>
      </ng-template>
    </mat-tree-node>

    <!-- This is the template for parent node. The checked statut is based on the children checked statut and could be indeterminate -->
    <mat-tree-node
      class="tree-view-component-node-row"
      [ngClass]="{'tree-view-node-row-selected': checklistSelection.isSelected(node) && !hierarchicalSelection}"
      [disabled]="node | isNodeDisabled : checkListDisabled : treeDataOptions"
      *matTreeNodeDef="let node; when: hasChild"
      matTreeNodePadding
      matTreeNodePaddingIndent="30px"
      (click)="onSingleSelection(node)"
      [ngClass]="{
        'selected-node':
          multipleSelection == false && showCheckBox && checklistSelection.isSelected(node),
        'single-selection-hover': multipleSelection == false
      }">
      <fa-icon
        matTreeNodeToggle
        [icon]="['fas', treeControl.isExpanded(node) ? 'chevron-down' : 'chevron-right']"
        class="tree-chevron-icon margined-row"></fa-icon>
      <mat-checkbox
        *ngIf="multipleSelection && showCheckBox; else simpleExpandableNodeTemplate"
        [disabled]="node | isNodeDisabled : checkListDisabled : treeDataOptions"
        [checked]="
          hierarchicalSelection ? descendantsAllSelected(node) : checklistSelection.isSelected(node)
        "
        [indeterminate]="hierarchicalSelection ? descendantsPartiallySelected(node) : false"
        (change)="
          hierarchicalSelection ? itemSelectionToggle(node) : leafItemSelectionToggle(node)
        ">
        <ng-container *ngTemplateOutlet="treeNodeTemplate; context: { node: node }"></ng-container>
      </mat-checkbox>
      <ng-template #simpleExpandableNodeTemplate>
        <ng-container *ngTemplateOutlet="treeNodeTemplate; context: { node: node }"></ng-container>
      </ng-template>
    </mat-tree-node>
  </mat-tree>
</ng-container>

<ng-template #noRecordToDisplay>
  <div class="empty-tree-placeholder">
    <fa-icon [icon]="['fal', 'empty-set']"></fa-icon>
    {{ 'txWebCore.components.treeView.noRecordFound' | translate }}
  </div>
</ng-template>

<!-- This is the template for a node. It check if we have custom template for node in component input. If not, it use the default template -->
<ng-template #treeNodeTemplate let-node="node">
  <ng-container *ngIf="nodeTemplate; else defaultNode">
    <ng-container *ngTemplateOutlet="nodeTemplate; context: { node: node }"></ng-container>
  </ng-container>
  <ng-template #defaultNode>
    <div class="node-row simple-node-spacer">
      <img
        *ngIf="node.objectData?.iconPath"
        [alt]="'concept-icon-' + node.objectData?.id"
        [src]="node.objectData.iconPath" />
        <span (click)="onNodeClicked(node)">{{ node.name }}</span>
    </div>
    <fa-icon
      *ngIf="multipleSelection == false && showCheckBox && checklistSelection.isSelected(node)"
      [icon]="['fal', 'check']"
      transform="grow-2"
      class="icon-padding"></fa-icon>
  </ng-template>
</ng-template>
