import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ViewToFocus } from '../path/view-to-focus.model';
import { Params, RouterModule } from '@angular/router';
import { Target } from '../path/target';
import { PathService } from '../path/path.service';
import { ViewTooltip } from '../view-tooltip.model';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { CommonModule } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  standalone: true,
  selector: 'tx-view',
  imports: [CommonModule, MatTooltipModule, FontAwesomeModule, TranslateModule, RouterModule],
  templateUrl: './view.component.html',
  styleUrls: ['./view.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ViewComponent {
  _toFocus: ViewToFocus | undefined;
  tooltip = '';
  viewDisabled = true;
  path = '/';
  params: Params | undefined;
  _target: Target = '_self';
  constructor(private pathService: PathService) {}

  get toFocus() {
    return this._toFocus;
  }

  @Input() set toFocus(value: ViewToFocus | undefined) {
    if (value) {
      this.viewDisabled = !this.pathService.getPath(value.toFocus)?.canNavigate;
      this.path = this.pathService.getPath(value.toFocus)
        ? `/${this.pathService.getPath(value.toFocus)?.path}`
        : '';
      this.params = this.getParams(value);
      if (value.viewTooltip) {
        this.tooltip = this.defineTooltip(value.toFocus, value.viewTooltip);
      } else {
        this.tooltip = this.defineTooltip(value.toFocus);
      }
      this._toFocus = value;
    }
  }
  // eslint-disable-next-line @typescript-eslint/member-ordering
  get target() {
    return this._target;
  }

  @Input() set target(value: Target | undefined) {
    if (value) {
      this._target = value;
    }
  }
  private defineTooltip(
    toFocus: string,
    viewTooltip: ViewTooltip = {
      view: _('txWebCore.admins.columns.view'),
      noSufficientRight: _('txWebCore.tooltip.noSufficientRight'),
      notAccessible: _('txWebCore.tooltip.notAccessible'),
    }
  ): string {
    const path = this.pathService.getPath(toFocus);
    if (path) {
      if (path.canNavigate) {
        return viewTooltip.view;
      } else {
        return viewTooltip.noSufficientRight;
      }
    } else {
      return viewTooltip.notAccessible;
    }
  }

  private getParams(value: ViewToFocus): Params | undefined {
    const id = value.id;
    const pathParams = this.pathService.getPath(value.toFocus)?.params ?? {};
    return this.path === '' ? undefined : { ...pathParams, idConcept: id };
  }
}
