import { Args, type Meta, type StoryObj } from '@storybook/angular';
import { FormControl } from '@angular/forms';
import { TxChipsFieldComponent } from './chips-field.component';
import { TxChip } from './models';

const control = new FormControl<TxChip[]>([
  { type: 'standard', name: 'test', selectable: true, removable: true },
]);

const meta: Meta<TxChipsFieldComponent> = {
  component: TxChipsFieldComponent,
  title: 'Generic Fields/TxChipsFieldComponent',
  tags: ['autodocs'],
};
export default meta;

export const Primary: StoryObj = {
  render: (args: Partial<Args>) => ({
    props: { ...args, formControl: control },
    template: `
    <div [style.width.px]="350">
    <tx-chips-field
    [formControl]="formControl"
    [label]="label"
    [labelTooltip]="labelTooltip"
    [actionIcon]="actionIcon"
    [actionIconToolTip]="actionIconToolTip"
    [required]="required"
    [readMode]="readMode"
    [multiple]="multiple"
    [multipleSelection]="multipleSelection"
    [removable]="removable"
    [placeHolder]="placeholder"
    [hintLabel]="hintLabel"
    [editable]="editable"
    [maxChipDisplay]="maxChipDisplay"
    [maxChipsMessage]="maxChipsMessage"
    [chipsIcon]="chipsIcon"
    [displayExtenderChip]="displayExtenderChip"
    [hideTitleImageThumbnail]="hideTitleImageThumbnail"
    [pattern]="pattern"
    ></tx-chips-field>
    </div>
   `,
  }),
};

Primary.args = {
  label: 'label chips',
  labelTooltip: 'tooltip chips',
  actionIcon: 'home',
  actionIconToolTip: 'action tooltip',
  chipsIcon: 'pen',
  required: true,
  readMode: false,
  multiple: true,
  multipleSelection: false,
  removable: true,
  placeHolder: 'placeholder',
  hintLabel: 'hint chips',
  editable: true,
  maxChipDisplay: 5,
  maxChipsMessage: 'Too many chips',
  displayExtenderChip: true,
  hideTitleImageThumbnail: false,
  pattern: '',
};
