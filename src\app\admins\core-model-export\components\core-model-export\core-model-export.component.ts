import { Component } from '@angular/core';
import { CoreModelExportService } from '../../services/core-model-export.service';
import { EMPTY, Observable, map, of, switchMap } from 'rxjs';
import { RightPaneService } from 'src/app/core/right-pane/right-pane.service';
import { CoreModelExportHistory } from '../../models/core-model-export-history.model';
import { CoreModelExportFormComponent } from '../core-model-export-form/core-model-export-form.component';
import { NbErrors } from 'src/app/admins/core-model-common';
import { FlatCoreModelExportConcept } from '../../models/core-model-export-concept.model';
import { toFlatCoreModelExportConcepts } from '../../utils/core-model-export.utils';

@Component({
  selector: 'app-core-model-export',
  templateUrl: './core-model-export.component.html',
  styleUrls: ['./core-model-export.component.scss'],
})
export class CoreModelExportComponent {
  selectedIndex = 0;
  isLoading$: Observable<boolean>;
  conceptsNbErrors$: Observable<NbErrors>;
  flatConcepts$: Observable<readonly FlatCoreModelExportConcept[]>;
  history$: Observable<readonly CoreModelExportHistory[]>;
  canExport$: Observable<boolean>;
  showOnlyErrors$: Observable<boolean>;
  constructor(
    private readonly _exportService: CoreModelExportService,
    private readonly _rightPaneService: RightPaneService
  ) {
    this._exportService.loadConcepts();
    this.isLoading$ = this._exportService.isLoading$;
    this.flatConcepts$ = this._exportService.concepts$.pipe(
      map((concepts) => toFlatCoreModelExportConcepts(concepts))
    );
    this.history$ = this._exportService.history$.pipe(
      map((historyList) =>
        [...historyList].sort((curr, prev) => prev.date.valueOf() - curr.date.valueOf())
      )
    );
    this.conceptsNbErrors$ = this._exportService.conceptsNbErrors$;
    this.canExport$ = this._exportService.canExport$;
    this.showOnlyErrors$ = this._exportService.showOnlyErrors$;
  }

  exportCoreModel(): void {
    this._exportService
      .getLastCoreModelExported()
      .pipe(
        switchMap((historic) =>
          of(
            this._rightPaneService.showNewPane(CoreModelExportFormComponent, {
              data: historic,
            })
          )
        ),
        switchMap((rightPaneRef) => rightPaneRef.afterClosed),
        switchMap((rightPaneResult) => {
          if (rightPaneResult) {
            return this._exportService.exportCoreModel({
              ...rightPaneResult.data,
              description: rightPaneResult.data.explanation,
            });
          } else {
            return EMPTY;
          }
        })
      )
      .subscribe();
  }

  refreshCoreModelConcepts(): void {
    this._exportService.refreshConcepts();
  }

  filterOnConceptsInError(event: boolean) {
    if (event) {
      this._exportService.filterOnConceptsInErrors();
    } else {
      this._exportService.removeFilterOnConceptsInErrors();
    }
  }
}
