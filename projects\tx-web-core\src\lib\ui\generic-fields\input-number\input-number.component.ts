import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormControl,
  FormsModule,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validator,
  Validators,
} from '@angular/forms';
import 'zone.js';
import { Subject, startWith, takeUntil } from 'rxjs';
import { CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTooltipModule } from '@angular/material/tooltip';
import { checkBoundValue } from './validators';
import { TranslateModule } from '@ngx-translate/core';
import { concatenateErrors } from '../validators.utils';

@Component({
  selector: 'tx-input-number',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    MatInputModule,
    MatFormFieldModule,
    MatTooltipModule,
    TranslateModule,
  ],
  templateUrl: './input-number.component.html',
  styleUrls: ['./input-number.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: TxInputNumberComponent,
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: TxInputNumberComponent,
      multi: true,
    },
  ],
})
export class TxInputNumberComponent implements ControlValueAccessor, OnDestroy, Validator {
  @Input() hintLabel = '';
  @Input() label = '';
  @Input() labelTooltip: string | undefined;
  @Input() lowerBound: number | undefined;
  @Input() upperBound: number | undefined;
  @Input() lowerBoundIncluded: boolean = false;
  @Input() upperBoundIncluded: boolean = false;
  @Input() required: boolean = false;

  @Output() focusEvent = new EventEmitter();

  focused = false;
  disabled: boolean = false;
  control: FormControl<number | null> | undefined;
  private _destroying$ = new Subject<void>();
  onValidationChange: () => void = () => {};
  private _onTouched: () => void = () => {};
  constructor(private cd: ChangeDetectorRef) {}

  writeValue(value: number): void {
    if (this.control) {
      this.control.setValue(value);
    } else {
      this.control = new FormControl(value);
    }
  }

  registerOnChange(fn: any): void {
    this.control?.valueChanges
      .pipe(takeUntil(this._destroying$), startWith(this.control?.value))
      .subscribe(fn);
  }

  registerOnTouched(fn: () => void): void {
    this._onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    isDisabled ? this.control?.disable() : this.control?.enable();
    this.disabled = isDisabled;
  }

  onFocusIn(event: FocusEvent) {
    this.focused = true;
    this.focusEvent.emit(true);
  }

  onFocusOut(event: FocusEvent) {
    this.focused = false;
    this._onTouched();
    this.focusEvent.emit(false);
    this.onValidationChange();
  }

  onKeyUp($event: any): void {}

  validate(control: AbstractControl<any, any>): ValidationErrors | null {
    const requiredError = this.required ? Validators.required(control) : null;
    const checkBoundValueError = checkBoundValue(
      this.lowerBound,
      this.upperBound,
      this.lowerBoundIncluded,
      this.upperBoundIncluded
    )(control);
    const inputNumberError =
      !requiredError && !checkBoundValueError
        ? null
        : concatenateErrors(requiredError, checkBoundValueError);
    this.control?.setErrors(inputNumberError);
    this.cd.detectChanges();
    return inputNumberError;
  }

  registerOnValidatorChange?(fn: () => void): void {
    this.onValidationChange = fn;
  }

  ngOnDestroy(): void {
    this._destroying$.next();
  }
}
