import { BaseEventArgs } from './base-event-args.model';
import { TxContextMenuItem } from './tx-context-menu-item.model';

export interface TxContextMenuEventArgs extends BaseEventArgs {
  /**
   * The target element where the event occurred.
   */
  element: EventTarget;

  /**
   * The selected context menu item.
   */
  item: TxContextMenuItem;

  /**
   * The optional event object related to the selection.
   */
  event?: Event;
}
