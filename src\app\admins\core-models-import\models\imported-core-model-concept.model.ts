import { ModificationTypeEnum } from '../enums/modification.enum';
import { ConceptType, TxObjectType, TxConcept } from '@bassetti-group/tx-web-core';

export interface CoreModelImportConcept extends TxConcept {
  readonly type: ConceptType;
  readonly modificationType: ModificationTypeEnum;
  readonly translatedModificationType: string;
  readonly objectType?: CoreModelImportConcept | TxObjectType;
  readonly icon?: number;
}
export interface ImportedCoreModelConcept extends CoreModelImportConcept {
  readonly objectType?: TxObjectType;
}

export interface FlatImportedCoreModelConcept {
  [key: string]: string | number | undefined;
  type: string;
  id: number;
  name: string;
  objectType?: string;
  objectTypeIcon?: number;
  icon: number;
  tags: string;
  modificationType: ModificationTypeEnum;
  translatedModificationType: string;
}

export enum ImportedCoreModelConceptFieldEnum {
  Id = 'id',
  Name = 'name',
  Tags = 'tags',
  ObjectType = 'objectType',
  ModificationType = 'translatedModificationType',
  Type = 'type',
}
