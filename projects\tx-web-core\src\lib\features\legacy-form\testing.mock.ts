import { Observable, of, ReplaySubject } from 'rxjs';
('./models/audit');
import { TxAttributeField } from './models/formConfiguration/businessClass/attribute-field';
import { TxFormConfiguration } from './models/formConfiguration/businessClass/form-configuration';
import { TxGroupAttributeField } from './models/formConfiguration/businessClass/group-attribute-field';
import { TxTabAttributeField } from './models/formConfiguration/businessClass/tab-attribute-field';
import {
  LegacyTxAttribute,
  LegacyTxAttributeRight,
  LegacyTxFile,
} from './services/structure/models/attribute';
import { MatFormFieldControl } from '@angular/material/form-field';
import {
  AbstractControl,
  ControlValueAccessor,
  FormControlDirective,
  FormGroup,
  NgControl,
} from '@angular/forms';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { TxInputNumbersControlComponent } from './components/generic-fields/input-numbers-field/input-numbers-control/input-numbers-control.component';
import { TxEditionMode } from './models/formConfiguration/businessClass/form-enum';
import { TxBaseFieldComponent } from './components/generic-fields/base-field/base-field.component';
import { TxUploadFileFieldComponent } from './components/generic-fields/file-field-alt/upload-file-field.component';

/**
 * An ActivateRoute test double with a `queryParams` observable.
 * Use the `setParams()` method to add the next `queryParams` value.
 */
export class ActivatedRouteStub {
  // Use a ReplaySubject to share previous values with subscribers
  // and pump new values into the `queryParams` observable
  private subject = new ReplaySubject<any>();

  constructor(initialParams?: any) {
    this.setParams(initialParams || {});
  }

  /** The mock queryParams observable */
  readonly queryParams = this.subject.asObservable();

  /** Set the queryParams observables's next value */
  setParams(params?: any) {
    this.subject.next(params);
  }
}

const attributesMock = [
  new LegacyTxAttribute({
    name: 'testGroup',
    idAttributeParent: 2,
    isInherited: false,
    dataType: 13,
    idObjectType: 1,
    id: 1,
    tags: [],
    right: LegacyTxAttributeRight.None,
    order: 0,
    idInheritedAttribute: 0,
    idLinkType: 0,
    option: {},
  }),
  new LegacyTxAttribute({
    name: 'testTab',
    idAttributeParent: 0,
    isInherited: false,
    dataType: -2,
    idObjectType: 1,
    id: 2,
    tags: [],
    right: LegacyTxAttributeRight.None,
    order: 0,
    idInheritedAttribute: 0,
    idLinkType: 0,
    option: {},
  }),
  new LegacyTxAttribute({
    name: 'testAttr1',
    idAttributeParent: 2,
    isInherited: false,
    dataType: 121,
    idObjectType: 1,
    id: 329,
    tags: [],
    right: LegacyTxAttributeRight.None,
    order: 0,
    idInheritedAttribute: 0,
    idLinkType: 0,
    option: {},
  }),
  new LegacyTxAttribute({
    name: 'testattr2',
    idAttributeParent: 555,
    isInherited: false,
    dataType: 121,
    idObjectType: 1,
    id: 330,
    tags: [],
    right: LegacyTxAttributeRight.None,
    order: 0,
    idInheritedAttribute: 0,
    idLinkType: 0,
    option: {},
  }),
  new LegacyTxAttribute({
    name: 'testattr3',
    idAttributeParent: 1,
    isInherited: false,
    dataType: 121,
    idObjectType: 1,
    id: 89,
    tags: [],
    right: LegacyTxAttributeRight.None,
    order: 0,
    idInheritedAttribute: 0,
    idLinkType: 0,
    option: {},
  }),
];

export class AttributesMockService {
  listObjectTypeAttributes(idObjectType: number): Observable<LegacyTxAttribute[]> {
    return of(attributesMock);
  }

  getHint() {
    return null;
  }
}

export class UnitMockService {
  start() {}

  get(id: number) {
    return null;
  }
}

export class ObjectTypeMockService {
  start() {}

  getObjectType(param: any) {
    return null;
  }

  getObjectTypeAttributes(idObjectType: number): Observable<any[]> {
    return of([
      {
        name: 'testGroup',
        idAttributeParent: 2,
        isInherited: false,
        dataType: 13,
        idObjectType: 1,
        id: 1,
      },
      {
        name: 'testTab',
        idAttributeParent: 0,
        isInherited: false,
        dataType: -2,
        idObjectType: 1,
        id: 2,
      },
      {
        name: 'testAttr1',
        idAttributeParent: 2,
        isInherited: false,
        dataType: 121,
        idObjectType: 1,
        id: 329,
      },
      {
        name: 'testattr2',
        idAttributeParent: 555,
        isInherited: false,
        dataType: 121,
        idObjectType: 1,
        id: 330,
      },
      {
        name: 'testattr3',
        idAttributeParent: 1,
        isInherited: false,
        dataType: 121,
        idObjectType: 1,
        id: 89,
      },
    ]);
  }
}

export class FilesMockService {
  download(id: number) {
    return of();
  }

  upload(file: File, idAttibute: number) {
    return of();
  }

  deleteCache(idsFile: number[]) {
    return of();
  }
}

export class FormConfigFillerMock {
  public addTab(
    formConfig: TxFormConfiguration,
    tabAttr: LegacyTxAttribute,
    idObjectType: number,
    addChildrenAttributes: boolean,
    treatInheritedAttributes: boolean
  ) {
    const tabField = new TxTabAttributeField(TxEditionMode.write);
    tabField.attribute = attributesMock[1];
    const attr1Field = new TxAttributeField();
    attr1Field.attribute = attributesMock[2];
    const grpField = new TxGroupAttributeField(TxEditionMode.write);
    grpField.attribute = attributesMock[0];
    const attr2Field = new TxAttributeField();
    attr2Field.attribute = attributesMock[4];
    tabField.children.push(attr1Field);
    grpField.children.push(attr2Field);
    tabField.children.push(grpField);
    formConfig.tabs.push(tabField);
  }

  public setMandatories(formConfig: TxFormConfiguration, listMandatoriesAttrIds: number[]) {
    formConfig.tabs[0].children[0].properties.mandatory = true;
  }
}

class NativeElementMock {
  offsetWidth?: number;
}

class BtnElementMock {
  nativeElement?: NativeElementMock;
}

export class MatButtonToggleMock {
  _buttonElement?: BtnElementMock;
}

@Component({
  selector: 'app-user-form',
  template: ``,
  providers: [
    { provide: TxInputNumbersControlComponent, useClass: InputNumbersFieldMockComponent },
  ],
})
export class InputNumbersFieldMockComponent
  implements MatFormFieldControl<Number>, ControlValueAccessor
{
  @Input() form?: FormGroup;
  @Input() minFormControl?: AbstractControl;
  @Input() maxFormControl?: AbstractControl;
  @Input() meanFormControl?: AbstractControl;

  @Input() minPlaceHolder = 'min';
  @Input() maxPlaceHolder = 'max';
  @Input() meanPlaceHolder = 'mean';

  @Input() field?: TxAttributeField;

  @Input() minLength?: number;
  @Input() maxLength?: number;
  @Input() lowerBoundValue: any;
  @Input() upperBoundValue: any;
  @Input() attribute?: LegacyTxAttribute;

  @Input() withSecondInput = false;
  @Input() withThirdInput = false;

  @Output() errorEvent = new EventEmitter();
  @Output() focusEvent = new EventEmitter();
  @Output() unfocusEvent = new EventEmitter();
  writeValue(obj: any): void {
    throw new Error('Method not implemented.');
  }
  registerOnChange(fn: any): void {
    throw new Error('Method not implemented.');
  }
  registerOnTouched(fn: any): void {
    throw new Error('Method not implemented.');
  }
  setDisabledState?(isDisabled: boolean): void {
    throw new Error('Method not implemented.');
  }
  value: number = 0;
  stateChanges: Observable<void> = new Observable();
  id: string = '';
  placeholder: string = '';
  ngControl: NgControl = new FormControlDirective([], [], [], null);
  focused: boolean = false;
  empty: boolean = false;
  shouldLabelFloat: boolean = false;
  required: boolean = false;
  disabled: boolean = false;
  errorState: boolean = false;
  controlType?: string;
  autofilled?: boolean;
  userAriaDescribedBy?: string;
  setDescribedByIds(ids: string[]): void {
    throw new Error('Method not implemented.');
  }
  onContainerClick(event: MouseEvent): void {
    throw new Error('Method not implemented.');
  }

  changeLowerBoundValue(newValue: number) {}

  changeUpperBoundValue(newValue: number) {}
}

@Component({
  selector: 'app-user-form',
  template: ``,
  providers: [{ provide: TxUploadFileFieldComponent, useClass: TxUploadFileFieldMockComponent }],
})
export class TxUploadFileFieldMockComponent
  extends TxBaseFieldComponent
  implements OnInit, MatFormFieldControl<LegacyTxFile[]>, ControlValueAccessor, OnDestroy
{
  @Input() apiUrl?: string;
  @Input() requiredFileType?: string;
  @Input() multiple?: boolean;
  @Input() maxMoFileSize?: number;
  @Input() field?: TxAttributeField;
  @Input() hideVisualisationToggle?: any;

  stateChanges: Observable<void> = new Observable();
  placeholder: string = '';
  ngControl: NgControl = new FormControlDirective([], [], [], null);
  focused: boolean = false;
  empty: boolean = false;
  shouldLabelFloat: boolean = false;
  errorState: boolean = false;
  controlType?: string;
  autofilled?: boolean;
  userAriaDescribedBy?: string;
  setDescribedByIds(ids: string[]): void {
    throw new Error('Method not implemented.');
  }
  onContainerClick(event: MouseEvent): void {
    throw new Error('Method not implemented.');
  }
  writeValue(obj: any): void {
    throw new Error('Method not implemented.');
  }
  registerOnChange(fn: any): void {
    throw new Error('Method not implemented.');
  }
  registerOnTouched(fn: any): void {
    throw new Error('Method not implemented.');
  }
  setDisabledState?(isDisabled: boolean): void {
    throw new Error('Method not implemented.');
  }
  ngOnDestroy(): void {
    throw new Error('Method not implemented.');
  }
}
