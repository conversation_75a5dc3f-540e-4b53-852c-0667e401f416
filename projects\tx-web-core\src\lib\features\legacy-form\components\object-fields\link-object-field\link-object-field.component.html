<ng-container [ngSwitch]="displayMode">
  <ng-container
    *ngSwitchCase="
      [txLinkDisplayMode.ComboTree, txLinkDisplayMode.Combo] | txMultiSwitchCase : displayMode
    ">
    <tx-lof-combo
      #lofCombo
      [field]="comboField"
      [showCheckBox]="multiple"
      [mainIcon]="mainIcon"
      [label]="label"
      [required]="required"
      [disabled]="disabled"
      [control]="control"
      [allowFiltering]="true"
      [displayIconOption]="displayIconOption"
      [idObjectType]="idDestinationObjectType"
      [idFilteringObject]="idFilteringObject"
      [sortedBy]="sortedBy"
      [treeMode]="treeMode"
      [txCheckedObjects]="linkedObjects"
      [labelTooltip]="labelTooltip"
      (valueChange)="updateSelectedObjects($event)"
      (beforeOpen)="onBeforeOpen()"></tx-lof-combo>
  </ng-container>

  <ng-container *ngSwitchCase="txLinkDisplayMode.Chips">
    <tx-elements-selection-alt-field
      [field]="field"
      [form]="form"
      [elements]="txObjects"
      [selection]="multiple ? 'Multiple' : 'Single'"
      [selectedElements]="selectedIds"
      [mainIcon]="mainIcon"
      [label]="label"
      [textKey]="'name'"
      [valueKey]="'id'"
      [labelTooltip]="labelTooltip"
      (clickEvent)="onCheckObject($event)"></tx-elements-selection-alt-field>
  </ng-container>

  <ng-container *ngSwitchCase="txLinkDisplayMode.List">
    <tx-elements-selection-field
      [field]="field"
      [form]="form"
      [elements]="txObjects"
      [selection]="multiple ? 'Multiple' : 'Single'"
      [selectedElements]="selectedIds"
      [mainIcon]="mainIcon"
      [label]="label"
      [textKey]="'name'"
      [valueKey]="'id'"
      [labelTooltip]="labelTooltip"
      (clickEvent)="onCheckObject($event)"
      (select)="onSelect($event)"
      (change)="onChanges($event)"></tx-elements-selection-field>
  </ng-container>

  <ng-container *ngSwitchCase="txLinkDisplayMode.Matrix">
    <tx-lof-matrix
      #lofMatrix
      [idObject]="idObject"
      [attribute]="attribute"
      [linkedFields]="linkedFields"
      [transposed]="transposed"></tx-lof-matrix>
  </ng-container>

  <ng-container *ngSwitchCase="txLinkDisplayMode.OneFieldPerRaw">
    <div class="form-field read-field">Inherited attribute One field per Raw TO DEVELOP ...</div>
  </ng-container>

  <ng-container *ngSwitchDefault>
    <tx-chips-field
      #chipsField
      [label]="label"
      [labelTooltip]="labelTooltip"
      [control]="control"
      [disabled]="disabled"
      [required]="required"
      [visible]="true"
      [selectable]="false"
      [addOnBlur]="true"
      [information]="information"
      [value]="value"
      [data]="data"
      [readMode]="readMode"
      [fieldType]="field.attribute.dataType"
      [maxChipDisplay]="10"
      (displayPaneEvent)="displayRightPane($event)"></tx-chips-field>
  </ng-container>
</ng-container>
