import { FormControl } from '@angular/forms';

export interface TxConcept {
  id: number;
  name: string;
  tags: string[];
  description?: string | null;
  explanation?: string;
  options?: any;
}

export type TxConceptErroneous = TxConcept & { errors?: string[] };
export interface TagAndDescriptionsForm {
  tags?: FormControl<string | null>;
  description?: FormControl<string | null>;
  explanation?: FormControl<string | null>;
}
