.dialog-header {
  padding: 12px;
  display: inherit;
  font-size: 20px;

  fa-icon {
    color: white;
  }

  span {
    margin: auto;
    color: white;
    padding-left: 8px;
  }
}

.dialog-content-container {
  padding: 16px;

  .copy-message {
    padding-bottom: 8px;
  }
  .same-files-message {
    padding-bottom: 8px;
  }
  .files-actions {
    .row-action {
      padding: 16px 8px;

      span {
        padding-left: 16px;
      }
    }
  }
  .file-selection-header {
    padding-top: 16px;
    padding-bottom: 8px;
  }
  .file-selection-row {
    padding: 8px 0px 16px;

    .annotation {
      padding-bottom: 8px;
    }
  }
  .file-left-column {
    display: inline-block;
    width: 50%;
    overflow: hidden;
  }
  .file-right-column {
    display: inline-block;
    width: 50%;
    overflow: hidden;
  }
  .file-detailed {
    display: inline-block;
    padding-left: 16px;
  }
}
.container-detailed-choice {
  width: 700px;
}

.button-container {
  padding-bottom: 16px;
  padding-right: 16px;
  display: flex;
  justify-content: flex-end;

  button {
    margin-left: 8px;
  }
}
