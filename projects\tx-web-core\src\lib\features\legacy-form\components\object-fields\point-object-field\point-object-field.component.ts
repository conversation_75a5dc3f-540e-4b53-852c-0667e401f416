import { AbstractControl } from '@angular/forms';
import { AfterViewInit, Component, Input, ViewChild } from '@angular/core';
import { TxAttributeField } from '../../../models/formConfiguration/businessClass/attribute-field';
import { TxInputNumbersControlComponent } from '../../generic-fields/input-numbers-field/input-numbers-control/input-numbers-control.component';
import { TxUnitService } from '../../../services/structure/services/unit.service';
import { TxInputObjectFieldComponent } from '../_system/input-object-field/input-object-field.component';
import { LegacyTxDataNumeric, LegacyTxDataType } from '../../../services/structure/models/data';
import { TxAttributePoint } from '../../../services/structure/models/attribute';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { TxUnit } from '../../../services/structure/models/unit';

@Component({
  selector: 'tx-point-object-field',
  templateUrl: './point-object-field.component.html',
  styleUrls: ['./point-object-field.component.scss'],
})
export class TxPointObjectFieldComponent
  extends TxInputObjectFieldComponent
  implements AfterViewInit
{
  @Input() declare data: LegacyTxDataNumeric;
  @Input() defaultWidth = 90;
  @Input() declare attribute: TxAttributePoint;

  withSecondValue = false;
  withThirdValue = false;

  /** TODO: sert à quoi ?? */
  attributeType = LegacyTxDataType;

  maxField!: TxAttributeField;
  meanField!: TxAttributeField;

  maxFieldId!: string;
  meanFieldId!: string;

  minPlaceHolder = '';
  maxPlaceHolder = 'max';
  meanPlaceHolder = 'mean';

  maxFormControl!: AbstractControl;
  meanFormControl!: AbstractControl;
  unitFormControl!: AbstractControl;

  mainUnit!: TxUnit;
  secondUnits!: TxUnit[];
  unitIdSelected!: number;

  @ViewChild('inputsNumber')
  inputsNumberComponent!: TxInputNumbersControlComponent;

  public get isValid() {
    const minFieldValid = this.control.valid;
    const maxFieldValid = this.maxFormControl ? this.maxFormControl.valid : true;
    const meanFieldValid = this.meanFormControl ? this.meanFormControl.valid : true;
    return minFieldValid && maxFieldValid && meanFieldValid;
  }

  get notEmpty() {
    const minFieldHasValue =
      this.control && this.control.value !== null && this.control.value !== undefined;
    const maxFieldHasValue =
      this.maxFormControl &&
      this.maxFormControl.value !== null &&
      this.maxFormControl.value !== undefined;
    const meanFieldHasValue =
      this.meanFormControl &&
      this.meanFormControl.value !== null &&
      this.meanFormControl.value !== undefined;
    return minFieldHasValue || maxFieldHasValue || meanFieldHasValue;
  }

  constructor(
    public attributeService: LegacyTxAttributesService,
    public unitService: TxUnitService
  ) {
    super(attributeService);
    this.inputType = 'number';
  }

  ngAfterViewInit() {
    super.ngAfterViewInit();
    if (this.withBounds) {
      this.updateBoundValue();
    }
  }

  initUnitProperties(): void {
    if (this.attribute.idUnit) {
      // to change : get unit from back
      this.mainUnit = this.unitService.get(this.attribute.idUnit) as TxUnit;
      // get seconds unit from
      // this.secondUnits = [this.mainUnit, {id: 2, name: 'g'}, {id: 3, name: 'lbs'}, {id: 130, name: 'vehicule/an'}];
      this.unitIdSelected = this.mainUnit.id;
      // this.unitFormControl = this.createFormControl(this.id + 'unit', this.mainUnit.id, this.getValidators(), this.disabled);
      // if (this.readMode){
      // this.unitFormControl.valueChanges.pipe(
      //   startWith(this.unitFormControl.value),
      //   pairwise()
      // ).subscribe(
      //   ([oldUnit, newUnit]) => {
      //       this.convertControlsValue(oldUnit, newUnit);
      //   }
      // )
      // }
    }
  }

  initBoundProperties(): void {
    if (this.lowerBoundValue === undefined) {
      this.lowerBoundValue = this.attribute.lowerBound;
    }

    if (this.upperBoundValue === undefined) {
      this.upperBoundValue = this.attribute.upperBound;
    }

    this.withBounds = this.lowerBoundValue !== undefined || this.upperBoundValue !== undefined;
  }

  initControlsProperties(): void {
    this.label = this.attribute.name;
    if (this.attribute.dataType === LegacyTxDataType.DecRange) {
      this.addMaxFormControl();
    }

    if (this.attribute.dataType === LegacyTxDataType.DecRangeMean) {
      this.addMaxFormControl();
      this.addMeanFormControl();
    }
  }

  initPropertiesFromAttribute(): void {
    super.initPropertiesFromAttribute();
    this.initUnitProperties();
    this.initBoundProperties();
    this.initControlsProperties();
    this.withSecondValue =
      this.attribute.dataType === this.attributeType.DecRange ||
      this.attribute.dataType === this.attributeType.DecRangeMean;
    this.withThirdValue = this.attribute.dataType === this.attributeType.DecRangeMean;
  }

  updateBoundValue() {
    // update information message about bounds under the field
    if (!this.readMode) {
      this.hintBound = '';
      if (this.lowerBoundValue) {
        if (this.secondUnits) {
          const newLowerBoundValue = this.unitService.Convert(
            this.lowerBoundValue,
            this.mainUnit.id,
            this.unitIdSelected
          );
          this.hintBound += newLowerBoundValue.toString();
          this.secondUnits.forEach((unit) => {
            if (unit.id === this.unitIdSelected) {
              this.hintBound += unit.name;
            }
          });
          this.inputsNumberComponent.changeLowerBoundValue(newLowerBoundValue);
        } else {
          this.hintBound += this.lowerBoundValue.toString();
        }
        this.hintBound += ' < _';
      }
      if (this.upperBoundValue) {
        if (!this.lowerBoundValue) {
          this.hintBound += '_';
        }
        this.hintBound += ' < ';
        if (this.secondUnits) {
          const newUpperBoundValue = this.unitService.Convert(
            this.upperBoundValue,
            this.mainUnit.id,
            this.unitIdSelected
          );
          this.hintBound += newUpperBoundValue.toString();
          this.secondUnits.forEach((unit) => {
            if (unit.id === this.unitIdSelected) {
              this.hintBound += unit.name;
            }
          });
          this.inputsNumberComponent.changeUpperBoundValue(newUpperBoundValue);
        } else {
          this.hintBound += this.upperBoundValue.toString();
        }
      }
    }
  }

  addMaxFormControl() {
    this.minPlaceHolder = 'min';
    this.maxField = new TxAttributeField();
    this.maxField.attribute = new TxAttributePoint(this.attribute);
    this.maxField.attribute.name = '';
    this.maxFieldId = this.id + 'max';
    this.maxFormControl = this.createFormControl(
      this.maxFieldId,
      this.maxField.value,
      this.getValidators(),
      this.disabled
    );
  }

  addMeanFormControl() {
    this.meanField = new TxAttributeField();
    this.meanField.attribute = new TxAttributePoint(this.attribute);
    this.meanField.attribute.name = '';
    this.meanFieldId = this.id + 'mean';
    this.meanFormControl = this.createFormControl(
      this.meanFieldId,
      this.meanField.value,
      this.getValidators(),
      this.disabled
    );
  }

  initValue() {
    if (this.data && this.field) {
      this.field.value = this.data.min;

      if (this.maxField) {
        this.maxField.value = this.data.max;
      }

      if (this.meanField) {
        this.meanField.value = this.data.mean;
      }
    }
  }

  setData(data: LegacyTxDataNumeric) {
    this.control.setValue(data.min);
    if (
      this.attribute.dataType === LegacyTxDataType.DecRange ||
      this.attribute.dataType === LegacyTxDataType.DecRangeMean
    ) {
      this.maxFormControl.setValue(data.max);
    }
    if (this.attribute.dataType === LegacyTxDataType.DecRangeMean) {
      this.meanFormControl.setValue(data.mean);
    }
    // if (data.idUnit && this.secondUnits.find(unit => unit.id === data.idUnit)) {
    //    this.unitFormControl.setValue(data.idUnit);
    //  }
  }

  getData(): LegacyTxDataNumeric {
    let minValue = this.control ? parseFloat(this.control.value) : null;
    let maxValue = this.maxFormControl ? parseFloat(this.maxFormControl.value) : null;
    let meanValue = this.meanFormControl ? parseFloat(this.meanFormControl.value) : null;
    const unitValue = this.unitFormControl ? parseFloat(this.unitFormControl.value) : null;
    if (unitValue && unitValue !== this.mainUnit.id) {
      minValue = minValue ? this.unitService.Convert(minValue, unitValue, this.mainUnit.id) : null;
      maxValue = maxValue ? this.unitService.Convert(maxValue, unitValue, this.mainUnit.id) : null;
      meanValue = meanValue
        ? this.unitService.Convert(meanValue, unitValue, this.mainUnit.id)
        : null;
    }
    return new LegacyTxDataNumeric(
      this.idObject,
      this.idAttribute,
      minValue as number,
      maxValue as number,
      meanValue as number,
      unitValue as number
    );
  }

  checkBoundValue(FieldFormControl: AbstractControl) {
    const fieldValue = FieldFormControl ? parseFloat(FieldFormControl.value) : null;
    if (fieldValue) {
      if (this.lowerBoundValue && fieldValue < this.lowerBoundValue) {
        this.control.setErrors({ min: this.lowerBoundValue });
      } else if (this.upperBoundValue && fieldValue > this.upperBoundValue) {
        this.control.setErrors({ max: this.upperBoundValue });
      } else {
        this.control.updateValueAndValidity();
      }
    }
  }

  // onValuesChecked(event: any) {
  //   this.value2UpperThanValue1 = event.value2UpperThanValue1;
  //   this.betweenLowerAndUpper();
  //   if (this.required) {
  //     this.valueRequiredUnassigned = (event.upperValue === undefined || event.upperValue === '') && (event.lowerValue === undefined || event.lowerValue === '');
  //   }
  // }

  convertControlsValue(oldUnitId: number, newUnitid: number) {
    if (oldUnitId !== newUnitid) {
      this.control.setValue(this.unitService.Convert(this.control.value, oldUnitId, newUnitid));
      if (this.maxFormControl && this.maxFormControl.value) {
        this.maxFormControl.setValue(
          this.unitService.Convert(this.maxFormControl.value, oldUnitId, newUnitid)
        );
      }
      if (this.meanFormControl && this.meanFormControl.value) {
        this.meanFormControl.setValue(
          this.unitService.Convert(this.meanFormControl.value, oldUnitId, newUnitid)
        );
      }
    }
  }
}
