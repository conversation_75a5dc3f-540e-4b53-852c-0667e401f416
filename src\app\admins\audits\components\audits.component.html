<div
  class="admin-container"
  id="fileControl"
  (dragover)="containsFiles($event)"
  style="position: relative">
  <mat-progress-bar
    *ngIf="isLoadingBar"
    mode="indeterminate"
    color="accent"
    style="position: absolute; top: 0; left: 0"></mat-progress-bar>
  <app-breadcrumd></app-breadcrumd>
  <div class="admin-content">
    <div class="h1-title">{{ 'admins.wording.audit' | translate }}</div>
    <fa-icon
      [matTooltip]="'tooltip.showExplanation' | translate"
      [icon]="['fal', 'question-circle']"
      size="lg"
      class="icon-explanation"
      (click)="
        isExplanationDisplayed ? closeHelpbox() : getExplanation('audit', 'expAudit', false)
      "></fa-icon>
    <mat-divider class="divider-title"></mat-divider>
    <div class="audit-container">
      <div class="audit-container-left">
        <div style="width: 100%">
          <div class="h2-section-title audit-section-title">
            {{ 'admins.audit.settings' | translate }}
          </div>
        </div>
        <app-drop-file
          [loadedFilePath]="fileNameUpload"
          [extensions]="extensions"
          [isDropzoneHovered]="isDropzoneHovered"
          [noFileSelectedTooltip]="defaultFileTranslation"
          [nofileSelectedLabel]="defaultFileTranslation"
          [templateContent]="auditPoints"
          (afterFileUpload)="handleFileInput($event)"
          (afterFileRemoved)="deleteFile()"
          (hideDropZone)="hideDropZone()"></app-drop-file>
        <ng-template #auditPoints>
          <fa-icon
            [icon]="['fal', 'list-ol']"
            size="lg"
            class="audit-settings-icon color-grey60"></fa-icon>
          <div class="audit-settings-label">
            {{ 'admins.audit.nbAuditPoints' | translate : { number: numberAuditPoints } }}
          </div>
        </ng-template>
        <div class="h2-section-title audit-section-title" style="margin-top: 16px">
          {{ 'admins.audit.databaseSettings' | translate }}
        </div>
        <div class="audit-border background-grey5 border-grey">
          <div class="audit-settings-line">
            <fa-icon
              [icon]="['fal', 'database']"
              size="lg"
              class="audit-icon-line color-grey80"></fa-icon>
            <div class="audit-settings-label">
              <div class="audit-settings-version">
                {{ 'admins.audit.revision' | translate : { revision: revision } }}
              </div>
            </div>
          </div>
          <div class="audit-settings-line">
            <fa-icon
              [icon]="['fal', 'info-square']"
              size="lg"
              class="audit-icon-line color-grey80"></fa-icon>
            <div class="audit-settings-label">
              <div class="audit-settings-version">
                {{ 'admins.audit.version' | translate : { version: version } }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="audit-container-right">
        <div class="h2-section-title audit-section-title">
          {{ (!isExecuteMode ? 'admins.audit.points' : 'admins.audit.results') | translate }}
        </div>
        <div *ngIf="!isLangLoading" class="audit-treegrid">
          <tx-tree-grid
            #treegrid
            class="tree-grid"
            [data]="sections"
            [expandState]="txTreeExpandState.Expanded"
            childMapping="auditPoints"
            [columns]="txColumns"
            [isRowSelectable]="true"
            primaryKey="name">
            <tx-grid-column
              *ngFor="let column of ['name', 'id', 'explanation']"
              [fieldName]="column">
              <ng-template let-data>
                <span [ngStyle]="{ 'font-weight': data.auditPoints ? 'bold' : 'normal' }">{{
                  data[column]
                }}</span>
              </ng-template>
            </tx-grid-column>

            <tx-grid-column fieldName="status">
              <ng-template let-data>
                <div
                  *ngIf="data.status === undefined && data.id !== undefined"
                  class="audit-loading-ellipsis">
                  {{ 'admins.audit.pending' | translate }}
                </div>
                <div
                  [ngClass]="{
                    'background-success-pastel': data.status === 'ok',
                    'background-warning-pastel': data.status === 'warning',
                    'background-error-pastel': data.status === 'error'
                  }"
                  class="audit-cell-status">
                  {{ data.status | translate }}
                </div>
              </ng-template>
            </tx-grid-column>

            <tx-grid-column fieldName="result">
              <ng-template let-data>
                <div>
                  <div class="audit-result" [innerHTML]="data.result | escapeHtml : true"></div>
                  <div
                    [@transitShowHide]="data.isShowAllLine ? true : false"
                    class="audit-result"
                    [innerHTML]="data.allResult | escapeHtml : true"></div>
                  <span
                    *ngIf="data.isMultiLine && !data.isShowAllLine"
                    class="text-link audit-result-link"
                    (click)="showAllLine(data)"
                    >{{ 'admins.audit.showMore' | translate }}</span
                  >
                  <span
                    *ngIf="data.isMultiLine && data.isShowAllLine"
                    class="text-link audit-result-link"
                    (click)="hideLine(data)"
                    >{{ 'admins.audit.hide' | translate }}</span
                  >
                </div>
              </ng-template>
            </tx-grid-column>
          </tx-tree-grid>
        </div>
        <div class="audit-actions">
          <button
            *ngIf="isExecuteMode"
            mat-flat-button
            id="audit-export-button"
            color="accent"
            class="audit-button-right"
            [disabled]="cannotExport()"
            (click)="export()">
            <ng-container *ngIf="!isCurrentlyExporting; else spinner">{{
              'button.export' | translate
            }}</ng-container>
            <ng-template #spinner>
              <app-loader [isSmallLoader]="true"></app-loader>
            </ng-template>
          </button>
          <button
            *ngIf="!isExecuteMode"
            mat-flat-button
            id="audit-execute-button"
            color="accent"
            class="audit-button-right"
            (click)="executeAudit()">
            {{ 'button.execute' | translate }}
          </button>
          <button
            mat-stroked-button
            id="audit-reset-button"
            class="audit-button-right"
            [disabled]="cannotReset()"
            (click)="reset()">
            {{ 'button.reset' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
