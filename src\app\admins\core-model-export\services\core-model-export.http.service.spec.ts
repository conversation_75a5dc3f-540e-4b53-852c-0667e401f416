import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { CoreModelsExportHttpService } from './core-model-export.http.service';
import { <PERSON><PERSON><PERSON><PERSON>ider } from 'ng-mocks';
import { MOCK_TX_CONFIG_SERVICE, TxConfigService } from '@bassetti-group/tx-web-core';
import { CoreModelExportHistoryDTO } from '../models/core-model-export-history-object.dto';
import { CoreModelExportConceptDTO } from '../models/core-model-export-concept.dto';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

const storedHistory: CoreModelExportHistoryDTO[] = [
  {
    version: '0.0.1',
    name: 'People',
    explanation: '',
    comment: '',
    date: new Date().toString(),
    username: 'user',
  },
  {
    version: '0.0.2',
    name: 'Project',
    explanation: '',
    comment: '',
    date: new Date().toString(),
    username: 'user',
  },
];
const storedConcepts: CoreModelExportConceptDTO[] = [
  {
    type: 'ObjectType',
    name: 'People',
    tag: 'otPeople_CM89A15415',
    missingConfigs: [],
    explanation: '',
    id: 1,
  },
  {
    type: 'ObjectType',
    name: 'Project',
    tag: 'otProject_CM89A15416',
    missingConfigs: [],
    explanation: '',
    id: 14,
  },
];
describe('CoreModelsExportService', () => {
  let service: CoreModelsExportHttpService;
  let httpTestingController: HttpTestingController;
  beforeEach(() => {
    TestBed.configureTestingModule({
    imports: [],
    providers: [
        CoreModelsExportHttpService,
        MockProvider(TxConfigService, MOCK_TX_CONFIG_SERVICE, 'useValue'),
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
    ]
});
    service = TestBed.inject(CoreModelsExportHttpService);
    httpTestingController = TestBed.inject(HttpTestingController);
  });

  it('CoreModelsExportService', () => {
    expect(service).toBeTruthy();
  });
  it('Should load core models export history list', () => {
    service.loadCoreModelsExportHistoryList().subscribe((history) => {
      expect(history).toEqual(storedHistory);
    });
    const testReq = httpTestingController.expectOne(
      'https://localhost:44336/api/CoreModel/export/history'
    );
    expect(testReq.request.method).toBe('GET');
    testReq.flush(storedHistory);
  });
  it('Should load core models concept data', () => {
    service.loadCoreModelsConceptData().subscribe((concepts) => {
      expect(concepts).toEqual(storedConcepts);
    });
    const testReq = httpTestingController.expectOne('https://localhost:44336/api/CoreModel');
    expect(testReq.request.method).toBe('GET');
    testReq.flush(storedConcepts);
  });
  it('Should export core models', () => {
    const arrayBuffer = new ArrayBuffer(2);
    const blob = new Blob([arrayBuffer]);
    const storedFile = new File([blob], 'core-model-exported.zip', {
      type: 'application/zip',
    });
    service.exportCoreModels(storedHistory[0]).subscribe((file) => {
      expect(file).toEqual(storedFile);
    });
    const testReq = httpTestingController.expectOne('https://localhost:44336/api/CoreModel/export');
    expect(testReq.request.method).toBe('POST');
    testReq.flush(storedFile);
  });
});
