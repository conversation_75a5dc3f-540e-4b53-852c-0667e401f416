import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { concat, Observable, of } from 'rxjs';
import {
  TxObjectTypeType,
  TxObjectType,
  TxObjectTypeDetailed,
} from '@bassetti-group/tx-web-core/src/lib/business-models';
import { map, tap } from 'rxjs/operators';
import { TxConfigService } from '@bassetti-group/tx-web-core/src/lib/data-access/config';
import { TxAbstractConceptService } from './abstract-concept.service';
import { StringUtils } from '@bassetti-group/tx-web-core/src/lib/utilities';
import { TxObjectTypeIconService } from '@bassetti-group/tx-web-core/src/lib/data-access/session';

@Injectable({
  providedIn: 'root',
})
export class TxObjectsTypeService extends TxAbstractConceptService<TxObjectType> {
  protected urlListAllConcepts = 'api/Structure/objecttype';

  constructor(
    configService: TxConfigService,
    http: HttpClient,
    objectTypeIcon: TxObjectTypeIconService
  ) {
    super(configService, http, objectTypeIcon);
  }

  filter(types: TxObjectTypeType[], onlyVisible = true): Observable<TxObjectType[]> {
    return this.listAll().pipe(
      map((objectTypes: TxObjectType[]) => {
        objectTypes = objectTypes
          .filter((ot) => {
            if (types.length) {
              return types.includes(ot.type);
            }
            return true;
          })
          .filter((ot) => {
            if (onlyVisible) {
              return ot.isVisible;
            }
            return true;
          });

        return objectTypes;
      })
    ) as Observable<TxObjectType[]>;
  }

  addObjectType(objectType: TxObjectType): Observable<TxObjectType> {
    return this.http
      .post<TxObjectType>(this.apiUrl + this.urlListAllConcepts + '/', objectType)
      .pipe(
        tap<any>((newObjectTypeDetailed: TxObjectTypeDetailed) => {
          delete newObjectTypeDetailed.attributes;
          this.concepts.push(newObjectTypeDetailed);
          this.send();
        })
      );
  }

  convertAndEditOT(
    objectType: TxObjectType,
    initialType: TxObjectTypeType
  ): Observable<TxObjectType> {
    return concat(this.convertOT(objectType, initialType), this.editOT(objectType)).pipe(
      tap(() => {
        this.send();
      })
    );
  }

  convertOT(objectType: TxObjectType, initialType: TxObjectTypeType): Observable<TxObjectType> {
    if (
      (objectType.type !== initialType &&
        this.isListingOT(objectType) &&
        initialType === TxObjectTypeType.Standard) ||
      (this.isStandardOT(objectType) && initialType === TxObjectTypeType.Listing)
    ) {
      return this.http
        .put<TxObjectType>(this.apiUrl + this.urlListAllConcepts + '/convert/', objectType)
        .pipe(
          tap<any>((editedObjectType: TxObjectTypeDetailed) => {
            const existingOT = this.concepts.find(
              (u: TxObjectType) => u.id === editedObjectType.id
            );
            if (existingOT) {
              existingOT.type = editedObjectType.type;
              existingOT.order = editedObjectType.order;
              this.send();
            }
          })
        );
    } else {
      return of(objectType);
    }
  }

  editOT(objectType: TxObjectType): Observable<TxObjectType> {
    return this.http
      .put<TxObjectType>(this.apiUrl + this.urlListAllConcepts + '/id/' + objectType.id, objectType)
      .pipe(
        tap(() => {
          const existingOT = this.concepts.find((u: TxObjectType) => u.id === objectType.id);
          if (existingOT) {
            Object.assign(existingOT, objectType);
            this.send();
          }
        })
      );
  }

  isPortalOT(objectType: TxObjectType): boolean {
    return objectType !== undefined && objectType.type === TxObjectTypeType.Portal;
  }

  isPeopleOT(objectType: TxObjectType): boolean {
    return objectType !== undefined && objectType.type === TxObjectTypeType.User;
  }

  isStandardOT(objectType: TxObjectType): boolean {
    return objectType !== undefined && objectType.type === TxObjectTypeType.Standard;
  }

  isListingOT(objectType: TxObjectType): boolean {
    return objectType !== undefined && objectType.type === TxObjectTypeType.Listing;
  }

  isAssociativityOT(objectType: TxObjectType): boolean {
    return objectType !== undefined && objectType.type === TxObjectTypeType.Associativity;
  }

  isSourceOT(objectType: TxObjectType): boolean {
    return objectType !== undefined && objectType.type === TxObjectTypeType.Source;
  }

  isFolderOT(objectType: TxObjectType): boolean {
    return objectType.isFolder;
  }

  getLastFreePositionOfObjectType(idObjectType: number): number {
    const otsChildren = this.concepts.filter((ot) => ot.idObjectTypeParent === idObjectType);
    if (otsChildren.length < 1) {
      return 0;
    }
    return Math.max(...otsChildren.map((ot) => ot.order)) + 1;
  }

  getLastFreePositionOfTypeObjectType(objectTypeType: TxObjectTypeType): number {
    const otsType = this.concepts.filter(
      (ot) => ot.type === objectTypeType && !ot.idObjectTypeParent
    );
    if (otsType.length < 1) {
      return 0;
    }
    return Math.max(...otsType.map((ot) => ot.order)) + 1;
  }

  isObjectTypeConvertible(objectType: TxObjectType): boolean {
    if (
      (!this.isStandardOT(objectType) && !this.isListingOT(objectType)) ||
      this.isFolderOT(objectType)
    ) {
      return false;
    }
    return true;
  }

  getObjectTypeWithAttributes(idsOT: number[]): Observable<TxObjectTypeDetailed[]> {
    return this.http
      .post<TxObjectType[]>(`${this.apiUrl + this.urlListAllConcepts}/id/detailled`, idsOT)
      .pipe(
        tap((OTs) => {
          OTs.forEach((ot) => {
            const existingOT = this.concepts.find((u) => u.id === ot.id);
            if (existingOT) {
              Object.assign(existingOT, ot);
              this.send();
            }
          });
        })
      );
  }

  getObjectTypeByIds(idsOT: number[]): Observable<TxObjectType[]> {
    return this.http
      .post<TxObjectType[]>(`${this.apiUrl + this.urlListAllConcepts}/id/`, idsOT)
      .pipe(
        tap((OTs) => {
          OTs.forEach((ot) => {
            const existingOT = this.concepts.find((u) => u.id === ot.id);
            if (existingOT) {
              Object.assign(existingOT, ot);
              this.send();
            }
          });
        })
      );
  }

  override getIconPath(id: number) {
    const objectType = this.getByID(id);

    if (objectType) {
      return super.getIconPath(objectType.icon);
    } else {
      throw new Error(
        'Object Types not loaded, please execute "ObjectsTypeService.listAll" before.'
      );
    }
  }

  toPrefix(idOT: number): string {
    const PREFIX_SIZE = 4;
    if (!idOT) {
      return '';
    }

    const words = this.getName(idOT).split(' ');
    // Number of characters to find inside the words themselves.
    let charsToAdd = PREFIX_SIZE - words.length;

    let word: string;
    let j: number;
    let result = '';
    for (let i = 0; i < Math.min(PREFIX_SIZE, words.length); i++) {
      word = StringUtils.replaceSpecialCharacter(words[i]).replace(/[-_]/g, '').toLowerCase();

      j = 0;
      if (word.length > 0) {
        // set the first chars of the word
        result = this.setFirstWordChar(result, word, i, j);

        j++;
        // Add characters while there is again chars to add & contain in the word
        ({ result, charsToAdd } = this.addCharactersToPrefix(word, result, charsToAdd, j));

        // if the final string is equal to the prefix size, then return it
        if (result.length === PREFIX_SIZE) {
          return result;
        }
      } else {
        // increment the number of chars in case of a word is empty (contains only specials chars '-')
        charsToAdd++;
      }
    }

    return result;
  }

  autoTag(idsOT: number[]): Observable<TxObjectType[]> {
    return this.http
      .post<TxObjectType[]>(`${this.apiUrl}api/Structure/tag/autotag?concept=ObjectType`, idsOT)
      .pipe(
        tap((OTs) => {
          OTs.forEach((ot) => {
            const existingOT = this.concepts.find((u) => u.id === ot.id);
            if (existingOT) {
              Object.assign(existingOT, ot);
              this.send();
            }
          });
        })
      );
  }

  private setFirstWordChar(result: string, word: string, i: number, j: number): string {
    if (i === 0) {
      result = word[j].toUpperCase();
    } else {
      result += word[j];
    }
    return result;
  }

  private addCharactersToPrefix(word: string, result: string, charsToAdd: number, j: number): any {
    while (charsToAdd > 0 && j < word.length) {
      // add chars if it is not a vowel or the last char of the word
      if ((/[^aeiouy]/.test(word[j]) && /[a-z]/.test(word[j])) || j + 1 === word.length) {
        result += word[j];
        charsToAdd--;
      }
      j++;
    }
    return { result, charsToAdd };
  }
}
