import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { LegacyTxFileType } from '../models/file-type';
import { TxApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class TxFileTypeService {
  fileTypes: LegacyTxFileType[] = [];

  fileTypesLoaded: Subject<boolean> = new Subject<boolean>();

  initialized = false;

  private fill(fileType: any): LegacyTxFileType {
    const newFileType = new LegacyTxFileType(fileType);

    this.fileTypes.push(newFileType);

    return newFileType;
  }

  private add(fileTypes: any[]): LegacyTxFileType[] {
    return fileTypes.map((f) => this.fill(f));
  }

  constructor(public apiService: TxApiService) {
    this.fileTypesLoaded.subscribe((value) => {
      this.initialized = true;
    });
  }

  start() {
    this.apiService.listFileTypes().subscribe((fileTypes: LegacyTxFileType[]) => {
      this.add(fileTypes);
      this.fileTypesLoaded.next(true);
      this.fileTypesLoaded.complete();
    });
  }

  get(idFileType: number): LegacyTxFileType {
    return this.fileTypes.find((f) => f.id === idFileType) as LegacyTxFileType;
  }
}
