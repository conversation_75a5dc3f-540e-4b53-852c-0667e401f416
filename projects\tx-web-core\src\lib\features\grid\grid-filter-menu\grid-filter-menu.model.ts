export enum TxGridFilterType {
  String = 'string',
  Number = 'number',
  Boolean = 'boolean',
  Date = 'date',
  ObjectType = 'objectType',
  ConceptDropDown = 'conceptDropDown',
  FilterSelectLarge = 'filter-select-large',
}

export enum TxGridFilterOperator {
  Equal = 'equal',
  NotEqual = 'notEqual',
  GreaterThan = 'greaterThan',
  GreaterThanOrEqual = 'greaterThanOrEqual',
  LessThan = 'lessThan',
  StartsWith = 'StartWith',
  EndsWith = 'endsWith',
  Contains = 'contains',
}
