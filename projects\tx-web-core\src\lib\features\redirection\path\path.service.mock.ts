import { PathDetailed } from './path-detailed.model';

export class PathServiceMock {
  getPath(key: string): PathDetailed | undefined {
    if (key === 'Attribute') {
      return {
        path: 'user',
        params: undefined,
        canNavigate: true,
      };
    } else if (key === 'Concept') {
      return {
        path: 'user',
        params: undefined,
        canNavigate: false,
      };
    }

    return undefined;
  }
}
