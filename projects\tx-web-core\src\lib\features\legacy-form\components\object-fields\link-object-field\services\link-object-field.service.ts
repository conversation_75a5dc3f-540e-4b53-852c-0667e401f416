import { Injectable } from '@angular/core';
import { LegacyTxObject } from '../../../../services/structure/models/object';

@Injectable({
  providedIn: 'root',
})
export class TxLinkObjectFieldService {
  counter = 1;

  iconPath = '/assets/tx-web-core/imgs/objectTypesIcons/';

  constructor() {}

  start(iconPath: string) {
    this.iconPath = iconPath;
  }

  createOption(
    object: LegacyTxObject,
    linear = false,
    displayIcon = true,
    iconPath = '/assets/tx-web-core/imgs/objectTypesIcons/',
    hidden = false
  ): any {
    const option: { [key: string]: any } = {
      id: '' + object.id,
      idParent: object.idObjectParent,
      idObjectParent: object.idObjectParent,
      name: object.name,
      // name: _StringUtils.replaceToXml(object.name),
      isParent: linear ? false : object.isParent,
    };

    iconPath = iconPath || this.iconPath;

    if (hidden) {
      option.hidden = true;
    }

    if (option.isParent && !linear) {
      option.child = [];
    }

    if (displayIcon) {
      option.image = iconPath + (object.isFolder ? 'folderClosed.gif' : object.image);
    }

    this.counter++;

    return option;
  }

  createOptions(
    objects: LegacyTxObject[],
    linear = false,
    displayIcon = true,
    iconPath = '/assets/tx-web-core/imgs/objectTypesIcons/',
    hidden = false
  ): any[] {
    return objects.map((o) => this.createOption(o, linear, displayIcon, iconPath, hidden));
  }
}
