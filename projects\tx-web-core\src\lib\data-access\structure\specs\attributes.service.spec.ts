import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { TxAttributesService } from '../attributes.service';
import { TxFileTypesService } from '../file-types.service';
import { MOCK_FILE_TYPES_SERVICE } from './mock-file-types.service';
import { MOCK_OBJECTS_TYPE_SERVICE } from './mock-objects-type.service';
import { TxLinkTypesService } from '../link-types.service';
import { MOCK_LINK_TYPE_SERVICE } from './mock-link-types.service';
import { TxTableTypesService } from '../table-types.service';
import { MOCK_TABLE_TYPE_SERVICE } from './mock-table-types.service';
import { TxObjectsTypeService } from '../objects-type.service';
import { TxConfigService, MOCK_TX_CONFIG_SERVICE } from '../../config';
import { TxAttribute, TxDataType, TxLinkType } from '../../../business-models';
import { FormControl } from '@angular/forms';
import { TxDialogService, MOCK_DIALOG_SERVICE } from '../../../utilities';
import { Subscriber, of } from 'rxjs';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { TRANSLATIONS_MOCK } from '../../../utilities/tests';
import { MockProvider } from 'ng-mocks';
import { MOCK_OBJECT_TYPE_ICON_SERVICE, TxObjectTypeIconService } from '../../session';

describe('Service: Attributes', () => {
  let apiUrl: string | undefined;
  let service: TxAttributesService;
  let configService: TxConfigService;
  let dialogConfirmService: TxDialogService;
  let http: HttpTestingController;
  let formControl: FormControl<string>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        TranslateTestingModule.withTranslations(TRANSLATIONS_MOCK),
      ],
      providers: [
        MockProvider(TxConfigService, MOCK_TX_CONFIG_SERVICE, 'useValue'),
        MockProvider(TxFileTypesService, MOCK_FILE_TYPES_SERVICE, 'useValue'),
        MockProvider(TxLinkTypesService, MOCK_LINK_TYPE_SERVICE, 'useValue'),
        MockProvider(TxTableTypesService, MOCK_TABLE_TYPE_SERVICE, 'useValue'),
        MockProvider(TxObjectsTypeService, MOCK_OBJECTS_TYPE_SERVICE, 'useValue'),
        MockProvider(TxDialogService, MOCK_DIALOG_SERVICE, 'useValue'),
        MockProvider(TxObjectTypeIconService, MOCK_OBJECT_TYPE_ICON_SERVICE, 'useValue'),
      ],
    });
    service = TestBed.inject(TxAttributesService);
    configService = TestBed.inject(TxConfigService);
    http = TestBed.inject(HttpTestingController);
    dialogConfirmService = TestBed.inject(TxDialogService);
    formControl = new FormControl<string>('') as FormControl<string>;
  });

  beforeEach(() => {
    apiUrl = configService.getApiUrl();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Get Attributes from Object Type', () => {
    let attributeTests: TxAttribute[];
    let idObjectType: number;
    beforeEach(() => {
      attributeTests = [
        {
          id: 1,
          name: 'Last Name',
          order: 1,
          color: '',
          precision: 0,
          linkDisplayMode: 0,
          isUnderlined: false,
          isUBInclusive: false,
          isTransposed: false,
          isTrackable: true,
          isTableDisplayed: false,
          isSeriesNameDisplayed: false,
          isList: false,
          isLBInclusive: false,
          isInherited: false,
          isIndexesDisplayed: false,
          isDisplayedInMainUnit: true,
          idObjectType: 1,
          digits: 0,
          dataType: 0 as TxDataType,
          tags: [],
        },
        {
          id: 2,
          name: 'First name',
          order: 2,
          color: '',
          precision: 0,
          linkDisplayMode: 0,
          isUnderlined: false,
          isUBInclusive: false,
          isTransposed: false,
          isTrackable: true,
          isTableDisplayed: false,
          isSeriesNameDisplayed: false,
          isList: false,
          isLBInclusive: false,
          isInherited: false,
          isIndexesDisplayed: false,
          isDisplayedInMainUnit: true,
          idObjectType: 1,
          digits: 0,
          dataType: 0 as TxDataType,
          tags: [],
        },
        {
          id: 3,
          name: 'email',
          order: 3,
          color: '',
          precision: 0,
          linkDisplayMode: 0,
          isUnderlined: false,
          isUBInclusive: false,
          isTransposed: false,
          isTrackable: true,
          isTableDisplayed: false,
          isSeriesNameDisplayed: false,
          isList: false,
          isLBInclusive: false,
          isInherited: false,
          isIndexesDisplayed: false,
          isDisplayedInMainUnit: true,
          idObjectType: 1,
          digits: 0,
          dataType: 0 as TxDataType,
          tags: [],
        },
      ];
      idObjectType = 1;
    });

    it('should get attributes from Object Type (ID=1)', () => {
      let response: TxAttribute[];
      service.listAttributesFromObjectType(idObjectType).subscribe((res: TxAttribute[]) => {
        expect(res).toStrictEqual(attributeTests);
      });
      http
        .expectOne(`${apiUrl}api/Structure/objectType/id/${idObjectType}/attributes`)
        .flush(attributeTests);
    });
  });

  describe('isLinkAttribute', () => {
    it('should return true for direct link attribute', () => {
      const validAttribute = { dataType: TxDataType.LinkDirect } as TxAttribute;
      expect(service.isLinkAttribute(validAttribute)).toBe(true);
    });

    it('should return true for inverse link attribute', () => {
      const validAttribute = { dataType: TxDataType.LinkInv } as TxAttribute;
      expect(service.isLinkAttribute(validAttribute)).toBe(true);
    });

    it('should return true for listing attribute', () => {
      const validAttribute = { dataType: TxDataType.Listing } as TxAttribute;
      expect(service.isLinkAttribute(validAttribute)).toBe(true);
    });

    it('should return false for invalid link attribute', () => {
      const invalidAttribute = {
        dataType: TxDataType.SingleValue,
      } as TxAttribute;
      expect(service.isLinkAttribute(invalidAttribute)).toBe(false);
    });
  });

  describe('isMultipleLinkAttribute', () => {
    it('should return true for direct link with multiplicity', () => {
      const attribute = {
        linkType: { multiplicity: true, multiplicityInv: false } as TxLinkType,
        dataType: TxDataType.LinkDirect,
      } as TxAttribute;
      expect(service.isMultipleLinkAttribute(attribute)).toBe(true);
    });

    it('should return true for bidirectional link with multiplicity', () => {
      const attribute = {
        linkType: { multiplicity: true, multiplicityInv: true } as TxLinkType,
        dataType: TxDataType.LinkBi,
      } as TxAttribute;
      expect(service.isMultipleLinkAttribute(attribute)).toBe(true);
    });

    it('should return true for listing with multiplicity', () => {
      const attribute = {
        linkType: { multiplicity: true, multiplicityInv: false } as TxLinkType,
        dataType: TxDataType.Listing,
      } as TxAttribute;
      expect(service.isMultipleLinkAttribute(attribute)).toBe(true);
    });

    it('should return true for inverse link with multiplicity', () => {
      const attribute = {
        linkType: { multiplicity: false, multiplicityInv: true } as TxLinkType,
        dataType: TxDataType.LinkInv,
      } as TxAttribute;
      expect(service.isMultipleLinkAttribute(attribute)).toBe(true);
    });
  });

  describe('System autotag', () => {
    it('should not call dialog confirmation if there is no tag missing', () => {
      formControl.setValue('ValidTag1;ValidTag2;ValidTag3 | validTag1;ValidTag4');
      const spyOpenDialog = jest.spyOn(dialogConfirmService, 'open');

      service.checkAutoTag(new Subscriber<boolean>(), formControl);
      expect(spyOpenDialog).not.toBeCalled();
    });

    describe('If there is tag missing', () => {
      let observer: Subscriber<boolean>;
      beforeEach(() => {
        observer = new Subscriber<boolean>();
        formControl.setValue('#attTag4321#;ValidTag2;ValidTag3 | #attTag4321#;#attTag4323#');
      });

      it('should call dialog confirmation', () => {
        const spyOpenDialog = jest.spyOn(dialogConfirmService, 'open');
        service.checkAutoTag(observer, formControl);
        expect(spyOpenDialog).toHaveBeenCalled();
      });

      describe('If the user confirmed', () => {
        it('should call autotag', () => {
          const spyAutotag = jest.spyOn(service, 'autoTag');
          service.checkAutoTag(observer, formControl);
          expect(spyAutotag).toBeCalledWith([4321, 4323]); //argument is the id of attributes with missing tags
        });

        it('should update value of the tag control', () => {
          service.autoTag = jest.fn().mockReturnValue(
            of([
              { id: 4321, tags: ['newTag1'] },
              { id: 4323, tags: ['newTag2'] },
            ])
          );
          service.checkAutoTag(observer, formControl);
          expect(formControl.value).toBe('newTag1;ValidTag2;ValidTag3 | newTag1;newTag2');
        });

        it('should return true', () => {
          const observerSpy = jest.spyOn(Subscriber.prototype, 'next');
          service.checkAutoTag(observer, formControl);
          expect(observerSpy).toBeCalledWith(true);
          observerSpy.mockRestore();
        });
      });

      describe('If the user canceled', () => {
        beforeEach(() => {
          dialogConfirmService.open = jest.fn().mockReturnValue(of(false));
        });

        it('should not call autotag', () => {
          const spyAutotag = jest.spyOn(service, 'autoTag');
          service.checkAutoTag(observer, formControl);
          expect(spyAutotag).not.toBeCalled();
        });

        it('should not update value of the tag', () => {
          service.checkAutoTag(observer, formControl);
          expect(formControl.value).toBe(
            '#attTag4321#;ValidTag2;ValidTag3 | #attTag4321#;#attTag4323#'
          );
        });

        it('should return false', () => {
          const observerSpy = jest.spyOn(Subscriber.prototype, 'next');
          service.checkAutoTag(observer, formControl);
          expect(observerSpy).toBeCalledWith(false); //argument is the id of attributes with missing tags
          observerSpy.mockRestore();
        });
      });
    });
  });
});
