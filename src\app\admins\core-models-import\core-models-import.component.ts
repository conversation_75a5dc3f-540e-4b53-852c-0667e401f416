import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { CheckConfig } from 'src/app/shared/models/check-config.model';
import { CoreModelsImportService } from './services/core-models-import.service';
import { Observable, Subject, combineLatest, map, takeUntil, tap } from 'rxjs';
import { CheckStatus } from 'src/app/shared/enums/check-status.enum';
import { ArchiveInfo, ArchiveStatus } from './models/archive-info.model';
import { FileAction } from 'src/app/shared/enums/file-action.enum';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { isNil } from 'ramda';
import { NbErrors } from '../core-model-common';
import { ToastService } from '@bassetti-group/tx-web-core';
import { TranslateService } from '@ngx-translate/core';
import { CoreModelImportHistory } from './models/import-history.model';
import { FlatTestedCoreModelConcept } from './models/tested-core-model-concept.model';
import { FlatImportedCoreModelConcept } from './models/imported-core-model-concept.model';
import { toFlatImportCoreModelConcepts } from './utils/core-models-import.utils';
import { ImportCoreModelConcepts } from './models/import-core-model-concept.model';
import { FileConfig } from 'src/app/shared/models/files/file-config.model';
import { FileHistory } from 'src/app/shared/models/files/file-history.model';
import { GroupService } from '@syncfusion/ej2-angular-grids';

@Component({
  selector: 'app-core-models-import',
  templateUrl: './core-models-import.component.html',
  styleUrls: ['./core-models-import.component.scss'],
  providers: [GroupService],
})
export class CoreModelsImportComponent implements OnInit, OnDestroy {
  importContentsConfig: CheckConfig = {
    title: _('admins.coreModelsImport.importTitle'),
    flatBtnName: _('button.import'),
    strokedBtnName: _('button.test'),
    mainActionFailureMessage: _('admins.coreModelsImport.importFailed'),
    mainActionSuccessMessage: _('admins.coreModelsImport.importSuccess'),
    secondActionSuccessMessage: _('admins.coreModelsImport.testSuccess'),
  };
  fileConfig: FileConfig = {
    title: _('admins.coreModelsImport.archiveInfo'),
    fileVersionsTitle: _('admins.coreModelsImport.archiveVersions'),
  };
  testIsDisabled = true;
  importIsDisabled = true;
  onlyErrorsBtnIsShow = false;
  readonly restartImportBtnLabel = _('button.restartImport');
  readonly importBtnLabel = _('button.import');
  readonly errorDisplayDuration = 4000;
  readonly errorIsPersistent = false;
  readonly fileValidationErrorDescription = this._translate.instant(
    _('admins.coreModelsImport.validationFailed')
  );
  readonly validTestImportDescription = this._translate.instant(
    _('admins.coreModelsImport.validTestImportDescription')
  );
  readonly importSuccess = this._translate.instant(_('admins.coreModelsImport.importSuccess'));
  readonly invalidTestImportDescription = this._translate.instant(
    _('admins.coreModelsImport.invalidTestImportDescription')
  );
  coreModelImportVO$:
    | Observable<{
        flatConcepts: FlatTestedCoreModelConcept[] | FlatImportedCoreModelConcept[];
        status: CheckStatus;
        archiveInfo: ArchiveInfo | undefined;
        file: File | undefined;
        coreModelConceptsNbErrors: NbErrors;
        fileIsLoading: boolean;
        checkCmpIsLoading: boolean;
        archiveHistory: readonly FileHistory[];
        importHistory: readonly CoreModelImportHistory[];
        showOnlyErrors: boolean;
        showOnlyLatestImports: boolean;
        showOnlyNotImportedVersions: boolean;
      }>
    | undefined;

  fileAction: FileAction | null = null;
  private readonly _destroying = new Subject<void>();
  constructor(
    private readonly _coreModelsImport: CoreModelsImportService,
    private readonly _toastService: ToastService,
    private readonly _translate: TranslateService
  ) {}
  ngOnInit(): void {
    this._coreModelsImport.loadHistory();
    this.coreModelImportVO$ = combineLatest({
      flatConcepts: this._coreModelsImport.concepts$.pipe(
        map((concepts) => toFlatImportCoreModelConcepts(concepts as ImportCoreModelConcepts))
      ),
      status: this._coreModelsImport.archiveData$.pipe(
        tap((archive) => {
          this.handleImportActionsBtn(archive?.status);
        }),
        map((archiveData) => this.toImportStatus(archiveData?.status))
      ),
      archiveInfo: this._coreModelsImport.archiveData$.pipe(map((archive) => archive?.archiveInfo)),
      file: this._coreModelsImport.archiveData$.pipe(map((archive) => archive?.file)),
      coreModelConceptsNbErrors: this._coreModelsImport.conceptsNbErrors$,
      fileIsLoading: combineLatest([
        this._coreModelsImport.isLoading$,
        this._coreModelsImport.archiveData$,
      ]).pipe(map(([isLoading, archiveData]) => isLoading && isNil(archiveData))),
      checkCmpIsLoading: combineLatest([
        this._coreModelsImport.isLoading$,
        this._coreModelsImport.archiveData$,
      ]).pipe(map(([isLoading, archiveData]) => isLoading && !isNil(archiveData))),
      archiveHistory: this._coreModelsImport.archiveData$.pipe(
        map((archiveData) => archiveData?.history ?? [])
      ),
      importHistory: this._coreModelsImport.historyList$,
      showOnlyErrors: this._coreModelsImport.showOnlyErrors$,
      showOnlyLatestImports: this._coreModelsImport.showOnlyLatestImports$,
      showOnlyNotImportedVersions: this._coreModelsImport.showOnlyNotImportedVersions$,
    });
    this._coreModelsImport.archiveValidationError$
      .pipe(takeUntil(this._destroying))
      .subscribe(() => {
        this.fileAction = FileAction.DeleteFile;
      });
    this._coreModelsImport.statusNotification$
      .pipe(takeUntil(this._destroying))
      .subscribe((status) => {
        this.statusNotification(status);
      });
  }

  validateArchive(file: File) {
    this.fileAction = null;
    this._coreModelsImport.validateArchive(file);
  }
  testImport(): void {
    this._coreModelsImport.testImport().subscribe();
  }

  import(): void {
    this._coreModelsImport.import();
  }
  cancelArchive(): void {
    if (this.fileAction === FileAction.DeleteFile) {
      this.fileAction = null;
    }
    this._coreModelsImport.cancelArchive();
  }
  ngOnDestroy(): void {
    this._destroying.next();
  }

  filterOnConceptsInConflicts(doFilter: boolean): void {
    if (doFilter) {
      this._coreModelsImport.filterOnConceptsInConflicts();
    } else {
      this._coreModelsImport.removeFilterOnConceptsInConflicts();
    }
  }

  filterOnLatestImports(doFilter: boolean): void {
    if (doFilter) {
      this._coreModelsImport.filterOnLatestImportVersions();
    } else {
      this._coreModelsImport.removeFilterOnLatestImportVersions();
    }
  }

  filterOnNotImportedVersions(doFilter: boolean): void {
    if (doFilter) {
      this._coreModelsImport.filterOnNotImportedVersions();
    } else {
      this._coreModelsImport.removeFilterOnNotImportedVersion();
    }
  }
  private handleImportActionsBtn(status: ArchiveStatus | undefined) {
    switch (status) {
      case ArchiveStatus.Valid:
      case ArchiveStatus.TestImportInvalid:
        this.testIsDisabled = false;
        this.importIsDisabled = true;
        break;
      case ArchiveStatus.TestImportValid:
      case ArchiveStatus.ImportFailed:
        this.testIsDisabled = true;
        this.importIsDisabled = false;
        break;
      case ArchiveStatus.Imported:
      case ArchiveStatus.Invalid:
      default:
        this.testIsDisabled = true;
        this.importIsDisabled = true;
        break;
    }
  }

  private toImportStatus(status: ArchiveStatus | undefined): CheckStatus {
    switch (status) {
      case ArchiveStatus.ImportFailed:
        return CheckStatus.MainActionFailure;
      case ArchiveStatus.Imported:
        return CheckStatus.MainActionSuccess;
      case ArchiveStatus.TestImportInvalid:
        return CheckStatus.SecondActionFailure;
      case ArchiveStatus.TestImportValid:
        return CheckStatus.SecondActionSuccess;
      case undefined:
      case ArchiveStatus.Valid:
      case ArchiveStatus.Invalid:
      default:
        return CheckStatus.Undefined;
    }
  }

  private statusNotification(status: ArchiveStatus | undefined): void {
    if (status === ArchiveStatus.TestImportInvalid) {
      this._toastService.show({
        type: 'error',
        description: this.invalidTestImportDescription,
        isPersistent: this.errorIsPersistent,
        interval: this.errorDisplayDuration,
      });
    } else if (status === ArchiveStatus.TestImportValid) {
      this._toastService.show({
        type: 'success',
        description: this.validTestImportDescription,
        isPersistent: this.errorIsPersistent,
        interval: this.errorDisplayDuration,
      });
    } else if (status === ArchiveStatus.Imported) {
      this._toastService.show({
        type: 'success',
        description: this.importSuccess,
        isPersistent: this.errorIsPersistent,
        interval: this.errorDisplayDuration,
      });
    }
  }
}
