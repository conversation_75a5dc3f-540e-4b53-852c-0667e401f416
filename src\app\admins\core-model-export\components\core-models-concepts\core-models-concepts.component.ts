import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateService } from '@ngx-translate/core';
import {
  FilterOptionsList,
  GridFilterType,
  InputSearchEventInfo,
  TxGridColumn,
  TxGridComponent,
  TxGridDataType,
  TxGridService,
  TxGroupByValue,
  TxObjectTypeType,
  ViewTooltip,
} from '@bassetti-group/tx-web-core';
import { BehaviorSubject, Subject, distinctUntilChanged } from 'rxjs';
import {
  CoreModelExportConceptFieldEnum,
  FlatCoreModelExportConcept,
} from '../../models/core-model-export-concept.model';
import { NbErrors, SUPPORTED_CONCEPTS } from 'src/app/admins/core-model-common';
import { TextUtils } from 'src/app/core/utils/text.utils';
import { FormControl } from '@angular/forms';
interface CoreModelsConceptsGridColumn {
  id: number;
  name: string;
  type: string;
  tags: string;
  explanation: string;
  metaDataList: string;
  objectType: string;
  errors: string;
  view: string;
}
@Component({
  selector: 'app-core-models-concepts',
  templateUrl: './core-models-concepts.component.html',
  styleUrls: ['./core-models-concepts.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [TxGridService],
})
export class CoreModelsConceptsComponent implements OnInit, OnDestroy {
  /**
   * Concepts fields type converted in only primary type string or number
   */
  @Input() flatConcepts: FlatCoreModelExportConcept[] | null = null;
  @Input() isLoading: boolean | null = null;
  /**
   * Allow to show the button "Display Only Errors" on the table bar
   */
  @Input() onlyErrorsBtnIsShow = true;
  @Output() filterOnConceptsInError = new EventEmitter<boolean>();
  viewTooltip: ViewTooltip = {
    view: _('admins.columns.view'),
    noSufficientRight: _('search.noSufficientRight'),
    notAccessible: _('search.conceptNotAccessible'),
  };
  slideToggleControl = new FormControl<boolean>(false, {
    nonNullable: true,
  });
  enableInfiniteScrolling = true;
  allowFiltering = true;
  allowResizing = true;
  allowSorting = true;
  allowGrouping = true;
  pathIcon: IconName = 'arrow-up';
  filterOptionsList: FilterOptionsList[] = [
    {
      column: CoreModelExportConceptFieldEnum.Type,
      options: Object.values(SUPPORTED_CONCEPTS).map((type) => ({
        value: type,
        text: this.translate.instant(_(`concepts.${TextUtils.firstLetterLowercase(type)}`)),
      })),
      hideFilterType: true,
      filterType: GridFilterType.FilterSelectLarge,
    },
    {
      column: CoreModelExportConceptFieldEnum.ObjectType,
      hideFilterType: true,
      filterType: GridFilterType.ObjectType,
      settings: {
        types: [TxObjectTypeType.Standard, TxObjectTypeType.User, TxObjectTypeType.Portal],
        onlyVisible: false,
      },
      options: [],
    },
  ];
  CoreModelsConceptFieldEnum = CoreModelExportConceptFieldEnum;

  errorsColumn: TxGridColumn<CoreModelsConceptsGridColumn> = {
    field: CoreModelExportConceptFieldEnum.Errors,
    headerText: _('generic.errors'),
    sorting: true,
    type: TxGridDataType.TEXT,
  };

  conceptTypeColumn: TxGridColumn<CoreModelsConceptsGridColumn> = {
    field: CoreModelExportConceptFieldEnum.Type,
    headerText: _('admins.columns.type'),
    visible: false,
    sorting: true,
    type: TxGridDataType.TEXT,
  };

  txGridColumns: TxGridColumn<CoreModelsConceptsGridColumn>[] = [
    {
      field: CoreModelExportConceptFieldEnum.Name,
      headerText: _('admins.columns.name'),
      sorting: true,
      type: TxGridDataType.TEXT,
    },
    {
      field: CoreModelExportConceptFieldEnum.ObjectType,
      headerText: _('admins.columns.objectType'),
      sorting: true,
      type: TxGridDataType.TEXT,
    },
    {
      field: CoreModelExportConceptFieldEnum.Tags,
      headerText: _('admins.coreModels.tag'),
      sorting: true,
      type: TxGridDataType.TEXT,
    },
    {
      field: CoreModelExportConceptFieldEnum.Explanation,
      headerText: _('admins.coreModels.explanation'),
      sorting: true,
      type: TxGridDataType.TEXT,
    },
    {
      field: CoreModelExportConceptFieldEnum.MetaDataList,
      headerText: _('generic.metaData'),
      width: '300px',
      sorting: true,
      type: TxGridDataType.TEXT,
    },
    {
      field: CoreModelExportConceptFieldEnum.Errors,
      headerText: _('generic.errors'),
      sorting: true,
      type: TxGridDataType.TEXT,
    },
    {
      field: CoreModelExportConceptFieldEnum.View,
      headerText: _('admins.columns.view'),
      width: '80px',
      sorting: true,
      type: TxGridDataType.TEXT,
    },
    {
      field: CoreModelExportConceptFieldEnum.Type,
      headerText: _('admins.columns.type'),
      visible: false,
      sorting: true,
      type: TxGridDataType.TEXT,
    },
  ];
  filterColumns = this.txGridColumns.filter((column) => {
    return (
      column.field !== CoreModelExportConceptFieldEnum.View &&
      column.field !== CoreModelExportConceptFieldEnum.Id
    );
  });
  searchById: number | undefined = -1;
  inputSearchValue = '';
  sliderControl = false;
  private _coreModelsGrid?: TxGridComponent<FlatCoreModelExportConcept>;
  private _nbErrors: NbErrors | null | undefined;
  private readonly _omitColumnsSub = new BehaviorSubject<Set<CoreModelExportConceptFieldEnum>>(
    new Set()
  );
  private readonly _destroying = new Subject<void>();

  constructor(
    private readonly translate: TranslateService,
    private readonly gridService: TxGridService,
    private readonly elementRef: ElementRef<Element>
  ) {}
  get dataSourceLength() {
    return this.flatConcepts?.length ?? 0;
  }
  get coreModelsGrid() {
    return this._coreModelsGrid;
  }
  @ViewChild('coreModelGrid') set coreModelsGrid(
    value: TxGridComponent<FlatCoreModelExportConcept> | undefined
  ) {
    if (value !== undefined && this._coreModelsGrid !== value) {
      this._coreModelsGrid = value;
    }
  }
  // eslint-disable-next-line @typescript-eslint/member-ordering
  get nbErrors() {
    return this._nbErrors;
  }
  @Input() set nbErrors(value: NbErrors | null | undefined) {
    this._nbErrors = value;
  }

  @Input() set showOnlyErrors(value: boolean | undefined) {
    if (value !== undefined) {
      this.slideToggleControl.setValue(value);
    }
  }
  @Input() set omitColumns(value: CoreModelExportConceptFieldEnum[]) {
    if (value !== undefined) {
      this._omitColumnsSub.next(new Set(value));
      this.txGridColumns = this.txGridColumns.flatMap((column) => {
        if (this._omitColumnsSub.value.has(column.field as CoreModelExportConceptFieldEnum)) {
          return [];
        }
        return [column];
      });
    }
  }
  ngOnInit(): void {
    this.slideToggleControl.valueChanges.pipe(distinctUntilChanged()).subscribe((show) => {
      if (
        this.coreModelsGrid &&
        this.coreModelsGrid.dataSource.dataList.length > 0 &&
        this.coreModelsGrid.dataSource.groupBy
      ) {
        this.coreModelsGrid.dataSource.removeFilteredColsByField(TxGroupByValue, true);
      }
      this.filterOnConceptsInError.emit(show);
    });
  }

  searchItem(inputSearchEventInfo: InputSearchEventInfo): void {
    if (this.coreModelsGrid) {
      this.inputSearchValue = inputSearchEventInfo.inputSearch.nativeElement.value;
      this.searchById = this.gridService.search(
        inputSearchEventInfo.inputSearch,
        this.coreModelsGrid,
        this.elementRef,
        inputSearchEventInfo.event,
        false,
        true
      );
    }
  }

  resetSearch(): void {
    this.inputSearchValue = '';
    this.searchById = this.gridService.resetSearch();
  }

  ngOnDestroy(): void {
    this._destroying.next();
  }
}
