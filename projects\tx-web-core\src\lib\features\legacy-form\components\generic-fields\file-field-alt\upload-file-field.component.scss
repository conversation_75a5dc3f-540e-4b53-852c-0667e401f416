.file-input {
  display: none;
}

.uploader-dragzone {
  opacity: 0;
  border-radius: 10px;
  position: absolute;
  z-index: -1;
  top: -1px;
  bottom: -1px;
  left: -1px;
  right: -1px;
  transition: border 0.3s;
  transition: opacity 0.3s;
  .uploader-drag {
    vertical-align: middle;
    text-align: center;
    top: calc(50% - 32px);
    left: calc(50% - 52px);
    position: absolute;

    .uploader-file {
      font-size: 28px;
    }

    .uploader-file-message {
      font-size: 16px;
      line-height: 18px;
      margin-top: 8px;
      font-weight: 600;
    }
    .uploader-file-extension {
      font-size: 12px;
    }
  }
}

.dropzone-hovered {
  z-index: 2;
  opacity: 0.97 !important;
}

.file-uploader {
  display: flex;
  padding-top: 2px;
  padding-bottom: 1px;
  margin-left: 2px;
  margin-right: 16px !important;
  width: 280px;
  fa-icon {
    font-size: 28px;
  }
}

.file-uploader-text {
  margin-top: 4px;
  margin-left: 8px;
  font-weight: 500;
}

.file-uploader-size-text {
  font-size: 10px !important;
  margin-left: 8px;
}

.clickable-url:hover {
  text-decoration: underline;
  cursor: pointer;
}

.files-container {
  padding-top: 8px;
  padding-bottom: 4px;
}

.head-colum-file-field-box {
  margin-top: 3px;
}

::ng-deep .multiple-file-chip-list .mat-chip-list-wrapper {
  max-height: 155px;
  flex-direction: column;
  overflow-x: auto;
  align-content: flex-start;
  padding-left: 1px;
  padding-bottom: 4px;
}

::ng-deep .scrollable-multiple-file-chip-list .mat-chip-list-wrapper {
  padding-bottom: 0px !important;
}
