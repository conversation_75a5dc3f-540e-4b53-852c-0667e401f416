import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  CoreModelImportHistory,
  CoreModelsImportHistoryFieldEnum,
  SuccessImportHistory,
} from '../../models/import-history.model';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { FormControl } from '@angular/forms';
import {
  FlatTestedCoreModelConcept,
  TestedCoreModelConcept,
  TestedCoreModelConceptFieldEnum,
} from '../../models/tested-core-model-concept.model';
import {
  FlatImportedCoreModelConcept,
  ImportedCoreModelConcept,
} from '../../models/imported-core-model-concept.model';
import { isFailedConcepts, statusIsFailed } from '../../utils/core-models-import.utils';

import { TableUtils } from 'src/app/core/utils/table.utils';
import { ImportCoreModelConcepts } from '../../models/import-core-model-concept.model';
import { FlatImportCoreModelConcepts } from '../../models/flat-import-core-model-concepts.model';
import { QueryCellInfoEventArgs } from '@syncfusion/ej2-angular-grids';
import { ErrorUtils } from 'src/app/core/utils/error.utils';
import { TxDialogService, TxGridColumn, TxGridDataType } from '@bassetti-group/tx-web-core';

const TABLE_HEADERS = {
  date: _('generic.date'),
  user: _('generic.importBy'),
  version: _('generic.version'),
  explanation: _('generic.explanation'),
  comment: _('generic.comment'),
  success: _('generic.Success'),
  failed: _('generic.Failed'),
  status: _('generic.status'),
  view: _('admins.coreModelsImport.view'),
};
@Component({
  selector: 'app-core-models-import-history',
  templateUrl: './core-models-import-history.component.html',
  styleUrls: ['./core-models-import-history.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CoreModelsImportHistoryComponent implements OnInit {
  private readonly defaultActiveFilter = true;
  @Input() history: CoreModelImportHistory[] = [];
  @Output() filterOnLatestImports = new EventEmitter<boolean>();
  @ViewChild('detailTemplate') detailTemplate: TemplateRef<unknown> | undefined;
  columns: TxGridColumn<CoreModelImportHistory>[] = Object.values(
    CoreModelsImportHistoryFieldEnum
  ).map((field) => ({
    field: field as keyof CoreModelImportHistory,
    isSearchable: true,
    headerText: _(
      `${field === CoreModelsImportHistoryFieldEnum.View ? 'admins.coreModelsImport' : 'generic'}.${
        field === CoreModelsImportHistoryFieldEnum.Username ? 'importBy' : field
      }`
    ),
    type: TxGridDataType.TEXT,
    activeFilter: this.activeFilter(field),
    width: this.getColumnWidth(field),
    sorting: field !== CoreModelsImportHistoryFieldEnum.View,
  }));
  CoreModelsImportHistoryFieldEnum = CoreModelsImportHistoryFieldEnum;
  filterLatestControl = new FormControl<boolean>(false, {
    nonNullable: true,
  });
  selectedConcepts: ImportCoreModelConcepts | undefined;
  flatConcepts: FlatImportCoreModelConcepts = [];

  coreModelConceptsNbErrors: number | undefined;
  showOnlyErrors = false;
  searchPlaceholder = _('generic.search');
  private _flatConcepts: FlatTestedCoreModelConcept[] = [];
  constructor(private readonly _dialog: TxDialogService, private readonly cd: ChangeDetectorRef) {}
  @Input() set showOnlyLatestImports(value: boolean | undefined) {
    if (value !== undefined && this.filterLatestControl.value !== value) {
      this.filterLatestControl.setValue(value);
    }
  }

  ngOnInit(): void {
    this.filterLatestControl.valueChanges.pipe().subscribe((show) => {
      this.filterOnLatestImports.emit(show);
    });
  }
  showDetails(history: CoreModelImportHistory): void {
    this.selectedConcepts = history.concepts;
    if (isFailedConcepts(history)) {
      this._flatConcepts = TableUtils.toFlatData<
        TestedCoreModelConcept,
        FlatTestedCoreModelConcept
      >(history.concepts);
      this.flatConcepts = this._flatConcepts;
      this.coreModelConceptsNbErrors = ErrorUtils.concatenatedNbErrors(
        history.concepts,
        TestedCoreModelConceptFieldEnum.Conflicts
      );
      this.showOnlyErrors = this.coreModelConceptsNbErrors > 0;
    } else {
      this.flatConcepts = TableUtils.toFlatData<
        ImportedCoreModelConcept,
        FlatImportedCoreModelConcept
      >((history as SuccessImportHistory).concepts);
    }
    this._dialog.open(
      {
        type: 'display',
        message: '',
        title: `${history.name} ${history.version}`,
        template: this.detailTemplate,
        okCaption: _('window.close'),
      },
      '50vw',
      '20rem'
    );
  }
  filterOnConceptsInConflicts(doFilter: boolean): void {
    if (doFilter) {
      this.flatConcepts = this._flatConcepts.filter((concept) => concept.conflicts.length > 0);
    } else {
      this.flatConcepts = this._flatConcepts;
    }
    this.showOnlyErrors = doFilter;
    this.cd.markForCheck();
  }

  customizeCell(queryCellInfo: QueryCellInfoEventArgs): void {
    TableUtils.addIssuesBackgroundColor(
      CoreModelsImportHistoryFieldEnum.Status,
      queryCellInfo,
      statusIsFailed
    );
  }

  private getColumnWidth(field: CoreModelsImportHistoryFieldEnum): string | undefined {
    switch (field) {
      case CoreModelsImportHistoryFieldEnum.Date:
      case CoreModelsImportHistoryFieldEnum.Username:
      case CoreModelsImportHistoryFieldEnum.Status:
        return '10%';
      case CoreModelsImportHistoryFieldEnum.Name:
        return '12%';
      case CoreModelsImportHistoryFieldEnum.Version:
      case CoreModelsImportHistoryFieldEnum.View:
        return '8%';
      default:
        return undefined;
    }
  }

  private activeFilter(field: CoreModelsImportHistoryFieldEnum): boolean {
    return field === CoreModelsImportHistoryFieldEnum.View ? false : this.defaultActiveFilter;
  }
}
