import type { Meta, StoryObj } from '@storybook/angular';
import { TxDatepickerComponent } from './datepicker.component';
import { FormControl } from '@angular/forms';

const meta: Meta<TxDatepickerComponent> = {
  component: TxDatepickerComponent,
  title: 'Generic fields/TxDatepickerComponent',
  tags: ['autodocs'],
};
export default meta;
type Story = StoryObj<TxDatepickerComponent>;

export const Primary: Story = {
  args: {
    label: 'date',
    placeholder: 'date',
    showDateFormatHint: false,
    width: '100%',
    singleDateCtrl: new FormControl(),
    startDateCtrl: new FormControl(),
    endDateCtrl: new FormControl(),
  },
};
