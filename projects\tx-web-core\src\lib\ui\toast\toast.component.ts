import { Component, Inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { toastAnimations, ToastAnimationState } from './toast.animation';
import { ToastData } from './toast.models';
import { ToastRef } from './toast.reference';
import { TOAST_DATA_TOKEN } from './toast-data-token';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'tx-toast',
  standalone: true,
  imports: [FontAwesomeModule, MatProgressBarModule, TranslateModule, CommonModule],
  templateUrl: './toast.component.html',
  styleUrls: ['./toast.component.scss'],
  animations: [toastAnimations.fadeToast],
})
export class ToastComponent implements OnInit, OnDestroy {
  animationState: ToastAnimationState = 'default';
  private intervalId?: number;

  constructor(@Inject(TOAST_DATA_TOKEN) public data: ToastData, readonly ref: ToastRef) {}

  ngOnInit(): void {
    if (typeof this.data.isUnread === 'undefined') {
      this.data.isUnread = true;
    }
    if (this.data.interval !== 0) {
      this.intervalId = window.setTimeout(
        () => (this.animationState = 'closing'),
        this.data.interval ? this.data.interval : 5000
      );
    }
  }

  ngOnDestroy() {
    clearTimeout(this.intervalId);
  }

  afterDisplay() {
    this.ref.afterDisplay();
  }

  close() {
    this.ref.close();
  }

  onClose() {
    this.animationState = 'closing';
  }

  onFadeFinished(state: string) {
    const isFadeOut = state === 'closing';
    const isFinished = this.animationState === 'closing';

    if (!isFadeOut && !isFinished) {
      this.afterDisplay();
    } else if (isFadeOut && isFinished) {
      this.close();
    }
  }
}
