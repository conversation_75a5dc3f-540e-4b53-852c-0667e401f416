import { Component, Input, ViewChild } from '@angular/core';
import { TxChipsFieldComponent } from '../../generic-fields/chips-field/chips-field.component';
import { TxInputObjectFieldComponent } from '../_system/input-object-field/input-object-field.component';
import { TxAttributeUrl } from '../../../services/structure/models/attribute';
import { LegacyTxDataString, LegacyTxDataTab } from '../../../services/structure/models/data';
import { LegacyTxAttributesService } from '../../../services/structure/services/attributes.service';
import { UrlUtilsHelper } from '../../../utilities/legacy-url-utils';

@Component({
  selector: 'tx-url-object-field',
  templateUrl: './url-object-field.component.html',
  styleUrls: [
    '../../generic-fields/chips-field/chips-field.component.scss',
    './url-object-field.component.scss',
  ],
})
export class TxUrlObjectFieldComponent extends TxInputObjectFieldComponent {
  @Input() declare data: LegacyTxDataString;
  @Input() removable: Boolean;
  @Input() isUrlField: Boolean;
  @Input() multiple!: Boolean;
  @Input() visible!: boolean;
  @Input() selectable!: boolean;
  @Input() addOnBlur = true;

  @ViewChild('chipsField') chipsField!: TxChipsFieldComponent;

  _urlUtilsHelper = new UrlUtilsHelper();

  declare attribute: TxAttributeUrl;
  maxChipsMessage = "Nombre d'url(s) max atteint";
  constructor(public attributeService: LegacyTxAttributesService) {
    super(attributeService);
    this.removable = true;
    this.isUrlField = true;
  }

  initProperties() {
    super.initProperties();
    if (this.attribute) {
      this.multiple = this.attribute.isList;
    }
  }

  initValue() {
    // if (this.data) {
    //   this.field.value = this.data.value.split('\n').map(m => ({ name: m }));
    // }
    // this.field.classes = 'mediumField';
  }

  valueIsEmpty(): boolean {
    return (
      this.control.value === '' ||
      this.control.value === undefined ||
      this.control.value === null ||
      (Array.isArray(this.control.value) &&
        this.control.value.length <= 1 &&
        this.control.value[0] !== 0 &&
        !this.control.value[0])
    );
  }

  getData(): LegacyTxDataTab {
    const values = this.control.value;
    return new LegacyTxDataTab(this.idObject, this.idAttribute, values);
  }

  setData(data: LegacyTxDataTab) {
    if (this.chipsField) {
      this.chipsField.clearField();
      this.chipsField.addList(data.value as string | string[]);
      // this.chipsField.isVisualisationSelected = data.boolVisualisation;
    } else {
      this.control.setValue(data.value);
    }
  }

  clear() {
    if (this.chipsField) {
      this.chipsField.clearField();
    }
  }

  isDirectoryUrl(url: string): boolean {
    return this._urlUtilsHelper.isDirectoryUrl(url);
  }

  openUrl(url: string) {
    if (!this.isDirectoryUrl(url)) {
      window.open(this._urlUtilsHelper.completeUrl(url), '_blank');
    }
  }
}
