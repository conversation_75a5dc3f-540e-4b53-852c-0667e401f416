import { type Meta, type StoryObj } from '@storybook/angular';
import { TxObjectTreeGridComponent } from './object-tree-grid.component';
import { DataBaseRights, TxLockingType, TxObjectTypeType } from '@bassetti-group/tx-web-core/src/public-api';

const meta: Meta<TxObjectTreeGridComponent> = {
  component: TxObjectTreeGridComponent,
  title: 'Tree Grid/TxObjectTreeGridComponent',
  tags: ['autodocs'],
};
export default meta;
type Story = StoryObj<TxObjectTreeGridComponent>;

export const Primary: Story = {
  args: {
    idObjectType: 0,
    showCheckbox: true,
    displayCheckedRowsOnInit: true,
    folderCheckable: true,
    reloadObjectsOnSameOT: false,
    displayFilteredSelectionButton: false,
    multipleSelection: true,
    objectsChecked: [],
    objectType : {
        id: 1,
        idObjectTypeParent: undefined,
        icon: 262,
        isFolder: false,
        type: TxObjectTypeType.Portal,
        hasDistinctName: true,
        isVisible: true,
        lockingType: TxLockingType.Auto,
        lockingDuration: 0,
        displayResultInTextSearch: true,
        right: DataBaseRights.DbrRead,
        order: 1,
        name: 'Départements',
        tags: []
      },
    dataFiltered : [{id : 1,
      name : "Informations générales",
      icon : "./img/icons/svg/0.svg",
      txObject : {
        id : 1,
        name : "",
        order : 1,
        isParent : true,
        isFolder : true,
        idObjectType : 1,
        idOwnerObject: 2,
        creationDate : "01/2025",
        tags: []
      }
    },
    {id : 2,
      name : "Prénom",
      icon : "./img/icons/svg/184.svg",
      idParent :1,
      txObject : {
        idObjectParent : 1,
        id : 2,
        name : "Prénom",
        order : 2,
        isParent : false,
        isFolder : false,
        idObjectType : 1,
        idOwnerObject: 2,
        creationDate : "01/2025",
        tags: []
      }
    },
    {id : 3,
      name : "Nom",
      icon : "./img/icons/svg/184.svg",
      idParent :1,
      txObject : {
        idObjectParent : 1,
        id : 3,
        name : "Nom",
        order : 3,
        isParent : false,
        isFolder : false,
        idObjectType : 1,
        idOwnerObject: 2,
        creationDate : "01/2025",
        tags: []
      }
    },
    {id : 4,
      name : "E-mail",
      icon : "./img/icons/svg/184.svg",
      idParent :1,
      txObject : {
        idObjectParent : 1,
        id : 4,
        name : "E-mail",
        order : 4,
        isParent : false,
        isFolder : false,
        idObjectType : 1,
        idOwnerObject: 4,
        creationDate : "01/2025",
        tags: []
      }
    }]
  },
};
