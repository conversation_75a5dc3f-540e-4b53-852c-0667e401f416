import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { DateAdapter } from '@angular/material/core';
import { MatDatepickerInputEvent, MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { Subject, takeUntil } from 'rxjs';
import { LocaleService } from '@bassetti-group/tx-web-core/src/lib/utilities';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';

@Component({
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatDatepickerModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    TranslateModule,
    MatInputModule,
    CommonModule,
  ],
  selector: 'tx-datepicker',
  templateUrl: './datepicker.component.html',
  styleUrls: ['./datepicker.component.scss'],
})
export class TxDatepickerComponent implements OnInit, OnDestroy {
  //To make a range date picker just set up the startDateCtrl and endDateCtrl FormControl. If not, just set up the singleDateCtrl
  @Input() label = '';
  @Input() value: Date | undefined;
  @Input() placeholder = '';
  @Input() showDateFormatHint = false;
  @Input() width = '100%';
  @Input() singleDateCtrl?: FormControl<Date | null>;
  @Input() startDateCtrl?: FormControl<Date>;
  @Input() endDateCtrl?: FormControl<Date>;
  @Input() minDate?: Date;
  @Input() maxDate?: Date;

  @Output() dateChange = new EventEmitter<Date | string | null>();

  private unsubscribe$ = new Subject<void>();

  constructor(private localeService: LocaleService, private _adapter: DateAdapter<any>) {}

  ngOnInit() {
    //here to use the component in a simple way, with the "value" to init input and the onDateChange to update value in parent component
    if (!this.singleDateCtrl && !this.startDateCtrl && !this.endDateCtrl) {
      this.singleDateCtrl = new FormControl<Date | null>(this.value ?? null);
    }
    this.localeService.$localeChanged
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((locale) => this._adapter.setLocale(locale));
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  getCurrentDateFormat(): string {
    return this.localeService.getDateFormat();
  }

  onDateChange(event: MatDatepickerInputEvent<Date, string>) {
    this.dateChange.emit(event.value);
  }

  clearDate() {
    this.singleDateCtrl?.setValue(null);
    this.dateChange.emit(null);
  }
}
