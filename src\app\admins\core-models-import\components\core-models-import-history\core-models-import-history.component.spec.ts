import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CoreModelsImportHistoryComponent } from './core-models-import-history.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Mock<PERSON>rovider } from 'ng-mocks';
import { MOCK_DIALOG_SERVICE, TxDialogService } from '@bassetti-group/tx-web-core';

describe('CoreModelsImportHistoryComponent', () => {
  let component: CoreModelsImportHistoryComponent;
  let fixture: ComponentFixture<CoreModelsImportHistoryComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CoreModelsImportHistoryComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [MockProvider(TxDialogService, MOCK_DIALOG_SERVICE, 'useValue')],
    }).compileComponents();

    fixture = TestBed.createComponent(CoreModelsImportHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
