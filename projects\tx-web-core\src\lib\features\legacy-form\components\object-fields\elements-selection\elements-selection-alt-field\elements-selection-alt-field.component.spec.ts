import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { TxElementsSelectionAltFieldComponent } from './elements-selection-alt-field.component';
import { LegacyTxAttributesService } from '../../../../services/structure/services/attributes.service';
import { AttributesMockService } from '../../../../testing.mock';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatChipsModule } from '@angular/material/chips';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ChipListModule } from '@syncfusion/ej2-angular-buttons';

describe('ElementsSelectionAltFieldComponent', () => {
  let component: TxElementsSelectionAltFieldComponent;
  let fixture: ComponentFixture<TxElementsSelectionAltFieldComponent>;
  let element;
  let attributesService: LegacyTxAttributesService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TxElementsSelectionAltFieldComponent],
      providers: [{ provide: LegacyTxAttributesService, useClass: AttributesMockService }],
      imports: [
        MatChipsModule,
        MatTooltipModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatInputModule,
        BrowserAnimationsModule,
        ChipListModule,
      ],
    }).compileComponents();
    attributesService = TestBed.inject(LegacyTxAttributesService);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TxElementsSelectionAltFieldComponent);
    component = fixture.componentInstance;
    element = fixture.nativeElement;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
