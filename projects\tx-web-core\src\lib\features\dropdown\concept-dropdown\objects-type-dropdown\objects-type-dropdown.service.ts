import { TxObjectType } from '@bassetti-group/tx-web-core/src/lib/business-models';
import { Injectable } from '@angular/core';
import { TxObjectsTypeService } from '@bassetti-group/tx-web-core/src/lib/data-access/structure';

@Injectable()
export class TxObjectsTypeDropdownService {
  counter = 1;

  constructor(private objectTypeService: TxObjectsTypeService) {}

  createOption(
    objectType: TxObjectType,
    selectedIds: string[] = [],
    linear = false,
    displayIcon = true,
    hidden = false
  ): any {
    if (objectType) {
      const option: { [key: string]: any } = {
        id: '' + objectType.id,
        name: objectType.name,
      };

      if (objectType.idObjectTypeParent) {
        option.idParent = '' + objectType.idObjectTypeParent;
      }

      if (!linear && objectType.options?.children) {
        option.isParent = true;
        option.expanded = true;
      }

      if (selectedIds.includes('' + objectType.id)) {
        option.selected = true;
      }

      if (hidden) {
        option.hidden = true;
      }

      if (displayIcon) {
        option.image = this.objectTypeService.getIconPath(objectType.id);
      }

      this.counter++;

      return option;
    }
  }

  createOptions(
    objectTypes: TxObjectType[],
    selectedIds: string[] = [],
    linear = false,
    displayIcon = true,
    hidden = false
  ): any[] {
    const ots: TxObjectType[] = JSON.parse(JSON.stringify(objectTypes));

    ots.forEach((ot) => {
      if (ot?.idObjectTypeParent) {
        const otParent = ots.find((o) => o.id === ot.idObjectTypeParent);
        if (otParent) {
          if (!otParent.options) {
            otParent.options = { children: [] };
          }

          otParent.options.children.push(ot);
        }
      }
    });

    return ots.map((o) => this.createOption(o, selectedIds, linear, displayIcon, hidden));
  }
}
