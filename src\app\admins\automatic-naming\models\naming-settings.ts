import { Subscriber } from 'rxjs';
import { ConfigurationSubConceptElement } from 'src/app/shared/components/configurations-framework/models/configurations-framework-model';

export enum NamingSettingsCaseValue {
  asIs = 'chAsIs',
  uppercase = 'chUppercase',
  lowercase = 'chLowercase',
}

export enum NamingSettingsIndexType {
  alphaLower = 'ifAlphaLower',
  alphaUpper = 'ifAlphaUpper',
  num = 'ifNumerical',
}

export enum NamingSettingsIncrementationType {
  data = 'itData',
  link = 'itLink',
}

export enum NamingSettingsType {
  text = 'Text',
  date = 'Date',
  username = 'Username',
  data = 'Data',
  index = 'Index',
}

export interface NamingSettings extends ConfigurationSubConceptElement {
  type: NamingSettingsType;
  value?: string;
  previewValue?: string;
  previewFormat?: string;
  previewAttributeList?: string[];
  sDateFormat?: string;
  sTags_Att?: string;
  sCase?: NamingSettingsCaseValue;
  sCaseSpecified?: boolean;
  iMaxSize?: number;
  iMaxSizeSpecified?: boolean;
  bPerData?: boolean;
  bPerDataSpecified?: boolean;
  sDataSeparator?: string;
  sPrefix?: string;
  sSuffix?: string;
  bDeleteExtension?: boolean;
  bDeleteExtensionSpecified?: boolean;
  sAbbreviation?: string;
  sIndexFormat?: NamingSettingsIndexType;
  sIndexFormatSpecified?: boolean;
  iNumberOfCharacters?: number;
  iNumberOfCharactersSpecified?: boolean;
  bConsiderAsIndex?: boolean;
  bConsiderAsIndexSpecified?: boolean;
  bTranslateIfNeeded?: boolean;
  bTranslateIfNeededSpecified?: boolean;
  sIncrementationType?: NamingSettingsIncrementationType;
  sIncrementationTypeSpecified?: boolean;
  sTag_Att_Index?: string;
  sTag_Att_Data?: string;
  sTag_Obj_Data?: string;
  sTags_Att_ReferenceIndex?: string;
  sInitialIndex?: string;
  bIncrement?: boolean;
  bIncrementSpecified?: boolean;
}

export type ConfirmAutoTagAttributeArgs = {
  idsWithMissingTags: number[];
  observer: Subscriber<any>;
};
