import { TxConcept } from './tx-concept.model';

export enum TxFileIndexType {
  // No index
  none = 'ftitNone',
  // An index specific to the file type
  fileType = 'ftitPerFileType',
  // A global index
  global = 'ftitGlobal',
}

export interface TxFileType extends TxConcept {
  baseName: string;
  fileIndexType: TxFileIndexType;
  includeDate: boolean;
  indexTitle: string;
  isAlphabeticalIndex: boolean;
  isAlphabeticalVersion: boolean;
  isBasenameModifiable: boolean;
  isIndexModifiable: boolean;
  isVersioned: boolean;
  relativeDirectory: string;
  versionTitle: string;
}

export class CTxFileType implements TxFileType {
  id: number;
  name: string;
  tags: string[];
  order?: number;
  options?: any;
  baseName: string;
  explanation?: string;
  fileIndexType: TxFileIndexType;
  includeDate: boolean;
  indexTitle: string;
  isAlphabeticalIndex: boolean;
  isAlphabeticalVersion: boolean;
  isBasenameModifiable: boolean;
  isIndexModifiable: boolean;
  isVersioned: boolean;
  relativeDirectory: string;
  versionTitle: string;

  constructor(fileType?: any) {
    this.id = fileType.id;
    this.baseName = fileType.baseName ?? '';
    this.isBasenameModifiable = fileType.isBasenameModifiable ?? true;
    this.isVersioned = fileType.isVersioned ?? false;
    this.includeDate = fileType.includeDate ?? false;
    this.isAlphabeticalIndex = fileType.isAlphabeticalIndex ?? false;
    this.isAlphabeticalVersion = fileType.isAlphabeticalVersion ?? false;
    this.isIndexModifiable = fileType.isIndexModifiable ?? false;
    this.relativeDirectory = fileType.relativeDirectory ?? '';
    this.indexTitle = fileType.indexTitle ?? '';
    this.versionTitle = fileType.versionTitle ?? '';
    this.fileIndexType = fileType.fileIndexType ?? TxFileIndexType.none;
    this.name = fileType.name ?? '';
    this.tags = fileType.tags ?? [];
    this.explanation = fileType.explanation ?? '';
    this.options = fileType.options || {};
    this.options.isUsedByAttribute = fileType.isUsedByAttribute || false;
    this.options.isUsed = fileType.isUsed || false;
  }
}
