<div
  *ngIf="!readMode"
  class="form-field"
  [ngClass]="{ 'form-field-error': control && !control.valid && !disabled }">
  <mat-label
    [class]="
      control.hasError('required')
        ? 'span-error mat-form-label form-label'
        : 'mat-form-label form-label'
    "
    [matTooltip]="labelTooltip"
    matTooltipClass="mat-label-tooltip mat-tooltip-multiline"
    matTooltipShowDelay="500"
    matTooltipPosition="above">
    {{ label }}
    <span
      *ngIf="required"
      [class]="
        control && control.hasError('required') ? 'span-error mat-form-label' : 'mat-form-label'
      ">
      *</span
    >
  </mat-label>
  <mat-button-toggle-group
    multiple
    name="fontStyle"
    appearance="legacy"
    aria-label="Font Style"
    #group="matButtonToggleGroup"
    [formControl]="control"
    [required]="required"
    (change)="toggleChange($event)">
    <mat-button-toggle
      class="mat-button-toggle-left"
      #leftBtn
      [style.min-width.px]="minWidthButtonToggle"
      [matTooltip]="trueCaption"
      matTooltipShowDelay="500"
      matTooltipPosition="above"
      [value]="true"
      >{{ trueCaption }}</mat-button-toggle
    >
    <mat-button-toggle
      class="mat-button-toggle-right"
      #rightBtn
      [style.min-width.px]="minWidthButtonToggle"
      [matTooltip]="falseCaption"
      matTooltipShowDelay="500"
      matTooltipPosition="above"
      [value]="false"
      >{{ falseCaption }}</mat-button-toggle
    >
  </mat-button-toggle-group>
  <mat-error
    class="boolean-error"
    *ngIf="control && control.hasError('required'); else emptyDivTemplate"
    ><strong>Required</strong></mat-error
  >
</div>

<ng-template #emptyDivTemplate>
  <div class="empty-div"></div>
</ng-template>

<div *ngIf="readMode" class="form-field read-field">
  <mat-label
    class="form-label mat-form-label"
    [matTooltip]="labelTooltip"
    matTooltipClass="mat-label-tooltip"
    matTooltipShowDelay="500"
    matTooltipPosition="above">
    {{ label }}
  </mat-label>
  <div class="read-form-field">
    <div
      class="read-bool-container"
      [ngClass]="{ 'true-caption-chip': isTrueValue, 'false-caption-chip': isFalseValue }">
      <span>{{ control.value }}</span>
    </div>
  </div>
</div>
