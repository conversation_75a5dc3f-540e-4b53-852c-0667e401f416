<ng-template #dialogContent>
  <div class="dialog-header background-accent">
    <fa-icon [icon]="['fal', 'file-edit']" size="lg"></fa-icon>
    <span>{{ 'admins.resources.renameFile' | translate }}</span>
  </div>
  <div class="dialog-content-container">
    <div class="dialog-message">
      {{
        'admins.resources.renameFileWithIncrement'
          | translate : { oldName: oldName, newName: newName }
      }}
    </div>
    <div class="dialog-message annotation">
      {{ 'admins.resources.sameFileExisting' | translate }}
    </div>
  </div>
  <div class="button-container">
    <button mat-flat-button color="accent" mat-dialog-close (click)="valid()">
      {{ 'window.yes' | translate }}
    </button>
    <button mat-stroked-button mat-dialog-close (click)="close()">
      {{ 'window.no' | translate }}
    </button>
  </div>
</ng-template>
