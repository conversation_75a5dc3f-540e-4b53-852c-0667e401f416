import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AccessRightsGuard } from 'src/app/core/guards/access-rights.guard';
import { AuthenticationGuard } from 'src/app/core/guards/authentication.guard';
import { BusinessViewsComponent } from './components/business-views.component';
import { AdminRights } from '@bassetti-group/tx-web-core';

const routes: Routes = [
  {
    path: '',
    component: BusinessViewsComponent,
    data: {
      breadcrumb: 'admins.wording.businessViews',
      adminRights: AdminRights.CanAdministrateStructure,
    },
    canActivate: [AuthenticationGuard, AccessRightsGuard],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BusinessViewsRoutingModule {}
