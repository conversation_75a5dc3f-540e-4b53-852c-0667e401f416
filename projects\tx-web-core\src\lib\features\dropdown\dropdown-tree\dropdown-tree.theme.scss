@use '@angular/material' as mat;

@mixin dropdown-tree-theme($theme) {
  // retrieve variables from theme
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);

  .dropdown-input-search-container {
    background: mat.m2-get-color-from-palette($background, form-field-background);
  }

  .dropdown-tree-input-search {
    background: mat.m2-get-color-from-palette($background, form-field-background);
    color: mat.m2-get-color-from-palette($foreground, base);
  }

  .dropdown-clear-input:hover {
    background: mat.m2-get-color-from-palette($foreground, grey20);
  }

  .dropdown-tree-global-panel {
    padding: 0 !important;
    min-width: max-content;
    max-height: 400px;
    position: absolute !important;
  }

  .dropdown-tree-container {
    background: mat.m2-get-color-from-palette($background, form-field-background);
  }

  .field-without-hint .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }
}
