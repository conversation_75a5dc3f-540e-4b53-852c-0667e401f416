import { Observable } from 'rxjs';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Renderer2 } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { TreeViewAllModule } from '@syncfusion/ej2-angular-navigations';
import { MockComponent, MockDirective } from 'ng-mocks';
import {
  SessionServiceMock,
  ToastServiceMock,
  HelpBoxServiceMock,
  AttributeSetsServiceMock,
  ObjectsTypeServiceMock,
  ActivatedRouteStub,
  AppServiceMock,
} from 'src/app/app.testing.mock';
import { BreadcrumdComponent } from 'src/app/shared/components/breadcrumd/breadcrumd.component';
import { ConceptsListComponent } from 'src/app/shared/components/concepts-list/concepts-list.component';
import { SessionService } from 'src/app/core/services/session/session.service';
import {
  ToastService,
  NoRecordComponent,
  TxObjectsTypeDropdownComponent,
  TxObjectsTypeService,
  CTxAttributeSet,
  TxDialogService,
} from '@bassetti-group/tx-web-core';
import { BusinessViewsComponent } from './business-views.component';
import { ActivatedRoute } from '@angular/router';
import { HelpBoxService } from 'src/app/core/services/help-box/help-box.service';
import { OffsetTopDirective } from 'src/app/shared/directives/scroll/offset-top.directive';
import { TruncatePipe } from 'src/app/shared/pipes/string/truncate.pipe';
import { ConceptFormPaneComponent } from 'src/app/shared/components/form-pane/concept-form-pane/concept-form-pane.component';
import { AttributeSetsService } from 'src/app/core/services/structure/attribute-sets.service';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { DialogConfirmServiceMock } from 'src/app/shared/tests/shared-testing-mock';
import { LoaderComponent } from 'src/app/shared/components/loader/loader.component';
import { AppService } from 'src/app/core/services/app.service';
import { LastSaveChipComponent } from 'src/app/shared/components/last-save-chip/last-save-chip.component';

describe('BusinessViewsComponent', () => {
  let component: BusinessViewsComponent;
  let fixture: ComponentFixture<BusinessViewsComponent>;
  let sessionService: SessionService;
  let attributeSetsService: AttributeSetsService;
  let activatedRoute: ActivatedRouteStub;
  let helpboxService: HelpBoxService;

  beforeEach(async () => {
    activatedRoute = new ActivatedRouteStub();
    await TestBed.configureTestingModule({
      declarations: [
        BusinessViewsComponent,
        MockComponent(TxObjectsTypeDropdownComponent),
        MockComponent(ConceptsListComponent),
        MockComponent(BreadcrumdComponent),
        MockComponent(NoRecordComponent),
        MockComponent(ConceptFormPaneComponent),
        MockComponent(LoaderComponent),
        MockComponent(LastSaveChipComponent),
        MockDirective(OffsetTopDirective),
        TruncatePipe,
      ],
      imports: [
        NoopAnimationsModule,
        MatDividerModule,
        FontAwesomeTestingModule,
        MatTooltipModule,
        MatFormFieldModule,
        MatInputModule,
        MatProgressBarModule,
        MatSlideToggleModule,
        MatListModule,
        TreeViewAllModule,
        TranslateTestingModule.withTranslations({ en: {}, fr: {} }),
      ],
      providers: [
        Renderer2,
        { provide: SessionService, useClass: SessionServiceMock },
        { provide: ToastService, useClass: ToastServiceMock },
        { provide: AttributeSetsService, useClass: AttributeSetsServiceMock },
        { provide: TxObjectsTypeService, useClass: ObjectsTypeServiceMock },
        { provide: ActivatedRoute, useValue: activatedRoute },
        { provide: HelpBoxService, useClass: HelpBoxServiceMock },
        { provide: TxDialogService, useClass: DialogConfirmServiceMock },
        { provide: AppService, useClass: AppServiceMock },
      ],
    }).compileComponents();

    sessionService = TestBed.inject(SessionService);
    attributeSetsService = TestBed.inject(AttributeSetsService);
    helpboxService = TestBed.inject(HelpBoxService);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BusinessViewsComponent);

    component = fixture.componentInstance;
    (component.dropdownObjectTypeFilter as any) = {
      dropdownTree: {
        value: [],
      },
    };
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Initial state', () => {
    it('should subscribe to language loading state', () => {
      const spyLoading = jest.spyOn(sessionService, 'getLoadingState');
      fixture.detectChanges(); // initialize component

      expect(spyLoading).toBeCalled();
    });

    it('should subscribe to helpbox states', () => {
      const spyGet = jest.spyOn(helpboxService, 'getMultipleStates');
      fixture.detectChanges(); // initialize component

      expect(spyGet).toBeCalled();
    });

    it('should get Data', () => {
      const spyGet = jest.spyOn(attributeSetsService, 'listBusinessViews');
      fixture.detectChanges(); // initialize component

      expect(spyGet).toBeCalled();
    });
  });

  describe('Check the beforeUnloadHandler function', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('The function pushChangedData must be called when beforeUnload is triggered', () => {
      const spy = jest.spyOn(component, 'pushChangedData');

      component.beforeUnloadHandler();

      expect(spy).toBeCalled();
    });
  });

  describe('Test pushChangedData function which permit to save fileTypes modifications', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should call edit function from file type service', () => {
      const spyEdit = jest.spyOn(attributeSetsService, 'editAttributeSets');
      component.businessViews.push({
        id: 5,
        name: 'new fileType to save',
        description: 'new description',
      } as CTxAttributeSet);
      component.previousBusinessViews.push({
        id: 5,
        name: 'new fileType to save',
        description: '',
      } as CTxAttributeSet);
      const filesTypeToSave = component.businessViews[component.businessViews.length - 1];
      component.pushChangedData();

      expect(spyEdit).toBeCalledWith([filesTypeToSave]);
    });

    it("shouldn't call edit function from file type service", () => {
      const spyEdit = jest.spyOn(attributeSetsService, 'editAttributeSets');
      component.businessViews.push({
        id: 5,
        name: 'new fileType to save',
        description: 'new description',
      } as CTxAttributeSet);
      component.previousBusinessViews.push({
        id: 5,
        name: 'new fileType to save',
        description: 'new description',
      } as CTxAttributeSet);
      component.pushChangedData();

      expect(spyEdit).not.toBeCalled();
    });
  });

  describe('Test updateBusinesViewsFiltered function which permit to update businessView filtered from ObjectType Dropdown', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('the filtered list has to be the same than the business views list --> same length', () => {
      component.objectTypesFilteredIds = [];
      component.businessViews = [
        { id: 5, name: 'new fileType to save', description: 'new description' } as CTxAttributeSet,
      ];

      component.updateBusinessViewsFiltered();

      expect(component.businessViewsFiltered.length).toEqual(component.businessViews.length);
    });

    it('the filtered list has to be the same than the business views list --> same object id', () => {
      component.objectTypesFilteredIds = [];
      component.businessViews = [
        { id: 5, name: 'new fileType to save', description: 'new description' } as CTxAttributeSet,
      ];

      component.updateBusinessViewsFiltered();

      expect(component.businessViewsFiltered[0].id).toEqual(component.businessViews[0].id);
    });

    it('Add an id object type id to the objectTypesFilteredIds to add and check new objectType in the dropDownList of objectTypes', () => {
      component.objectTypesFilteredIds = ['6'];
      component.businessViews = [
        { id: 5, name: 'new fileType to save', description: 'new description' } as CTxAttributeSet,
      ];

      component.updateBusinessViewsFiltered(3);

      expect(component.objectTypesFilteredIds[1]).toEqual('3');
    });
  });

  describe('Test isDeleteButtonDisabled function which permit to update the delete button activation', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('The businessView cannot be removable --> it has a tx tag', () => {
      const disabled = component.isDeleteButtonDisabled({
        id: 5,
        tags: ['txBool'],
      } as CTxAttributeSet);

      expect(disabled).toBeTruthy();
    });

    it('The businessView can be removable --> tag not start with "tx"', () => {
      const disabled = component.isDeleteButtonDisabled({
        id: 5,
        tags: ['tBool'],
      } as CTxAttributeSet);

      expect(disabled).toBeFalsy();
    });
  });

  describe('Test onAddingBusinessView function which permit to add a new business view', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('Should call the addAttributeSet function of attributeSetService', () => {
      const spy = jest.spyOn(attributeSetsService, 'addAttributeSet');
      const args = { data: { id: 12, name: 'new BV', idObjectType: 5 }, observer: Observable };
      component.onAddingBusinessView(args);

      expect(spy).toBeCalled();
    });
  });

  describe('Test onObjectTypesDropdownChange function which permit to update object types on dropdown change', () => {
    beforeEach(() => {
      component.objectTypesFilteredIds = ['1', '2', '3', '4'];
      component.objectTypesDisabledIds = ['2', '3', '4'];
    });

    it('Should uncheck all object types that are not from main filter', () => {
      component.onObjectTypesDropdownChange([], false);
      expect(component.objectTypesFilteredIds).toEqual(['2', '3', '4']);
    });

    it('Should update object types checked and disabled (change from main filter)', () => {
      component.onObjectTypesDropdownChange(['4', '5', '6'], true);
      expect(component.objectTypesFilteredIds).toEqual(['1', '4', '5', '6']);
      expect(component.objectTypesDisabledIds).toEqual(['4', '5', '6']);
    });
  });
});
