import { ModuleWithProviders, NgModule, Provider } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface TxStructureModuleConfig {
  loader?: Provider;
  compiler?: Provider;
  parser?: Provider;
  missingTranslationHandler?: Provider;
  isolate?: boolean;
  extend?: boolean;
  useDefaultLang?: boolean;
  defaultLanguage?: string;
}

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    // HttpClientModule
  ],
  // providers: [
  //   TxApiService
  // ],
  exports: [
    // HttpClientModule
  ],
})
export class TxStructureModule {
  // static forRoot(config: TxStructureModuleConfig): ModuleWithProviders<TxStructureModule> {
  //   return {
  //     ngModule: TxStructureModule,
  //     providers: [{ provide: TxApiService, useValue: config }],
  //   }
  // }
}
