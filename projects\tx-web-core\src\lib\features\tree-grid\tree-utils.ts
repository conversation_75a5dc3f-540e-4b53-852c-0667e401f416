import { map, Observable } from 'rxjs';
import { TxTreeGridChildren } from './tree-grid.interface';

export function addLevelsToTree<T extends TxTreeGridChildren<T>>(
  treeData: T[],
  childMapping: keyof TxTreeGridChildren<T>,
  level = 0
) {
  return treeData.map((node) => {
    const newNode = { ...node, level };

    if (node[childMapping]) {
      newNode[childMapping] = addLevelsToTree(node[childMapping] as T[], childMapping, level + 1);
    }

    return newNode;
  });
}

export function generateTree<T>(
  input: (T & TxTreeGridChildren<T>)[],
  idMapping: keyof T,
  parentMapping: keyof T
): (T & TxTreeGridChildren<T>)[] {
  const idMap: { [key: number]: T & TxTreeGridChildren<T> } = {};
  const root: (T & TxTreeGridChildren<T>)[] = [];

  input.forEach((item) => {
    if (!idMap[item[idMapping] as number]) {
      idMap[item[idMapping] as number] = { ...item };
    } else {
      Object.assign(idMap[item[idMapping] as number], item);
    }

    const currentItem = idMap[item[idMapping] as number];

    if (item[parentMapping] !== undefined) {
      if (!idMap[item[parentMapping] as number]) {
        idMap[item[parentMapping] as number] = { children: [] } as T & TxTreeGridChildren<T>;
      }

      const parent = idMap[item[parentMapping] as number];

      parent.children ??= [];
      parent.children.push(currentItem);
    } else {
      root.push(currentItem);
    }
  });

  return root;
}
export function assignTxId<T extends TxTreeGridChildren<T>>(
  dataArray: Observable<T[]> | T[],
  childMapping: keyof TxTreeGridChildren<T>,
  primaryKey: keyof T
): Observable<T[]> | T[] {
  let txId = 0;
  const assignIdRecursively = (item: any) => {
    item[primaryKey] = txId++;
    if (item[childMapping] && item[childMapping].length > 0) {
      item[childMapping].forEach((child: any) => {
        assignIdRecursively(child);
      });
    }
  };
  if (dataArray instanceof Observable) {
    return dataArray.pipe(
      map((data) => {
        data.forEach((parent) => {
          assignIdRecursively(parent);
        });
        return data;
      })
    );
  } else {
    dataArray.forEach((parent) => {
      assignIdRecursively(parent);
    });
    return dataArray;
  }
}

export function flattenTree<T>(data: T[], childrenKey: string = 'children') {
  let result: (T & { hasChildren?: boolean })[] = [];
  function flatten(node: any) {
    const { [childrenKey]: children, ...rest } = node;
    result.push({ ...rest, hasChildren: !!children });
    if (children) {
      children.forEach(flatten);
    }
  }
  data.forEach(flatten);
  return result;
}
export function flattenTreeWithChild<T>(data: T[]): T[] {
  let result: T[] = [];
  function flatten(node: any) {
    result.push(node);
    if (node.children) {
      node.children.forEach(flatten);
    }
  }
  data.forEach(flatten);
  return result;
}

export function addLevelsToTreeSourceData<T extends TxTreeGridChildren<T>>(
  data: Observable<T[]> | T[],
  childMapping: keyof TxTreeGridChildren<T>
): Observable<T[]> | T[] {
  if (data instanceof Observable) {
    return data.pipe(map((data) => addLevelsToTree(data, childMapping)));
  } else {
    return addLevelsToTree(data, childMapping);
  }
}

export function updateNodeById<T extends { id: number; children?: T[] }>(
  data: T[],
  newData: T,
  primaryKey: keyof T
): { success: boolean; updatedData: T[] } {
  const updatedData = [...data];
  let found = false;

  function traverse(nodes: T[]): void {
    if (found) {
      return;
    }
    for (let i = 0; i < nodes.length && !found; i++) {
      const node = nodes[i];

      if (node[primaryKey] === newData[primaryKey]) {
        nodes[i] = { ...newData };
        found = true;
        return;
      }

      if (node.children?.length) {
        traverse(node.children);
      }
    }
  }
  traverse(updatedData);
  return {
    success: found,
    updatedData: found ? updatedData : data,
  };
}

export function findNodeById<T extends { id: number | string; children?: T[] }>(
  data: T[],
  id: number | string,
  primaryKey?: keyof T
): T | null {
  if (primaryKey == undefined) {
    return null;
  }
  const stack: T[] = [...data];

  while (stack.length > 0) {
    const node = stack.pop()!;

    if (node[primaryKey] === id) {
      return node;
    }

    if (node.children) {
      stack.push(...node.children);
    }
  }

  return null;
}

export function parentToRootIds<T>(
  data: T[],
  id: number | string,
  primaryKey: keyof T = 'id' as keyof T,
  idParent: keyof T = 'idParent' as keyof T
) {
  const parentIds: (number | string)[] = [];
  function findParent(id: number | string) {
    parentIds.push(id);
    const parentId = data.find((item) => item[primaryKey] === id)?.[idParent];
    if (parentId) {
      findParent(parentId as string | number);
    }
  }
  findParent(id);
  return parentIds.reverse();
}
