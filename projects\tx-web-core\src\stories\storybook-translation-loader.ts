import { HttpClient } from '@angular/common/http';
import { TranslateLoader } from '@ngx-translate/core';
import { of } from 'rxjs';
import { catchError } from 'rxjs/operators';

export class StorybookTranslationLoader extends TranslateLoader {
  constructor(private http: HttpClient, private prefix: string, private suffix: string) {
    super();
  }

  getTranslation(lang: string): any {
    return this.http
      .get(`${this.prefix}${lang}${this.suffix}`)
      .pipe(catchError(this.handlerGetTranslationObjectError));
  }

  private handlerGetTranslationObjectError(err: unknown) {
    return of({});
  }
}
