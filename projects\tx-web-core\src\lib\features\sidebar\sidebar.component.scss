.sidenav-container {
  height: 100%;
}
.sidenav-content-expand {
  margin-left: 240px;
  transition: margin-left 0.3s ease-in-out;
}
.sidenav-content-collapse {
  margin-left: 65px !important;
  transition: margin-left 0.3s ease-in-out;
}
.sidenav-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}
.sidenav {
  width: 240px;
  transition: width 0.3s ease-in-out;

  .navbar-bottom-description {
    width: 100%;
    text-align: center;
    padding: 16px 0px;
  }
  .navbar-logo {
    height: 45px;
    padding: 16px;
  }
  .annotation {
    white-space: nowrap;
  }
}
.sidenav-closed {
  width: 65px;
  transition: width 0.3s ease-in-out;

  .navbar-bottom-description {
    width: 100%;
    text-align: center;
    padding: 16px 0px;

    div {
      width: 38px;
      overflow: hidden;
      white-space: nowrap;
      text-align: center;
      margin: auto;
    }
  }
  .navbar-logo {
    height: 36px;
    padding: 16px;
  }
}
.sideNav-treeview-container {
  flex: 1;
  overflow: auto;
  width: 100%;
}
.sidenav-closed .sideNav-treeview-container {
  ::ng-deep .mat-tree-node .tree-content {
    padding-left: 0px !important;
  }
}
.text-item-sidebar {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 5px;
  font-size: 13px;
}
.main-item-sidebar {
  display: flex;
  padding: 0px 4px 0px 0px;
  opacity: 0.8;
  fa-icon {
    font-size: 13px;
  }
}

.tree-text {
  padding: 0px 16px 0px 8px !important;
}
fa-icon {
  padding: 0px;
  width: 27px;
  min-width: 27px; /* necessary to align icons */

  svg {
    width: 27px !important;
  }
}
