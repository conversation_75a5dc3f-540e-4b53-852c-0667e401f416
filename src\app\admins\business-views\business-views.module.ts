import { NgModule } from '@angular/core';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { SharedModule } from 'src/app/shared/shared.module';
import { BusinessViewsRoutingModule } from './business-views-routing.module';
import { BusinessViewsComponent } from './components/business-views.component';

@NgModule({
  declarations: [BusinessViewsComponent],
  imports: [
    SharedModule,
    BusinessViewsRoutingModule,
    // Material
    MatSlideToggleModule,
  ],
})
export class BusinessViewsModule {
  getComponent() {
    return BusinessViewsComponent;
  }
}
