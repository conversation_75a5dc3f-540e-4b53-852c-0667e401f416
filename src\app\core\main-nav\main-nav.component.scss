.main-content {
  height: 100%;
}

.spacer {
  flex: 1 1 auto;
}
.main-toolbar {
  height: 40px;
  transition: background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),
    box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0 24px;

  .toolbar-logo {
    margin: 0px 8px 0px 24px;
    height: 20px;
    cursor: pointer;
  }

  .main-toolbar-subtitle {
    font-size: 14px;
    font-weight: 400;
  }

  .main-toolbar-search {
    height: 30px;
    width: 350px;
    border: none;
    border-radius: 4px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;

    .filter-field {
      width: 100%;
    }

    input {
      background: transparent;
      outline: none;
      border: none;
      flex: 1;
      font-size: 16px;
    }

    fa-icon {
      font-size: 15px;
      padding: 0px 16px;
    }

    .icon-search {
      padding-right: 8px;
    }
  }

  fa-icon {
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s;
  }
  fa-icon:hover {
    filter: drop-shadow(0 0 10px white);
  }

  .main-toolbar-icon {
    font-size: 15px;
    margin-left: 24px;
  }
}

.menu-icon {
  margin-right: 16px;
}

.tx-icon {
  display: flex;
  align-items: center;

  .tx-icon-img {
    margin-top: 5px;
    .menu-icon {
      width: 20px;
      margin-right: 16px;
    }
  }

  fa-icon {
    margin-right: 0px;
    margin-left: 22px;
  }
}
.menu-flag {
  margin-right: 16px;
  vertical-align: middle;
  height: 24px;
}

.background-transparency {
  background: transparent;
}
.nav-content {
  height: Calc(100% - 40px);
  app-main-filter {
    font-size: 12px;
  }
}

.content-layer {
  height: 100%;
  transform: none !important;
}
::ng-deep app-main-filter .title-container span {
  white-space: nowrap;
}
.main-toolbar-loading {
  position: absolute;
  top: 0px;
  height: 40px;
  width: 100%;
  z-index: 1000;
}
.nav-content-loading {
  position: absolute;
  top: 40px;
  height: calc(100% - 40px);
  width: 100%;
  z-index: 1000;
}
.auto-complete-option {
  .clock-icon {
    padding: 0px 3px;
  }
}

/* Notification */
.unread-notification-background {
  border-radius: 12px;
  height: 12px;
  width: 12px;
  position: absolute;
  right: 66px;
  top: 11px;

  .unread-notification-point {
    display: inherit;
    height: 8px;
    width: 8px;
    margin: 2px;
    border-radius: 8px;
  }
}
